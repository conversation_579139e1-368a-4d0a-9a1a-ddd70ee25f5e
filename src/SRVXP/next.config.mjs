/** @type {import('next').NextConfig} */
// Optimized for Bun runtime
const nextConfig = {
  reactStrictMode: true,
  distDir: process.env.NODE_ENV === "production" ? "build" : ".next",
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
    domains: [
      "source.unsplash.com",
      "images.unsplash.com",
      "ext.same-assets.com",
      "ugc.same-assets.com",
      "web-assets.same.dev",
    ],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "source.unsplash.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "ext.same-assets.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "ugc.same-assets.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "web-assets.same.dev",
        pathname: "/**",
      },
    ],
  },

  // Optimize bundle size
  experimental: {
    optimizeCss: true,
    optimizePackageImports: [
      '@fullcalendar/core',
      '@fullcalendar/daygrid',
      '@fullcalendar/timegrid',
      '@fullcalendar/interaction',
      '@fullcalendar/react',
      'date-fns',
      'react-day-picker',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-popover',
      '@radix-ui/react-select',
      'framer-motion',
      'lucide-react',
    ],
  },

  // Optimize code splitting
  webpack: (config, { isServer }) => {
    // Create custom chunk groups
    config.optimization.splitChunks = {
      chunks: 'all',
      maxInitialRequests: 25,
      minSize: 20000,
      cacheGroups: {
        // Group external dependencies into separate chunks
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name(module) {
            // Get the package name
            const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)?.[1];
            if (!packageName) return 'vendors';

            // Create specific chunks for large packages
            if (['@fullcalendar', 'date-fns', 'react-day-picker', 'framer-motion'].some(pkg =>
              packageName.startsWith(pkg))) {
              return `vendor-${packageName.replace('@', '')}`;
            }

            return 'vendors';
          },
          priority: 10,
        },
        // Group routes into a separate chunk
        routes: {
          test: /[\\/]src[\\/]app[\\/]/,
          name: 'routes',
          priority: 5,
        },
        // Group components into a separate chunk
        components: {
          test: /[\\/]src[\\/]components[\\/]/,
          name: 'components',
          priority: 5,
        },
        // Group utilities and lib files
        utils: {
          test: /[\\/]src[\\/]lib[\\/]/,
          name: 'utils',
          priority: 5,
        },
      },
    };

    return config;
  },
};

export default nextConfig;
