const http = require('http');

http.get('http://localhost:3000/auth/sign-in', (res) => {
  let data = '';

  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    if (data.includes('Continue with Google') || data.includes('Continuer avec Google')) {
      console.log('Google sign-in button found: SUCCESS');
    } else {
      console.log('Google sign-in button NOT found');
    }

    if (data.includes('Sign in') || data.includes('Se connecter')) {
      console.log('Regular sign-in button found: SUCCESS');
    } else {
      console.log('Regular sign-in button NOT found');
    }

    if (data.includes('divider')) {
      console.log('Divider found: SUCCESS');
    } else {
      console.log('Divider NOT found');
    }
  });
}).on('error', (err) => {
  console.log('Error: ' + err.message);
});
