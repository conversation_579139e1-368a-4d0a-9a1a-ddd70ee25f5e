/**
 * Tests for the subscription store
 * 
 * To run these tests:
 * bun test src/stores/__tests__/useSubscriptionStore.test.ts
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'bun:test'
import { useSubscriptionStore } from '../useSubscriptionStore'
import { useUserStore } from '../useUserStore'

// Mock fetch
global.fetch = vi.fn()

// Mock Supabase
vi.mock('@/lib/supabase/client', () => {
  return {
    supabase: {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      in: vi.fn().mockReturnThis(),
      maybeSingle: vi.fn(),
      update: vi.fn().mockReturnThis()
    }
  }
})

// Import the mocked supabase
import { supabase } from '@/lib/supabase/client'

// Mock useUserStore
vi.mock('../useUserStore', () => {
  return {
    useUserStore: {
      getState: vi.fn().mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' }
      })
    }
  }
})

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {}
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { store[key] = value },
    removeItem: (key: string) => { delete store[key] },
    clear: () => { store = {} },
    getAllKeys: () => Object.keys(store)
  }
})()

describe('Subscription Store', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    mockLocalStorage.clear()
    
    // Reset the store to its initial state
    useSubscriptionStore.getState().reset()
    
    // Reset mocks
    vi.resetAllMocks()
    
    // Mock global objects
    Object.defineProperty(global, 'localStorage', { value: mockLocalStorage })
    
    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  test('should initialize with default values', () => {
    const state = useSubscriptionStore.getState()
    expect(state.subscription).toBeNull()
    expect(state.isLoading).toBe(false)
    expect(state.error).toBeNull()
  })
  
  test('should fetch subscription successfully', async () => {
    // Mock successful Supabase query
    const mockSubscription = {
      id: 'sub-1',
      stripe_id: 'sub_123',
      user_id: 'user-123',
      price_id: 'price_123',
      stripe_price_id: 'price_123',
      plan_type: 'individual',
      interval: 'month',
      status: 'active',
      current_period_start: Math.floor(Date.now() / 1000) - 86400, // 1 day ago
      current_period_end: Math.floor(Date.now() / 1000) + 2592000, // 30 days from now
      cancel_at_period_end: false,
      amount: 795, // $7.95
      currency: 'usd',
      started_at: Math.floor(Date.now() / 1000) - 86400,
      customer_id: 'cus_123'
    }
    
    vi.mocked(supabase.from).mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      in: vi.fn().mockReturnThis(),
      maybeSingle: vi.fn().mockResolvedValue({
        data: mockSubscription,
        error: null
      })
    } as any)
    
    const { fetchSubscription } = useSubscriptionStore.getState()
    const result = await fetchSubscription('user-123')
    
    expect(result).toEqual(mockSubscription)
    expect(useSubscriptionStore.getState().subscription).toEqual(mockSubscription)
    expect(useSubscriptionStore.getState().isLoading).toBe(false)
    expect(useSubscriptionStore.getState().error).toBeNull()
    expect(supabase.from).toHaveBeenCalledWith('subscriptions')
  })
  
  test('should handle fetch subscription error', async () => {
    // Mock Supabase error
    const mockError = new Error('Database error')
    
    vi.mocked(supabase.from).mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      in: vi.fn().mockReturnThis(),
      maybeSingle: vi.fn().mockResolvedValue({
        data: null,
        error: mockError
      })
    } as any)
    
    const { fetchSubscription } = useSubscriptionStore.getState()
    const result = await fetchSubscription('user-123')
    
    expect(result).toBeNull()
    expect(useSubscriptionStore.getState().subscription).toBeNull()
    expect(useSubscriptionStore.getState().isLoading).toBe(false)
    expect(useSubscriptionStore.getState().error).toEqual(mockError)
  })
  
  test('should update subscription successfully', async () => {
    // Set up initial state with a subscription
    const mockSubscription = {
      id: 'sub-1',
      stripe_id: 'sub_123',
      user_id: 'user-123',
      price_id: 'price_123',
      stripe_price_id: 'price_123',
      plan_type: 'individual',
      interval: 'month',
      status: 'active',
      current_period_start: Math.floor(Date.now() / 1000) - 86400,
      current_period_end: Math.floor(Date.now() / 1000) + 2592000,
      cancel_at_period_end: false,
      amount: 795,
      currency: 'usd',
      started_at: Math.floor(Date.now() / 1000) - 86400,
      customer_id: 'cus_123'
    }
    
    useSubscriptionStore.setState({
      subscription: mockSubscription
    })
    
    // Mock successful Supabase update
    vi.mocked(supabase.from).mockReturnValue({
      update: vi.fn().mockReturnThis(),
      eq: vi.fn().mockResolvedValue({
        error: null
      })
    } as any)
    
    const { updateSubscription } = useSubscriptionStore.getState()
    const result = await updateSubscription({ plan_type: 'family' })
    
    expect(result).toBe(true)
    expect(useSubscriptionStore.getState().subscription?.plan_type).toBe('family')
    expect(useSubscriptionStore.getState().isLoading).toBe(false)
    expect(useSubscriptionStore.getState().error).toBeNull()
    expect(supabase.from).toHaveBeenCalledWith('subscriptions')
  })
  
  test('should handle update subscription error', async () => {
    // Set up initial state with a subscription
    const mockSubscription = {
      id: 'sub-1',
      stripe_id: 'sub_123',
      user_id: 'user-123',
      price_id: 'price_123',
      stripe_price_id: 'price_123',
      plan_type: 'individual',
      interval: 'month',
      status: 'active',
      current_period_start: Math.floor(Date.now() / 1000) - 86400,
      current_period_end: Math.floor(Date.now() / 1000) + 2592000,
      cancel_at_period_end: false,
      amount: 795,
      currency: 'usd',
      started_at: Math.floor(Date.now() / 1000) - 86400,
      customer_id: 'cus_123'
    }
    
    useSubscriptionStore.setState({
      subscription: mockSubscription
    })
    
    // Mock Supabase error
    const mockError = new Error('Database error')
    
    vi.mocked(supabase.from).mockReturnValue({
      update: vi.fn().mockReturnThis(),
      eq: vi.fn().mockResolvedValue({
        error: mockError
      })
    } as any)
    
    const { updateSubscription } = useSubscriptionStore.getState()
    const result = await updateSubscription({ plan_type: 'family' })
    
    expect(result).toBe(false)
    expect(useSubscriptionStore.getState().subscription?.plan_type).toBe('individual')
    expect(useSubscriptionStore.getState().isLoading).toBe(false)
    expect(useSubscriptionStore.getState().error).toEqual(mockError)
  })
  
  test('should cancel subscription successfully', async () => {
    // Set up initial state with a subscription
    const mockSubscription = {
      id: 'sub-1',
      stripe_id: 'sub_123',
      user_id: 'user-123',
      price_id: 'price_123',
      stripe_price_id: 'price_123',
      plan_type: 'individual',
      interval: 'month',
      status: 'active',
      current_period_start: Math.floor(Date.now() / 1000) - 86400,
      current_period_end: Math.floor(Date.now() / 1000) + 2592000,
      cancel_at_period_end: false,
      amount: 795,
      currency: 'usd',
      started_at: Math.floor(Date.now() / 1000) - 86400,
      customer_id: 'cus_123'
    }
    
    useSubscriptionStore.setState({
      subscription: mockSubscription
    })
    
    // Mock successful fetch
    global.fetch = vi.fn().mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true })
    })
    
    const { cancelSubscription } = useSubscriptionStore.getState()
    const result = await cancelSubscription('sub_123')
    
    expect(result).toBe(true)
    expect(useSubscriptionStore.getState().subscription?.cancel_at_period_end).toBe(true)
    expect(useSubscriptionStore.getState().subscription?.canceled_at).toBeDefined()
    expect(useSubscriptionStore.getState().isLoading).toBe(false)
    expect(useSubscriptionStore.getState().error).toBeNull()
    expect(global.fetch).toHaveBeenCalledWith(
      '/api/subscriptions/cancel',
      expect.objectContaining({
        method: 'POST',
        body: JSON.stringify({ subscriptionId: 'sub_123' })
      })
    )
  })
  
  test('should handle cancel subscription error', async () => {
    // Set up initial state with a subscription
    const mockSubscription = {
      id: 'sub-1',
      stripe_id: 'sub_123',
      user_id: 'user-123',
      price_id: 'price_123',
      stripe_price_id: 'price_123',
      plan_type: 'individual',
      interval: 'month',
      status: 'active',
      current_period_start: Math.floor(Date.now() / 1000) - 86400,
      current_period_end: Math.floor(Date.now() / 1000) + 2592000,
      cancel_at_period_end: false,
      amount: 795,
      currency: 'usd',
      started_at: Math.floor(Date.now() / 1000) - 86400,
      customer_id: 'cus_123'
    }
    
    useSubscriptionStore.setState({
      subscription: mockSubscription
    })
    
    // Mock fetch error
    global.fetch = vi.fn().mockResolvedValueOnce({
      ok: false,
      json: async () => ({ error: 'Failed to cancel subscription' })
    })
    
    const { cancelSubscription } = useSubscriptionStore.getState()
    const result = await cancelSubscription('sub_123')
    
    expect(result).toBe(false)
    expect(useSubscriptionStore.getState().subscription?.cancel_at_period_end).toBe(false)
    expect(useSubscriptionStore.getState().isLoading).toBe(false)
    expect(useSubscriptionStore.getState().error).toBeInstanceOf(Error)
    expect(useSubscriptionStore.getState().error?.message).toBe('Failed to cancel subscription')
  })
  
  test('should compute subscription details correctly', () => {
    // Set up initial state with a subscription
    const now = Math.floor(Date.now() / 1000)
    const mockSubscription = {
      id: 'sub-1',
      stripe_id: 'sub_123',
      user_id: 'user-123',
      price_id: 'price_123',
      stripe_price_id: 'price_123',
      plan_type: 'individual',
      interval: 'month',
      status: 'active',
      current_period_start: now - 86400, // 1 day ago
      current_period_end: now + 2592000, // 30 days from now
      cancel_at_period_end: false,
      amount: 795, // $7.95
      currency: 'usd',
      started_at: now - 86400,
      customer_id: 'cus_123'
    }
    
    useSubscriptionStore.setState({
      subscription: mockSubscription
    })
    
    const { useSubscriptionDetails } = require('../useSubscriptionStore')
    const details = useSubscriptionDetails()
    
    expect(details.hasSubscription).toBe(true)
    expect(details.planType).toBe('individual')
    expect(details.billingPeriod).toBe('monthly')
    expect(details.status).toBe('active')
    expect(details.currentPeriodStart).toBeInstanceOf(Date)
    expect(details.currentPeriodEnd).toBeInstanceOf(Date)
    expect(details.cancelAtPeriodEnd).toBe(false)
    expect(details.amount).toBe(7.95) // Converted from cents
    expect(details.currency).toBe('usd')
    expect(details.formattedAmount).toBe('$7.95')
    expect(details.daysUntilRenewal).toBeGreaterThan(25) // Should be around 30 days
    expect(details.isTrialing).toBe(false)
    expect(details.isPastDue).toBe(false)
    expect(details.isCancelled).toBe(false)
  })
  
  test('should handle cancelled subscription details', () => {
    // Set up initial state with a cancelled subscription
    const now = Math.floor(Date.now() / 1000)
    const mockSubscription = {
      id: 'sub-1',
      stripe_id: 'sub_123',
      user_id: 'user-123',
      price_id: 'price_123',
      stripe_price_id: 'price_123',
      plan_type: 'individual',
      interval: 'month',
      status: 'active', // Still active until period end
      current_period_start: now - 86400,
      current_period_end: now + 2592000,
      cancel_at_period_end: true, // But will cancel at period end
      amount: 795,
      currency: 'usd',
      started_at: now - 86400,
      customer_id: 'cus_123',
      canceled_at: now
    }
    
    useSubscriptionStore.setState({
      subscription: mockSubscription
    })
    
    const { useSubscriptionDetails } = require('../useSubscriptionStore')
    const details = useSubscriptionDetails()
    
    expect(details.hasSubscription).toBe(true) // Still has active subscription
    expect(details.cancelAtPeriodEnd).toBe(true)
    expect(details.isCancelled).toBe(true) // Marked as cancelled due to cancel_at_period_end
  })
})
