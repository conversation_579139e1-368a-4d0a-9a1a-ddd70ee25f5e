/**
 * Tests for advanced selectors in the appointment history store
 *
 * To run these tests:
 * bun test src/stores/__tests__/advanced-selectors.test.ts
 */

import { describe, test, expect, beforeEach, vi } from 'bun:test'
import { useAppointmentHistoryStore } from '../useAppointmentHistoryStore'

// Mock fetch
global.fetch = vi.fn()

// Mock Supabase
vi.mock('@/lib/supabase/client', () => {
  return {
    supabase: {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      range: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      filter: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      lte: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis()
    }
  }
})

// Mock useUserStore
vi.mock('../useUserStore', () => {
  return {
    useUserStore: {
      getState: vi.fn().mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' }
      })
    }
  }
})

describe('Appointment History Store - Advanced Selectors', () => {
  // Sample appointment data for testing
  const pendingAppointment1 = {
    id: 'pending-1',
    user_id: 'user-123',
    patient_id: null,
    status: 'pending',
    request_type: 'general',
    request_details: {
      postalCode: '12345',
      date: '2023-01-15',
      time: '10:00'
    },
    created_at: '2023-01-10T10:00:00Z',
    updated_at: '2023-01-10T10:00:00Z'
  }

  const pendingAppointment2 = {
    id: 'pending-2',
    user_id: 'user-123',
    patient_id: null,
    status: 'pending',
    request_type: 'specialist',
    request_details: {
      postalCode: '12345',
      date: '2023-02-20',
      time: '14:00'
    },
    created_at: '2023-02-15T14:00:00Z',
    updated_at: '2023-02-15T14:00:00Z'
  }

  const inProgressAppointment = {
    id: 'in-progress-1',
    user_id: 'user-123',
    patient_id: null,
    status: 'in_progress',
    request_type: 'general',
    request_details: {
      postalCode: '12345',
      date: '2023-03-25',
      time: '11:00'
    },
    created_at: '2023-03-20T11:00:00Z',
    updated_at: '2023-03-20T11:00:00Z'
  }

  const completedAppointment = {
    id: 'completed-1',
    appointment_request_id: 'req-completed-1',
    completion_details: {
      clinic_name: 'Test Clinic',
      doctor_name: 'Dr. Test'
    },
    completed_at: '2023-04-15T09:00:00Z',
    appointment_request: {
      id: 'req-completed-1',
      user_id: 'user-123',
      patient_id: null,
      status: 'completed',
      request_type: 'specialist',
      request_details: {
        postalCode: '12345',
        date: '2023-04-15',
        time: '09:00'
      },
      created_at: '2023-04-10T09:00:00Z',
      updated_at: '2023-04-15T09:00:00Z'
    }
  }

  const cancelledAppointment = {
    id: 'cancelled-1',
    appointment_request_id: 'req-cancelled-1',
    cancellation_reason: 'Patient request',
    cancelled_by: 'user',
    cancelled_at: '2023-05-10T15:00:00Z',
    appointment_request: {
      id: 'req-cancelled-1',
      user_id: 'user-123',
      patient_id: null,
      status: 'cancelled',
      request_type: 'general',
      request_details: {
        postalCode: '12345',
        date: '2023-05-15',
        time: '15:00'
      },
      created_at: '2023-05-05T15:00:00Z',
      updated_at: '2023-05-10T15:00:00Z'
    }
  }

  beforeEach(() => {
    // Reset the store to initial state with test data
    useAppointmentHistoryStore.setState({
      appointmentRequests: [pendingAppointment1, pendingAppointment2, inProgressAppointment],
      completedAppointments: [completedAppointment],
      cancelledAppointments: [cancelledAppointment],
      requestsPagination: {
        currentPage: 1,
        totalPages: 1,
        totalCount: 3,
        hasMore: false,
        pageSize: 10
      },
      completedPagination: {
        currentPage: 1,
        totalPages: 1,
        totalCount: 1,
        hasMore: false,
        pageSize: 10
      },
      cancelledPagination: {
        currentPage: 1,
        totalPages: 1,
        totalCount: 1,
        hasMore: false,
        pageSize: 10
      },
      isLoading: false,
      error: null,
      lastFetched: Date.now()
    })

    // Reset all mocks
    vi.resetAllMocks()
  })

  test('filterByStatus should return appointments with the specified status', () => {
    const { useAppointmentHistory } = require('../useAppointmentHistoryStore')
    const { filterByStatus } = useAppointmentHistory()

    // Test filtering by pending status
    const pendingResults = filterByStatus('pending')
    expect(pendingResults).toHaveLength(2)
    expect(pendingResults[0].id).toBe('pending-1')
    expect(pendingResults[1].id).toBe('pending-2')

    // Test filtering by in_progress status
    const inProgressResults = filterByStatus('in_progress')
    expect(inProgressResults).toHaveLength(1)
    expect(inProgressResults[0].id).toBe('in-progress-1')

    // Test filtering by completed status
    const completedResults = filterByStatus('completed')
    expect(completedResults).toHaveLength(1)
    expect(completedResults[0].id).toBe('completed-1')

    // Test filtering by cancelled status
    const cancelledResults = filterByStatus('cancelled')
    expect(cancelledResults).toHaveLength(1)
    expect(cancelledResults[0].id).toBe('cancelled-1')
  })

  test('filterByType should return appointments with the specified request type', () => {
    const { useAppointmentHistory } = require('../useAppointmentHistoryStore')
    const { filterByType } = useAppointmentHistory()

    // Test filtering by general type
    const generalResults = filterByType('general')
    expect(generalResults).toHaveLength(3) // 2 pending + 1 cancelled

    // Test filtering by specialist type
    const specialistResults = filterByType('specialist')
    expect(specialistResults).toHaveLength(2) // 1 pending + 1 completed
  })

  test('getAppointmentCountsByStatus should return correct counts', () => {
    const { useAppointmentHistory } = require('../useAppointmentHistoryStore')
    const { getAppointmentCountsByStatus } = useAppointmentHistory()

    const counts = getAppointmentCountsByStatus()
    expect(counts.pending).toBe(2)
    expect(counts.in_progress).toBe(1)
    expect(counts.completed).toBe(1)
    expect(counts.cancelled).toBe(1)
    expect(counts.total).toBe(5)
  })

  test('getAppointmentSummary should return correct summary statistics', () => {
    const { useAppointmentHistory } = require('../useAppointmentHistoryStore')
    const { getAppointmentSummary } = useAppointmentHistory()

    const summary = getAppointmentSummary()
    expect(summary.counts.total).toBe(5)
    expect(summary.completionRate).toBe(20) // 1/5 * 100
    expect(summary.cancellationRate).toBe(20) // 1/5 * 100

    // Most recent appointment should be the one with the latest created_at
    expect(summary.mostRecentAppointment).not.toBeNull()
    expect(summary.mostRecentAppointment?.id).toBe('req-cancelled-1')
  })

  test('groupByStatus should group appointments by their status', () => {
    const { useAppointmentHistory } = require('../useAppointmentHistoryStore')
    const { groupByStatus } = useAppointmentHistory()

    const groups = groupByStatus()
    expect(groups.pending).toHaveLength(2)
    expect(groups.in_progress).toHaveLength(1)
    expect(groups.completed).toHaveLength(1)
    expect(groups.cancelled).toHaveLength(1)
  })

  test('groupByMonth should group appointments by their creation month', () => {
    const { useAppointmentHistory } = require('../useAppointmentHistoryStore')
    const { groupByMonth } = useAppointmentHistory()

    const groups = groupByMonth(2023)

    // January (month 0) should have 1 appointment
    expect(groups[0]).toHaveLength(1)
    expect(groups[0][0].id).toBe('pending-1')

    // February (month 1) should have 1 appointment
    expect(groups[1]).toHaveLength(1)
    expect(groups[1][0].id).toBe('pending-2')

    // March (month 2) should have 1 appointment
    expect(groups[2]).toHaveLength(1)
    expect(groups[2][0].id).toBe('in-progress-1')

    // April (month 3) should have 1 appointment
    expect(groups[3]).toHaveLength(1)
    expect(groups[3][0].id).toBe('req-completed-1')

    // May (month 4) should have 1 appointment
    expect(groups[4]).toHaveLength(1)
    expect(groups[4][0].id).toBe('req-cancelled-1')
  })

  test('groupByType should group appointments by their request type', () => {
    const { useAppointmentHistory } = require('../useAppointmentHistoryStore')
    const { groupByType } = useAppointmentHistory()

    const groups = groupByType()

    // General type should have 3 appointments
    expect(groups['general']).toHaveLength(3)

    // Specialist type should have 2 appointments
    expect(groups['specialist']).toHaveLength(2)
  })

  test('sortByDate should sort appointments by date', () => {
    const { useAppointmentHistory } = require('../useAppointmentHistoryStore')
    const { sortByDate, allAppointments } = useAppointmentHistory()

    // Sort descending (newest first - default)
    const sortedDesc = sortByDate(allAppointments)
    expect(sortedDesc[0].id).toBe('req-cancelled-1') // May 2023
    expect(sortedDesc[sortedDesc.length - 1].id).toBe('pending-1') // January 2023

    // Sort ascending (oldest first)
    const sortedAsc = sortByDate(allAppointments, true)
    expect(sortedAsc[0].id).toBe('pending-1') // January 2023
    expect(sortedAsc[sortedAsc.length - 1].id).toBe('req-cancelled-1') // May 2023
  })

  test('getAppointmentsInDateRange should return appointments within the date range', () => {
    const { useAppointmentHistory } = require('../useAppointmentHistoryStore')
    const { getAppointmentsInDateRange } = useAppointmentHistory()

    // Get appointments between March and April 2023
    const startDate = new Date('2023-03-01')
    const endDate = new Date('2023-04-30')
    const appointments = getAppointmentsInDateRange(startDate, endDate)

    expect(appointments).toHaveLength(2)
    expect(appointments.some(a => a.id === 'in-progress-1')).toBe(true) // March
    expect(appointments.some(a => a.id === 'req-completed-1')).toBe(true) // April
  })

  test('getAppointmentsForDay should return appointments for a specific day', () => {
    const { useAppointmentHistory } = require('../useAppointmentHistoryStore')
    const { getAppointmentsForDay } = useAppointmentHistory()

    // Get appointments for April 10, 2023
    const date = new Date('2023-04-10')
    const appointments = getAppointmentsForDay(date)

    expect(appointments).toHaveLength(1)
    expect(appointments[0].id).toBe('req-completed-1')
  })

  test('getUpcomingAppointments should return future pending or in-progress appointments', () => {
    // Mock the current date to be January 1, 2023
    const realDate = Date
    global.Date = class extends Date {
      constructor(...args) {
        if (args.length === 0) {
          return new realDate('2023-01-01T00:00:00Z')
        }
        return new realDate(...args)
      }
    }

    const { useAppointmentHistory } = require('../useAppointmentHistoryStore')
    const { getUpcomingAppointments } = useAppointmentHistory()

    const upcomingAppointments = getUpcomingAppointments()

    // Should include all pending and in-progress appointments with future dates
    expect(upcomingAppointments).toHaveLength(3)

    // Should be sorted by date (ascending)
    expect(upcomingAppointments[0].id).toBe('pending-1') // January 15
    expect(upcomingAppointments[1].id).toBe('pending-2') // February 20
    expect(upcomingAppointments[2].id).toBe('in-progress-1') // March 25

    // Restore the original Date
    global.Date = realDate
  })
})
