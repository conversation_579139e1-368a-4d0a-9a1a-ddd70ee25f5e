/**
 * Tests for the combined app store
 *
 * To run these tests:
 * bun test src/stores/__tests__/useAppStore.test.ts
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'bun:test'
import { useAppStore } from '../useAppStore'

// Mock all the individual stores
vi.mock('../useUserStore', () => {
  return {
    useUserStore: {
      getState: vi.fn().mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        status: 'authenticated',
        signIn: vi.fn().mockResolvedValue({ success: true }),
        signInWithGoogle: vi.fn().mockResolvedValue({ success: true }),
        signOut: vi.fn().mockResolvedValue(undefined),
        refresh: vi.fn().mockResolvedValue(undefined)
      })
    }
  }
})

vi.mock('../useUserProfileStore', () => {
  return {
    useUserProfileStore: {
      getState: vi.fn().mockReturnValue({
        profile: { firstName: 'John', lastName: 'Doe' },
        isLoading: false,
        fetchProfile: vi.fn().mockResolvedValue({ firstName: 'John', lastName: 'Doe' }),
        updateProfile: vi.fn().mockResolvedValue(true)
      })
    }
  }
})

vi.mock('../usePreferencesStore', () => {
  return {
    usePreferencesStore: {
      getState: vi.fn().mockReturnValue({
        preferences: { theme: 'light', language: 'fr' },
        isLoading: false,
        fetchPreferences: vi.fn().mockResolvedValue({ theme: 'light', language: 'fr' }),
        updatePreferences: vi.fn().mockResolvedValue(true),
        setTheme: vi.fn(),
        setLanguage: vi.fn()
      })
    }
  }
})

vi.mock('../useLanguageStore', () => {
  return {
    useLanguageStore: {
      getState: vi.fn().mockReturnValue({
        language: 'fr',
        translations: { 'common.hello': 'Bonjour' },
        isLoading: false,
        translate: vi.fn().mockReturnValue('Bonjour'),
        setLanguage: vi.fn().mockResolvedValue(undefined),
        loadTranslations: vi.fn().mockResolvedValue(undefined)
      })
    }
  }
})

vi.mock('../useAppointmentHistoryStore', () => {
  return {
    useAppointmentHistoryStore: {
      getState: vi.fn().mockReturnValue({
        appointmentRequests: [],
        completedAppointments: [],
        cancelledAppointments: [],
        requestsPagination: { currentPage: 1, totalPages: 1, totalCount: 0, hasMore: false, pageSize: 10 },
        completedPagination: { currentPage: 1, totalPages: 1, totalCount: 0, hasMore: false, pageSize: 10 },
        cancelledPagination: { currentPage: 1, totalPages: 1, totalCount: 0, hasMore: false, pageSize: 10 },
        isLoading: false,
        fetchAppointmentRequests: vi.fn().mockResolvedValue([]),
        fetchCompletedAppointments: vi.fn().mockResolvedValue([]),
        fetchCancelledAppointments: vi.fn().mockResolvedValue([]),
        fetchAllAppointmentHistory: vi.fn().mockResolvedValue(undefined),
        createAppointmentRequest: vi.fn().mockResolvedValue({ success: true }),
        cancelAppointmentRequest: vi.fn().mockResolvedValue({ success: true }),
        // Pagination actions
        fetchNextPage: vi.fn().mockResolvedValue(undefined),
        fetchPreviousPage: vi.fn().mockResolvedValue(undefined),
        goToPage: vi.fn().mockResolvedValue(undefined),
        setPageSize: vi.fn(),
        // Advanced selectors
        filterByDateRange: vi.fn().mockResolvedValue([[], [], []]),
        filterByStatus: vi.fn().mockReturnValue([]),
        filterByType: vi.fn().mockReturnValue([]),
        getAppointmentCountsByStatus: vi.fn().mockReturnValue({
          pending: 0,
          in_progress: 0,
          completed: 0,
          cancelled: 0,
          total: 0
        }),
        getAppointmentCountsByMonth: vi.fn().mockReturnValue([]),
        getAppointmentSummary: vi.fn().mockReturnValue({
          counts: { total: 0 },
          completionRate: 0,
          cancellationRate: 0
        }),
        groupByStatus: vi.fn().mockReturnValue({}),
        groupByMonth: vi.fn().mockReturnValue({}),
        groupByType: vi.fn().mockReturnValue({}),
        sortByDate: vi.fn().mockReturnValue([]),
        getAppointmentsInDateRange: vi.fn().mockReturnValue([]),
        getAppointmentsForDay: vi.fn().mockReturnValue([]),
        getUpcomingAppointments: vi.fn().mockReturnValue([])
      })
    }
  }
})

vi.mock('../useSubscriptionStore', () => {
  return {
    useSubscriptionStore: {
      getState: vi.fn().mockReturnValue({
        subscription: { plan_type: 'individual' },
        isLoading: false,
        fetchSubscription: vi.fn().mockResolvedValue({ plan_type: 'individual' }),
        updateSubscription: vi.fn().mockResolvedValue(true),
        cancelSubscription: vi.fn().mockResolvedValue(true)
      })
    }
  }
})

// Import the mocked stores
import { useUserStore } from '../useUserStore'
import { useUserProfileStore } from '../useUserProfileStore'
import { usePreferencesStore } from '../usePreferencesStore'
import { useLanguageStore } from '../useLanguageStore'
import { useAppointmentHistoryStore } from '../useAppointmentHistoryStore'
import { useSubscriptionStore } from '../useSubscriptionStore'

describe('App Store', () => {
  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks()

    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  test('should delegate signIn to user store', async () => {
    const { signIn } = useAppStore.getState()
    const result = await signIn('<EMAIL>', 'password')

    expect(result.success).toBe(true)
    expect(useUserStore.getState().signIn).toHaveBeenCalledWith('<EMAIL>', 'password')
  })

  test('should delegate signInWithGoogle to user store', async () => {
    const { signInWithGoogle } = useAppStore.getState()
    const result = await signInWithGoogle()

    expect(result.success).toBe(true)
    expect(useUserStore.getState().signInWithGoogle).toHaveBeenCalled()
  })

  test('should delegate signOut to user store', async () => {
    const { signOut } = useAppStore.getState()
    await signOut('/login')

    expect(useUserStore.getState().signOut).toHaveBeenCalledWith('/login')
  })

  test('should delegate fetchProfile to profile store', async () => {
    const { fetchProfile } = useAppStore.getState()
    const result = await fetchProfile('user-123')

    expect(result).toEqual({ firstName: 'John', lastName: 'Doe' })
    expect(useUserProfileStore.getState().fetchProfile).toHaveBeenCalledWith('user-123')
  })

  test('should delegate updateProfile to profile store', async () => {
    const { updateProfile } = useAppStore.getState()
    const result = await updateProfile('user-123', { firstName: 'Jane' })

    expect(result).toBe(true)
    expect(useUserProfileStore.getState().updateProfile).toHaveBeenCalledWith('user-123', { firstName: 'Jane' })
  })

  test('should delegate fetchPreferences to preferences store', async () => {
    const { fetchPreferences } = useAppStore.getState()
    const result = await fetchPreferences('user-123')

    expect(result).toEqual({ theme: 'light', language: 'fr' })
    expect(usePreferencesStore.getState().fetchPreferences).toHaveBeenCalledWith('user-123')
  })

  test('should delegate updatePreferences to preferences store', async () => {
    const { updatePreferences } = useAppStore.getState()
    const result = await updatePreferences('user-123', { theme: 'dark' })

    expect(result).toBe(true)
    expect(usePreferencesStore.getState().updatePreferences).toHaveBeenCalledWith('user-123', { theme: 'dark' })
  })

  test('should delegate setTheme to preferences store', () => {
    const { setTheme } = useAppStore.getState()
    setTheme('dark')

    expect(usePreferencesStore.getState().setTheme).toHaveBeenCalledWith('dark')
  })

  test('should delegate setLanguage to both preferences and language stores', () => {
    const { setLanguage } = useAppStore.getState()
    setLanguage('en')

    expect(usePreferencesStore.getState().setLanguage).toHaveBeenCalledWith('en')
    expect(useLanguageStore.getState().setLanguage).toHaveBeenCalledWith('en')
  })

  test('should delegate translate to language store', () => {
    const { translate } = useAppStore.getState()
    const result = translate('common.hello')

    expect(result).toBe('Bonjour')
    expect(useLanguageStore.getState().translate).toHaveBeenCalledWith('common.hello')
  })

  test('should delegate loadTranslations to language store', async () => {
    const { loadTranslations } = useAppStore.getState()
    await loadTranslations()

    expect(useLanguageStore.getState().loadTranslations).toHaveBeenCalled()
  })

  test('should delegate fetchAppointmentRequests to appointment history store', async () => {
    const { fetchAppointmentRequests } = useAppStore.getState()
    const params = { page: 2, pageSize: 10, status: 'pending' }
    await fetchAppointmentRequests(params)

    expect(useAppointmentHistoryStore.getState().fetchAppointmentRequests).toHaveBeenCalledWith(params)
  })

  test('should delegate fetchCompletedAppointments to appointment history store', async () => {
    const { fetchCompletedAppointments } = useAppStore.getState()
    const params = { page: 1, pageSize: 5 }
    await fetchCompletedAppointments(params)

    expect(useAppointmentHistoryStore.getState().fetchCompletedAppointments).toHaveBeenCalledWith(params)
  })

  test('should delegate fetchCancelledAppointments to appointment history store', async () => {
    const { fetchCancelledAppointments } = useAppStore.getState()
    const params = { page: 1, pageSize: 5 }
    await fetchCancelledAppointments(params)

    expect(useAppointmentHistoryStore.getState().fetchCancelledAppointments).toHaveBeenCalledWith(params)
  })

  test('should delegate fetchAllAppointmentHistory to appointment history store', async () => {
    const { fetchAllAppointmentHistory } = useAppStore.getState()
    await fetchAllAppointmentHistory()

    expect(useAppointmentHistoryStore.getState().fetchAllAppointmentHistory).toHaveBeenCalled()
  })

  test('should delegate createAppointmentRequest to appointment history store', async () => {
    const { createAppointmentRequest } = useAppStore.getState()
    const data = { request_type: 'general_appointment' }
    const result = await createAppointmentRequest(data)

    expect(result.success).toBe(true)
    expect(useAppointmentHistoryStore.getState().createAppointmentRequest).toHaveBeenCalledWith(data)
  })

  test('should delegate cancelAppointmentRequest to appointment history store', async () => {
    const { cancelAppointmentRequest } = useAppStore.getState()
    const result = await cancelAppointmentRequest('req-123', 'Changed my mind')

    expect(result.success).toBe(true)
    expect(useAppointmentHistoryStore.getState().cancelAppointmentRequest).toHaveBeenCalledWith('req-123', 'Changed my mind')
  })

  test('should delegate fetchSubscription to subscription store', async () => {
    const { fetchSubscription } = useAppStore.getState()
    const result = await fetchSubscription('user-123')

    expect(result).toEqual({ plan_type: 'individual' })
    expect(useSubscriptionStore.getState().fetchSubscription).toHaveBeenCalledWith('user-123')
  })

  test('should delegate updateSubscription to subscription store', async () => {
    const { updateSubscription } = useAppStore.getState()
    const result = await updateSubscription({ plan_type: 'family' })

    expect(result).toBe(true)
    expect(useSubscriptionStore.getState().updateSubscription).toHaveBeenCalledWith({ plan_type: 'family' })
  })

  test('should delegate cancelSubscription to subscription store', async () => {
    const { cancelSubscription } = useAppStore.getState()
    const result = await cancelSubscription('sub_123')

    expect(result).toBe(true)
    expect(useSubscriptionStore.getState().cancelSubscription).toHaveBeenCalledWith('sub_123')
  })

  test('should initialize app correctly', async () => {
    const { initializeApp } = useAppStore.getState()
    await initializeApp()

    // Should refresh auth state first
    expect(useUserStore.getState().refresh).toHaveBeenCalled()

    // Should fetch user data
    expect(useUserProfileStore.getState().fetchProfile).toHaveBeenCalledWith('user-123')
    expect(usePreferencesStore.getState().fetchPreferences).toHaveBeenCalledWith('user-123')
    expect(useSubscriptionStore.getState().fetchSubscription).toHaveBeenCalledWith('user-123')
    expect(useAppointmentHistoryStore.getState().fetchAllAppointmentHistory).toHaveBeenCalled()

    // Should always load translations
    expect(useLanguageStore.getState().loadTranslations).toHaveBeenCalled()
  })

  test('should handle initialization error', async () => {
    // Mock refresh to throw an error
    vi.mocked(useUserStore.getState().refresh).mockRejectedValueOnce(new Error('Auth error'))

    const { initializeApp } = useAppStore.getState()

    await expect(initializeApp()).rejects.toThrow('Auth error')
    expect(console.error).toHaveBeenCalled()
  })

  // Tests for pagination actions
  test('should delegate fetchNextPage to appointment history store', async () => {
    const { fetchNextPage } = useAppStore.getState()
    await fetchNextPage('requests')

    expect(useAppointmentHistoryStore.getState().fetchNextPage).toHaveBeenCalledWith('requests')
  })

  test('should delegate fetchPreviousPage to appointment history store', async () => {
    const { fetchPreviousPage } = useAppStore.getState()
    await fetchPreviousPage('completed')

    expect(useAppointmentHistoryStore.getState().fetchPreviousPage).toHaveBeenCalledWith('completed')
  })

  test('should delegate goToPage to appointment history store', async () => {
    const { goToPage } = useAppStore.getState()
    await goToPage('cancelled', 3)

    expect(useAppointmentHistoryStore.getState().goToPage).toHaveBeenCalledWith('cancelled', 3)
  })

  test('should delegate setPageSize to appointment history store', () => {
    const { setPageSize } = useAppStore.getState()
    setPageSize('requests', 20)

    expect(useAppointmentHistoryStore.getState().setPageSize).toHaveBeenCalledWith('requests', 20)
  })

  // Tests for advanced selectors
  test('should delegate filterByDateRange to appointment history store', () => {
    const { filterByDateRange } = useAppStore.getState()
    filterByDateRange('2023-01-01', '2023-12-31')

    expect(useAppointmentHistoryStore.getState().filterByDateRange).toHaveBeenCalledWith('2023-01-01', '2023-12-31')
  })

  test('should delegate filterByStatus to appointment history store', () => {
    const { filterByStatus } = useAppStore.getState()
    filterByStatus('pending')

    expect(useAppointmentHistoryStore.getState().filterByStatus).toHaveBeenCalledWith('pending')
  })

  test('should delegate filterByType to appointment history store', () => {
    const { filterByType } = useAppStore.getState()
    filterByType('general')

    expect(useAppointmentHistoryStore.getState().filterByType).toHaveBeenCalledWith('general')
  })

  test('should delegate getAppointmentCountsByStatus to appointment history store', () => {
    const { getAppointmentCountsByStatus } = useAppStore.getState()
    getAppointmentCountsByStatus()

    expect(useAppointmentHistoryStore.getState().getAppointmentCountsByStatus).toHaveBeenCalled()
  })

  test('should delegate getAppointmentCountsByMonth to appointment history store', () => {
    const { getAppointmentCountsByMonth } = useAppStore.getState()
    getAppointmentCountsByMonth(2023)

    expect(useAppointmentHistoryStore.getState().getAppointmentCountsByMonth).toHaveBeenCalledWith(2023)
  })

  test('should delegate getAppointmentSummary to appointment history store', () => {
    const { getAppointmentSummary } = useAppStore.getState()
    getAppointmentSummary()

    expect(useAppointmentHistoryStore.getState().getAppointmentSummary).toHaveBeenCalled()
  })

  test('should delegate groupByStatus to appointment history store', () => {
    const { groupByStatus } = useAppStore.getState()
    groupByStatus()

    expect(useAppointmentHistoryStore.getState().groupByStatus).toHaveBeenCalled()
  })

  test('should delegate groupByMonth to appointment history store', () => {
    const { groupByMonth } = useAppStore.getState()
    groupByMonth(2023)

    expect(useAppointmentHistoryStore.getState().groupByMonth).toHaveBeenCalledWith(2023)
  })

  test('should delegate groupByType to appointment history store', () => {
    const { groupByType } = useAppStore.getState()
    groupByType()

    expect(useAppointmentHistoryStore.getState().groupByType).toHaveBeenCalled()
  })

  test('should delegate sortByDate to appointment history store', () => {
    const { sortByDate } = useAppStore.getState()
    const appointments = [{ created_at: '2023-01-01' }]
    sortByDate(appointments, true)

    expect(useAppointmentHistoryStore.getState().sortByDate).toHaveBeenCalledWith(appointments, true)
  })

  test('should delegate getAppointmentsInDateRange to appointment history store', () => {
    const { getAppointmentsInDateRange } = useAppStore.getState()
    const startDate = new Date('2023-01-01')
    const endDate = new Date('2023-12-31')
    getAppointmentsInDateRange(startDate, endDate)

    expect(useAppointmentHistoryStore.getState().getAppointmentsInDateRange).toHaveBeenCalledWith(startDate, endDate)
  })

  test('should delegate getAppointmentsForDay to appointment history store', () => {
    const { getAppointmentsForDay } = useAppStore.getState()
    const date = new Date('2023-01-01')
    getAppointmentsForDay(date)

    expect(useAppointmentHistoryStore.getState().getAppointmentsForDay).toHaveBeenCalledWith(date)
  })

  test('should delegate getUpcomingAppointments to appointment history store', () => {
    const { getUpcomingAppointments } = useAppStore.getState()
    getUpcomingAppointments()

    expect(useAppointmentHistoryStore.getState().getUpcomingAppointments).toHaveBeenCalled()
  })
})
