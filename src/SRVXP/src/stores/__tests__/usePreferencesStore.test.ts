/**
 * Tests for the preferences store
 * 
 * To run these tests:
 * bun test src/stores/__tests__/usePreferencesStore.test.ts
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'bun:test'
import { usePreferencesStore } from '../usePreferencesStore'
import { useUserStore } from '../useUserStore'
import { invalidateCache } from '../persistence'

// Mock Supabase
vi.mock('@/lib/supabase/client', () => {
  return {
    supabase: {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      maybeSingle: vi.fn(),
      update: vi.fn().mockReturnThis()
    }
  }
})

// Import the mocked supabase
import { supabase } from '@/lib/supabase/client'

// Mock useUserStore
vi.mock('../useUserStore', () => {
  return {
    useUserStore: {
      getState: vi.fn().mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' }
      })
    }
  }
})

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {}
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { store[key] = value },
    removeItem: (key: string) => { delete store[key] },
    clear: () => { store = {} },
    getAllKeys: () => Object.keys(store)
  }
})()

// Mock document
const mockDocument = {
  documentElement: {
    lang: ''
  }
}

describe('Preferences Store', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    mockLocalStorage.clear()
    
    // Reset the store to its initial state
    usePreferencesStore.getState().reset()
    
    // Reset mocks
    vi.resetAllMocks()
    
    // Mock global objects
    Object.defineProperty(global, 'localStorage', { value: mockLocalStorage })
    Object.defineProperty(global, 'document', { value: mockDocument })
    
    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => {})
    
    // Mock setTimeout
    vi.useFakeTimers()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
    vi.useRealTimers()
  })
  
  test('should initialize with default values', () => {
    const state = usePreferencesStore.getState()
    expect(state.preferences).toEqual({
      theme: 'light',
      language: 'fr'
    })
    expect(state.isLoading).toBe(false)
    expect(state.isSaving).toBe(false)
    expect(state.error).toBeNull()
    expect(state.saveError).toBeNull()
    expect(state.saveSuccess).toBe(false)
  })
  
  test('should fetch preferences successfully', async () => {
    // Mock successful preferences fetch
    vi.mocked(supabase.from).mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          maybeSingle: vi.fn().mockResolvedValue({
            data: {
              language: 'en',
              theme: 'dark'
            },
            error: null
          })
        })
      })
    } as any)
    
    const { fetchPreferences } = usePreferencesStore.getState()
    const preferences = await fetchPreferences('user-123')
    
    expect(preferences).toEqual({
      language: 'en',
      theme: 'dark'
    })
    
    expect(usePreferencesStore.getState().isLoading).toBe(false)
    expect(usePreferencesStore.getState().error).toBeNull()
    expect(usePreferencesStore.getState().preferences).toEqual({
      language: 'en',
      theme: 'dark'
    })
  })
  
  test('should handle fetch preferences error', async () => {
    // Mock error during preferences fetch
    const mockError = new Error('Failed to fetch preferences')
    
    vi.mocked(supabase.from).mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          maybeSingle: vi.fn().mockResolvedValue({
            data: null,
            error: mockError
          })
        })
      })
    } as any)
    
    const { fetchPreferences } = usePreferencesStore.getState()
    const preferences = await fetchPreferences('user-123')
    
    // Should return default preferences on error
    expect(preferences).toEqual({
      language: 'fr',
      theme: 'light'
    })
    
    expect(usePreferencesStore.getState().isLoading).toBe(false)
    expect(usePreferencesStore.getState().error).toEqual(mockError)
    // Store state should not be updated on error
    expect(usePreferencesStore.getState().preferences).toEqual({
      language: 'fr',
      theme: 'light'
    })
  })
  
  test('should update preferences successfully', async () => {
    // Mock successful preferences update
    vi.mocked(supabase.from).mockReturnValue({
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          error: null
        })
      })
    } as any)
    
    const { updatePreferences } = usePreferencesStore.getState()
    const success = await updatePreferences('user-123', {
      language: 'en',
      theme: 'dark'
    })
    
    expect(success).toBe(true)
    expect(usePreferencesStore.getState().isSaving).toBe(false)
    expect(usePreferencesStore.getState().saveSuccess).toBe(true)
    expect(usePreferencesStore.getState().saveError).toBeNull()
    
    expect(usePreferencesStore.getState().preferences).toEqual({
      language: 'en',
      theme: 'dark'
    })
    
    // Should update localStorage and document.lang
    expect(localStorage.getItem('language')).toBe('en')
    expect(document.documentElement.lang).toBe('en')
    
    // Test auto-reset of success message
    vi.advanceTimersByTime(3000)
    expect(usePreferencesStore.getState().saveSuccess).toBe(false)
  })
  
  test('should handle update preferences error', async () => {
    // Mock error during preferences update
    const mockError = new Error('Failed to update preferences')
    
    vi.mocked(supabase.from).mockReturnValue({
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          error: mockError
        })
      })
    } as any)
    
    const { updatePreferences } = usePreferencesStore.getState()
    const success = await updatePreferences('user-123', {
      language: 'en'
    })
    
    expect(success).toBe(false)
    expect(usePreferencesStore.getState().isSaving).toBe(false)
    expect(usePreferencesStore.getState().saveSuccess).toBe(false)
    expect(usePreferencesStore.getState().saveError).toEqual(mockError)
    
    // Preferences should not be updated on error
    expect(usePreferencesStore.getState().preferences).toEqual({
      language: 'fr',
      theme: 'light'
    })
  })
  
  test('should set theme', () => {
    const { setTheme } = usePreferencesStore.getState()
    
    // Mock updatePreferences
    const updatePreferencesMock = vi.fn().mockResolvedValue(true)
    usePreferencesStore.setState({
      updatePreferences: updatePreferencesMock
    })
    
    setTheme('dark')
    
    expect(updatePreferencesMock).toHaveBeenCalledWith('user-123', { theme: 'dark' })
  })
  
  test('should set language', () => {
    const { setLanguage } = usePreferencesStore.getState()
    
    // Mock updatePreferences
    const updatePreferencesMock = vi.fn().mockResolvedValue(true)
    usePreferencesStore.setState({
      updatePreferences: updatePreferencesMock
    })
    
    setLanguage('en')
    
    expect(updatePreferencesMock).toHaveBeenCalledWith('user-123', { language: 'en' })
    expect(localStorage.getItem('language')).toBe('en')
    expect(document.documentElement.lang).toBe('en')
  })
  
  test('should handle cache invalidation', () => {
    // Set up initial preferences
    usePreferencesStore.setState({
      preferences: {
        language: 'en',
        theme: 'dark'
      },
      lastFetched: Date.now()
    })
    
    // Invalidate the cache
    invalidateCache('preferences-store')
    
    // Reset the store (simulating page refresh)
    const newStore = usePreferencesStore
    
    // The store should be reset to initial state
    expect(newStore.getState().preferences).toEqual({
      language: 'fr',
      theme: 'light'
    })
    expect(newStore.getState().lastFetched).toBeNull()
  })
  
  test('should use cached preferences if valid', async () => {
    // Set up cached preferences
    usePreferencesStore.setState({
      preferences: {
        language: 'en',
        theme: 'dark'
      },
      lastFetched: Date.now()
    })
    
    const { fetchPreferences } = usePreferencesStore.getState()
    await fetchPreferences('user-123')
    
    // Should not call Supabase if cache is valid
    expect(supabase.from).not.toHaveBeenCalled()
    
    // Should return cached preferences
    expect(usePreferencesStore.getState().preferences).toEqual({
      language: 'en',
      theme: 'dark'
    })
  })
})
