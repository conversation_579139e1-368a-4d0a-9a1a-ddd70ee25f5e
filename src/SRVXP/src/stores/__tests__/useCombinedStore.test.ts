/**
 * Tests for the combined store hook
 * 
 * To run these tests:
 * bun test src/stores/__tests__/useCombinedStore.test.ts
 */

import { describe, test, expect, beforeEach, vi } from 'bun:test'
import { renderHook } from '@testing-library/react'
import { useCombinedStore } from '../useAppStore'

// Mock all the individual stores
vi.mock('../useUserStore', () => {
  return {
    useUserStore: vi.fn().mockImplementation((selector) => {
      const state = {
        user: { id: 'user-123', email: '<EMAIL>' },
        status: 'authenticated'
      }
      return selector(state)
    })
  }
})

vi.mock('../useUserProfileStore', () => {
  return {
    useUserProfileStore: vi.fn().mockImplementation((selector) => {
      const state = {
        profile: { firstName: 'John', lastName: 'Doe' },
        isLoading: false
      }
      return selector(state)
    })
  }
})

vi.mock('../usePreferencesStore', () => {
  return {
    usePreferencesStore: vi.fn().mockImplementation((selector) => {
      const state = {
        preferences: { theme: 'light', language: 'fr' },
        theme: 'light',
        isLoading: false
      }
      return selector(state)
    })
  }
})

vi.mock('../useLanguageStore', () => {
  return {
    useLanguageStore: vi.fn().mockImplementation((selector) => {
      const state = {
        language: 'fr',
        translations: { 'common.hello': 'Bonjour' },
        isLoading: false
      }
      return selector(state)
    })
  }
})

vi.mock('../useAppointmentHistoryStore', () => {
  return {
    useAppointmentHistoryStore: vi.fn().mockImplementation((selector) => {
      const state = {
        appointmentRequests: [
          { id: 'req-1', status: 'pending' },
          { id: 'req-2', status: 'in_progress' }
        ],
        completedAppointments: [{ id: 'comp-1' }],
        cancelledAppointments: [{ id: 'cancel-1' }],
        requestsPagination: { currentPage: 1, totalPages: 1 },
        completedPagination: { currentPage: 1, totalPages: 1 },
        cancelledPagination: { currentPage: 1, totalPages: 1 },
        isLoading: false
      }
      return selector(state)
    })
  }
})

vi.mock('../useSubscriptionStore', () => {
  return {
    useSubscriptionStore: vi.fn().mockImplementation((selector) => {
      const state = {
        subscription: { plan_type: 'individual' },
        isLoading: false
      }
      return selector(state)
    })
  }
})

// Mock the useAppStore
vi.mock('../useAppStore', async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    useAppStore: vi.fn().mockImplementation((selector) => {
      const state = {
        signIn: vi.fn(),
        signInWithGoogle: vi.fn(),
        signOut: vi.fn(),
        fetchProfile: vi.fn(),
        updateProfile: vi.fn(),
        fetchPreferences: vi.fn(),
        updatePreferences: vi.fn(),
        setTheme: vi.fn(),
        setLanguage: vi.fn(),
        translate: vi.fn(),
        loadTranslations: vi.fn(),
        fetchAppointmentRequests: vi.fn(),
        fetchCompletedAppointments: vi.fn(),
        fetchCancelledAppointments: vi.fn(),
        fetchAllAppointmentHistory: vi.fn(),
        createAppointmentRequest: vi.fn(),
        cancelAppointmentRequest: vi.fn(),
        fetchNextPage: vi.fn(),
        fetchPreviousPage: vi.fn(),
        goToPage: vi.fn(),
        setPageSize: vi.fn(),
        filterByDateRange: vi.fn(),
        filterByStatus: vi.fn(),
        filterByType: vi.fn(),
        getAppointmentCountsByStatus: vi.fn(),
        getAppointmentCountsByMonth: vi.fn(),
        getAppointmentSummary: vi.fn(),
        groupByStatus: vi.fn(),
        groupByMonth: vi.fn(),
        groupByType: vi.fn(),
        sortByDate: vi.fn(),
        getAppointmentsInDateRange: vi.fn(),
        getAppointmentsForDay: vi.fn(),
        getUpcomingAppointments: vi.fn(),
        fetchSubscription: vi.fn(),
        updateSubscription: vi.fn(),
        cancelSubscription: vi.fn(),
        initializeApp: vi.fn()
      }
      return selector(state)
    }),
    // Export the original useCombinedStore
    useCombinedStore: actual.useCombinedStore
  }
})

describe('useCombinedStore Hook', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.resetAllMocks()
  })
  
  test('should combine data from all stores', () => {
    const { result } = renderHook(() => useCombinedStore())
    
    // Check user data
    expect(result.current.user).toEqual({ id: 'user-123', email: '<EMAIL>' })
    expect(result.current.status).toBe('authenticated')
    expect(result.current.isAuthenticated).toBe(true)
    
    // Check profile data
    expect(result.current.profile).toEqual({ firstName: 'John', lastName: 'Doe' })
    
    // Check preferences data
    expect(result.current.preferences).toEqual({ theme: 'light', language: 'fr' })
    expect(result.current.theme).toBe('light')
    
    // Check language data
    expect(result.current.language).toBe('fr')
    expect(result.current.translations).toEqual({ 'common.hello': 'Bonjour' })
    
    // Check subscription data
    expect(result.current.subscription).toEqual({ plan_type: 'individual' })
    
    // Check appointment data
    expect(result.current.appointmentRequests).toHaveLength(2)
    expect(result.current.completedAppointments).toHaveLength(1)
    expect(result.current.cancelledAppointments).toHaveLength(1)
    expect(result.current.pendingAppointments).toHaveLength(1)
    expect(result.current.inProgressAppointments).toHaveLength(1)
    
    // Check pagination data
    expect(result.current.requestsPagination).toEqual({ currentPage: 1, totalPages: 1 })
    expect(result.current.completedPagination).toEqual({ currentPage: 1, totalPages: 1 })
    expect(result.current.cancelledPagination).toEqual({ currentPage: 1, totalPages: 1 })
    
    // Check loading states
    expect(result.current.isLoading).toBe(false)
    expect(result.current.isProfileLoading).toBe(false)
    expect(result.current.isPreferencesLoading).toBe(false)
    expect(result.current.isLanguageLoading).toBe(false)
    expect(result.current.isAppointmentHistoryLoading).toBe(false)
    expect(result.current.isSubscriptionLoading).toBe(false)
  })
  
  test('should provide all action functions', () => {
    const { result } = renderHook(() => useCombinedStore())
    
    // Check authentication actions
    expect(typeof result.current.signIn).toBe('function')
    expect(typeof result.current.signInWithGoogle).toBe('function')
    expect(typeof result.current.signOut).toBe('function')
    
    // Check profile actions
    expect(typeof result.current.updateProfile).toBe('function')
    
    // Check preferences actions
    expect(typeof result.current.updatePreferences).toBe('function')
    expect(typeof result.current.setTheme).toBe('function')
    
    // Check language actions
    expect(typeof result.current.setLanguage).toBe('function')
    expect(typeof result.current.translate).toBe('function')
    
    // Check subscription actions
    expect(typeof result.current.fetchSubscription).toBe('function')
    expect(typeof result.current.updateSubscription).toBe('function')
    expect(typeof result.current.cancelSubscription).toBe('function')
    
    // Check appointment actions
    expect(typeof result.current.fetchAppointmentRequests).toBe('function')
    expect(typeof result.current.fetchCompletedAppointments).toBe('function')
    expect(typeof result.current.fetchCancelledAppointments).toBe('function')
    expect(typeof result.current.fetchAllAppointmentHistory).toBe('function')
    expect(typeof result.current.createAppointmentRequest).toBe('function')
    expect(typeof result.current.cancelAppointmentRequest).toBe('function')
    
    // Check pagination actions
    expect(typeof result.current.fetchNextPage).toBe('function')
    expect(typeof result.current.fetchPreviousPage).toBe('function')
    expect(typeof result.current.goToPage).toBe('function')
    expect(typeof result.current.setPageSize).toBe('function')
    
    // Check advanced selectors
    expect(typeof result.current.filterByDateRange).toBe('function')
    expect(typeof result.current.filterByStatus).toBe('function')
    expect(typeof result.current.filterByType).toBe('function')
    expect(typeof result.current.getAppointmentCountsByStatus).toBe('function')
    expect(typeof result.current.getAppointmentCountsByMonth).toBe('function')
    expect(typeof result.current.getAppointmentSummary).toBe('function')
    expect(typeof result.current.groupByStatus).toBe('function')
    expect(typeof result.current.groupByMonth).toBe('function')
    expect(typeof result.current.groupByType).toBe('function')
    expect(typeof result.current.sortByDate).toBe('function')
    expect(typeof result.current.getAppointmentsInDateRange).toBe('function')
    expect(typeof result.current.getAppointmentsForDay).toBe('function')
    expect(typeof result.current.getUpcomingAppointments).toBe('function')
    
    // Check initialization action
    expect(typeof result.current.initializeApp).toBe('function')
  })
})
