/**
 * Tests for the appointment history store
 * 
 * To run these tests:
 * bun test src/stores/__tests__/useAppointmentHistoryStore.test.ts
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'bun:test'
import { useAppointmentHistoryStore } from '../useAppointmentHistoryStore'
import { useUserStore } from '../useUserStore'

// Mock fetch
global.fetch = vi.fn()

// Mock Supabase
vi.mock('@/lib/supabase/client', () => {
  return {
    supabase: {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis()
    }
  }
})

// Import the mocked supabase
import { supabase } from '@/lib/supabase/client'

// Mock useUserStore
vi.mock('../useUserStore', () => {
  return {
    useUserStore: {
      getState: vi.fn().mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' }
      })
    }
  }
})

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {}
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { store[key] = value },
    removeItem: (key: string) => { delete store[key] },
    clear: () => { store = {} },
    getAllKeys: () => Object.keys(store)
  }
})()

describe('Appointment History Store', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    mockLocalStorage.clear()
    
    // Reset the store to its initial state
    useAppointmentHistoryStore.getState().reset()
    
    // Reset mocks
    vi.resetAllMocks()
    
    // Mock global objects
    Object.defineProperty(global, 'localStorage', { value: mockLocalStorage })
    
    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  test('should initialize with default values', () => {
    const state = useAppointmentHistoryStore.getState()
    expect(state.appointmentRequests).toEqual([])
    expect(state.completedAppointments).toEqual([])
    expect(state.cancelledAppointments).toEqual([])
    expect(state.isLoading).toBe(false)
    expect(state.error).toBeNull()
  })
  
  test('should fetch appointment requests successfully', async () => {
    // Mock successful fetch
    const mockAppointmentRequests = [
      {
        id: 'req-1',
        user_id: 'user-123',
        patient_id: null,
        status: 'pending',
        request_type: 'general_appointment',
        request_details: {
          postalCode: 'H2T 1R8',
          date: '2025-03-12',
          time: '14:15'
        },
        created_at: '2025-03-10T14:15:00Z',
        updated_at: '2025-03-10T14:15:00Z'
      }
    ]
    
    global.fetch = vi.fn().mockResolvedValueOnce({
      ok: true,
      json: async () => ({ data: mockAppointmentRequests })
    })
    
    const { fetchAppointmentRequests } = useAppointmentHistoryStore.getState()
    const result = await fetchAppointmentRequests()
    
    expect(result).toEqual(mockAppointmentRequests)
    expect(useAppointmentHistoryStore.getState().appointmentRequests).toEqual(mockAppointmentRequests)
    expect(useAppointmentHistoryStore.getState().isLoading).toBe(false)
    expect(useAppointmentHistoryStore.getState().error).toBeNull()
    expect(global.fetch).toHaveBeenCalledWith(expect.stringContaining('/api/appointment-requests'))
  })
  
  test('should handle fetch appointment requests error', async () => {
    // Mock fetch error
    global.fetch = vi.fn().mockResolvedValueOnce({
      ok: false,
      json: async () => ({ error: 'Failed to fetch appointment requests' })
    })
    
    const { fetchAppointmentRequests } = useAppointmentHistoryStore.getState()
    const result = await fetchAppointmentRequests()
    
    expect(result).toEqual([])
    expect(useAppointmentHistoryStore.getState().appointmentRequests).toEqual([])
    expect(useAppointmentHistoryStore.getState().isLoading).toBe(false)
    expect(useAppointmentHistoryStore.getState().error).toBeInstanceOf(Error)
    expect(useAppointmentHistoryStore.getState().error?.message).toBe('Failed to fetch appointment requests')
  })
  
  test('should fetch completed appointments successfully', async () => {
    // Mock successful Supabase query
    const mockCompletedAppointments = [
      {
        id: 'comp-1',
        appointment_request_id: 'req-1',
        completion_details: {
          clinic_name: 'Test Clinic',
          doctor_name: 'Dr. Smith'
        },
        completed_at: '2025-03-15T10:00:00Z',
        appointment_request: {
          id: 'req-1',
          status: 'completed'
        }
      }
    ]
    
    vi.mocked(supabase.from).mockReturnValue({
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue({
        data: mockCompletedAppointments,
        error: null
      })
    } as any)
    
    const { fetchCompletedAppointments } = useAppointmentHistoryStore.getState()
    const result = await fetchCompletedAppointments()
    
    expect(result).toEqual(mockCompletedAppointments)
    expect(useAppointmentHistoryStore.getState().completedAppointments).toEqual(mockCompletedAppointments)
    expect(useAppointmentHistoryStore.getState().isLoading).toBe(false)
    expect(useAppointmentHistoryStore.getState().error).toBeNull()
    expect(supabase.from).toHaveBeenCalledWith('completed_appointments')
  })
  
  test('should handle fetch completed appointments error', async () => {
    // Mock Supabase error
    const mockError = new Error('Database error')
    
    vi.mocked(supabase.from).mockReturnValue({
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue({
        data: null,
        error: mockError
      })
    } as any)
    
    const { fetchCompletedAppointments } = useAppointmentHistoryStore.getState()
    const result = await fetchCompletedAppointments()
    
    expect(result).toEqual([])
    expect(useAppointmentHistoryStore.getState().completedAppointments).toEqual([])
    expect(useAppointmentHistoryStore.getState().isLoading).toBe(false)
    expect(useAppointmentHistoryStore.getState().error).toEqual(mockError)
  })
  
  test('should fetch cancelled appointments successfully', async () => {
    // Mock successful Supabase query
    const mockCancelledAppointments = [
      {
        id: 'cancel-1',
        appointment_request_id: 'req-2',
        cancellation_reason: 'No longer needed',
        cancelled_by: 'user',
        cancelled_at: '2025-03-14T09:00:00Z',
        appointment_request: {
          id: 'req-2',
          status: 'cancelled'
        }
      }
    ]
    
    vi.mocked(supabase.from).mockReturnValue({
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue({
        data: mockCancelledAppointments,
        error: null
      })
    } as any)
    
    const { fetchCancelledAppointments } = useAppointmentHistoryStore.getState()
    const result = await fetchCancelledAppointments()
    
    expect(result).toEqual(mockCancelledAppointments)
    expect(useAppointmentHistoryStore.getState().cancelledAppointments).toEqual(mockCancelledAppointments)
    expect(useAppointmentHistoryStore.getState().isLoading).toBe(false)
    expect(useAppointmentHistoryStore.getState().error).toBeNull()
    expect(supabase.from).toHaveBeenCalledWith('cancelled_appointments')
  })
  
  test('should create appointment request successfully', async () => {
    // Mock successful fetch
    const mockRequestData = {
      request_type: 'general_appointment',
      request_details: {
        postalCode: 'H2T 1R8',
        date: '2025-03-20',
        time: '10:00'
      }
    }
    
    const mockResponse = {
      id: 'new-req-1',
      user_id: 'user-123',
      status: 'pending',
      ...mockRequestData,
      created_at: '2025-03-18T10:00:00Z',
      updated_at: '2025-03-18T10:00:00Z'
    }
    
    global.fetch = vi.fn().mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockResponse })
    })
    
    const { createAppointmentRequest } = useAppointmentHistoryStore.getState()
    const result = await createAppointmentRequest(mockRequestData)
    
    expect(result.success).toBe(true)
    expect(result.data).toEqual(mockResponse)
    expect(useAppointmentHistoryStore.getState().appointmentRequests).toEqual([mockResponse])
    expect(useAppointmentHistoryStore.getState().isLoading).toBe(false)
    expect(useAppointmentHistoryStore.getState().error).toBeNull()
    expect(global.fetch).toHaveBeenCalledWith(
      '/api/appointment-requests',
      expect.objectContaining({
        method: 'POST',
        body: JSON.stringify(mockRequestData)
      })
    )
  })
  
  test('should handle create appointment request error', async () => {
    // Mock fetch error
    global.fetch = vi.fn().mockResolvedValueOnce({
      ok: false,
      json: async () => ({ error: 'Failed to create appointment request' })
    })
    
    const mockRequestData = {
      request_type: 'general_appointment',
      request_details: {
        postalCode: 'H2T 1R8',
        date: '2025-03-20',
        time: '10:00'
      }
    }
    
    const { createAppointmentRequest } = useAppointmentHistoryStore.getState()
    const result = await createAppointmentRequest(mockRequestData)
    
    expect(result.success).toBe(false)
    expect(result.error).toBe('Failed to create appointment request')
    expect(useAppointmentHistoryStore.getState().appointmentRequests).toEqual([])
    expect(useAppointmentHistoryStore.getState().isLoading).toBe(false)
    expect(useAppointmentHistoryStore.getState().error).toBeInstanceOf(Error)
  })
  
  test('should cancel appointment request successfully', async () => {
    // Set up initial state with an appointment request
    const mockRequest = {
      id: 'req-to-cancel',
      user_id: 'user-123',
      patient_id: null,
      status: 'pending',
      request_type: 'general_appointment',
      request_details: {
        postalCode: 'H2T 1R8',
        date: '2025-03-25',
        time: '15:30'
      },
      created_at: '2025-03-20T15:30:00Z',
      updated_at: '2025-03-20T15:30:00Z'
    }
    
    useAppointmentHistoryStore.setState({
      appointmentRequests: [mockRequest]
    })
    
    // Mock successful Supabase operations
    vi.mocked(supabase.from).mockImplementation((table) => {
      if (table === 'appointment_requests') {
        return {
          update: vi.fn().mockReturnThis(),
          eq: vi.fn().mockResolvedValue({
            error: null
          })
        } as any
      } else if (table === 'cancelled_appointments') {
        return {
          insert: vi.fn().mockResolvedValue({
            error: null
          })
        } as any
      }
      return {} as any
    })
    
    // Mock fetchCancelledAppointments to be a no-op
    const fetchCancelledAppointmentsMock = vi.fn().mockResolvedValue([])
    useAppointmentHistoryStore.setState({
      fetchCancelledAppointments: fetchCancelledAppointmentsMock
    })
    
    const { cancelAppointmentRequest } = useAppointmentHistoryStore.getState()
    const result = await cancelAppointmentRequest('req-to-cancel', 'Changed my mind')
    
    expect(result.success).toBe(true)
    
    // Check that the appointment request status was updated
    const updatedRequests = useAppointmentHistoryStore.getState().appointmentRequests
    expect(updatedRequests[0].status).toBe('cancelled')
    
    // Check that fetchCancelledAppointments was called
    expect(fetchCancelledAppointmentsMock).toHaveBeenCalled()
  })
  
  test('should handle cancel appointment request error', async () => {
    // Set up initial state with an appointment request
    const mockRequest = {
      id: 'req-to-cancel',
      user_id: 'user-123',
      patient_id: null,
      status: 'pending',
      request_type: 'general_appointment',
      request_details: {
        postalCode: 'H2T 1R8',
        date: '2025-03-25',
        time: '15:30'
      },
      created_at: '2025-03-20T15:30:00Z',
      updated_at: '2025-03-20T15:30:00Z'
    }
    
    useAppointmentHistoryStore.setState({
      appointmentRequests: [mockRequest]
    })
    
    // Mock Supabase error
    const mockError = new Error('Database error')
    
    vi.mocked(supabase.from).mockReturnValue({
      update: vi.fn().mockReturnThis(),
      eq: vi.fn().mockResolvedValue({
        error: mockError
      })
    } as any)
    
    const { cancelAppointmentRequest } = useAppointmentHistoryStore.getState()
    const result = await cancelAppointmentRequest('req-to-cancel')
    
    expect(result.success).toBe(false)
    expect(result.error).toBe('Database error')
    
    // Check that the appointment request status was not updated
    const updatedRequests = useAppointmentHistoryStore.getState().appointmentRequests
    expect(updatedRequests[0].status).toBe('pending')
  })
  
  test('should compute derived properties correctly', () => {
    // Set up state with various appointment types
    const pendingRequest = {
      id: 'pending-1',
      status: 'pending',
      user_id: 'user-123',
      patient_id: null,
      request_type: 'general_appointment',
      request_details: {},
      created_at: '2025-03-01T10:00:00Z',
      updated_at: '2025-03-01T10:00:00Z'
    }
    
    const inProgressRequest = {
      id: 'in-progress-1',
      status: 'in_progress',
      user_id: 'user-123',
      patient_id: null,
      request_type: 'general_appointment',
      request_details: {},
      created_at: '2025-03-02T10:00:00Z',
      updated_at: '2025-03-02T10:00:00Z'
    }
    
    const completedRequest = {
      id: 'completed-1',
      status: 'completed',
      user_id: 'user-123',
      patient_id: null,
      request_type: 'general_appointment',
      request_details: {},
      created_at: '2025-03-03T10:00:00Z',
      updated_at: '2025-03-03T10:00:00Z'
    }
    
    const cancelledRequest = {
      id: 'cancelled-1',
      status: 'cancelled',
      user_id: 'user-123',
      patient_id: null,
      request_type: 'general_appointment',
      request_details: {},
      created_at: '2025-03-04T10:00:00Z',
      updated_at: '2025-03-04T10:00:00Z'
    }
    
    useAppointmentHistoryStore.setState({
      appointmentRequests: [pendingRequest, inProgressRequest],
      completedAppointments: [{
        id: 'comp-1',
        appointment_request_id: 'completed-1',
        completion_details: {},
        completed_at: '2025-03-05T10:00:00Z',
        appointment_request: completedRequest
      }],
      cancelledAppointments: [{
        id: 'cancel-1',
        appointment_request_id: 'cancelled-1',
        cancellation_reason: 'Test',
        cancelled_by: 'user',
        cancelled_at: '2025-03-06T10:00:00Z',
        appointment_request: cancelledRequest
      }]
    })
    
    const { useAppointmentHistory } = require('../useAppointmentHistoryStore')
    const {
      pendingAppointments,
      inProgressAppointments,
      allAppointments
    } = useAppointmentHistory()
    
    expect(pendingAppointments).toHaveLength(1)
    expect(pendingAppointments[0].id).toBe('pending-1')
    
    expect(inProgressAppointments).toHaveLength(1)
    expect(inProgressAppointments[0].id).toBe('in-progress-1')
    
    expect(allAppointments).toHaveLength(4)
    expect(allAppointments.map(a => a.id)).toContain('pending-1')
    expect(allAppointments.map(a => a.id)).toContain('in-progress-1')
    expect(allAppointments.map(a => a.id)).toContain('completed-1')
    expect(allAppointments.map(a => a.id)).toContain('cancelled-1')
  })
})
