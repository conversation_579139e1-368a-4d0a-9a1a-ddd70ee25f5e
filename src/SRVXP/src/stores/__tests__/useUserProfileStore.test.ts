/**
 * Tests for the user profile store
 * 
 * To run these tests:
 * bun test src/stores/__tests__/useUserProfileStore.test.ts
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'bun:test'
import { useUserProfileStore } from '../useUserProfileStore'
import { useUserStore } from '../useUserStore'
import { invalidateCache } from '../persistence'

// Mock Supabase
vi.mock('@/lib/supabase/client', () => {
  return {
    supabase: {
      auth: {
        getUser: vi.fn(),
        updateUser: vi.fn()
      },
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockReturnThis(),
      maybeSingle: vi.fn(),
      update: vi.fn().mockReturnThis()
    }
  }
})

// Import the mocked supabase
import { supabase } from '@/lib/supabase/client'

// Mock useUserStore
vi.mock('../useUserStore', () => {
  return {
    useUserStore: {
      getState: vi.fn().mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        session: { user: { id: 'user-123', email: '<EMAIL>' } }
      })
    }
  }
})

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {}
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { store[key] = value },
    removeItem: (key: string) => { delete store[key] },
    clear: () => { store = {} },
    getAllKeys: () => Object.keys(store)
  }
})()

describe('User Profile Store', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    mockLocalStorage.clear()
    
    // Reset the store to its initial state
    useUserProfileStore.getState().reset()
    
    // Reset mocks
    vi.resetAllMocks()
    
    // Mock global objects
    Object.defineProperty(global, 'localStorage', { value: mockLocalStorage })
    
    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    
    // Mock setTimeout
    vi.useFakeTimers()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
    vi.useRealTimers()
  })
  
  test('should initialize with default values', () => {
    const state = useUserProfileStore.getState()
    expect(state.profile).toBeNull()
    expect(state.isLoading).toBe(false)
    expect(state.isSaving).toBe(false)
    expect(state.error).toBeNull()
    expect(state.saveError).toBeNull()
    expect(state.saveSuccess).toBe(false)
  })
  
  test('should fetch profile successfully', async () => {
    // Mock successful profile fetch
    const mockUser = {
      user: {
        email: '<EMAIL>',
        user_metadata: {
          first_name: 'John',
          last_name: 'Doe',
          phone: '************'
        }
      }
    }
    
    const mockPublicUser = {
      id: 'user-123',
      first_name: 'John',
      last_name: 'Doe',
      updated_at: '2023-01-01T00:00:00Z'
    }
    
    vi.mocked(supabase.auth.getUser).mockResolvedValue({
      data: mockUser,
      error: null
    } as any)
    
    vi.mocked(supabase.from).mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockPublicUser,
            error: null
          })
        })
      })
    } as any)
    
    const { fetchProfile } = useUserProfileStore.getState()
    const profile = await fetchProfile('user-123')
    
    expect(profile).not.toBeNull()
    expect(profile?.firstName).toBe('John')
    expect(profile?.lastName).toBe('Doe')
    expect(profile?.email).toBe('<EMAIL>')
    expect(profile?.phone).toBe('************')
    expect(profile?.fullName).toBe('John Doe')
    expect(profile?.initials).toBe('JD')
    
    expect(useUserProfileStore.getState().isLoading).toBe(false)
    expect(useUserProfileStore.getState().error).toBeNull()
  })
  
  test('should handle fetch profile error', async () => {
    // Mock error during profile fetch
    const mockError = new Error('Failed to fetch user')
    
    vi.mocked(supabase.auth.getUser).mockRejectedValue(mockError)
    
    const { fetchProfile } = useUserProfileStore.getState()
    const profile = await fetchProfile('user-123')
    
    expect(profile).toBeNull()
    expect(useUserProfileStore.getState().isLoading).toBe(false)
    expect(useUserProfileStore.getState().error).toEqual(mockError)
  })
  
  test('should update profile successfully', async () => {
    // Set up initial profile
    useUserProfileStore.setState({
      profile: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '************',
        avatar: null,
        initials: 'JD',
        fullName: 'John Doe'
      }
    })
    
    // Mock successful profile update
    vi.mocked(supabase.auth.updateUser).mockResolvedValue({
      data: { user: {} },
      error: null
    } as any)
    
    vi.mocked(supabase.from).mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          maybeSingle: vi.fn().mockResolvedValue({
            data: { id: 'user-123' },
            error: null
          })
        })
      }),
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          error: null
        })
      })
    } as any)
    
    const { updateProfile } = useUserProfileStore.getState()
    const success = await updateProfile('user-123', {
      firstName: 'Jane',
      lastName: 'Smith'
    })
    
    expect(success).toBe(true)
    expect(useUserProfileStore.getState().isSaving).toBe(false)
    expect(useUserProfileStore.getState().saveSuccess).toBe(true)
    expect(useUserProfileStore.getState().saveError).toBeNull()
    
    const updatedProfile = useUserProfileStore.getState().profile
    expect(updatedProfile?.firstName).toBe('Jane')
    expect(updatedProfile?.lastName).toBe('Smith')
    expect(updatedProfile?.fullName).toBe('Jane Smith')
    expect(updatedProfile?.initials).toBe('JS')
    
    // Test auto-reset of success message
    vi.advanceTimersByTime(3000)
    expect(useUserProfileStore.getState().saveSuccess).toBe(false)
  })
  
  test('should handle update profile error', async () => {
    // Set up initial profile
    useUserProfileStore.setState({
      profile: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '************',
        avatar: null,
        initials: 'JD',
        fullName: 'John Doe'
      }
    })
    
    // Mock error during profile update
    const mockError = new Error('Failed to update user metadata')
    
    vi.mocked(supabase.auth.updateUser).mockRejectedValue(mockError)
    
    const { updateProfile } = useUserProfileStore.getState()
    const success = await updateProfile('user-123', {
      firstName: 'Jane'
    })
    
    expect(success).toBe(false)
    expect(useUserProfileStore.getState().isSaving).toBe(false)
    expect(useUserProfileStore.getState().saveSuccess).toBe(false)
    expect(useUserProfileStore.getState().saveError).toEqual(mockError)
    
    // Profile should not be updated
    const profile = useUserProfileStore.getState().profile
    expect(profile?.firstName).toBe('John')
  })
  
  test('should handle cache invalidation', () => {
    // Set up initial profile
    useUserProfileStore.setState({
      profile: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '************',
        avatar: null,
        initials: 'JD',
        fullName: 'John Doe'
      },
      lastFetched: Date.now()
    })
    
    // Invalidate the cache
    invalidateCache('user-profile-store')
    
    // Reset the store (simulating page refresh)
    const newStore = useUserProfileStore
    
    // The store should be reset to initial state
    expect(newStore.getState().profile).toBeNull()
    expect(newStore.getState().lastFetched).toBeNull()
  })
  
  test('should use email to derive name when profile is incomplete', () => {
    // Set up initial profile with missing name
    useUserProfileStore.setState({
      profile: {
        firstName: '',
        lastName: '',
        email: '<EMAIL>',
        phone: '',
        avatar: null,
        initials: '??',
        fullName: 'User'
      }
    })
    
    const { useUserProfile } = require('../useUserProfileStore')
    const profile = useUserProfile()
    
    expect(profile.firstName).toBe('John')
    expect(profile.lastName).toBe('Doe')
    expect(profile.fullName).toBe('User')
    expect(profile.initials).toBe('??')
  })
})
