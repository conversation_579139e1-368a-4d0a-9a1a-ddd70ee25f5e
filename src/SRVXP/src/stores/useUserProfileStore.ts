/**
 * User Profile Store
 * 
 * This store manages user profile data and provides actions for
 * fetching and updating profile information.
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { supabase } from '@/lib/supabase/client'
import { createSelectors } from './utils'
import { createPersistConfig, createHybridStorage, invalidateCache } from './persistence'
import { useUserStore } from './useUserStore'

// Define the user profile type
export interface UserProfile {
  firstName: string
  lastName: string
  email: string
  phone: string
  avatar: string | null
  initials: string
  fullName: string
}

// Define the store state
interface UserProfileState {
  profile: UserProfile | null
  isLoading: boolean
  isSaving: boolean
  error: Error | null
  saveError: Error | null
  saveSuccess: boolean
  lastFetched: number | null
}

// Define update data type
export interface UserUpdateData {
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
}

// Define the store actions
interface UserProfileActions {
  // Profile actions
  fetchProfile: (userId: string) => Promise<UserProfile | null>
  updateProfile: (userId: string, data: UserUpdateData) => Promise<boolean>
  
  // State management actions
  setProfile: (profile: UserProfile | null) => void
  setIsLoading: (isLoading: boolean) => void
  setIsSaving: (isSaving: boolean) => void
  setError: (error: Error | null) => void
  setSaveError: (error: Error | null) => void
  setSaveSuccess: (success: boolean) => void
  clearErrors: () => void
  reset: () => void
}

// Combine state and actions
type UserProfileStore = UserProfileState & UserProfileActions

// Define initial state
const initialState: UserProfileState = {
  profile: null,
  isLoading: false,
  isSaving: false,
  error: null,
  saveError: null,
  saveSuccess: false,
  lastFetched: null
}

// Cache duration in milliseconds
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Create the store
const createUserProfileStore = () => {
  return create<UserProfileStore>()(
    persist(
      (set, get) => ({
        // Initial state
        ...initialState,
        
        // Profile actions
        fetchProfile: async (userId: string) => {
          try {
            set({ isLoading: true, error: null })
            
            // Check if we have a cached profile that's still valid
            const { profile, lastFetched } = get()
            const now = Date.now()
            
            if (profile && lastFetched && now - lastFetched < CACHE_DURATION) {
              set({ isLoading: false })
              return profile
            }
            
            // Get user data from auth.users
            const { data: userData, error: userError } = await supabase.auth.getUser()
            
            if (userError) {
              throw userError
            }
            
            // Get data from public.users table
            let publicUserData = null
            try {
              const { data, error: publicUserError } = await supabase
                .from('users')
                .select('*')
                .eq('id', userId)
                .single()
              
              if (!publicUserError) {
                publicUserData = data
              }
            } catch (err) {
              console.warn('Error getting public user data:', err)
            }
            
            // Combine the data
            const userMetadata = userData.user.user_metadata || {}
            
            // Parse name fields - first try from metadata
            let firstName = userMetadata.first_name || ''
            let lastName = userMetadata.last_name || ''
            
            // Use first_name and last_name from public.users if not in metadata
            if (!firstName) firstName = publicUserData?.first_name || ''
            if (!lastName) lastName = publicUserData?.last_name || ''
            
            // Create complete user profile
            const fullName = `${firstName} ${lastName}`.trim() || 'User'
            const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || '??'
            
            const userProfile: UserProfile = {
              firstName,
              lastName,
              email: userData.user.email || '',
              phone: userMetadata.phone || '',
              avatar: null,
              initials,
              fullName
            }
            
            set({ 
              profile: userProfile, 
              isLoading: false,
              lastFetched: Date.now()
            })
            
            return userProfile
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error fetching profile')
            set({ 
              isLoading: false, 
              error: err 
            })
            return null
          }
        },
        
        updateProfile: async (userId: string, data: UserUpdateData) => {
          try {
            set({ isSaving: true, saveError: null, saveSuccess: false })
            
            // Get current session for email updates
            const session = useUserStore.getState().session
            
            // Update metadata fields
            const metadataToUpdate: Record<string, any> = {}
            let hasMetadataUpdates = false
            
            if (data.firstName !== undefined) {
              metadataToUpdate.first_name = data.firstName
              hasMetadataUpdates = true
            }
            
            if (data.lastName !== undefined) {
              metadataToUpdate.last_name = data.lastName
              hasMetadataUpdates = true
            }
            
            if (data.phone !== undefined) {
              metadataToUpdate.phone = data.phone
              hasMetadataUpdates = true
            }
            
            if (hasMetadataUpdates) {
              // Update user metadata
              const { error: metadataError } = await supabase.auth.updateUser({
                data: metadataToUpdate,
              })
              
              if (metadataError) {
                throw new Error(`Error updating user metadata: ${metadataError.message}`)
              }
            }
            
            // Update email if provided and different from current email
            if (data.email !== undefined && session?.user?.email !== data.email) {
              const { error: emailError } = await supabase.auth.updateUser({
                email: data.email,
              })
              
              if (emailError) {
                throw new Error(`Error updating email: ${emailError.message}`)
              }
            }
            
            // Update any additional profile fields in users table
            try {
              // Check if the users table exists and has the user's record
              const { data: userExists, error: checkError } = await supabase
                .from('users')
                .select('id')
                .eq('id', userId)
                .maybeSingle()
              
              if (checkError && !checkError.message.includes('does not exist')) {
                console.warn(`Error checking users table: ${checkError.message}`)
              }
              
              if (userExists) {
                // Prepare data for users table
                const userData: Record<string, any> = {}
                
                if (data.firstName !== undefined) userData.first_name = data.firstName
                if (data.lastName !== undefined) userData.last_name = data.lastName
                if (data.phone !== undefined) userData.phone = data.phone
                
                // Only update if we have data to update
                if (Object.keys(userData).length > 0) {
                  // Add updated_at timestamp
                  userData.updated_at = new Date().toISOString()
                  
                  // Update existing user record
                  const { error: updateError } = await supabase
                    .from('users')
                    .update(userData)
                    .eq('id', userId)
                  
                  if (updateError) {
                    console.warn(`Error updating users table: ${updateError.message}`)
                  }
                }
              }
            } catch (err) {
              // Non-critical error, just log it
              console.warn('Error updating users table:', err)
            }
            
            // Update local profile state
            const currentProfile = get().profile
            if (currentProfile) {
              const updatedProfile = {
                ...currentProfile,
                firstName: data.firstName !== undefined ? data.firstName : currentProfile.firstName,
                lastName: data.lastName !== undefined ? data.lastName : currentProfile.lastName,
                email: data.email !== undefined ? data.email : currentProfile.email,
                phone: data.phone !== undefined ? data.phone : currentProfile.phone,
                fullName: `${data.firstName || currentProfile.firstName} ${data.lastName || currentProfile.lastName}`.trim(),
                initials: `${(data.firstName || currentProfile.firstName).charAt(0)}${(data.lastName || currentProfile.lastName).charAt(0)}`.toUpperCase()
              }
              
              set({ profile: updatedProfile })
            }
            
            // Invalidate any cached profile data
            invalidateCache('user-profile-store')
            
            // Set success state
            set({ 
              isSaving: false, 
              saveSuccess: true 
            })
            
            // Auto-reset success message after 3 seconds
            setTimeout(() => {
              set({ saveSuccess: false })
            }, 3000)
            
            return true
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error updating profile')
            set({ 
              isSaving: false, 
              saveError: err,
              saveSuccess: false
            })
            return false
          }
        },
        
        // State management actions
        setProfile: (profile) => set({ profile }),
        setIsLoading: (isLoading) => set({ isLoading }),
        setIsSaving: (isSaving) => set({ isSaving }),
        setError: (error) => set({ error }),
        setSaveError: (error) => set({ saveError: error }),
        setSaveSuccess: (success) => set({ saveSuccess: success }),
        clearErrors: () => set({ error: null, saveError: null }),
        reset: () => set(initialState)
      }),
      createPersistConfig<UserProfileStore>('user-profile-store', {
        storage: createJSONStorage(() => createHybridStorage()),
        // Only persist specific parts of the state
        partialize: (state) => ({
          profile: state.profile,
          lastFetched: state.lastFetched
          // Don't persist loading states or errors
        }),
        // Set version for cache invalidation
        version: 1,
        // Handle rehydration completion
        onRehydrateStorage: () => (state) => {
          // Check if we need to refresh the profile
          if (state?.profile && state.lastFetched) {
            const now = Date.now()
            
            // If the profile is older than the cache duration, refresh it
            if (now - state.lastFetched > CACHE_DURATION) {
              const userId = useUserStore.getState().user?.id
              if (userId) {
                // We need to use getState() here because the store might not be fully initialized yet
                setTimeout(() => {
                  useUserProfileStore.getState().fetchProfile(userId)
                }, 0)
              }
            }
          }
        }
      })
    )
  )
}

// Create and export the store with selectors
export const useUserProfileStore = createSelectors(createUserProfileStore())

// Export common selectors as hooks for convenience
export const useUserProfile = () => {
  const profile = useUserProfileStore((state) => state.profile)
  const isLoading = useUserProfileStore((state) => state.isLoading)
  const error = useUserProfileStore((state) => state.error)
  const fetchProfile = useUserProfileStore((state) => state.fetchProfile)
  const updateProfile = useUserProfileStore((state) => state.updateProfile)
  const isSaving = useUserProfileStore((state) => state.isSaving)
  const saveError = useUserProfileStore((state) => state.saveError)
  const saveSuccess = useUserProfileStore((state) => state.saveSuccess)
  
  // Get user ID from auth store
  const user = useUserStore((state) => state.user)
  const userId = user?.id
  
  // Compute derived properties
  const firstName = profile?.firstName || ''
  const lastName = profile?.lastName || ''
  const email = profile?.email || user?.email || ''
  const phone = profile?.phone || ''
  const initials = profile?.initials || '??'
  const fullName = profile?.fullName || email.split('@')[0] || 'User'
  
  // Derive a fallback name from email if we have no profile or incomplete profile
  let derivedFirstName = firstName
  let derivedLastName = lastName
  
  if ((!firstName && !lastName) && email) {
    const nameParts = email.split('@')[0].split('.')
    derivedFirstName = nameParts[0] ? nameParts[0].charAt(0).toUpperCase() + nameParts[0].slice(1) : ''
    derivedLastName = nameParts[1] ? nameParts[1].charAt(0).toUpperCase() + nameParts[1].slice(1) : ''
  }
  
  return {
    profile,
    isLoading,
    error,
    isSaving,
    saveError,
    saveSuccess,
    fetchProfile,
    updateProfile,
    // Helper computed properties
    firstName: firstName || derivedFirstName,
    lastName: lastName || derivedLastName,
    email,
    phone,
    initials,
    fullName,
    userId
  }
}
