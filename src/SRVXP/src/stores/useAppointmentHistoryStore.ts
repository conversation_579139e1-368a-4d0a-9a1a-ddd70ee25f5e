/**
 * Appointment History Store
 *
 * This store manages appointment history data, including pending, completed, and cancelled appointments.
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { supabase } from '@/lib/supabase/client'
import { createSelectors } from './utils'
import { createPersistConfig, createHybridStorage } from './persistence'
import { useUserStore } from './useUserStore'

// Define appointment types
export interface AppointmentRequest {
  id: string
  user_id: string
  patient_id: string | null
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  request_type: string
  request_details: {
    postalCode: string
    date: string
    time: string
    specialty?: string
    healthCardLastDigits?: string
    cardSequenceNumber?: string
    patientName?: string
    [key: string]: any
  }
  created_at: string
  updated_at: string
  family_members?: {
    first_name: string
    last_name: string
    health_card: string
    birth_date: string
  } | null
}

export interface CompletedAppointment {
  id: string
  appointment_request_id: string
  completion_details: {
    clinic_name?: string
    doctor_name?: string
    notes?: string
    follow_up_required?: boolean
    follow_up_date?: string
    [key: string]: any
  }
  completed_at: string
  appointment_request?: AppointmentRequest
}

export interface CancelledAppointment {
  id: string
  appointment_request_id: string
  cancellation_reason: string
  cancelled_by: 'user' | 'system' | 'admin'
  cancelled_at: string
  appointment_request?: AppointmentRequest
}

// Define pagination metadata interface
export interface PaginationMeta {
  currentPage: number
  totalPages: number
  totalCount: number
  hasMore: boolean
  pageSize: number
}

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 10

// Define the store state
interface AppointmentHistoryState {
  // Appointment data
  appointmentRequests: AppointmentRequest[]
  completedAppointments: CompletedAppointment[]
  cancelledAppointments: CancelledAppointment[]

  // Pagination metadata
  requestsPagination: PaginationMeta
  completedPagination: PaginationMeta
  cancelledPagination: PaginationMeta

  // Status flags
  isLoading: boolean
  error: Error | null
  lastFetched: number | null
}

// Define pagination parameters interface
export interface PaginationParams {
  page?: number
  pageSize?: number
  status?: string
  startDate?: string
  endDate?: string
}

// Define the store actions
interface AppointmentHistoryActions {
  // Fetch actions with pagination
  fetchAppointmentRequests: (params?: PaginationParams) => Promise<AppointmentRequest[]>
  fetchCompletedAppointments: (params?: PaginationParams) => Promise<CompletedAppointment[]>
  fetchCancelledAppointments: (params?: PaginationParams) => Promise<CancelledAppointment[]>
  fetchAllAppointmentHistory: () => Promise<void>

  // Pagination navigation
  fetchNextPage: (type: 'requests' | 'completed' | 'cancelled') => Promise<void>
  fetchPreviousPage: (type: 'requests' | 'completed' | 'cancelled') => Promise<void>
  goToPage: (type: 'requests' | 'completed' | 'cancelled', page: number) => Promise<void>
  setPageSize: (type: 'requests' | 'completed' | 'cancelled', size: number) => void

  // Appointment actions
  createAppointmentRequest: (data: any) => Promise<{ success: boolean; data?: AppointmentRequest; error?: string }>
  cancelAppointmentRequest: (requestId: string, reason?: string) => Promise<{ success: boolean; error?: string }>

  // State management actions
  setAppointmentRequests: (requests: AppointmentRequest[], pagination?: Partial<PaginationMeta>) => void
  setCompletedAppointments: (appointments: CompletedAppointment[], pagination?: Partial<PaginationMeta>) => void
  setCancelledAppointments: (appointments: CancelledAppointment[], pagination?: Partial<PaginationMeta>) => void
  setIsLoading: (isLoading: boolean) => void
  setError: (error: Error | null) => void
  clearError: () => void
  invalidateCache: () => void
  reset: () => void
}

// Combine state and actions
type AppointmentHistoryStore = AppointmentHistoryState & AppointmentHistoryActions

// Define initial pagination state
const initialPaginationState: PaginationMeta = {
  currentPage: 1,
  totalPages: 0,
  totalCount: 0,
  hasMore: false,
  pageSize: DEFAULT_PAGE_SIZE
}

// Define initial state
const initialState: AppointmentHistoryState = {
  // Appointment data
  appointmentRequests: [],
  completedAppointments: [],
  cancelledAppointments: [],

  // Pagination metadata
  requestsPagination: { ...initialPaginationState },
  completedPagination: { ...initialPaginationState },
  cancelledPagination: { ...initialPaginationState },

  // Status flags
  isLoading: false,
  error: null,
  lastFetched: null
}

// Cache duration in milliseconds
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Create the store
const createAppointmentHistoryStore = () => {
  return create<AppointmentHistoryStore>()(
    persist(
      (set, get) => ({
        // Initial state
        ...initialState,

        // Fetch actions with pagination
        fetchAppointmentRequests: async (params?: PaginationParams) => {
          try {
            set((state) => ({ isLoading: true, error: null }))

            // Get pagination parameters with defaults
            const state = get()
            const page = params?.page || state.requestsPagination.currentPage
            const pageSize = params?.pageSize || state.requestsPagination.pageSize
            const status = params?.status

            // Calculate offset for pagination
            const from = (page - 1) * pageSize
            const to = from + pageSize - 1

            // Get user ID for filtering
            const userId = useUserStore.getState().user?.id
            if (!userId) {
              throw new Error('User not authenticated')
            }

            // Fetch appointment requests directly from Supabase with pagination
            let query = supabase
              .from('appointment_requests')
              .select(`
                *,
                family_members(first_name, last_name, health_card, birth_date)
              `, { count: 'exact' }) // Get total count for pagination
              .eq('user_id', userId)
              .order('created_at', { ascending: false })
              .range(from, to)

            // Add status filter if provided
            if (status) {
              query = query.eq('status', status)
            }

            // Add date filters if provided
            if (params?.startDate) {
              query = query.gte('created_at', params.startDate)
            }
            if (params?.endDate) {
              query = query.lte('created_at', params.endDate)
            }

            // Execute query
            const { data, error, count } = await query

            if (error) {
              throw error
            }

            const requests = data as AppointmentRequest[]
            const totalCount = count || 0

            // Calculate pagination metadata
            const totalPages = Math.ceil(totalCount / pageSize)
            const hasMore = page < totalPages

            // Update state with new requests and pagination metadata
            set((state) => ({
              appointmentRequests: requests,
              requestsPagination: {
                currentPage: page,
                totalPages,
                totalCount,
                hasMore,
                pageSize
              },
              isLoading: false,
              lastFetched: Date.now()
            }))

            return requests
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error fetching appointment requests')
            set({ isLoading: false, error: err })
            console.error('Error fetching appointment requests:', error)
            return []
          }
        },

        fetchCompletedAppointments: async (params?: PaginationParams) => {
          try {
            set((state) => ({ isLoading: true, error: null }))

            // Get pagination parameters with defaults
            const state = get()
            const page = params?.page || state.completedPagination.currentPage
            const pageSize = params?.pageSize || state.completedPagination.pageSize

            // Calculate offset for pagination
            const from = (page - 1) * pageSize
            const to = from + pageSize - 1

            // Get user ID for filtering
            const userId = useUserStore.getState().user?.id
            if (!userId) {
              throw new Error('User not authenticated')
            }

            // Fetch completed appointments from Supabase with pagination
            let query = supabase
              .from('completed_appointments')
              .select(`
                *,
                appointment_request:appointment_request_id(*)
              `, { count: 'exact' }) // Get total count for pagination
              .order('completed_at', { ascending: false })
              .range(from, to)

            // Join with appointment_requests to filter by user_id
            query = query.filter('appointment_request.user_id', 'eq', userId)

            // Add date filters if provided
            if (params?.startDate) {
              query = query.gte('completed_at', params.startDate)
            }
            if (params?.endDate) {
              query = query.lte('completed_at', params.endDate)
            }

            // Execute query
            const { data, error, count } = await query

            if (error) {
              throw error
            }

            const completedAppointments = data as CompletedAppointment[]
            const totalCount = count || 0

            // Calculate pagination metadata
            const totalPages = Math.ceil(totalCount / pageSize)
            const hasMore = page < totalPages

            // Update state with new completed appointments and pagination metadata
            set((state) => ({
              completedAppointments,
              completedPagination: {
                currentPage: page,
                totalPages,
                totalCount,
                hasMore,
                pageSize
              },
              isLoading: false,
              lastFetched: Date.now()
            }))

            return completedAppointments
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error fetching completed appointments')
            set({ isLoading: false, error: err })
            console.error('Error fetching completed appointments:', error)
            return []
          }
        },

        fetchCancelledAppointments: async (params?: PaginationParams) => {
          try {
            set((state) => ({ isLoading: true, error: null }))

            // Get pagination parameters with defaults
            const state = get()
            const page = params?.page || state.cancelledPagination.currentPage
            const pageSize = params?.pageSize || state.cancelledPagination.pageSize

            // Calculate offset for pagination
            const from = (page - 1) * pageSize
            const to = from + pageSize - 1

            // Get user ID for filtering
            const userId = useUserStore.getState().user?.id
            if (!userId) {
              throw new Error('User not authenticated')
            }

            // Fetch cancelled appointments from Supabase with pagination
            let query = supabase
              .from('cancelled_appointments')
              .select(`
                *,
                appointment_request:appointment_request_id(*)
              `, { count: 'exact' }) // Get total count for pagination
              .order('cancelled_at', { ascending: false })
              .range(from, to)

            // Join with appointment_requests to filter by user_id
            query = query.filter('appointment_request.user_id', 'eq', userId)

            // Add date filters if provided
            if (params?.startDate) {
              query = query.gte('cancelled_at', params.startDate)
            }
            if (params?.endDate) {
              query = query.lte('cancelled_at', params.endDate)
            }

            // Execute query
            const { data, error, count } = await query

            if (error) {
              throw error
            }

            const cancelledAppointments = data as CancelledAppointment[]
            const totalCount = count || 0

            // Calculate pagination metadata
            const totalPages = Math.ceil(totalCount / pageSize)
            const hasMore = page < totalPages

            // Update state with new cancelled appointments and pagination metadata
            set((state) => ({
              cancelledAppointments,
              cancelledPagination: {
                currentPage: page,
                totalPages,
                totalCount,
                hasMore,
                pageSize
              },
              isLoading: false,
              lastFetched: Date.now()
            }))

            return cancelledAppointments
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error fetching cancelled appointments')
            set({ isLoading: false, error: err })
            console.error('Error fetching cancelled appointments:', error)
            return []
          }
        },

        fetchAllAppointmentHistory: async () => {
          try {
            set({ isLoading: true, error: null })

            // Check if we have cached data that's still valid
            const { lastFetched } = get()
            const now = Date.now()

            if (lastFetched && now - lastFetched < CACHE_DURATION) {
              set({ isLoading: false })
              return
            }

            // Reset pagination to first page
            const params: PaginationParams = {
              page: 1,
              pageSize: DEFAULT_PAGE_SIZE
            }

            // Fetch all appointment data in parallel
            await Promise.all([
              get().fetchAppointmentRequests(params),
              get().fetchCompletedAppointments(params),
              get().fetchCancelledAppointments(params)
            ])

            set({ lastFetched: Date.now() })
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error fetching appointment history')
            set({ isLoading: false, error: err })
            console.error('Error fetching appointment history:', error)
          }
        },

        // Appointment actions
        createAppointmentRequest: async (data) => {
          try {
            set({ isLoading: true, error: null })

            const response = await fetch('/api/appointment-requests', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            })

            const result = await response.json()

            if (!response.ok) {
              throw new Error(result.error || 'Failed to create appointment request')
            }

            const newRequest = result.data as AppointmentRequest

            // Update state with new request
            set((state) => ({
              appointmentRequests: [newRequest, ...state.appointmentRequests],
              isLoading: false
            }))

            return { success: true, data: newRequest }
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error creating appointment request')
            set({ isLoading: false, error: err })
            console.error('Error creating appointment request:', error)
            return {
              success: false,
              error: err.message
            }
          }
        },

        cancelAppointmentRequest: async (requestId, reason) => {
          try {
            set({ isLoading: true, error: null })

            // Update the appointment request status in Supabase
            const { error } = await supabase
              .from('appointment_requests')
              .update({ status: 'cancelled' })
              .eq('id', requestId)

            if (error) {
              throw error
            }

            // Create a cancelled appointment record
            const { error: cancelError } = await supabase
              .from('cancelled_appointments')
              .insert({
                appointment_request_id: requestId,
                cancellation_reason: reason || 'Cancelled by user',
                cancelled_by: 'user'
              })

            if (cancelError) {
              console.error('Error creating cancelled appointment record:', cancelError)
            }

            // Update state
            set((state) => ({
              appointmentRequests: state.appointmentRequests.map((req) =>
                req.id === requestId ? { ...req, status: 'cancelled' } : req
              ),
              isLoading: false
            }))

            // Refresh cancelled appointments
            get().fetchCancelledAppointments()

            return { success: true }
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error cancelling appointment request')
            set({ isLoading: false, error: err })
            console.error('Error cancelling appointment request:', error)
            return {
              success: false,
              error: err.message
            }
          }
        },

        // Pagination navigation functions
        fetchNextPage: async (type: 'requests' | 'completed' | 'cancelled') => {
          const state = get()
          let currentPage: number
          let hasMore: boolean

          // Get current pagination state based on type
          switch (type) {
            case 'requests':
              currentPage = state.requestsPagination.currentPage
              hasMore = state.requestsPagination.hasMore
              break
            case 'completed':
              currentPage = state.completedPagination.currentPage
              hasMore = state.completedPagination.hasMore
              break
            case 'cancelled':
              currentPage = state.cancelledPagination.currentPage
              hasMore = state.cancelledPagination.hasMore
              break
          }

          // If there are more pages, fetch the next one
          if (hasMore) {
            const nextPage = currentPage + 1
            await get().goToPage(type, nextPage)
          }
        },

        fetchPreviousPage: async (type: 'requests' | 'completed' | 'cancelled') => {
          const state = get()
          let currentPage: number

          // Get current pagination state based on type
          switch (type) {
            case 'requests':
              currentPage = state.requestsPagination.currentPage
              break
            case 'completed':
              currentPage = state.completedPagination.currentPage
              break
            case 'cancelled':
              currentPage = state.cancelledPagination.currentPage
              break
          }

          // If we're not on the first page, fetch the previous one
          if (currentPage > 1) {
            const prevPage = currentPage - 1
            await get().goToPage(type, prevPage)
          }
        },

        goToPage: async (type: 'requests' | 'completed' | 'cancelled', page: number) => {
          // Create params with the requested page
          const params: PaginationParams = { page }

          // Fetch the appropriate data based on type
          switch (type) {
            case 'requests':
              await get().fetchAppointmentRequests(params)
              break
            case 'completed':
              await get().fetchCompletedAppointments(params)
              break
            case 'cancelled':
              await get().fetchCancelledAppointments(params)
              break
          }
        },

        setPageSize: (type: 'requests' | 'completed' | 'cancelled', size: number) => {
          // Update the page size in the appropriate pagination state
          switch (type) {
            case 'requests':
              set((state) => ({
                requestsPagination: {
                  ...state.requestsPagination,
                  pageSize: size,
                  currentPage: 1 // Reset to first page when changing page size
                }
              }))
              // Refetch with new page size
              get().fetchAppointmentRequests({ pageSize: size, page: 1 })
              break
            case 'completed':
              set((state) => ({
                completedPagination: {
                  ...state.completedPagination,
                  pageSize: size,
                  currentPage: 1
                }
              }))
              get().fetchCompletedAppointments({ pageSize: size, page: 1 })
              break
            case 'cancelled':
              set((state) => ({
                cancelledPagination: {
                  ...state.cancelledPagination,
                  pageSize: size,
                  currentPage: 1
                }
              }))
              get().fetchCancelledAppointments({ pageSize: size, page: 1 })
              break
          }
        },

        // State management actions
        setAppointmentRequests: (requests, pagination) => set((state) => ({
          appointmentRequests: requests,
          requestsPagination: pagination ? { ...state.requestsPagination, ...pagination } : state.requestsPagination
        })),

        setCompletedAppointments: (appointments, pagination) => set((state) => ({
          completedAppointments: appointments,
          completedPagination: pagination ? { ...state.completedPagination, ...pagination } : state.completedPagination
        })),

        setCancelledAppointments: (appointments, pagination) => set((state) => ({
          cancelledAppointments: appointments,
          cancelledPagination: pagination ? { ...state.cancelledPagination, ...pagination } : state.cancelledPagination
        })),

        setIsLoading: (isLoading) => set({ isLoading }),
        setError: (error) => set({ error }),
        clearError: () => set({ error: null }),

        // Cache invalidation
        invalidateCache: () => {
          set({ lastFetched: null })
          get().fetchAllAppointmentHistory()
        },

        reset: () => set(initialState)
      }),
      createPersistConfig<AppointmentHistoryStore>('appointment-history-store', {
        storage: createJSONStorage(() => createHybridStorage()),
        // Only persist specific parts of the state
        partialize: (state) => ({
          appointmentRequests: state.appointmentRequests,
          completedAppointments: state.completedAppointments,
          cancelledAppointments: state.cancelledAppointments,
          requestsPagination: state.requestsPagination,
          completedPagination: state.completedPagination,
          cancelledPagination: state.cancelledPagination,
          lastFetched: state.lastFetched
          // Don't persist loading states or errors
        }),
        // Set version for cache invalidation
        version: 3, // Increment version to invalidate old caches after adding advanced selectors
        // Handle rehydration completion
        onRehydrateStorage: () => (state) => {
          if (state) {
            // Check if we need to refresh the data
            const { lastFetched } = state
            const now = Date.now()

            // If the data is older than the cache duration, refresh it
            if (!lastFetched || now - lastFetched > CACHE_DURATION) {
              // We need to use getState() here because the store might not be fully initialized yet
              setTimeout(() => {
                const userId = useUserStore.getState().user?.id
                if (userId) {
                  useAppointmentHistoryStore.getState().fetchAllAppointmentHistory()
                }
              }, 0)
            }
          }
        }
      })
    )
  )
}

// Create and export the store with selectors
export const useAppointmentHistoryStore = createSelectors(createAppointmentHistoryStore())

// Export common selectors as hooks for convenience
export const useAppointmentHistory = () => {
  // Data selectors
  const appointmentRequests = useAppointmentHistoryStore((state) => state.appointmentRequests)
  const completedAppointments = useAppointmentHistoryStore((state) => state.completedAppointments)
  const cancelledAppointments = useAppointmentHistoryStore((state) => state.cancelledAppointments)

  // Pagination metadata selectors
  const requestsPagination = useAppointmentHistoryStore((state) => state.requestsPagination)
  const completedPagination = useAppointmentHistoryStore((state) => state.completedPagination)
  const cancelledPagination = useAppointmentHistoryStore((state) => state.cancelledPagination)

  // Status selectors
  const isLoading = useAppointmentHistoryStore((state) => state.isLoading)
  const error = useAppointmentHistoryStore((state) => state.error)

  // Action selectors
  const fetchAppointmentRequests = useAppointmentHistoryStore((state) => state.fetchAppointmentRequests)
  const fetchCompletedAppointments = useAppointmentHistoryStore((state) => state.fetchCompletedAppointments)
  const fetchCancelledAppointments = useAppointmentHistoryStore((state) => state.fetchCancelledAppointments)
  const fetchAllAppointmentHistory = useAppointmentHistoryStore((state) => state.fetchAllAppointmentHistory)
  const createAppointmentRequest = useAppointmentHistoryStore((state) => state.createAppointmentRequest)
  const cancelAppointmentRequest = useAppointmentHistoryStore((state) => state.cancelAppointmentRequest)

  // Pagination action selectors
  const fetchNextPage = useAppointmentHistoryStore((state) => state.fetchNextPage)
  const fetchPreviousPage = useAppointmentHistoryStore((state) => state.fetchPreviousPage)
  const goToPage = useAppointmentHistoryStore((state) => state.goToPage)
  const setPageSize = useAppointmentHistoryStore((state) => state.setPageSize)

  // Cache invalidation
  const invalidateCache = useAppointmentHistoryStore((state) => state.invalidateCache)

  // Compute derived properties
  const pendingAppointments = appointmentRequests.filter((req) => req.status === 'pending')
  const inProgressAppointments = appointmentRequests.filter((req) => req.status === 'in_progress')

  // Get all appointments (including completed and cancelled)
  const allAppointments = [
    ...appointmentRequests,
    ...completedAppointments.map((ca) => ca.appointment_request).filter(Boolean),
    ...cancelledAppointments.map((ca) => ca.appointment_request).filter(Boolean)
  ] as AppointmentRequest[]

  // Advanced filtering functions

  // Filter appointments by date range
  const filterByDateRange = (startDate?: string, endDate?: string) => {
    // Create params with date filters
    const params: PaginationParams = {
      startDate,
      endDate,
      page: 1 // Reset to first page when filtering
    }

    // Fetch all appointment types with the date filters
    return Promise.all([
      fetchAppointmentRequests(params),
      fetchCompletedAppointments(params),
      fetchCancelledAppointments(params)
    ])
  }

  // Filter appointments by status
  const filterByStatus = (status: 'pending' | 'in_progress' | 'completed' | 'cancelled') => {
    if (status === 'completed') {
      return completedAppointments
    } else if (status === 'cancelled') {
      return cancelledAppointments
    } else {
      return appointmentRequests.filter(req => req.status === status)
    }
  }

  // Filter appointments by type
  const filterByType = (requestType: string) => {
    return allAppointments.filter(appointment => appointment.request_type === requestType)
  }

  // Advanced counting and summary functions

  // Get appointment counts by status
  const getAppointmentCountsByStatus = () => {
    return {
      pending: appointmentRequests.filter(req => req.status === 'pending').length,
      in_progress: appointmentRequests.filter(req => req.status === 'in_progress').length,
      completed: completedAppointments.length,
      cancelled: cancelledAppointments.length,
      total: allAppointments.length
    }
  }

  // Get appointment counts by month
  const getAppointmentCountsByMonth = (year = new Date().getFullYear()) => {
    // Initialize counts for each month
    const monthlyCounts = Array(12).fill(0).map(() => ({
      pending: 0,
      in_progress: 0,
      completed: 0,
      cancelled: 0,
      total: 0
    }))

    // Count appointments by month and status
    allAppointments.forEach(appointment => {
      const date = new Date(appointment.created_at)
      if (date.getFullYear() === year) {
        const month = date.getMonth()

        // Increment the appropriate counter
        if (appointment.status === 'pending') {
          monthlyCounts[month].pending++
        } else if (appointment.status === 'in_progress') {
          monthlyCounts[month].in_progress++
        } else if (appointment.status === 'completed') {
          monthlyCounts[month].completed++
        } else if (appointment.status === 'cancelled') {
          monthlyCounts[month].cancelled++
        }

        // Always increment the total
        monthlyCounts[month].total++
      }
    })

    return monthlyCounts
  }

  // Get appointment summary statistics
  const getAppointmentSummary = () => {
    const counts = getAppointmentCountsByStatus()

    // Calculate completion rate
    const completionRate = counts.total > 0
      ? (counts.completed / counts.total) * 100
      : 0

    // Calculate cancellation rate
    const cancellationRate = counts.total > 0
      ? (counts.cancelled / counts.total) * 100
      : 0

    // Get most recent appointment
    const sortedAppointments = [...allAppointments].sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )
    const mostRecentAppointment = sortedAppointments.length > 0 ? sortedAppointments[0] : null

    return {
      counts,
      completionRate,
      cancellationRate,
      mostRecentAppointment,
      totalAppointments: counts.total
    }
  }

  // Advanced grouping functions

  // Group appointments by status
  const groupByStatus = () => {
    return {
      pending: appointmentRequests.filter(req => req.status === 'pending'),
      in_progress: appointmentRequests.filter(req => req.status === 'in_progress'),
      completed: completedAppointments.map(ca => ca.appointment_request).filter(Boolean),
      cancelled: cancelledAppointments.map(ca => ca.appointment_request).filter(Boolean)
    }
  }

  // Group appointments by month
  const groupByMonth = (year = new Date().getFullYear()) => {
    // Initialize arrays for each month
    const monthlyGroups: Record<number, AppointmentRequest[]> = {}

    // Group appointments by month
    allAppointments.forEach(appointment => {
      const date = new Date(appointment.created_at)
      if (date.getFullYear() === year) {
        const month = date.getMonth()

        // Initialize the month array if it doesn't exist
        if (!monthlyGroups[month]) {
          monthlyGroups[month] = []
        }

        // Add the appointment to the month
        monthlyGroups[month].push(appointment)
      }
    })

    return monthlyGroups
  }

  // Group appointments by type
  const groupByType = () => {
    const groups: Record<string, AppointmentRequest[]> = {}

    allAppointments.forEach(appointment => {
      const type = appointment.request_type

      // Initialize the type array if it doesn't exist
      if (!groups[type]) {
        groups[type] = []
      }

      // Add the appointment to the type
      groups[type].push(appointment)
    })

    return groups
  }

  // Additional utility functions

  // Sort appointments by date (newest first by default)
  const sortByDate = (appointments: AppointmentRequest[], ascending = false) => {
    return [...appointments].sort((a, b) => {
      const dateA = new Date(a.created_at).getTime()
      const dateB = new Date(b.created_at).getTime()
      return ascending ? dateA - dateB : dateB - dateA
    })
  }

  // Get appointments for a specific date range
  const getAppointmentsInDateRange = (startDate: Date, endDate: Date) => {
    return allAppointments.filter(appointment => {
      const appointmentDate = new Date(appointment.created_at)
      return appointmentDate >= startDate && appointmentDate <= endDate
    })
  }

  // Get appointments for a specific day
  const getAppointmentsForDay = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const day = date.getDate()

    return allAppointments.filter(appointment => {
      const appointmentDate = new Date(appointment.created_at)
      return (
        appointmentDate.getFullYear() === year &&
        appointmentDate.getMonth() === month &&
        appointmentDate.getDate() === day
      )
    })
  }

  // Get upcoming appointments (pending or in_progress with future dates)
  const getUpcomingAppointments = () => {
    const now = new Date()

    return appointmentRequests
      .filter(req => {
        // Only include pending or in_progress appointments
        if (req.status !== 'pending' && req.status !== 'in_progress') {
          return false
        }

        // Check if the appointment date is in the future
        const appointmentDate = new Date(
          `${req.request_details.date}T${req.request_details.time}`
        )
        return appointmentDate > now
      })
      .sort((a, b) => {
        // Sort by appointment date (ascending)
        const dateA = new Date(
          `${a.request_details.date}T${a.request_details.time}`
        ).getTime()
        const dateB = new Date(
          `${b.request_details.date}T${b.request_details.time}`
        ).getTime()
        return dateA - dateB
      })
  }

  return {
    // Data
    appointmentRequests,
    completedAppointments,
    cancelledAppointments,
    pendingAppointments,
    inProgressAppointments,
    allAppointments,

    // Pagination metadata
    requestsPagination,
    completedPagination,
    cancelledPagination,

    // Status
    isLoading,
    error,

    // Data fetching actions
    fetchAppointmentRequests,
    fetchCompletedAppointments,
    fetchCancelledAppointments,
    fetchAllAppointmentHistory,
    createAppointmentRequest,
    cancelAppointmentRequest,

    // Pagination actions
    fetchNextPage,
    fetchPreviousPage,
    goToPage,
    setPageSize,

    // Advanced filtering functions
    filterByDateRange,
    filterByStatus,
    filterByType,

    // Advanced counting and summary functions
    getAppointmentCountsByStatus,
    getAppointmentCountsByMonth,
    getAppointmentSummary,

    // Advanced grouping functions
    groupByStatus,
    groupByMonth,
    groupByType,

    // Additional utility functions
    sortByDate,
    getAppointmentsInDateRange,
    getAppointmentsForDay,
    getUpcomingAppointments,

    // Cache management
    invalidateCache
  }
}
