/**
 * Zustand Store Persistence Utilities
 * 
 * This file contains utility functions for managing persistence in Zustand stores.
 */

import { StateStorage, StorageValue } from 'zustand/middleware'
import { PersistOptions } from 'zustand/middleware/persist'

/**
 * Custom storage implementation that adds version and timestamp to stored data
 * This helps with cache invalidation based on version or time
 */
export const createVersionedStorage = (
  storage: StateStorage = localStorage,
  version: number = 1
): StateStorage => {
  return {
    getItem: (name: string): StorageValue => {
      const storedItem = storage.getItem(name)
      
      if (!storedItem) return null
      
      try {
        const parsed = JSON.parse(storedItem)
        
        // If the stored version doesn't match the current version, return null
        // This will cause the store to use the initial state
        if (parsed.__version !== version) {
          console.log(`[Storage] Version mismatch for ${name}, invalidating cache`)
          return null
        }
        
        // Check if the data has expired
        if (parsed.__expires && Date.now() > parsed.__expires) {
          console.log(`[Storage] Data expired for ${name}, invalidating cache`)
          return null
        }
        
        // Return the actual data without metadata
        return JSON.stringify(parsed.data)
      } catch (error) {
        console.error(`[Storage] Error parsing stored item ${name}:`, error)
        return null
      }
    },
    
    setItem: (name: string, value: string): void => {
      try {
        const data = JSON.parse(value)
        const wrappedData = {
          __version: version,
          __timestamp: Date.now(),
          __expires: null, // Will be set by setExpiryTime if needed
          data
        }
        storage.setItem(name, JSON.stringify(wrappedData))
      } catch (error) {
        console.error(`[Storage] Error storing item ${name}:`, error)
      }
    },
    
    removeItem: (name: string): void => {
      storage.removeItem(name)
    }
  }
}

/**
 * Sets an expiry time for a specific store
 * After this time, the data will be considered stale and will be reloaded
 * 
 * @param storageName The name of the store in localStorage
 * @param expiryTimeMs The expiry time in milliseconds
 */
export const setExpiryTime = (
  storageName: string,
  expiryTimeMs: number,
  storage: Storage = localStorage
): void => {
  try {
    const item = storage.getItem(storageName)
    if (!item) return
    
    const parsed = JSON.parse(item)
    parsed.__expires = Date.now() + expiryTimeMs
    
    storage.setItem(storageName, JSON.stringify(parsed))
  } catch (error) {
    console.error(`[Storage] Error setting expiry time for ${storageName}:`, error)
  }
}

/**
 * Invalidates the cache for a specific store
 * This will cause the store to use the initial state on next load
 * 
 * @param storageName The name of the store in localStorage
 */
export const invalidateCache = (
  storageName: string,
  storage: Storage = localStorage
): void => {
  storage.removeItem(storageName)
}

/**
 * Invalidates all caches that match a prefix
 * Useful for clearing all related stores at once
 * 
 * @param prefix The prefix to match
 */
export const invalidateCachesByPrefix = (
  prefix: string,
  storage: Storage = localStorage
): void => {
  for (let i = 0; i < storage.length; i++) {
    const key = storage.key(i)
    if (key && key.startsWith(prefix)) {
      storage.removeItem(key)
    }
  }
}

/**
 * Creates persistence configuration with common options
 * 
 * @param name The name of the store in localStorage
 * @param options Additional persistence options
 */
export const createPersistConfig = <T>(
  name: string,
  options?: Partial<PersistOptions<T>>
): PersistOptions<T> => {
  return {
    name,
    storage: options?.storage,
    partialize: options?.partialize,
    merge: options?.merge,
    onRehydrateStorage: options?.onRehydrateStorage,
    version: options?.version || 1,
    migrate: options?.migrate
  }
}

/**
 * Creates a hybrid storage that uses memory for SSR and localStorage for client
 * This prevents hydration mismatches in Next.js
 */
export const createHybridStorage = (): StateStorage => {
  // In-memory storage for SSR
  const memoryStorage: Record<string, string> = {}
  
  // Determine if we're in a browser environment
  const isBrowser = typeof window !== 'undefined' && typeof window.localStorage !== 'undefined'
  
  return {
    getItem: (name: string): StorageValue => {
      if (isBrowser) {
        return localStorage.getItem(name)
      }
      return memoryStorage[name] || null
    },
    
    setItem: (name: string, value: string): void => {
      if (isBrowser) {
        localStorage.setItem(name, value)
      } else {
        memoryStorage[name] = value
      }
    },
    
    removeItem: (name: string): void => {
      if (isBrowser) {
        localStorage.removeItem(name)
      } else {
        delete memoryStorage[name]
      }
    }
  }
}
