# Zustand Store Implementation

This directory contains the Zustand store implementation for the application. Zustand is a small, fast, and scalable state management solution that provides a simple API for managing global state.

## Store Structure

The store is organized into several slices, each responsible for a specific domain of the application:

- **User Store**: Manages authentication state and user session
- **User Profile Store**: Manages user profile data
- **Preferences Store**: Manages user preferences like theme and language
- **Language Store**: Manages translations and language settings
- **Appointment History Store**: Manages appointment requests, completed appointments, and cancelled appointments
- **Subscription Store**: Manages subscription data and status
- **App Store**: Combines all stores into a single store for easier access

## Directory Structure

```
src/
└── stores/
    ├── index.ts                  # Main entry point, re-exports all stores
    ├── utils.ts                  # Utility functions for stores
    ├── persistence.ts            # Persistence utilities
    ├── useUserStore.ts           # User authentication store
    ├── useUserProfileStore.ts    # User profile store
    ├── usePreferencesStore.ts    # User preferences store (theme)
    ├── useLanguageStore.ts       # Language and translations store
    ├── useAppointmentHistoryStore.ts # Appointment history store
    ├── useSubscriptionStore.ts   # Subscription data store
    ├── useAppStore.ts            # Combined store
    └── __tests__/                # Test files for each store
```

## Store Naming Conventions

- All store hooks should be named with the `use` prefix followed by the store name and `Store` suffix (e.g., `useUserStore`)
- Store files should match the hook name (e.g., `useUserStore.ts`)
- Internal store implementation variables should use the `create` prefix (e.g., `createUserStore`)

## Store Implementation Pattern

Each store should follow this general pattern:

```typescript
// 1. Define types
interface StoreState {
  // State properties
}

interface StoreActions {
  // Action methods
}

// 2. Create the store
export const useMyStore = create<StoreState & StoreActions>()(
  persist(
    (set, get) => ({
      // Initial state

      // Actions
      someAction: () => set((state) => ({ /* updated state */ })),
    }),
    {
      name: 'my-store',
      // Other persistence options
    }
  )
)
```

## Selectors

For performance reasons, always use selectors when accessing store state in components:

```typescript
// Good - only re-renders when count changes
const count = useMyStore((state) => state.count)

// Bad - re-renders when any state changes
const { count } = useMyStore()
```

For convenience, we provide auto-generated selectors via the `use` property:

```typescript
const count = useMyStore.use.count()
```

## Persistence

Stores that need to persist data should use the Zustand persist middleware:

```typescript
export const useMyStore = create<MyStoreState & MyStoreActions>()(
  persist(
    (set, get) => ({
      // Store implementation
    }),
    {
      name: 'my-store', // Unique name for localStorage
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        // Only persist specific parts of the state
        someValue: state.someValue,
      }),
    }
  )
)
```

## Integration with Supabase

Stores that need to fetch data from Supabase should:

1. Provide loading states and error handling
2. Implement cache invalidation logic
3. Handle authentication state changes
4. Use optimistic updates where appropriate

## Cross-Store Communication

For actions that need to update multiple stores, use the combined store approach or access other stores directly:

```typescript
const updateProfile = async (profile) => {
  // Update profile in user store
  await updateUserProfile(profile)

  // Also update related data in another store
  useOtherStore.getState().refreshData()
}
```

## Usage

### Basic Usage

```tsx
import { useUserStore } from '@/stores/index'

function MyComponent() {
  // Access state
  const user = useUserStore((state) => state.user)

  // Access actions
  const signOut = useUserStore((state) => state.signOut)

  return (
    <div>
      <p>Hello, {user?.email}</p>
      <button onClick={() => signOut()}>Sign Out</button>
    </div>
  )
}
```

### Using Selectors

For better performance, use selectors to access only the state you need:

```tsx
import { useUserStore } from '@/stores/index'

function MyComponent() {
  // Only re-renders when user.email changes
  const email = useUserStore((state) => state.user?.email)

  return <p>Hello, {email}</p>
}
```

### Using Auto-Generated Selectors

Each store has auto-generated selectors for convenient access:

```tsx
import { useUserStore } from '@/stores/index'

function MyComponent() {
  // Using auto-generated selectors
  const email = useUserStore.use.user()?.email

  return <p>Hello, {email}</p>
}
```

### Using Convenience Hooks

Each store provides convenience hooks for common use cases:

```tsx
import { useCurrentUser, useIsAuthenticated } from '@/stores/index'

function MyComponent() {
  // Using convenience hooks
  const user = useCurrentUser()
  const isAuthenticated = useIsAuthenticated()

  return (
    <div>
      {isAuthenticated ? (
        <p>Hello, {user?.email}</p>
      ) : (
        <p>Please sign in</p>
      )}
    </div>
  )
}
```

### Using the Combined App Store

The app store provides a central place to access all application state:

```tsx
import { useAppStore } from '@/stores/index'

function MyComponent() {
  // Access actions from different stores through the app store
  const { signIn, fetchProfile, setTheme } = useAppStore(state => ({
    signIn: state.signIn,
    fetchProfile: state.fetchProfile,
    setTheme: state.setTheme
  }))

  return (
    <div>
      <button onClick={() => signIn('<EMAIL>', 'password')}>Sign In</button>
      <button onClick={() => fetchProfile('user-123')}>Fetch Profile</button>
      <button onClick={() => setTheme('dark')}>Dark Mode</button>
    </div>
  )
}
```

### Initializing the App

Use the `AppInitializer` component to initialize the app state:

```tsx
import { AppInitializer } from '@/components/app-initializer'

function MyApp({ children }) {
  return (
    <AppInitializer>
      {children}
    </AppInitializer>
  )
}
```

## Store Features

### Persistence

All stores use the Zustand persist middleware to persist state to localStorage. This ensures that state is preserved across page refreshes and browser sessions.

### Cache Invalidation

Stores implement cache invalidation to ensure that stale data is refreshed when needed. Each store has a `lastFetched` timestamp that is used to determine if the data needs to be refreshed.

### Type Safety

All stores are fully typed with TypeScript, providing type safety and autocompletion.

### Optimistic Updates

Stores implement optimistic updates for a better user experience. When an action is performed, the state is updated immediately, and then the server is updated in the background.

### Error Handling

All stores include error handling to ensure that errors are properly captured and displayed to the user.

## Testing

Each store has a corresponding test file in the `__tests__` directory. These tests ensure that the store behaves as expected and that all actions work correctly.

To run the tests:

```bash
bun test src/stores/__tests__
```

## Migration from Context API

The Zustand implementation is designed to gradually replace the existing Context API implementation. Each store provides integration utilities to help with the migration:

- **Auth Store Integration**: `src/lib/auth-store-integration.tsx`
- **Profile Store Integration**: `src/lib/profile-store-integration.tsx`
- **Preferences Store Integration**: `src/lib/preferences-store-integration.tsx`
- **Language Store Integration**: `src/lib/language-store-integration.tsx`

These utilities allow for a gradual migration from the Context API to Zustand, ensuring that both implementations can coexist during the transition period.
