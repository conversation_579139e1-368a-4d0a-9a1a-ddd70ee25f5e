"use client"

import React from "react"
import Link from "next/link"
import Image from "next/image"
import "@/styles/dashboard-fixes.css"
import { Sidebar } from "@/components/layout/sidebar"
import { MobileNav } from "@/components/layout/mobile-nav"
import { LanguageToggle } from "@/components/language-toggle"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { DashboardThemeInitializer } from "@/components/layout/dashboard-theme-initializer"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { useUserProfile } from "@/hooks/use-user-profile"
import { T } from "@/components/t"
import { translationKeys } from "@/lib/translations"

export interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const { initials } = useUserProfile()

  return (
    <ProtectedRoute>
      {/* Initialize theme from user preferences */}
      <DashboardThemeInitializer />

      <div className="flex min-h-screen flex-col">
        <header className="sticky top-0 z-30 flex h-14 items-center border-b bg-gray-50 dark:bg-gray-900 px-4 sm:px-6 md:px-3 md:z-10 md:border-l-0">
          <div className="flex w-full justify-between">
            <div className="flex items-center gap-2 md:w-56 lg:w-72">
              <Link href="/dashboard" className="flex items-center group pl-1 md:pl-1.5">
                <div className="w-8 h-8 flex items-center justify-center transition-transform duration-300 group-hover:scale-110">
                  <Image
                    src="/zaply-images/srvxp_logorevised.svg"
                    alt="Logo"
                    width={32}
                    height={32}
                    priority
                    suppressHydrationWarning
                    className="text-brandBlue fill-current"
                  />
                </div>
                <span className="ml-3 text-base font-bold text-[#212242] dark:text-white whitespace-nowrap hidden md:inline-block">
                  <T keyName={translationKeys.landing.navbar.title} />
                </span>
              </Link>
            </div>
            <div className="flex items-center gap-4">
              <LanguageToggle />
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-blue-100 text-blue-700 text-sm font-normal">
                  {initials}
                </AvatarFallback>
              </Avatar>
              <div className="md:hidden">
                <MobileNav />
              </div>
            </div>
          </div>
        </header>
        <div className="flex flex-1 relative">
          <div className="flex w-full">
            <Sidebar />
            <main className="flex-1 p-4 md:p-6">
              <div className="max-w-[1000px]">
                {children}
              </div>
            </main>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}