"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { ClipboardList, HelpCircle, LogOut, Search, Settings, User } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { useTranslation } from "@/components/t"
import { translationKeys } from "@/lib/translations"
import { useState, memo } from "react"
import { useAuth } from "@/lib/AuthContext"

export const Sidebar = memo(function Sidebar() {
  const pathname = usePathname()
  const { t } = useTranslation()
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const { signOut } = useAuth()

  const sidebarLinks = [
    {
      title: t(translationKeys.nav.dashboard),
      href: "/dashboard",
      icon: Settings,
    },
    {
      title: t(translationKeys.nav.findAppointment),
      href: "/trouver-rendez-vous",
      icon: Search,
    },
    {
      title: t(translationKeys.nav.appointments),
      href: "/mes-rendez-vous",
      icon: ClipboardList,
    },
    {
      title: t(translationKeys.account.yourAccount),
      href: "/compte",
      icon: User,
    },
    {
      title: t(translationKeys.nav.needHelp),
      href: "/aide",
      icon: HelpCircle,
    },
  ]

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true)
      await signOut('/')
    } catch (error) {
      console.error("Error during logout:", error)
      setIsLoggingOut(false)
    }
  }

  return (
    <div className="hidden border-r bg-gray-50 dark:bg-gray-900 md:block md:w-56 lg:w-72 sidebar-no-top-border">
      <div className="flex h-full flex-col">
        {/* Navigation links */}
        <div className="pt-4 px-1.5">
          <nav className="grid gap-1">
            {sidebarLinks.map((link) => (
              <Button
                key={link.href}
                variant={pathname === link.href ? "secondary" : "ghost"}
                className={cn(
                  "flex w-full items-center justify-start gap-2 pl-5",
                  pathname === link.href
                    ? "bg-secondary font-medium"
                    : "font-normal"
                )}
                asChild
              >
                <Link href={link.href}>
                  <link.icon className="h-4 w-4" />
                  {link.title}
                </Link>
              </Button>
            ))}

            <div className="pt-3 mt-2 border-t border-border/40">
              <Button
                variant="ghost"
                size="sm"
                className="flex w-full items-center justify-start gap-2 pl-5 text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-950/10"
                onClick={handleLogout}
                disabled={isLoggingOut}
              >
                <LogOut className="h-4 w-4" />
                <span className="text-sm">{isLoggingOut ? t(translationKeys.common.loggingOut) : t(translationKeys.common.logout)}</span>
              </Button>
            </div>
          </nav>
        </div>
      </div>
    </div>
  )
})
