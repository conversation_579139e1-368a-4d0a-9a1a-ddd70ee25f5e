"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { ClipboardList, HelpCircle, LogOut, Menu, Search, Settings, User, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
  <PERSON>etTrigger,
  SheetClose,
  SheetHeader,
  SheetTitle
} from "@/components/ui/sheet"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { useTranslation } from "@/components/t"
import { translationKeys } from "@/lib/translations"
import { signOutCompletely } from "@/lib/auth-utils"
import { useUserProfile } from "@/hooks/use-user-profile"

import { memo } from "react"

export const MobileNav = memo(function MobileNav() {
  const pathname = usePathname()
  const router = useRouter()
  const { t } = useTranslation()
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const { fullName, initials } = useUserProfile()

  const mobileLinks = [
    {
      title: t(translationKeys.nav.dashboard),
      href: "/dashboard",
      icon: Settings,
    },
    {
      title: t(translationKeys.nav.findAppointment),
      href: "/trouver-rendez-vous",
      icon: Search,
    },
    {
      title: t(translationKeys.nav.appointments),
      href: "/mes-rendez-vous",
      icon: ClipboardList,
    },
    {
      title: t(translationKeys.account.yourAccount),
      href: "/compte",
      icon: User,
    },
    {
      title: t(translationKeys.nav.needHelp),
      href: "/aide",
      icon: HelpCircle,
    },
  ]

  const handleLogout = async () => {
    try {
      console.log("Starting logout process...")
      setIsLoggingOut(true)

      // Use the utility function for complete sign out
      await signOutCompletely('/')
    } catch (error) {
      console.error("Error during logout:", error)
      setIsLoggingOut(false)
    }
  }

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" size="sm" className="md:hidden">
          <Menu className="h-4 w-4" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="px-0 [&>button]:hidden">
        <SheetHeader className="sr-only">
          <SheetTitle>{t(translationKeys.nav.mobileNavigation)}</SheetTitle>
        </SheetHeader>
        <div className="flex h-full flex-col">
          <div className="flex flex-col items-center py-6">
            <Avatar className="h-16 w-16 mb-2">
              <AvatarFallback className="bg-blue-100 text-blue-700 text-xl font-normal">
                {initials}
              </AvatarFallback>
            </Avatar>
            <h2 className="text-lg font-medium">
              {fullName}
            </h2>
            <SheetClose asChild className="absolute right-4 top-4">
              <Button variant="ghost" size="sm">
                <X className="h-4 w-4" />
                <span className="sr-only">{t(translationKeys.common.close)}</span>
              </Button>
            </SheetClose>
          </div>
          <div className="px-3 py-4">
            <nav className="grid gap-1 px-2">
              {mobileLinks.map((link) => (
                <Button
                  key={link.href}
                  variant={pathname === link.href ? "secondary" : "ghost"}
                  className={cn(
                    "flex w-full items-center justify-start gap-2 text-base py-2.5",
                    pathname === link.href
                      ? "bg-secondary font-medium"
                      : "font-normal"
                  )}
                  asChild
                >
                  <Link href={link.href}>
                    <link.icon className="h-5 w-5 mr-1" />
                    {link.title}
                  </Link>
                </Button>
              ))}

              <div className="pt-3 mt-2 border-t border-border/40">
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex w-full items-center justify-start gap-2 text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-950/10 text-base py-2.5"
                  onClick={handleLogout}
                  disabled={isLoggingOut}
                >
                  <LogOut className="h-5 w-5 mr-1" />
                  <span>{isLoggingOut ? t(translationKeys.common.loggingOut) : t(translationKeys.common.logout)}</span>
                </Button>
              </div>
            </nav>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
})
