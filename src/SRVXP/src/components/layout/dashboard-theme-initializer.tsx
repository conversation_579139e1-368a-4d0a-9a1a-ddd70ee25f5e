"use client";

import { useEffect } from "react";
import { useThemeSync } from "@/hooks/use-theme-sync";

/**
 * A component that initializes the theme for the dashboard
 * This ensures the user's preferences are applied when they log in
 */
export function DashboardThemeInitializer() {
  const { theme } = useThemeSync();

  // This component doesn't render anything, it just initializes the theme
  return null;
}
