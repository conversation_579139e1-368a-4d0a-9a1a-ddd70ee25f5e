"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Globe } from "lucide-react"
import { useLanguage } from "@/lib/LanguageContext"
import { memo } from "react"

interface LanguageToggleProps {
  className?: string
  variant?: "default" | "mobile"
}

export const LanguageToggle = memo(function LanguageToggle({ 
  className, 
  variant = "default" 
}: LanguageToggleProps) {
  const { language, setLanguage, isLoading } = useLanguage()

  const toggleLanguage = () => {
    // Switch between FR and EN directly
    const newLanguage = language === "fr" ? "en" : "fr"
    setLanguage(newLanguage)
    // The language context will handle persisting the preference
  }

  // Don't render until translations are loaded to prevent flickering
  if (isLoading) {
    return null;
  }

  // For mobile, we'll keep the current approach as it's a dual-button 
  // interface that shows both options simultaneously
  if (variant === "mobile") {
    return (
      <div className="mt-4 border-t pt-4">
        <p className="mb-2 px-2 text-sm font-medium text-muted-foreground">
          Langue / Language
        </p>
        <div className="flex gap-2">
          <Button
            variant={language === "fr" ? "secondary" : "ghost"}
            size="sm"
            className="flex-1 justify-start gap-2"
            onClick={() => setLanguage("fr")}
          >
            <Globe className="h-4 w-4" />
            Français
          </Button>
          <Button
            variant={language === "en" ? "secondary" : "ghost"}
            size="sm"
            className="flex-1 justify-start gap-2"
            onClick={() => setLanguage("en")}
          >
            <Globe className="h-4 w-4" />
            English
          </Button>
        </div>
      </div>
    )
  }

  // For desktop: button that toggles directly with full language name and globe icon
  return (
    <Button
      variant="outline"
      onClick={toggleLanguage}
      className={`${className} flex items-center justify-center gap-2 border border-border bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3`}
      aria-label={language === "fr" ? "Switch to English" : "Passer au français"}
    >
      <Globe className="h-4 w-4" />
      <span className="text-sm font-normal">{language === "fr" ? "English" : "Français"}</span>
    </Button>
  )
})