"use client";

import { useEffect, ReactNode } from "react";
import { useAuth } from "@/lib/AuthContext";
import { useRouter } from "next/navigation";

interface ProtectedRouteProps {
  children: ReactNode;
  redirectTo?: string;
}

/**
 * Component to protect routes that require authentication
 * Redirects to signin page if not authenticated
 */
export function ProtectedRoute({ 
  children, 
  redirectTo = "/auth/sign-in" 
}: ProtectedRouteProps) {
  const { status, refresh } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // If auth state is loading, do nothing yet
    if (status === "loading") return;

    // If not authenticated, redirect to login
    if (status === "unauthenticated") {
      const currentPath = window.location.pathname;
      const redirect = encodeURIComponent(currentPath);
      // Redirect with the current path as a parameter
      router.push(`${redirectTo}?redirectedFrom=${redirect}`);
    }
  }, [status, router, redirectTo]);

  // Handle visibility change to recheck auth when user returns to tab
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        refresh();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [refresh]);

  // Show loading state while checking authentication
  if (status === "loading") {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-white">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
      </div>
    );
  }

  // If authenticated, render children
  return status === "authenticated" ? <>{children}</> : null;
}