"use client";

import { useEffect, useState, useRef } from "react";
import { useAuth } from "@/lib/AuthContext";
import { useExternalNavigation } from "@/lib/hooks/useExternalNavigation";

interface StripeReturnHandlerProps {
  children: React.ReactNode;
}

/**
 * Component that handles authentication restoration when returning from Stripe
 */
export function StripeReturnHandler({ children }: StripeReturnHandlerProps) {
  const { status, refresh } = useAuth();
  const { detectStripeReturn } = useExternalNavigation();
  const [isHandlingReturn, setIsHandlingReturn] = useState(false);
  const hasHandledReturn = useRef(false);
  const isProcessing = useRef(false);

  useEffect(() => {
    const handleStripeReturn = async () => {
      // Prevent multiple simultaneous processing
      if (isProcessing.current || hasHandledReturn.current) {
        return;
      }

      // Check if this is a return from <PERSON><PERSON> and user is not authenticated
      const isStripeReturn = detectStripeReturn();
      
      if (isStripeReturn && status !== "authenticated") {
        isProcessing.current = true;
        setIsHandlingReturn(true);
        hasHandledReturn.current = true;
        
        console.log("Handling Stripe return, attempting auth restoration...");
        
        try {
          // Wait a bit for cookies to settle
          await new Promise(resolve => setTimeout(resolve, 1500));
          
          // Refresh auth state
          await refresh();
          
          // Give additional time for state to update
          await new Promise(resolve => setTimeout(resolve, 1000));
          
        } catch (error) {
          console.error("Error handling Stripe return:", error);
        } finally {
          setIsHandlingReturn(false);
          isProcessing.current = false;
        }
      }
    };

    // Only run once when component mounts
    handleStripeReturn();
  }, []); // Empty dependency array to run only once

  // Reset if user becomes authenticated
  useEffect(() => {
    if (status === "authenticated" && hasHandledReturn.current) {
      console.log("User authenticated, resetting Stripe return handler");
      hasHandledReturn.current = false;
      isProcessing.current = false;
      setIsHandlingReturn(false);
    }
  }, [status]);

  // Show loading state while handling return
  if (isHandlingReturn) {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-white">
        <div className="text-center">
          <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">Restoring your session...</p>
          <p className="mt-2 text-sm text-gray-500">Please wait while we verify your authentication.</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
} 