"use client";

import { useLanguage } from "@/lib/LanguageContext";

export function TranslationLoader({ children }: { children: React.ReactNode }) {
  const { isLoading } = useLanguage();

  if (isLoading) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">Chargement / Loading...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}