"use client";

import { dynamicComponent } from "@/lib/dynamic-imports";
import { DashboardLayoutProps } from "@/components/layout/dashboard-layout";

// Load the dashboard layout component dynamically
export const DynamicDashboardLayout = dynamicComponent<DashboardLayoutProps>(
  () => import("@/components/layout/dashboard-layout").then(mod => ({ default: mod.DashboardLayout })),
  {
    ssr: false,
    displayName: "DashboardLayout",
  }
);
