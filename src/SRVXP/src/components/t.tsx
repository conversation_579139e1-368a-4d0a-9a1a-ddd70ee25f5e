"use client"

import { useLanguage } from "@/lib/LanguageContext"
import { memo } from "react"

interface TranslateProps {
  keyName: string;
  params?: Record<string, string | number>;
}

// The T component allows for easy translation usage in JSX
// Usage: <T keyName="common.hello" />
export const T = memo(function T({ keyName, params }: TranslateProps) {
  const { translate, isLoading } = useLanguage()
  
  // If translations are still loading, show the key
  if (isLoading) {
    return <>{keyName}</>;
  }
  
  let text = translate(keyName)

  // Replace parameters if any
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      text = text.replace(`{{${key}}}`, String(value))
    })
  }

  return <>{text}</>
})

// For use outside of JSX
export const useTranslation = () => {
  const { translate, language, isLoading } = useLanguage()

  // Extended translate function that also handles parameter substitution
  const t = (key: string, params?: Record<string, string | number>) => {
    // If translations are still loading, return the key
    if (isLoading) {
      return key;
    }
    
    let text = translate(key)

    // Replace parameters if any
    if (params) {
      Object.entries(params).forEach(([paramKey, value]) => {
        text = text.replace(`{{${paramKey}}}`, String(value))
      })
    }

    return text
  }

  return { t, language, isLoading }
}