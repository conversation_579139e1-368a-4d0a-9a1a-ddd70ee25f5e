"use client";

import { <PERSON><PERSON> } from "@/components/zaply/ui/button";
import { Check } from "lucide-react";
import Link from "next/link";
import { useState, useEffect } from "react";
import { useScrollReveal } from "@/lib/zaply/hooks/useScrollReveal";
import { PriceToggle } from "@/components/zaply/core/price-toggle";
import { T } from "@/components/t";
import { translationKeys } from "@/lib/translations";
import { useLanguage } from "@/lib/LanguageContext";

interface PricingCardProps {
  title: string | React.ReactNode;
  price: string;
  description: string | React.ReactNode;
  features: (string | React.ReactNode)[];
  annualSavings?: string | React.ReactNode;
}

const PricingCard = ({ title, price, description, features, annualSavings }: PricingCardProps) => {
  const { language } = useLanguage();

  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm flex flex-col h-full sm:max-w-[75%] sm:mx-auto md:max-w-none">
      <div className="flex-grow">
        <h3 className="text-xl font-bold mb-2">{title}</h3>
        {/* Adding a key to the price div forces it to re-render when price changes */}
        <div
          key={`price-${price}`}
          className="text-5xl font-bold mb-5 animate-price-fade text-[#212244]"
        >
          {/* Extract just the price portion and always show monthly suffix */}
          {language === 'fr'
            ? price.split('/')[0]
            : price.split('/')[0]
          }
          <span className="text-2xl font-normal">{language === 'fr' ? '/mois' : '/month'}</span>
        </div>

        {annualSavings && (
          <div className="text-green-600 text-sm font-medium mb-2">
            {annualSavings}
          </div>
        )}
        <p className="text-gray-600 mb-6">{description}</p>
      </div>

      <Button asChild size="lg" className="w-full rounded-lg font-medium text-base py-6 mb-8 group hover:shadow-md transition-all duration-300">
        <Link href="/auth/sign-up">
          <T keyName={translationKeys.landing.pricing.choosePlan} />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="ml-2 h-5 w-5 arrow-icon"
          >
            <path d="M5 12h14"/>
            <path d="m12 5 7 7-7 7"/>
          </svg>
        </Link>
      </Button>

      <div className="space-y-4">
        <h4 className="font-bold text-[#212244]"><T keyName={translationKeys.landing.pricing.included} /></h4>

        {features.map((feature, index) => (
          <div key={index} className="flex items-center">
            <div className="flex-shrink-0 w-5 h-5 mr-3">
              <Check className="w-full h-full text-brandBlue" strokeWidth={2} />
            </div>
            <span className="text-[#212244]">{feature}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export function PricingSection() {
  const [pricingPeriod, setPricingPeriod] = useState<"monthly" | "annual">("monthly");
  const [isMounted, setIsMounted] = useState(false);
  const { ref, isIntersecting } = useScrollReveal({
    threshold: 0.2,
    rootMargin: "-50px"
  });
  const { language } = useLanguage();

  // Set isMounted to true after component mounts to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Handle pricing period change
  const handlePricingPeriodChange = (period: "monthly" | "annual") => {
    console.log("Pricing period changed to:", period);
    setPricingPeriod(period);
  };

  // Calculate prices based on period and language
  const formatPrice = (amount: string) => {
    return language === 'fr' ? `${amount}$` : `$${amount}`;
  };

  // Monthly prices
  const monthlyIndividualPrice = language === 'fr' ? formatPrice("7,95") : formatPrice("7.95");
  const monthlyFamilyPrice = language === 'fr' ? formatPrice("14,95") : formatPrice("14.95");

  // Annual prices (30% discount)
  const annualIndividualPrice = language === 'fr' ? formatPrice("5,95") : formatPrice("5.95"); // 7.95 * 12 * 0.7 = 66.78
  const annualFamilyPrice = language === 'fr' ? formatPrice("11,20") : formatPrice("11.20"); // 14.95 * 12 * 0.7 = 125.58

  // Individual price savings calculation
  const individualSavings = pricingPeriod === "annual" ? <T keyName={translationKeys.landing.pricing.individual.annualSavings} /> : undefined;
  const familySavings = pricingPeriod === "annual" ? <T keyName={translationKeys.landing.pricing.family.annualSavings} /> : undefined;

  // Select the right price based on period
  const individualPrice = pricingPeriod === "monthly" ? monthlyIndividualPrice : annualIndividualPrice;
  const familyPrice = pricingPeriod === "monthly" ? monthlyFamilyPrice : annualFamilyPrice;

  // Use a basic suffix just to construct the keys, the display is handled in the PricingCard component
  const periodSuffix = "/suffix";

  // Full formatted price strings for use as keys in the PricingCard
  const formattedIndividualPrice = `${individualPrice}${periodSuffix}`;
  const formattedFamilyPrice = `${familyPrice}${periodSuffix}`;

  return (
    <section
      className="py-16 lg:py-24 overflow-hidden"
      ref={ref}
    >
      <div className="flex flex-col lg:flex-row items-start gap-12 lg:gap-16">
        {/* Left side - Text */}
        <div className={`w-full lg:w-1/3 ${isMounted && isIntersecting ? 'animate-fade-in-up-1' : 'opacity-0'}`}>
          <h2 className="text-[42px] sm:text-[52px] m:text-[52px] lg:text-[56px] xl:text-[60px] leading-none font-bold text-[#212244] mb-6">
            <T keyName={translationKeys.landing.pricing.title} />
          </h2>
          <p className="text-gray-600 text-lg mb-6">
            <T keyName={translationKeys.landing.pricing.description} />
          </p>

          {/* Price toggle for desktop */}
          <div className="hidden lg:flex lg:items-center lg:justify-start">
            <PriceToggle period={pricingPeriod} onChange={handlePricingPeriodChange} />
          </div>
        </div>

        {/* Right side - Pricing Cards */}
        <div className="w-full lg:w-2/3">
          {/* Price toggle for mobile */}
          <div className={`flex justify-center mb-8 -mt-12 lg:hidden ${isMounted && isIntersecting ? 'animate-fade-in-up-1' : 'opacity-0'}`}>
            <PriceToggle period={pricingPeriod} onChange={handlePricingPeriodChange} />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className={`${isMounted && isIntersecting ? 'animate-fade-in-up-2' : 'opacity-0'}`}>
              <PricingCard
                key={`individual-${pricingPeriod}`}
                title={<T keyName={translationKeys.landing.pricing.individual.title} />}
                price={formattedIndividualPrice}
                description={<T keyName={translationKeys.landing.pricing.individual.description} />}
                features={[
                  <T key="1" keyName={translationKeys.landing.pricing.feature1} />,
                  <T key="2" keyName={translationKeys.landing.pricing.feature2} />,
                  <T key="3" keyName={translationKeys.landing.pricing.feature3} />,
                  <T key="4" keyName={translationKeys.landing.pricing.feature4} />
                ]}
                annualSavings={pricingPeriod === "annual" ? individualSavings : undefined}
              />
            </div>

            <div className={`${isMounted && isIntersecting ? 'animate-fade-in-up-3' : 'opacity-0'}`}>
              <PricingCard
                key={`family-${pricingPeriod}`}
                title={<T keyName={translationKeys.landing.pricing.family.title} />}
                price={formattedFamilyPrice}
                description={<T keyName={translationKeys.landing.pricing.family.description} />}
                features={[
                  <T key="1" keyName={translationKeys.landing.pricing.family.features} />,
                  <T key="2" keyName={translationKeys.landing.pricing.feature2} />,
                  <T key="3" keyName={translationKeys.landing.pricing.feature3} />,
                  <T key="4" keyName={translationKeys.landing.pricing.feature4} />
                ]}
                annualSavings={pricingPeriod === "annual" ? familySavings : undefined}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
