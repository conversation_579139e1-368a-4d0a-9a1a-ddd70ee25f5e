"use client";

import { But<PERSON> } from "@/components/zaply/ui/button";
import Link from "next/link";
import Image from "next/image";
import { useScrollReveal } from "@/lib/zaply/hooks/useScrollReveal";
import { useState, useEffect } from "react";
import { T } from "@/components/t";
import { useLanguage } from "@/lib/LanguageContext";
import { translationKeys } from "@/lib/translations";

export function CtaSection2() {
  const { language } = useLanguage();
  const { ref, isIntersecting } = useScrollReveal({
    threshold: 0.2,
    rootMargin: "-50px"
  });
  const [isMounted, setIsMounted] = useState(false);

  // Set isMounted to true after component mounts to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <section
      className="bg-white text-[#212244] w-full pt-0 pb-16 overflow-hidden"
      ref={ref}
    >
      <div
        className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8"
      >
        <div className="bg-[#f8f9fb] rounded-xl py-16 px-8 sm:px-8 md:px-16 w-full max-w-6xl">
          <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
            {/* Left side text content - text-left instead of text-center */}
            <div className="w-full lg:w-1/2 text-left">
              <h2
                className={`text-[42px] sm:text-[52px] m:text-[52px] lg:text-[56px] xl:text-[60px] leading-none font-bold text-[#212244] mb-8 ${
                  isMounted && isIntersecting ? 'animate-fade-in-up-1' : 'opacity-0'
                }`}
              >
                {language === "fr" ? (
                  <>
                    Ne tardez pas à consulter <span className="text-brandBlue">un médecin.</span>
                  </>
                ) : (
                  <>
                    Don't wait to see <span className="text-brandBlue">a doctor.</span>
                  </>
                )}
              </h2>

              <p
                className={`text-xl font-normal text-gray-600 mb-10 ${
                  isMounted && isIntersecting ? 'animate-fade-in-up-2' : 'opacity-0'
                }`}
              >
                <T keyName={translationKeys.landing.cta.subtitle} />
              </p>

              <div className={`${isMounted && isIntersecting ? 'animate-fade-in-up-3' : 'opacity-0'}`}>
                <Button asChild size="lg" className="rounded-md font-medium text-base px-8 py-4 bg-brandBlue text-white hover:bg-brandBlue/90 transition-all duration-300 h-auto hover:shadow-lg group hover:scale-105">
                  <Link href="/auth/sign-up">
                    <T keyName={translationKeys.landing.cta.buttonText} />
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="ml-2 h-5 w-5 arrow-icon"
                    >
                      <path d="M5 12h14"/>
                      <path d="m12 5 7 7-7 7"/>
                    </svg>
                  </Link>
                </Button>
              </div>
            </div>

            {/* Right side image */}
            <div className={`w-full lg:w-1/2 flex justify-center items-center ${isMounted && isIntersecting ? 'animate-fade-in-up-4' : 'opacity-0'}`}>
              <img
                src="/zaply-images/CTAimage.png"
                alt={translationKeys.landing.cta.imageAlt}
                className="rounded-xl w-full max-w-[450px] h-auto object-contain"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
