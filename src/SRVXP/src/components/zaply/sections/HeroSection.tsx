"use client";

import { But<PERSON> } from "@/components/zaply/ui/button";
import Link from "next/link";
import Image from "next/image";
import { useEffect, useState } from "react";
import { T } from "@/components/t";
import { useLanguage } from "@/lib/LanguageContext";
import { translationKeys } from "@/lib/translations";

export function HeroSection() {
  const { language } = useLanguage();
  // Use state to control animation on client-side only
  const [isMounted, setIsMounted] = useState(false);

  // Set isMounted to true after component mounts to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Add smooth scroll function for the "En savoir plus" button
  const scrollToFeaturesSection = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();

    const section = document.getElementById('features-section');
    if (section) {
      // Get the section's position on the page
      const sectionRect = section.getBoundingClientRect();
      const offsetTop = sectionRect.top + window.pageYOffset;

      // Scroll to the section with a slight offset to position it at the very top
      window.scrollTo({
        top: offsetTop - 1, // Tiny offset to ensure we're at the very top edge
        behavior: 'smooth'
      });
    }
  };

  return (
    <section className="py-8 sm:py-12 lg:py-16 xl:py-20 overflow-hidden">
      <div className="flex flex-col lg:flex-row items-center gap-8 sm:gap-12 lg:gap-16">
        {/* Content - Now on the left */}
        <div className="w-full lg:w-1/2 space-y-4 sm:space-y-6 xl:space-y-8">
          <h1
            className={`text-5xl xs:text-4xl sm:text-5xl md:text-[52px] lg:text-[60px] xl:text-[66px] font-bold tracking-tight text-[#212244] leading-none ${isMounted ? 'animate-fade-in-up-1' : 'opacity-0'}`}
          >
            {language === "fr" ? (
              <>
                Obtenez un rendez-vous avec un médecin <span className="text-brandBlue">dès aujourd'hui.</span>
              </>
            ) : (
              <>
                Find an appointment with a doctor <span className="text-brandBlue">today.</span>
              </>
            )}
          </h1>

          <p
            className={`text-gray-600 max-w-lg md:max-w-none lg:max-w-xl xl:max-w-2xl text-lg xs:text-sm sm:text-lg md:text-lg lg:text-xl xl:text-[20px] ${isMounted ? 'animate-fade-in-up-2' : 'opacity-0'}`}
          >
            <T keyName={translationKeys.landing.hero.subtitle} />
          </p>

          <div
            className={`pt-4 flex flex-col gap-4 md:flex-row ${isMounted ? 'animate-fade-in-up-3' : 'opacity-0'}`}
          >
            <Button asChild size="lg" className="rounded-lg font-medium text-base sm:text-lg px-8 py-6 group hover:shadow-md transition-all duration-300">
              <Link href="/auth/sign-up">
                <T keyName={translationKeys.landing.hero.findAppointment} />
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="ml-2 h-5 w-5 arrow-icon"
                >
                  <path d="M5 12h14"/>
                  <path d="m12 5 7 7-7 7"/>
                </svg>
              </Link>
            </Button>

            <Button
              asChild
              variant="outline"
              size="lg"
              className="rounded-lg font-medium text-base sm:text-lg px-8 py-6 border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-md transition-all duration-300 hover:border-gray-300"
            >
              <Link href="#features-section" onClick={scrollToFeaturesSection}>
                <T keyName={translationKeys.landing.hero.learnMore} />
              </Link>
            </Button>
          </div>
        </div>

        {/* Hero Image - Now on the right */}
        <div
          className={`w-full lg:w-1/2 flex justify-center items-center ${isMounted ? 'animate-fade-in-up-4' : 'opacity-0'}`}
        >
          <Image
            src="/zaply-images/hero-image-version2.png"
            alt={translationKeys.landing.hero.imageAlt}
            className="max-w-full h-auto object-contain"
            width={600}
            height={480}
            priority
            suppressHydrationWarning
          />
        </div>
      </div>
    </section>
  );
}
