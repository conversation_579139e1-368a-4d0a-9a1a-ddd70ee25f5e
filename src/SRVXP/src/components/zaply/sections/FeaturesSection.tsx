"use client";

import { Circle } from "lucide-react";
import { useScrollReveal } from "@/lib/zaply/hooks/useScrollReveal";
import { useState, useEffect } from "react";
import { T } from "@/components/t";
import { translationKeys } from "@/lib/translations";

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const FeatureCard = ({ icon, title, description }: FeatureCardProps) => {
  return (
    <div className="flex flex-col items-start">
      <div className="mb-5">
        {icon}
      </div>
      <h3 className="text-2xl font-bold mb-4">{title}</h3>
      <p className="text-gray-600 text-lg">
        {description}
      </p>
    </div>
  );
};

export function FeaturesSection() {
  const { ref, isIntersecting } = useScrollReveal({
    threshold: 0.2,
    rootMargin: "-50px"
  });
  const [isMounted, setIsMounted] = useState(false);

  // Set isMounted to true after component mounts to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <section
      id="features-section"
      className="py-16 lg:py-24 overflow-hidden"
      ref={ref}
    >
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 sm:gap-12">
        <div className={`${isMounted && isIntersecting ? 'animate-fade-in-up-1' : 'opacity-0'}`}>
          <FeatureCard
            icon={
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 2V5" stroke="#144ee0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M16 2V5" stroke="#144ee0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3 8H21" stroke="#144ee0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M19 4H5C3.89543 4 3 4.89543 3 6V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V6C21 4.89543 20.1046 4 19 4Z" stroke="#144ee0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 12H12.01" stroke="#144ee0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M16 12H16.01" stroke="#144ee0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M8 12H8.01" stroke="#144ee0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 16H12.01" stroke="#144ee0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M8 16H8.01" stroke="#144ee0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                }
                title={<T keyName={translationKeys.landing.features.sameDay.title} />}
                description={<T keyName={translationKeys.landing.features.sameDay.description} />}
          />
        </div>
        <div className={`${isMounted && isIntersecting ? 'animate-fade-in-up-2' : 'opacity-0'}`}>
          <FeatureCard
            icon={
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 10C21 17 12 23 12 23C12 23 3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.364 3.63604C20.0518 5.32387 21 7.61305 21 10Z" stroke="#144ee0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z" stroke="#144ee0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            }
            title={<T keyName={translationKeys.landing.features.nearbyClinic.title} />}
            description={<T keyName={translationKeys.landing.features.nearbyClinic.description} />}
          />
        </div>
        <div className={`${isMounted && isIntersecting ? 'animate-fade-in-up-3' : 'opacity-0'}`}>
          <FeatureCard
            icon={
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="#144ee0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M9 22V12H15V22" stroke="#144ee0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            }
            title={<T keyName={translationKeys.landing.features.anywhereInQuebec.title} />}
            description={<T keyName={translationKeys.landing.features.anywhereInQuebec.description} />}
          />
        </div>
      </div>
    </section>
  );
}
