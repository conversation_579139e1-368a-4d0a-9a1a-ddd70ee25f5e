"use client";

import Link from "next/link";
import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/zaply/ui/button";
import { ChevronDown, User, LogIn, Globe, X, Menu } from "lucide-react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { T } from "@/components/t";
import { translationKeys } from "@/lib/translations";
import { useLanguage } from "@/lib/LanguageContext";
import { useAuth } from "@/lib/AuthContext";

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const languageDropdownRef = useRef<HTMLDivElement>(null);
  const { language, setLanguage } = useLanguage();
  const { status } = useAuth();

  // Check if user is authenticated
  const isAuthenticated = status === "authenticated";

  // Map of pages that have language-specific versions
  const languageSpecificPages = {
    fr: {
      "/politique-de-confidentialite": "/politique-de-confidentialite",
      "/politique-de-confidentialite-EN": "/politique-de-confidentialite",
      "/conditions-utilisation": "/conditions-utilisation",
      "/conditions-utilisation-EN": "/conditions-utilisation",
      "/conditions-generales-de-vente": "/conditions-generales-de-vente",
      "/conditions-generales-de-vente-EN": "/conditions-generales-de-vente",
      "/foire-aux-questions": "/foire-aux-questions",
      "/foire-aux-questions-EN": "/foire-aux-questions"
    },
    en: {
      "/politique-de-confidentialite": "/politique-de-confidentialite-EN",
      "/politique-de-confidentialite-EN": "/politique-de-confidentialite-EN",
      "/conditions-utilisation": "/conditions-utilisation-EN",
      "/conditions-utilisation-EN": "/conditions-utilisation-EN",
      "/conditions-generales-de-vente": "/conditions-generales-de-vente-EN",
      "/conditions-generales-de-vente-EN": "/conditions-generales-de-vente-EN",
      "/foire-aux-questions": "/foire-aux-questions-EN",
      "/foire-aux-questions-EN": "/foire-aux-questions-EN"
    }
  };

  // Determine background color based on page type
  // White for FAQ, Privacy, Terms pages, and light gray for others
  const isWhiteBgPage = [
    "/foire-aux-questions",
    "/foire-aux-questions-EN",
    "/politique-de-confidentialite",
    "/politique-de-confidentialite-EN",
    "/conditions-utilisation",
    "/conditions-utilisation-EN",
    "/conditions-generales-de-vente",
    "/conditions-generales-de-vente-EN"
  ].includes(pathname);

  const bgColor = isWhiteBgPage ? "bg-white" : "bg-[#f8f9fb]";

  // Function to get the language code (FR/EN) based on current language
  const getLanguageCode = () => {
    return language === "fr" ? "FR" : "EN";
  };

  const setLanguageHandler = (lang: "fr" | "en") => {
    // First update the language in the context
    setLanguage(lang);
    setShowLanguageDropdown(false);

    // Check if we're on a page that has language-specific versions
    if (pathname && languageSpecificPages[lang][pathname]) {
      // Redirect to the appropriate language-specific page
      const targetPath = languageSpecificPages[lang][pathname];
      if (targetPath !== pathname) {
        router.push(targetPath);
      }
    }
  };

  // Handle clicks outside language dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        languageDropdownRef.current &&
        !languageDropdownRef.current.contains(event.target as Node) &&
        showLanguageDropdown
      ) {
        setShowLanguageDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showLanguageDropdown]);

  // Add scroll detection
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }

      // Close language dropdown when scrolling
      if (showLanguageDropdown) {
        setShowLanguageDropdown(false);
      }

      // Close mobile menu when scrolling
      if (isOpen) {
        setIsOpen(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [showLanguageDropdown, isOpen]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  return (
    <>
      <div className={`w-full ${bgColor} lg:static lg:relative lg:top-auto lg:z-auto sticky top-0 z-50 ${isScrolled ? 'shadow-md' : 'shadow-sm lg:shadow-none'} transition-shadow duration-300`}>
        <nav className="container mx-auto flex items-center justify-between py-4 px-4 md:px-6 relative">
          <div className="flex items-center">
            <Link href="/" className="flex items-center group">
              <div className="w-8 h-8 flex items-center justify-center transition-transform duration-300 group-hover:scale-110">
                <Image
                  src="/zaply-images/srvxp_logorevised.svg"
                  alt="Logo"
                  width={32}
                  height={32}
                  priority
                  suppressHydrationWarning
                  className="text-brandBlue fill-current"
                />
              </div>
              <span className="ml-4 text-lg sm:text-xl font-bold text-[#212242]">
                <T keyName={translationKeys.landing.navbar.title} />
              </span>
            </Link>
          </div>

          {/* Desktop Menu - now using lg breakpoint instead of md */}
          <div className="hidden lg:flex items-center gap-4">
            {/* Language Dropdown */}
            <div ref={languageDropdownRef} className="relative">
              <button
                onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
                className="flex items-center gap-1 font-normal text-base text-[#4d5562] hover:text-primary transition-all duration-300 hover:scale-105"
                aria-label="Toggle language"
              >
                <span>{getLanguageCode()}</span>
                <ChevronDown className={`w-4 h-4 transition-transform duration-300 ${showLanguageDropdown ? 'rotate-180' : ''}`} strokeWidth={1.5} />
              </button>

              {showLanguageDropdown && (
                <div className="absolute top-full left-0 mt-1 bg-white shadow-md rounded-md py-2 min-w-[150px]">
                  <button
                    onClick={() => setLanguageHandler("fr")}
                    className={`w-full text-left px-4 py-2 text-base ${language === "fr" ? "text-primary" : "text-[#4d5562]"} hover:bg-gray-50 transition-colors duration-200`}
                  >
                    Français
                  </button>
                  <button
                    onClick={() => setLanguageHandler("en")}
                    className={`w-full text-left px-4 py-2 text-base ${language === "en" ? "text-primary" : "text-[#4d5562]"} hover:bg-gray-50 transition-colors duration-200`}
                  >
                    English
                  </button>
                </div>
              )}
            </div>

            {/* Log In Link - Only show if not authenticated */}
            {!isAuthenticated && (
              <Link
                href="/auth/sign-in"
                className="font-normal text-base text-[#4d5562] hover:text-primary transition-all duration-300 px-4 py-2 hover:scale-105"
              >
                <T keyName={translationKeys.landing.navbar.signIn} />
              </Link>
            )}

            <Button asChild size="lg" className="rounded-lg font-medium text-base h-auto py-2 px-4 transition-transform duration-300 hover:scale-105 shadow-sm hover:shadow">
              {isAuthenticated ? (
                <Link href="/dashboard">
                  <span className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    <span>{language === "fr" ? "Mon compte" : "My account"}</span>
                  </span>
                </Link>
              ) : (
                <Link href="/auth/sign-up">
                  <T keyName={translationKeys.landing.navbar.signUp} />
                </Link>
              )}
            </Button>
          </div>

          {/* Mobile menu button - show on lg screens and below */}
          <div className="lg:hidden flex items-center">
            <button
              className="flex items-center justify-center w-10 h-10 text-[#212244] hover:bg-gray-100 rounded-full transition-all duration-300"
              onClick={() => setIsOpen(!isOpen)}
              aria-label="Toggle menu"
            >
              {isOpen ? (
                <X size={24} className="text-[#212244] transition-transform duration-300" />
              ) : (
                <Menu className="h-5 w-5 text-[#212244] transition-transform duration-300" />
              )}
            </button>
          </div>
        </nav>
      </div>

      {/* Mobile Menu - Modified to take up 3/4 of the screen */}
      {isOpen && (
        <div className="fixed inset-0 z-50 lg:hidden overflow-hidden">
          {/* Top 3/4 content area */}
          <div className={`${isWhiteBgPage ? 'bg-white' : 'bg-[#f8f9fb]'} h-[75vh] overflow-auto`}>
            <div className="container mx-auto px-4 pt-4 pb-8 h-full flex flex-col">
              {/* Header with logo and close button */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-8 h-8 flex items-center justify-center">
                    <Image
                      src="/zaply-images/srvxp_logorevised.svg"
                      alt="Logo"
                      width={32}
                      height={32}
                      className="text-brandBlue fill-current"
                    />
                  </div>
                  <span className="ml-4 text-lg sm:text-xl font-bold text-[#212242]">
                    <T keyName={translationKeys.landing.navbar.title} />
                  </span>
                </div>
                <button
                  onClick={() => setIsOpen(false)}
                  className="flex items-center justify-center w-10 h-10"
                  aria-label="Close menu"
                >
                  <X size={24} className="text-[#212244]" />
                </button>
              </div>

              {/* Menu Items - Matching the design in screenshot */}
              <div className="mt-10 flex-grow">
                <div className="space-y-8">
                  <Link
                    href="#FeaturesSection"
                    className="block text-xl font-normal text-[#212242] hover:text-brandBlue transition-colors duration-200 font-sans"
                    onClick={() => setIsOpen(false)}
                  >
                    <T keyName={translationKeys.landing.navbar.service} />
                  </Link>
                  <Link
                    href="#tarifs"
                    className="block text-xl font-normal text-[#212242] hover:text-brandBlue transition-colors duration-200 font-sans"
                    onClick={() => setIsOpen(false)}
                  >
                    <T keyName={translationKeys.landing.navbar.pricing} />
                  </Link>
                  <Link
                    href="/foire-aux-questions"
                    className="block text-xl font-normal text-[#212242] hover:text-brandBlue transition-colors duration-200 font-sans"
                    onClick={() => setIsOpen(false)}
                  >
                    <T keyName={translationKeys.landing.navbar.faq} />
                  </Link>
                  <button
                    className="block text-xl font-normal text-[#212242] hover:text-brandBlue transition-colors duration-200 text-left font-sans"
                    onClick={() => {
                      const newLanguage = language === "fr" ? "en" : "fr";
                      setLanguageHandler(newLanguage);
                      setIsOpen(false);
                    }}
                  >
                    {language === "fr" ? "English" : "Français"}
                  </button>
                </div>
              </div>

              {/* Spacer div to push content to the bottom */}
              <div className="flex-grow"></div>

              {/* Thicker, more visible divider line */}
              <hr className="border-gray-300 border-t-[1px] w-full my-6" />

              {/* Bottom Buttons - Login and Signup */}
              <div className="space-y-4 mb-6">
                {/* Login Button - Only show if not authenticated */}
                {!isAuthenticated && (
                  <Link
                    href="/auth/sign-in"
                    className="w-full flex items-center justify-center gap-2 py-3 px-4 border border-gray-200 rounded-lg bg-white text-[#212244] hover:bg-gray-50 transition-all duration-200 font-sans"
                    onClick={() => setIsOpen(false)}
                  >
                    <LogIn className="w-5 h-5" strokeWidth={1.5} />
                    <span className="text-lg"><T keyName={translationKeys.landing.navbar.signIn} /></span>
                  </Link>
                )}

                {/* Signup Button or My Account Button */}
                <Link
                  href={isAuthenticated ? "/dashboard" : "/auth/sign-up"}
                  className="w-full flex items-center justify-center gap-2 py-3 px-4 rounded-lg bg-brandBlue text-white transition-all duration-200 hover:bg-brandBlue/90 font-sans"
                  onClick={() => setIsOpen(false)}
                >
                  <User className="w-5 h-5" strokeWidth={1.5} />
                  <span className="text-lg">
                    {isAuthenticated
                      ? (language === "fr" ? "Mon compte" : "My account")
                      : <T keyName={translationKeys.landing.navbar.signUp} />
                    }
                  </span>
                </Link>
              </div>
            </div>
          </div>

          {/* Bottom 1/4 semi-transparent overlay */}
          <div
            className="h-[25vh] bg-black/50 backdrop-blur-sm cursor-pointer"
            onClick={() => setIsOpen(false)}
            aria-label="Close menu"
          />
        </div>
      )}
    </>
  );
}
