"use client";

import { AnimatedBackground } from '@/components/zaply/ui/animated-background';
import { useLanguage } from "@/lib/LanguageContext";
import { translationKeys } from "@/lib/translations";

interface PriceToggleProps {
  period: "monthly" | "annual";
  onChange: (period: "monthly" | "annual") => void;
}

export function PriceToggle({ period, onChange }: PriceToggleProps) {
  const { language } = useLanguage();
  // Reduced toggle width to 2/3 the original
  const TOGGLE_WIDTH = 261; // px (392 * 2/3 = 261.33)

  // Map between toggle labels and period values
  const monthly = language === 'fr' ? 'Mensuel' : 'Monthly';
  const annual = language === 'fr' ? 'Annuel -30%' : 'Yearly -30%';
  
  const labelToPeriod = {
    [monthly]: 'monthly',
    [annual]: 'annual'
  } as const;

  // Handle toggle value change
  const handleValueChange = (newActiveId: string | null) => {
    if (newActiveId && (newActiveId === monthly || newActiveId === annual)) {
      onChange(labelToPeriod[newActiveId]);
    }
  };

  return (
    <div className='relative rounded-lg bg-gray-100 p-1 flex' style={{ width: `${TOGGLE_WIDTH}px` }}>
      <AnimatedBackground
        defaultValue={period === 'monthly' ? monthly : annual}
        className='rounded-lg bg-white border border-gray-100 shadow-sm'
        transition={{
          ease: 'easeInOut',
          duration: 0.2,
        }}
        onValueChange={handleValueChange}
        textColorActive="#4B5563" // Dark gray for active text
        textColorInactive="#9CA3AF" // Light gray for inactive text
      >
        {([monthly, annual] as const).map((label, index) => {
          const value = labelToPeriod[label];
          const isActive = period === value;
          // Set each button to be half of total width
          const buttonWidth = TOGGLE_WIDTH / 2 - 4; // Subtract for the padding

          return (
            <button
              key={index}
              data-id={label}
              type='button'
              aria-label={`${label} pricing`}
              aria-pressed={isActive}
              className='inline-flex items-center justify-center text-center px-2 py-2 text-sm font-medium transition-transform active:scale-[0.98] rounded-lg'
              style={{
                width: `${buttonWidth}px`,
              }}
            >
              {label === annual ? (
                <>
                  <span>{language === 'fr' ? 'Annuel' : 'Yearly'}</span>
                  <span style={{
                    color: isActive ? '#16a349' : '#9CA3AF'
                  }}>
                    {' -30%'}
                  </span>
                </>
              ) : (
                label
              )}
            </button>
          );
        })}
      </AnimatedBackground>
    </div>
  );
}
