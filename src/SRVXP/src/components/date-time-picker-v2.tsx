"use client";

import { useEffect, useState } from "react";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { DayPicker } from "react-day-picker";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";

interface DateTimePickerV2Props {
  date: Date | undefined;
  onSelect: (date: Date | undefined) => void;
  locale?: any;
  fromYear?: number;
  toYear?: number;
}

export function DateTimePickerV2({
  date,
  onSelect,
  locale,
  fromYear = 1900,
  toYear = new Date().getFullYear()
}: DateTimePickerV2Props) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(date);
  const [currentMonth, setCurrentMonth] = useState<Date>(selectedDate || new Date());

  // Handler for month dropdown change
  const handleMonthChange = (month: string) => {
    const newDate = new Date(currentMonth);
    const monthIndex = new Date(Date.parse(`${month} 1, 2000`)).getMonth();
    newDate.setMonth(monthIndex);
    setCurrentMonth(newDate);
  };

  // Handler for year dropdown change
  const handleYearChange = (year: string) => {
    const newDate = new Date(currentMonth);
    newDate.setFullYear(parseInt(year));
    setCurrentMonth(newDate);
  };

  // Update current month when selected date changes
  useEffect(() => {
    if (selectedDate) {
      setCurrentMonth(selectedDate);
    }
  }, [selectedDate]);

  // Custom day picker component to replace the Calendar component
  const CustomDayPicker = () => {
    return (
      <DayPicker
        mode="single"
        selected={selectedDate}
        onSelect={(newDate) => {
          setSelectedDate(newDate || undefined);
          onSelect(newDate || undefined);
          if (newDate) setIsOpen(false);
        }}
        month={currentMonth}
        fromYear={fromYear}
        toYear={toYear}
        locale={locale}
        showOutsideDays
        className="custom-calendar"
        classNames={{
          months: "flex flex-col",
          month: "space-y-4",
          caption: "hidden",
          caption_label: "hidden",
          caption_dropdowns: "hidden",
          nav: "hidden",
          table: "w-full border-collapse",
          head_row: "flex",
          head_cell: "text-muted-foreground rounded-md w-9 font-medium text-[0.8rem] flex items-center justify-center h-9",
          row: "flex w-full mt-0",
          cell: "relative p-0 text-center text-sm focus-within:relative focus-within:z-20",
          day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground rounded-md",
          day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground rounded-md",
          day_today: "bg-accent text-accent-foreground rounded-md",
          day_outside: "text-muted-foreground opacity-50",
          day_disabled: "text-muted-foreground opacity-50",
          day_hidden: "invisible",
        }}
      />
    );
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "w-full justify-start text-left font-normal",
            !selectedDate && "text-muted-foreground"
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {selectedDate ? (
            format(selectedDate, 'PPP', { locale })
          ) : (
            <span>Sélectionner une date</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="p-4 pb-3">
          {/* Month and Year dropdowns */}
          <div className="flex gap-2 justify-between mb-4">
            <div className="relative flex-1">
              <Select
                value={format(currentMonth, 'MMMM')}
                onValueChange={handleMonthChange}
              >
                <SelectTrigger className="w-full h-10 pl-3 rounded-md text-left font-normal">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <ScrollArea className="h-60">
                    {Array.from({ length: 12 }).map((_, i) => {
                      const monthDate = new Date();
                      monthDate.setMonth(i);
                      return (
                        <SelectItem key={i} value={format(monthDate, 'MMMM')}>
                          {format(monthDate, 'MMMM', { locale })}
                        </SelectItem>
                      );
                    })}
                  </ScrollArea>
                </SelectContent>
              </Select>
            </div>
            <div className="relative flex-1">
              <Select
                value={currentMonth.getFullYear().toString()}
                onValueChange={handleYearChange}
              >
                <SelectTrigger className="w-full h-10 pl-3 rounded-md text-left font-normal">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <ScrollArea className="h-60">
                    {Array.from({ length: toYear - fromYear + 1 }).map((_, i) => {
                      const year = toYear - i;
                      return (
                        <SelectItem key={i} value={year.toString()}>
                          {year}
                        </SelectItem>
                      );
                    })}
                  </ScrollArea>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Custom day picker component */}
          <CustomDayPicker />
        </div>
      </PopoverContent>
    </Popover>
  );
}
