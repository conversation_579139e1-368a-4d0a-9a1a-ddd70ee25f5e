"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/AuthContext";
import { createClient } from "@/lib/supabase/client";

export type SubscriptionPlanType = 'individual' | 'family' | null;
export type SubscriptionBillingPeriod = 'monthly' | 'annual' | null;

export interface SubscriptionDetails {
  hasSubscription: boolean;
  planType: SubscriptionPlanType;
  billingPeriod: SubscriptionBillingPeriod;
}

export function useSubscription() {
  const { user, status } = useAuth();
  const [subscriptionDetails, setSubscriptionDetails] = useState<SubscriptionDetails>({
    hasSubscription: false,
    planType: null,
    billingPeriod: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function checkSubscription() {
      if (status === "loading") {
        return;
      }

      if (status === "unauthenticated" || !user) {
        setSubscriptionDetails({
          hasSubscription: false,
          planType: null,
          billingPeriod: null
        });
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClient();

        // Check if user has an active subscription
        const { data: subscription, error: subscriptionError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .eq('status', 'active')
          .in('plan_type', ['individual', 'family'])
          .maybeSingle();

        if (subscriptionError) {
          throw subscriptionError;
        }

        if (subscription) {
          setSubscriptionDetails({
            hasSubscription: true,
            planType: subscription.plan_type as SubscriptionPlanType,
            billingPeriod: subscription.billing_period as SubscriptionBillingPeriod || 'monthly' // Default to monthly if not specified
          });
        } else {
          setSubscriptionDetails({
            hasSubscription: false,
            planType: null,
            billingPeriod: null
          });
        }
      } catch (err) {
        console.error("Error checking subscription:", err);
        setError(err instanceof Error ? err : new Error('Unknown error'));
        setSubscriptionDetails({
          hasSubscription: false,
          planType: null,
          billingPeriod: null
        });
      } finally {
        setIsLoading(false);
      }
    }

    checkSubscription();
  }, [user, status]);

  return {
    ...subscriptionDetails,
    isLoading,
    error
  };
}
