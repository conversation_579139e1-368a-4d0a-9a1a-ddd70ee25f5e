"use client";

import { useEffect } from "react";
import { useTheme } from "next-themes";
import { useAuth } from "@/lib/AuthContext";
import { getUserPreferences } from "@/lib/user-preferences-utils";

/**
 * A hook that synchronizes the theme with user preferences
 * and handles initial theme loading on app start
 */
export function useThemeSync() {
  const { theme, setTheme } = useTheme();
  const { user, status } = useAuth();

  // On initial load, try to restore theme from user preferences or localStorage
  useEffect(() => {
    const initializeTheme = async () => {
      // Wait for auth to be ready
      if (status !== "authenticated" || !user) {
        return;
      }

      try {
        // Get theme from user preferences in Supabase
        const preferences = await getUserPreferences(user.id);
        
        if (preferences?.theme) {
          console.log(`Setting theme from user preferences: ${preferences.theme}`);
          setTheme(preferences.theme);
          return;
        }
        
        // If no preferences exist in Supabase, use light theme by default
        console.log(`No theme preferences found, setting default theme: light`);
        setTheme('light');
      } catch (error) {
        console.error("Error initializing theme:", error);
      }
    };

    initializeTheme();
  }, [user, status, setTheme]);

  // No longer need to keep localStorage in sync with the current theme
  // Theme is exclusively stored in Supabase now

  return { theme };
}
