/**
 * Profile Store Integration
 * 
 * This file provides integration between the Zustand user profile store and the existing profile hooks.
 * It allows for a gradual migration from hook-based profile management to store-based profile management.
 */

'use client'

import React, { useEffect, createContext, useContext, ReactNode } from 'react'
import { useUserProfileStore, UserProfile, UserUpdateData } from '@/stores/useUserProfileStore'
import { useUserStore } from '@/stores/useUserStore'

// Create a context for the integration
interface ProfileIntegrationContextValue {
  isStoreEnabled: boolean
  enableStore: () => void
  disableStore: () => void
}

const ProfileIntegrationContext = createContext<ProfileIntegrationContextValue>({
  isStoreEnabled: false,
  enableStore: () => {},
  disableStore: () => {}
})

export const useProfileIntegration = () => useContext(ProfileIntegrationContext)

interface ProfileIntegrationProviderProps {
  children: ReactNode
  initialEnabled?: boolean
}

/**
 * Provider component that manages the integration between profile hooks and UserProfileStore
 */
export function ProfileIntegrationProvider({ 
  children, 
  initialEnabled = false 
}: ProfileIntegrationProviderProps) {
  const [isStoreEnabled, setIsStoreEnabled] = React.useState(initialEnabled)
  
  const enableStore = () => setIsStoreEnabled(true)
  const disableStore = () => setIsStoreEnabled(false)
  
  return (
    <ProfileIntegrationContext.Provider value={{ isStoreEnabled, enableStore, disableStore }}>
      {children}
    </ProfileIntegrationContext.Provider>
  )
}

/**
 * Hook that provides the same interface as useUserProfile but uses the Zustand store
 * This allows for a gradual migration from hooks to store
 */
export function useProfileFromStore() {
  const {
    profile,
    isLoading,
    error,
    isSaving,
    saveError,
    saveSuccess,
    fetchProfile,
    updateProfile,
    firstName,
    lastName,
    email,
    phone,
    initials,
    fullName,
    userId
  } = useUserProfileStore.use.useUserProfile()
  
  // Initial profile fetch
  useEffect(() => {
    if (userId) {
      fetchProfile(userId)
    }
  }, [userId, fetchProfile])
  
  return {
    profile,
    isLoading,
    error,
    isSaving,
    saveError,
    saveSuccess,
    firstName,
    lastName,
    email,
    phone,
    initials,
    fullName,
    // Functions
    invalidateCache: (userId: string) => {
      if (userId) {
        fetchProfile(userId)
      }
    },
    reloadProfile: fetchProfile,
    updateProfile
  }
}

/**
 * Component that synchronizes the profile hooks with the UserProfileStore
 * This ensures that both state management systems have the same data
 */
export function ProfileStoreSynchronizer() {
  const { isStoreEnabled } = useProfileIntegration()
  
  // Import the original useUserProfile hook
  const { useUserProfile } = require('@/hooks/use-user-profile')
  const { 
    profile: hookProfile,
    firstName: hookFirstName,
    lastName: hookLastName,
    phone: hookPhone
  } = useUserProfile()
  
  // Get store actions
  const setProfile = useUserProfileStore(state => state.setProfile)
  const userId = useUserStore(state => state.user?.id)
  
  // Sync hook data to store if store is enabled
  useEffect(() => {
    if (isStoreEnabled && hookProfile && userId) {
      setProfile({
        firstName: hookFirstName,
        lastName: hookLastName,
        email: hookProfile.email || '',
        phone: hookPhone,
        avatar: hookProfile.avatar,
        initials: hookProfile.initials,
        fullName: hookProfile.fullName || `${hookFirstName} ${hookLastName}`.trim()
      })
    }
  }, [isStoreEnabled, hookProfile, hookFirstName, hookLastName, hookPhone, setProfile, userId])
  
  return null
}

/**
 * HOC that wraps a component to provide profile data from either hooks or store
 * based on the integration settings
 */
export function withProfile<P extends object>(Component: React.ComponentType<P & { 
  profile: UserProfile | null
  isLoading: boolean
  error: Error | null
  firstName: string
  lastName: string
  fullName: string
  initials: string
  phone: string
  invalidateCache: (userId: string, email: string, reload?: boolean) => void
  reloadProfile: (userId: string) => Promise<any>
}>) {
  return function WithProfileComponent(props: P) {
    const { isStoreEnabled } = useProfileIntegration()
    
    // Use either store or hooks based on settings
    const storeProfile = useProfileFromStore()
    const { useUserProfile } = require('@/hooks/use-user-profile')
    const hookProfile = useUserProfile()
    
    const profile = isStoreEnabled ? storeProfile : hookProfile
    
    return <Component {...props} {...profile} />
  }
}
