"use client";

import { supabase } from "./supabase/client";
import { AuthSession } from "@supabase/supabase-js";

interface UserUpdateData {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
}

/**
 * Updates the user's profile information in Supabase
 * - Updates user metadata for first name, last name, phone
 * - Updates email in auth.users if changed
 * - Syncs data with the profiles table
 * 
 * @param userId The user's ID
 * @param data Profile data to update
 * @param session Current auth session (required for email updates)
 * @returns Promise with result of the update operation
 */
export async function updateUserProfile(
  userId: string,
  data: UserUpdateData,
  session?: AuthSession | null
) {
  // Validate required session for email updates
  if (data.email && data.email !== session?.user?.email && !session) {
    throw new Error("Auth session is required to update email")
  }
  try {
    const updates = [];
    
    // 1. Update user metadata (first name, last name, phone)
    const metadataToUpdate: Record<string, any> = {};
    let hasMetadataUpdates = false;
    
    if (data.firstName !== undefined) {
      metadataToUpdate.first_name = data.firstName;
      hasMetadataUpdates = true;
    }
    
    if (data.lastName !== undefined) {
      metadataToUpdate.last_name = data.lastName;
      hasMetadataUpdates = true;
    }
    
    if (data.phone !== undefined) {
      metadataToUpdate.phone = data.phone;
      hasMetadataUpdates = true;
    }
    
    if (hasMetadataUpdates) {
      // Update user metadata
      const { data: authUpdateData, error: metadataError } = await supabase.auth.updateUser({
        data: metadataToUpdate,
      });
      
      if (metadataError) {
        throw new Error(`Error updating user metadata: ${metadataError.message}`);
      }
      
      updates.push("metadata");
    }
    
    // 2. Update email if provided and different from current email
    if (data.email !== undefined && session?.user?.email !== data.email) {
      const { data: emailUpdateData, error: emailError } = await supabase.auth.updateUser({
        email: data.email,
      });
      
      if (emailError) {
        throw new Error(`Error updating email: ${emailError.message}`);
      }
      
      updates.push("email");
    }
    
    // No need to update profiles table anymore since it's been removed
    
    // If no updates happened, consider it a success (no changes needed)
    return {
      success: updates.length > 0 || Object.keys(data).length === 0,
      updates: updates,
    };
  } catch (error) {
    console.error("Error updating user profile:", error);
    throw error;
  }
}
// export async function updateUserProfile(
//   userId: string,
//   data: UserUpdateData,
//   session?: AuthSession | null
// ) {
//   // Validate required session for email updates
//   if (data.email && data.email !== session?.user?.email && !session) {
//     throw new Error("Auth session is required to update email")
//   }
//   try {
//     const updates = [];
    
//     // 1. Update user metadata (first name, last name, phone)
//     const metadataToUpdate: Record<string, any> = {};
//     let hasMetadataUpdates = false;
    
//     if (data.firstName !== undefined) {
//       metadataToUpdate.first_name = data.firstName;
//       hasMetadataUpdates = true;
//     }
    
//     if (data.lastName !== undefined) {
//       metadataToUpdate.last_name = data.lastName;
//       hasMetadataUpdates = true;
//     }
    
//     if (data.phone !== undefined) {
//       metadataToUpdate.phone = data.phone;
//       hasMetadataUpdates = true;
//     }
    
//     if (hasMetadataUpdates) {
//       // Update user metadata
//       const { data: authUpdateData, error: metadataError } = await supabase.auth.updateUser({
//         data: metadataToUpdate,
//       });
      
//       if (metadataError) {
//         throw new Error(`Error updating user metadata: ${metadataError.message}`);
//       }
      
//       updates.push("metadata");
//     }
    
//     // 2. Update email if provided and different from current email
//     if (data.email !== undefined && session?.user?.email !== data.email) {
//       const { data: emailUpdateData, error: emailError } = await supabase.auth.updateUser({
//         email: data.email,
//       });
      
//       if (emailError) {
//         throw new Error(`Error updating email: ${emailError.message}`);
//       }
      
//       updates.push("email");
//     }
    
//     // 3. Update any additional profile fields in profiles table
//     // Check if we have a profiles table and update any additional fields there
//     const profileData: Record<string, any> = {};
//     let hasProfileUpdates = false;
    
//     if (data.firstName !== undefined) {
//       profileData.first_name = data.firstName;
//       hasProfileUpdates = true;
//     }
    
//     if (data.lastName !== undefined) {
//       profileData.last_name = data.lastName;
//       hasProfileUpdates = true;
//     }
    
//     if (data.phone !== undefined) {
//       profileData.phone_number = data.phone;
//       hasProfileUpdates = true;
//     }
    
//     if (hasProfileUpdates) {
//       // Add updated_at timestamp to track when profile was last modified
//       profileData.updated_at = new Date().toISOString();
      
//       try {
//         // Check if the profiles table exists and has the user's record
//         const { data: profileExists, error: checkError } = await supabase
//           .from('profiles')
//           .select('id')
//           .eq('id', userId)
//           .maybeSingle();
        
//         if (checkError && !checkError.message.includes('does not exist')) {
//           console.warn(`Error checking profiles table: ${checkError.message}`);
//         }
        
//         if (profileExists) {
//           // Update existing profile record
//           const { error: updateError } = await supabase
//             .from("profiles")
//             .update(profileData)
//             .eq("id", userId);
          
//           if (updateError) {
//             console.warn(`Error updating profiles table: ${updateError.message}`);
//           } else {
//             updates.push("profile");
//           }
//         } else {
//           // Try to create profile record if it doesn't exist
//           try {
//             // First check if the profiles table exists but user doesn't have a record
//             const { error: tableExistsError } = await supabase
//               .from('profiles')
//               .select('id')
//               .limit(1);
            
//             if (!tableExistsError) {
//               // Table exists, insert new profile
//               const { error: insertError } = await supabase
//                 .from("profiles")
//                 .insert({
//                   id: userId,
//                   ...profileData
//                 });
              
//               if (insertError) {
//                 console.warn(`Error inserting into profiles table: ${insertError.message}`);
//               } else {
//                 updates.push("profile_created");
//               }
//             }
//           } catch (err) {
//             // Silently catch errors here as the profiles table may not exist
//             console.warn("Error working with profiles table, might not exist:", err);
//           }
//         }
//       } catch (profileError) {
//         // Log but don't fail the entire update if profile table operations fail
//         console.warn("Error updating profile table:", profileError);
//       }
//     }
    
//     // If no updates happened, consider it a success (no changes needed)
//     return {
//       success: updates.length > 0 || Object.keys(data).length === 0,
//       updates: updates,
//     };
//   } catch (error) {
//     console.error("Error updating user profile:", error);
//     throw error;
//   }
// }

/**
 * Gets the current full user profile by combining auth data and profiles table
 * 
 * @param userId The user's ID
 * @returns Promise with the complete user profile
 */
export async function getUserProfile(userId: string) {
  try {
    // Get user data from auth.users via admin functions
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      throw userError;
    }
    
    // Get data from public.users table instead of profiles
    let publicUserData = null;
    try {
      const { data, error: publicUserError } = await supabase
        .from("users")
        .select("*")
        .eq("id", userId)
        .single();
      
      if (!publicUserError) {
        publicUserData = data;
      }
    } catch (err) {
      console.warn("Error getting public user data:", err);
    }
    
    // Combine the data
    const userMetadata = userData.user.user_metadata || {};
    
    // Parse name fields - first try from metadata
    let firstName = userMetadata.first_name || "";
    let lastName = userMetadata.last_name || "";
    
    // Use first_name and last_name from public.users if not in metadata
    if (!firstName) firstName = publicUserData?.first_name || "";
    if (!lastName) lastName = publicUserData?.last_name || "";
    
    // Create complete user profile
    const completeProfile = {
      id: userId,
      email: userData.user.email,
      firstName: firstName,
      lastName: lastName,
      phone: userMetadata.phone || "",
      updatedAt: publicUserData?.updated_at || new Date().toISOString(),
      // Include other fields from public user data that might be useful
      ...(publicUserData || {})
    };
    
    return completeProfile;
  } catch (error) {
    console.error("Error getting user profile:", error);
    throw error;
  }
}