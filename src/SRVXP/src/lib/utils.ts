import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Utility for parsing and validating health card inputs
export function formatHealthCardNumber(input: string, maxDigits = 8): string {
  // Remove all non-numeric characters
  const digits = input.replace(/\D/g, "").substring(0, maxDigits);

  // Format as xxxx-xxxx if we have more than 4 digits
  if (digits.length <= 4) {
    return digits;
  } else {
    return `${digits.slice(0, 4)}-${digits.slice(4)}`;
  }
}

// Check if health card number is complete (has the required number of digits)
export function isHealthCardComplete(input: string, requiredDigits = 8): boolean {
  const digits = input.replace(/\D/g, "");
  return digits.length === requiredDigits;
}
