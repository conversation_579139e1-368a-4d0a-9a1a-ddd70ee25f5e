"use client"

import { supabase } from "../supabase/client"

export interface AppointmentRequestData {
  request_type: string
  patient_id?: string | null
  request_details: {
    postalCode: string
    date: string
    time: string
    specialty?: string
    healthCardLastDigits?: string
    cardSequenceNumber?: string
    [key: string]: any
  }
}

/**
 * Create a new appointment request
 * @param data The appointment request data
 * @returns Promise with the result
 */
export async function createAppointmentRequest(
  data: AppointmentRequestData
): Promise<{ success: boolean; data?: any; error?: string; errorType?: string }> {
  try {
    const response = await fetch('/api/appointment-requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    const result = await response.json()

    if (!response.ok) {
      return {
        success: false,
        error: result.error || 'Failed to create appointment request',
        errorType: result.errorType
      }
    }

    return { success: true, data: result.data }
  } catch (error) {
    console.error('Error creating appointment request:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Get appointment requests for the current user
 * @param status Optional status filter
 * @param limit Maximum number of requests to return
 * @returns Promise with the result
 */
export async function getAppointmentRequests(
  status?: string,
  limit: number = 10
): Promise<{ success: boolean; data?: any[]; error?: string }> {
  try {
    // Build URL with query parameters
    const url = new URL('/api/appointment-requests', window.location.origin)
    if (status) url.searchParams.append('status', status)
    url.searchParams.append('limit', limit.toString())

    const response = await fetch(url.toString())
    const result = await response.json()

    if (!response.ok) {
      return {
        success: false,
        error: result.error || 'Failed to fetch appointment requests'
      }
    }

    return { success: true, data: result.data }
  } catch (error) {
    console.error('Error fetching appointment requests:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Cancel an appointment request
 * @param requestId The appointment request ID
 * @param reason Optional cancellation reason
 * @returns Promise with the result
 */
export async function cancelAppointmentRequest(
  requestId: string,
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // For now, we'll use the Supabase client directly
    // In a real app, you might want to create an API endpoint for this
    const { error } = await supabase
      .from('appointment_requests')
      .update({ status: 'cancelled' })
      .eq('id', requestId)

    if (error) throw error

    // If you have a cancelled_appointments table as shown in the migrations
    // you might want to insert a record there as well
    if (reason) {
      await supabase
        .from('cancelled_appointments')
        .insert({
          appointment_request_id: requestId,
          cancellation_reason: reason,
          cancelled_by: 'user'
        })
    }

    return { success: true }
  } catch (error) {
    console.error('Error cancelling appointment request:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
