import { supabase } from './supabase/client'

/**
 * Clears all authentication-related flags and tokens
 */
export const clearAuthFlags = () => {
  try {
    // Clear logout flag
    sessionStorage.removeItem('isLoggedOut')

    // Check for and clear any other auth-related flags
    const keysToRemove = []
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i)
      if (key && (key.includes('auth') || key.includes('logout') || key.includes('supabase'))) {
        keysToRemove.push(key)
      }
    }

    // Remove gathered keys
    keysToRemove.forEach(key => sessionStorage.removeItem(key))

    console.log('Auth flags cleared successfully')
  } catch (error) {
    console.error('Error clearing auth flags:', error)
  }
}

/**
 * Checks if the user has a valid session
 * @returns Promise<boolean> - true if user has valid session, false otherwise
 */
export const hasValidSession = async (): Promise<boolean> => {
  try {
    const { data: { session } } = await supabase.auth.getSession()
    return !!session
  } catch (error) {
    console.error('Error checking session:', error)
    return false
  }
}

/**
 * Complete sign out - clears all auth state and redirects
 * @param redirectUrl - URL to redirect to after logout
 */
export const signOutCompletely = async (redirectUrl: string = '/') => {
  try {
    // First sign out with Supabase
    await supabase.auth.signOut()

    // Set logout flag (will be cleared on next login)
    sessionStorage.setItem('isLoggedOut', 'true')

    // Clear any other auth tokens
    localStorage.removeItem('supabase.auth.token')

    // Force reload to the redirect URL
    window.location.href = redirectUrl
  } catch (error) {
    console.error('Error during complete sign out:', error)
    // Even if there was an error, try to redirect
    window.location.href = redirectUrl
  }
}
