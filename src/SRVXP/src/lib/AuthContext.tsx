"use client";

import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode, useRef } from "react";
import { Session, User } from "@supabase/supabase-js";
import { supabase } from "./supabase/client";

// Auth state types
export type AuthStatus = "loading" | "authenticated" | "unauthenticated";

interface AuthContextValue {
  user: User | null;
  session: Session | null;
  status: AuthStatus;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: Error }>;
  signInWithGoogle: () => Promise<{ success: boolean; error?: Error }>;
  signOut: (redirectUrl?: string) => Promise<void>;
  refresh: () => Promise<void>;
}

// Create context with default values
const AuthContext = createContext<AuthContextValue>({
  user: null,
  session: null,
  status: "loading",
  signIn: async () => ({ success: false }),
  signInWithGoogle: async () => ({ success: false }),
  signOut: async () => {},
  refresh: async () => {},
});

// Custom hook to use auth context
export const useAuth = () => useContext(AuthContext);

// Cache keys
const AUTH_CACHE_KEY = "auth_cache";
const AUTH_CACHE_EXPIRY_KEY = "auth_cache_expiry";
const SESSION_BACKUP_KEY = "session_backup";
const EXTERNAL_RETURN_KEY = "external_return_detected";
const CACHE_DURATION = 30 * 60 * 1000; // Increased to 30 minutes

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [status, setStatus] = useState<AuthStatus>("loading");
  
  // Rate limiting for refresh function
  const lastRefreshTime = useRef(0);
  const REFRESH_DEBOUNCE_MS = 3000; // 3 seconds between refreshes

  // Function to detect if user returned from external site
  const detectExternalReturn = useCallback(() => {
    if (typeof window === "undefined") return false;

    // Check if we have a session backup but no current session
    const hasSessionBackup = localStorage.getItem(SESSION_BACKUP_KEY);
    const hasCurrentSession = localStorage.getItem(AUTH_CACHE_KEY);

    // Check if URL contains return parameters from Stripe or other external services
    const urlParams = new URLSearchParams(window.location.search);
    const isStripeReturn = urlParams.has('session_id') ||
                          window.location.pathname.includes('/compte/abonnement') ||
                          document.referrer.includes('stripe.com');

    // Check if user was marked as returning from external site
    const wasExternal = sessionStorage.getItem(EXTERNAL_RETURN_KEY);

    // Check for OAuth success flag (indicates recent OAuth login)
    const hasOAuthSuccess = document.cookie.includes('oauth_success=true');
    const hasOAuthNewUser = document.cookie.includes('oauth_new_user=true');

    return !!(hasSessionBackup && (!hasCurrentSession || isStripeReturn || wasExternal || hasOAuthSuccess || hasOAuthNewUser));
  }, []);

  // Function to backup session before external navigation
  const backupSession = useCallback((session: Session) => {
    if (typeof window === "undefined") return;
    
    localStorage.setItem(SESSION_BACKUP_KEY, JSON.stringify({
      session,
      timestamp: Date.now(),
      user: session.user
    }));
  }, []);

  // Function to restore session from backup
  const restoreSessionFromBackup = useCallback(async () => {
    if (typeof window === "undefined") return null;
    
    const backup = localStorage.getItem(SESSION_BACKUP_KEY);
    if (!backup) return null;
    
    try {
      const { session: backedUpSession, timestamp } = JSON.parse(backup);
      
      // Check if backup is not too old (within 2 hours)
      if (Date.now() - timestamp > 2 * 60 * 60 * 1000) {
        localStorage.removeItem(SESSION_BACKUP_KEY);
        return null;
      }
      
      // Try to refresh the session using the backed up access token
      if (backedUpSession?.access_token) {
        console.log('Attempting to restore session from backup...');
        
        // Set the session manually to trigger Supabase to use it
        const { data, error } = await supabase.auth.setSession({
          access_token: backedUpSession.access_token,
          refresh_token: backedUpSession.refresh_token
        });
        
        if (!error && data.session) {
          console.log('Successfully restored session from backup');
          localStorage.removeItem(SESSION_BACKUP_KEY);
          return data.session;
        }
      }
    } catch (error) {
      console.error('Error restoring session from backup:', error);
      localStorage.removeItem(SESSION_BACKUP_KEY);
    }
    
    return null;
  }, []);

  // Enhanced refresh function with rate limiting and external return detection
  const refresh = useCallback(async () => {
    try {
      // Check if this is a new OAuth user first
      const isNewOAuthUser = typeof window !== "undefined" &&
                            document.cookie.includes('oauth_new_user=true');

      // Rate limiting - prevent too frequent refreshes (but allow for new OAuth users)
      const now = Date.now();
      if (!isNewOAuthUser && now - lastRefreshTime.current < REFRESH_DEBOUNCE_MS) {
        console.log('Auth refresh rate limited, skipping');
        return;
      }
      lastRefreshTime.current = now;

      console.log(`AuthContext: Starting auth refresh... ${isNewOAuthUser ? '(new OAuth user)' : ''}`);
      setStatus("loading");

      // For new OAuth users, try multiple attempts with delays
      let sessionAttempts = isNewOAuthUser ? 3 : 1;
      let sessionData = null;
      let sessionError = null;

      for (let attempt = 1; attempt <= sessionAttempts; attempt++) {
        console.log(`AuthContext: Session check attempt ${attempt}/${sessionAttempts}`);

        const { data, error } = await supabase.auth.getSession();

        if (error) {
          sessionError = error;
          console.error(`AuthContext: Session error (attempt ${attempt}):`, error);
        } else if (data?.session) {
          sessionData = data;
          sessionError = null;
          break;
        }

        if (attempt < sessionAttempts) {
          console.log('Waiting before next session check attempt...');
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      if (sessionError && !sessionData) {
        console.error('AuthContext: Final session error:', sessionError);
        throw sessionError;
      }

      if (sessionData?.session) {
        console.log(`AuthContext: Session found for user: ${sessionData.session.user.email} ${isNewOAuthUser ? '(new OAuth user)' : ''}`);
        setSession(sessionData.session);
        setUser(sessionData.session.user);
        setStatus("authenticated");

        // Backup session for potential external navigation recovery
        backupSession(sessionData.session);

        // Cache the auth state for faster subsequent loads
        if (typeof window !== "undefined") {
          try {
            localStorage.setItem(
              AUTH_CACHE_KEY,
              JSON.stringify({ session: sessionData.session, user: sessionData.session.user })
            );
            localStorage.setItem(
              AUTH_CACHE_EXPIRY_KEY,
              (Date.now() + CACHE_DURATION).toString()
            );

            // Clear new OAuth user flag if it exists
            if (isNewOAuthUser) {
              document.cookie = "oauth_new_user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            }
          } catch (storageError) {
            console.warn('Failed to update localStorage cache:', storageError);
            // Don't fail auth if localStorage is not available
          }
        }
      } else {
        console.log('AuthContext: No session found');
        
        // Check if user is returning from external site and try backup restoration
        const isExternalReturn = detectExternalReturn();
        
        if (isExternalReturn) {
          console.log('External return detected, attempting session restoration...');
          sessionStorage.removeItem(EXTERNAL_RETURN_KEY);
          
          // Try to restore from backup
          const restoredSession = await restoreSessionFromBackup();
          if (restoredSession) {
            console.log('Session restored from backup');
            setSession(restoredSession);
            setUser(restoredSession.user);
            setStatus("authenticated");
            
            // Update cache
            if (typeof window !== "undefined") {
              try {
                localStorage.setItem(
                  AUTH_CACHE_KEY,
                  JSON.stringify({ session: restoredSession, user: restoredSession.user })
                );
                localStorage.setItem(
                  AUTH_CACHE_EXPIRY_KEY,
                  (Date.now() + CACHE_DURATION).toString()
                );
              } catch (storageError) {
                console.warn('Failed to update localStorage after restore:', storageError);
              }
            }
            return;
          }
        }

        // No session found, set unauthenticated state
        setSession(null);
        setUser(null);
        setStatus("unauthenticated");

        // Clear cache and backup
        if (typeof window !== "undefined") {
          try {
            localStorage.removeItem(AUTH_CACHE_KEY);
            localStorage.removeItem(AUTH_CACHE_EXPIRY_KEY);
            localStorage.removeItem(SESSION_BACKUP_KEY);
          } catch (storageError) {
            console.warn('Failed to clear localStorage:', storageError);
          }
        }
      }
    } catch (error) {
      console.error("AuthContext: Error refreshing auth state:", error);
      
      // On error, assume not authenticated
      setStatus("unauthenticated");
      setSession(null);
      setUser(null);

      // Clear cache on error
      if (typeof window !== "undefined") {
        try {
          localStorage.removeItem(AUTH_CACHE_KEY);
          localStorage.removeItem(AUTH_CACHE_EXPIRY_KEY);
        } catch (storageError) {
          console.warn('Failed to clear localStorage on error:', storageError);
        }
      }
    }
  }, [detectExternalReturn, restoreSessionFromBackup, backupSession]);

  // Sign in with email and password
  const signIn = useCallback(async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      if (data.session) {
        // Update state with new session
        setSession(data.session);
        setUser(data.session.user);
        setStatus("authenticated");

        // Backup session for external navigation
        backupSession(data.session);

        // Cache the auth state
        localStorage.setItem(
          AUTH_CACHE_KEY,
          JSON.stringify({ session: data.session, user: data.session.user })
        );
        localStorage.setItem(
          AUTH_CACHE_EXPIRY_KEY,
          (Date.now() + CACHE_DURATION).toString()
        );

        // Clear any old auth-related flags from sessionStorage
        const keysToRemove = [];
        for (let i = 0; i < sessionStorage.length; i++) {
          const key = sessionStorage.key(i);
          if (key && (key.includes("auth") || key.includes("logout") || key.includes("supabase"))) {
            keysToRemove.push(key);
          }
        }
        keysToRemove.forEach(key => sessionStorage.removeItem(key));
      }

      return { success: true };
    } catch (error) {
      console.error("Sign in error:", error);
      return { success: false, error: error as Error };
    }
  }, [backupSession]);

  // Sign in with Google
  const signInWithGoogle = useCallback(async () => {
    try {
      // Get the current origin to ensure correct redirect URL
      const origin = window.location.origin;
      // Create the callback URL with explicit dashboard redirect
      const callbackUrl = `${origin}/auth/callback?redirectTo=/dashboard`;

      console.log(`Setting up Google auth with callback URL: ${callbackUrl}`);

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: callbackUrl
        }
      });

      if (error) {
        throw error;
      }

      return { success: true };
    } catch (error) {
      console.error("Google sign in error:", error);
      return { success: false, error: error as Error };
    }
  }, []);

  // Enhanced sign out function
  const signOut = useCallback(async (redirectUrl: string = "/") => {
    try {
      // Sign out with Supabase
      await supabase.auth.signOut();

      // Clear auth state
      setSession(null);
      setUser(null);
      setStatus("unauthenticated");

      // Clear all auth-related storage
      localStorage.removeItem(AUTH_CACHE_KEY);
      localStorage.removeItem(AUTH_CACHE_EXPIRY_KEY);
      localStorage.removeItem(SESSION_BACKUP_KEY);
      localStorage.removeItem("supabase.auth.token");

      // Reset theme to system default when logging out
      localStorage.removeItem("theme");

      // Clear user-specific preferences cache
      if (user?.id) {
        localStorage.removeItem(`user_preferences_${user.id}`);
      }

      // Set a flag in session storage to indicate logout
      sessionStorage.setItem("isLoggedOut", "true");

      // Force page refresh for clean state
      window.location.href = redirectUrl;
    } catch (error) {
      console.error("Sign out error:", error);

      // Even if there was an error, try to redirect and clean up state
      setSession(null);
      setUser(null);
      setStatus("unauthenticated");
      window.location.href = redirectUrl;
    }
  }, [user]);

  // Setup auth state with enhanced external navigation detection
  useEffect(() => {
    // Initial auth check
    refresh();

    // Setup auth state listener for changes
    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      console.log(`AuthContext: Auth state change - ${event}`, { hasSession: !!session, userEmail: session?.user?.email });

      if (event === "SIGNED_IN" && session) {
        setSession(session);
        setUser(session.user);
        setStatus("authenticated");

        // Backup session for external navigation
        backupSession(session);

        // Update cache
        localStorage.setItem(
          AUTH_CACHE_KEY,
          JSON.stringify({ session, user: session.user })
        );
        localStorage.setItem(
          AUTH_CACHE_EXPIRY_KEY,
          (Date.now() + CACHE_DURATION).toString()
        );

        // Clear OAuth success flag if it exists
        if (typeof window !== "undefined") {
          document.cookie = "oauth_success=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        }
      } else if (event === "SIGNED_OUT") {
        setSession(null);
        setUser(null);
        setStatus("unauthenticated");

        // Clear cache and backup
        localStorage.removeItem(AUTH_CACHE_KEY);
        localStorage.removeItem(AUTH_CACHE_EXPIRY_KEY);
        localStorage.removeItem(SESSION_BACKUP_KEY);
      } else if (event === "TOKEN_REFRESHED" && session) {
        console.log("AuthContext: Token refreshed successfully");
        setSession(session);
        setUser(session.user);
        setStatus("authenticated");

        // Update backup and cache with refreshed session
        backupSession(session);
        localStorage.setItem(
          AUTH_CACHE_KEY,
          JSON.stringify({ session, user: session.user })
        );
        localStorage.setItem(
          AUTH_CACHE_EXPIRY_KEY,
          (Date.now() + CACHE_DURATION).toString()
        );
      }
    });

    // Enhanced visibility change handler for external returns
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        // Mark as potential external return
        sessionStorage.setItem(EXTERNAL_RETURN_KEY, "true");
        
        // Refresh auth state
        setTimeout(() => {
          refresh();
        }, 100); // Small delay to ensure page is fully visible
      }
    };

    // Enhanced focus handler for external returns
    const handleWindowFocus = () => {
      // Mark as potential external return
      sessionStorage.setItem(EXTERNAL_RETURN_KEY, "true");
      
      // Refresh auth state
      setTimeout(() => {
        refresh();
      }, 100);
    };

    // Add event listeners
    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("focus", handleWindowFocus);

    // Cleanup on unmount
    return () => {
      authListener.subscription.unsubscribe();
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("focus", handleWindowFocus);
    };
  }, [refresh, backupSession]);

  return (
    <AuthContext.Provider
      value={{
        user,
        session,
        status,
        signIn,
        signInWithGoogle,
        signOut,
        refresh,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}
