"use client";

import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode, useRef } from "react";
import { Language, TranslationDictionary } from "./translations";
import { loadTranslations } from "./translation-utils";
import { getUserPreferences, saveUserPreferences } from "./user-preferences-utils";
import { supabase } from "./supabase/client";
import { useAuth } from "./AuthContext";

// Define context type
interface LanguageContextType {
  language: Language;
  translations: TranslationDictionary;
  setLanguage: (language: Language) => void;
  translate: (key: string) => string;
  isLoading: boolean;
}

// Create context with default values
const LanguageContext = createContext<LanguageContextType>({
  language: "fr",
  translations: {},
  setLanguage: () => {},
  translate: (key) => key,
  isLoading: true,
});

// Custom hook to use language context
export const useLanguage = () => useContext(LanguageContext);

interface LanguageProviderProps {
  children: ReactNode;
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  const { user } = useAuth();
  const [language, setLanguageState] = useState<Language>("fr");
  const [translations, setTranslations] = useState<TranslationDictionary>({});
  const [isLoading, setIsLoading] = useState(false);
  
  // Rate limiting for language updates
  const lastUpdateTime = useRef(0);
  const UPDATE_DEBOUNCE_MS = 5000; // 5 seconds between updates

  // Function to handle language change with rate limiting
  const handleLanguageChange = useCallback(async (newLanguage: Language) => {
    const now = Date.now();
    if (now - lastUpdateTime.current < UPDATE_DEBOUNCE_MS) {
      console.log('Language update rate limited, skipping server update');
      setLanguageState(newLanguage);
      if (typeof window !== "undefined") {
        localStorage.setItem("language", newLanguage);
        document.documentElement.lang = newLanguage;
      }
      return;
    }
    
    lastUpdateTime.current = now;
    setIsLoading(true);
    
    console.log(`Language context: changing to ${newLanguage}`);

    try {
      // Update local state immediately for responsive UI
      setLanguageState(newLanguage);
      
      // Update local storage and document language
      if (typeof window !== "undefined") {
        localStorage.setItem("language", newLanguage);
        document.documentElement.lang = newLanguage;
      }

      // Load translations for the new language
      try {
        const translationData = await loadTranslations(newLanguage);
        setTranslations(translationData);
      } catch (translationError) {
        console.error(`Failed to load ${newLanguage} translations:`, translationError);
        // Continue with empty translations rather than failing
        setTranslations({});
      }

      // Update user metadata in Supabase with error handling
      if (user) {
        try {
          const { error } = await supabase.auth.updateUser({
            data: { language: newLanguage }
          });

          if (error) {
            console.error('Error updating user metadata with language preference:', error);
            // Don't throw here, allow the UI to continue working
          } else {
            console.log('Language context updated successfully to', newLanguage);
          }
        } catch (authError) {
          console.error('Error updating user metadata with language preference:', authError);
          // Don't throw here, allow the UI to continue working
        }

        // Also save to user preferences with error handling
        try {
          await saveUserPreferences(user.id, { 
            language: newLanguage,
            theme: "light" // Provide default theme to satisfy interface
          });
        } catch (prefError) {
          console.error('Error saving language preference:', prefError);
          // Don't throw here, allow the UI to continue working
        }
      }

    } catch (error) {
      console.error("Error updating language:", error);
      // Keep the UI language change even if server update fails
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Wrapper function for external use
  const setLanguage = useCallback((newLanguage: Language) => {
    if (newLanguage !== language) {
      handleLanguageChange(newLanguage);
    }
  }, [language, handleLanguageChange]);

  // Initialize language on mount
  useEffect(() => {
    const initializeLanguage = async () => {
      setIsLoading(true);
      
      try {
        let initialLanguage: Language = "fr"; // Default
        
        // Check localStorage first
        const savedLanguage = localStorage.getItem("language") as Language | null;
        
        if (savedLanguage && (savedLanguage === "fr" || savedLanguage === "en")) {
          initialLanguage = savedLanguage;
          setLanguageState(savedLanguage);
        } else if (user) {
          // If user is logged in and no saved language, try to get from user metadata
          const userLanguage = user.user_metadata?.language as Language;
          if (userLanguage && (userLanguage === "fr" || userLanguage === "en")) {
            initialLanguage = userLanguage;
            setLanguageState(userLanguage);
            localStorage.setItem("language", userLanguage);
          }
        }

        // Set document language
        document.documentElement.lang = initialLanguage;

        // Load translations for the initial language
        try {
          const translationData = await loadTranslations(initialLanguage);
          setTranslations(translationData);
        } catch (translationError) {
          console.error(`Failed to load ${initialLanguage} translations:`, translationError);
          setTranslations({});
        }

      } catch (error) {
        console.error("Error initializing language:", error);
        // Use default language if initialization fails
        setLanguageState("fr");
        document.documentElement.lang = "fr";
        setTranslations({});
      } finally {
        setIsLoading(false);
      }
    };

    initializeLanguage();
  }, [user]);

  // Translation function
  const translate = (key: string): string => {
    // Return the translation if it exists, otherwise return the key
    return translations[key] || key;
  };

  return (
    <LanguageContext.Provider
      value={{
        language,
        translations,
        setLanguage,
        translate,
        isLoading,
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
}

