"use client"

import React, { createContext, useContext, useState, ReactNode } from 'react'

interface ProfileUpdateContextType {
  /**
   * Trigger a profile update notification
   * @param userId The user ID whose profile was updated
   */
  notifyProfileUpdate: (userId: string) => void
  
  /**
   * The last updated user ID (used for subscriptions)
   */
  lastUpdatedUserId: string | null
  
  /**
   * Timestamp of the last update (used for forcing refreshes)
   */
  lastUpdateTimestamp: number
}

const ProfileUpdateContext = createContext<ProfileUpdateContextType>({
  notifyProfileUpdate: () => {},
  lastUpdatedUserId: null,
  lastUpdateTimestamp: 0
})

/**
 * Provider component for profile update notifications
 */
export function ProfileUpdateProvider({ children }: { children: ReactNode }) {
  const [lastUpdatedUserId, setLastUpdatedUserId] = useState<string | null>(null)
  const [lastUpdateTimestamp, setLastUpdateTimestamp] = useState<number>(0)

  const notifyProfileUpdate = (userId: string) => {
    setLastUpdatedUserId(userId)
    setLastUpdateTimestamp(Date.now())
  }

  return (
    <ProfileUpdateContext.Provider
      value={{
        notifyProfileUpdate,
        lastUpdatedUserId,
        lastUpdateTimestamp
      }}
    >
      {children}
    </ProfileUpdateContext.Provider>
  )
}

/**
 * Hook to access the profile update context
 */
export const useProfileUpdates = () => useContext(ProfileUpdateContext)
