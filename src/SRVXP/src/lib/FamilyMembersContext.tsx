"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { useAuth } from "./AuthContext"
import { getFamilyMembers, saveAllFamilyMembers, saveFamilyMember, deleteFamilyMember } from "./family-members/api"

export interface FamilyMember {
  id: number
  firstName: string
  lastName: string
  healthCard: string  // 4 characters
  birthDate: Date | undefined
  editing?: boolean
  // Temporary values for editing
  tempFirstName?: string
  tempLastName?: string
  tempHealthCard?: string
  tempBirthDate?: Date | undefined
  // Supabase ID for database operations
  supabaseId?: string
}

interface FamilyMembersContextType {
  familyMembers: FamilyMember[]
  setFamilyMembers: React.Dispatch<React.SetStateAction<FamilyMember[]>>
  updateFamilyMember: (id: number, data: Partial<FamilyMember>) => void
  updateTempFamilyMember: (id: number, data: Partial<FamilyMember>) => void
  toggleEditing: (id: number) => void
  saveMemberChanges: (id: number) => boolean
  deleteMember: (id: number) => void
  isLoading: boolean
  error: Error | null
}

// Create initial data
const initialFamilyMembers: FamilyMember[] = [
  {
    id: 1,
    firstName: "Félix",
    lastName: "Tremblay",
    healthCard: "TREF",
    birthDate: new Date(1985, 3, 12),
    editing: false
  },
  {
    id: 2,
    firstName: "Marie",
    lastName: "Tremblay",
    healthCard: "TREM",
    birthDate: new Date(1987, 8, 23),
    editing: false
  },
  {
    id: 3,
    firstName: "",
    lastName: "",
    healthCard: "",
    birthDate: undefined,
    editing: false
  },
  {
    id: 4,
    firstName: "",
    lastName: "",
    healthCard: "",
    birthDate: undefined,
    editing: false
  },
  {
    id: 5,
    firstName: "",
    lastName: "",
    healthCard: "",
    birthDate: undefined,
    editing: false
  }
]

// Create a default empty context value for SSR
const defaultContextValue: FamilyMembersContextType = {
  familyMembers: initialFamilyMembers,
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  setFamilyMembers: () => {},
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  updateFamilyMember: () => {},
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  updateTempFamilyMember: () => {},
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  toggleEditing: () => {},
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  saveMemberChanges: () => false,
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  deleteMember: () => {},
  isLoading: false,
  error: null
}

// Create the context with the default value
const FamilyMembersContext = createContext<FamilyMembersContextType>(defaultContextValue)

export function FamilyMembersProvider({ children }: { children: ReactNode }) {
  const { user, status } = useAuth()
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>(initialFamilyMembers)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<Error | null>(null)

  // Update actual family member data
  const updateFamilyMember = (id: number, data: Partial<FamilyMember>) => {
    setFamilyMembers(prev =>
      prev.map(member =>
        member.id === id
          ? { ...member, ...data }
          : member
      )
    )
  }

  // Update temporary data during editing
  const updateTempFamilyMember = (id: number, data: Partial<FamilyMember>) => {
    setFamilyMembers(prev =>
      prev.map(member =>
        member.id === id
          ? {
              ...member,
              tempFirstName: 'tempFirstName' in data ? data.tempFirstName : member.tempFirstName,
              tempLastName: 'tempLastName' in data ? data.tempLastName : member.tempLastName,
              tempHealthCard: 'tempHealthCard' in data ? data.tempHealthCard : member.tempHealthCard,
              tempBirthDate: 'tempBirthDate' in data ? data.tempBirthDate : member.tempBirthDate
            }
          : member
      )
    )
  }

  // Toggle editing mode for a member
  const toggleEditing = (id: number) => {
    setFamilyMembers(prev =>
      prev.map(member => {
        if (member.id === id) {
          if (!member.editing) {
            // When starting to edit, initialize temp values
            return {
              ...member,
              editing: true,
              tempFirstName: member.firstName,
              tempLastName: member.lastName,
              tempHealthCard: member.healthCard,
              tempBirthDate: member.birthDate
            }
          } else {
            // When canceling edit, discard temp values
            return {
              ...member,
              editing: false,
              tempFirstName: undefined,
              tempLastName: undefined,
              tempHealthCard: undefined,
              tempBirthDate: undefined
            }
          }
        }
        return member
      })
    )
  }

  // Save changes from temp values to actual values
  const saveMemberChanges = (id: number): boolean => {
    // Validation - check if required fields are filled
    const memberToSave = familyMembers.find(m => m.id === id);

    if (!memberToSave) return false;

    const firstName = memberToSave.tempFirstName !== undefined ? memberToSave.tempFirstName : memberToSave.firstName;
    const lastName = memberToSave.tempLastName !== undefined ? memberToSave.tempLastName : memberToSave.lastName;
    const healthCard = memberToSave.tempHealthCard !== undefined ? memberToSave.tempHealthCard : memberToSave.healthCard;
    const birthDate = memberToSave.tempBirthDate !== undefined ? memberToSave.tempBirthDate : memberToSave.birthDate;

    // Validate required fields
    const isValid = !!firstName && !!lastName && !!healthCard && healthCard.length === 4 && !!birthDate;

    // If validation fails, return false and don't save
    if (!isValid) return false;

    setFamilyMembers(prev => {
      const updatedMembers = prev.map(member => {
        if (member.id === id && member.editing) {
          // Apply temporary values to actual values
          const updatedMember = {
            ...member,
            editing: false,
            firstName: member.tempFirstName !== undefined ? member.tempFirstName : member.firstName,
            lastName: member.tempLastName !== undefined ? member.tempLastName : member.lastName,
            healthCard: member.tempHealthCard !== undefined ? member.tempHealthCard : member.healthCard,
            birthDate: member.tempBirthDate !== undefined ? member.tempBirthDate : member.birthDate,
            // Clear temp values
            tempFirstName: undefined,
            tempLastName: undefined,
            tempHealthCard: undefined,
            tempBirthDate: undefined
          }

          // Save to Supabase if user is authenticated
          if (user?.id && (updatedMember.firstName || updatedMember.lastName || updatedMember.healthCard || updatedMember.birthDate)) {
            saveFamilyMember(user.id, updatedMember, member.id)
              .then(result => {
                if (result.success && result.id) {
                  // Update the member with the Supabase ID
                  setFamilyMembers(current =>
                    current.map(m =>
                      m.id === member.id ? { ...m, supabaseId: result.id } : m
                    )
                  )
                }
              })
              .catch(err => console.error("Error saving member:", err))
          }

          return updatedMember
        }
        return member
      })

      // Ensure we always have exactly 5 members after saving
      if (updatedMembers.length < 5) {
        // If we somehow have fewer than 5 members, add empty slots
        const existingIds = updatedMembers.map(m => m.id);

        for (let i = 1; i <= 5; i++) {
          if (!existingIds.includes(i)) {
            updatedMembers.push({
              id: i,
              firstName: "",
              lastName: "",
              healthCard: "",
              birthDate: undefined,
              editing: false
            });
          }
        }

        // Sort by ID to ensure correct order
        updatedMembers.sort((a, b) => a.id - b.id);
      }

      return updatedMembers;
    });

    return true; // Return true if validation passes and save is initiated
  }

  // Function to save all family members to Supabase
  // Load family members from Supabase when user is authenticated
  useEffect(() => {
    if (status === "loading") return

    if (status === "authenticated" && user?.id) {
      setIsLoading(true)
      setError(null)

      getFamilyMembers(user.id)
        .then(members => {
          // The API now always returns exactly 5 slots
          setFamilyMembers(members)
        })
        .catch(err => {
          console.error("Error loading family members:", err)
          setError(new Error("Failed to load family members"))

          // On error, fall back to empty members
          const emptyMembers: FamilyMember[] = Array(5)
            .fill(null)
            .map((_, index) => ({
              id: index + 1,
              firstName: "",
              lastName: "",
              healthCard: "",
              birthDate: undefined,
              editing: false
            }))

          setFamilyMembers(emptyMembers)
        })
        .finally(() => {
          setIsLoading(false)
        })
    } else if (status === "unauthenticated") {
      // Not authenticated, use initial data
      setFamilyMembers(initialFamilyMembers)
      setIsLoading(false)
    }
  }, [user?.id, status])

  // Delete a family member
  const deleteMember = (id: number) => {
    const member = familyMembers.find(m => m.id === id);

    if (!member || !member.supabaseId) {
      // If there's no Supabase ID, just reset the member data in the UI and close the form
      setFamilyMembers(prev => prev.map(m =>
        m.id === id
          ? { ...m, firstName: "", lastName: "", healthCard: "", birthDate: undefined, editing: false, tempFirstName: undefined, tempLastName: undefined, tempHealthCard: undefined, tempBirthDate: undefined }
          : m
      ));
      return;
    }

    // If we have a Supabase ID, delete from database
    if (user?.id) {
      deleteFamilyMember(member.supabaseId)
        .then(result => {
          if (result.success) {
            // Reset the member data in the UI but keep the slot and close the form
            setFamilyMembers(prev => prev.map(m =>
              m.id === id
                ? { ...m, firstName: "", lastName: "", healthCard: "", birthDate: undefined, supabaseId: undefined, editing: false, tempFirstName: undefined, tempLastName: undefined, tempHealthCard: undefined, tempBirthDate: undefined }
                : m
            ));
          }
        })
        .catch(err => console.error("Error deleting member:", err));
    }
  };

  // Provide the actual implementation
  const contextValue: FamilyMembersContextType = {
    familyMembers,
    setFamilyMembers,
    updateFamilyMember,
    updateTempFamilyMember,
    toggleEditing,
    saveMemberChanges,
    deleteMember,
    isLoading,
    error
  }

  return (
    <FamilyMembersContext.Provider value={contextValue}>
      {children}
    </FamilyMembersContext.Provider>
  )
}

export function useFamilyMembers() {
  const context = useContext(FamilyMembersContext)
  return context
}
