import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  console.warn('Warning: Supabase environment variables are missing. Using placeholder values.')
}

// Cookie configuration optimized for OAuth session persistence
const getCookieOptions = () => {
  if (typeof window === 'undefined') return {}

  return {
    cookies: {
      getAll() {
        if (typeof document === 'undefined' || typeof window === 'undefined') return []
        try {
          return document.cookie
            .split(';')
            .map(cookie => cookie.trim().split('='))
            .filter(([name]) => name)
            .map(([name, value]) => ({ name, value: decodeURIComponent(value || '') }))
        } catch (error) {
          console.warn('Error reading cookies:', error)
          return []
        }
      },
      setAll(cookiesToSet: Array<{ name: string; value: string; options?: any }>) {
        if (typeof document === 'undefined' || typeof window === 'undefined') return
        try {
          cookiesToSet.forEach(({ name, value, options = {} }) => {
            const cookieOptions = {
              path: '/',
              maxAge: 60 * 60 * 24 * 30, // 30 days
              sameSite: 'lax',
              secure: process.env.NODE_ENV === 'production',
              ...options
            }

            const cookieString = [
              `${name}=${encodeURIComponent(value)}`,
              `Path=${cookieOptions.path}`,
              `Max-Age=${cookieOptions.maxAge}`,
              `SameSite=${cookieOptions.sameSite}`,
              cookieOptions.secure ? 'Secure' : '',
              cookieOptions.domain ? `Domain=${cookieOptions.domain}` : ''
            ].filter(Boolean).join('; ')

            document.cookie = cookieString
          })
        } catch (error) {
          console.warn('Error setting cookies:', error)
        }
      },
    },
  }
}

// Create browser client with proper cookie handling for OAuth
export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey, getCookieOptions())

// Export a function to create a new client (useful for hooks)
export function createClient() {
  return createBrowserClient(supabaseUrl, supabaseAnonKey, getCookieOptions())
}
