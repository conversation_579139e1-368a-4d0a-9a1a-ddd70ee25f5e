import { createServerClient } from '@supabase/ssr'
import { NextRequest, NextResponse } from 'next/server'

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            const cookieOptions = {
              ...options,
              // Ensure cookies can be accessed by client-side code for OAuth
              httpOnly: false,
              // Use secure in production
              secure: process.env.NODE_ENV === 'production',
              // Allow cross-site requests for OAuth
              sameSite: 'lax' as const,
              // Set longer expiration for OAuth sessions
              maxAge: options?.maxAge || 60 * 60 * 24 * 30, // 30 days
              path: '/',
              // Add domain for broader cookie accessibility
              domain: process.env.NODE_ENV === 'production' ? undefined : 'localhost',
            }

            // Set cookie on both request and response
            request.cookies.set(name, value)
            supabaseResponse.cookies.set(name, value, cookieOptions)
          })
        },
      },
    }
  )

  // Try to get the current session
  let session = null
  let sessionError = null

  try {
    console.log('Middleware: Attempting to get session...')
    const { data, error } = await supabase.auth.getSession()
    session = data?.session
    sessionError = error

    if (session) {
      console.log('Middleware: Session found for user:', session.user.email)
    } else {
      console.log('Middleware: No session found')

      // Check if this might be an external return or OAuth callback that needs session restoration
      const isExternalReturn = request.nextUrl.searchParams.has('session_id') ||
                              request.headers.get('referer')?.includes('stripe.com') ||
                              request.headers.get('referer')?.includes('checkout.stripe.com')

      // Check for OAuth success flag
      const hasOAuthSuccess = request.cookies.get('oauth_success')?.value === 'true'
      const hasOAuthNewUser = request.cookies.get('oauth_new_user')?.value === 'true'

      // Check if this is right after OAuth callback
      const isPostOAuth = request.nextUrl.pathname === '/dashboard' &&
                         request.headers.get('referer')?.includes('/auth/callback')

      if (isExternalReturn || hasOAuthSuccess || isPostOAuth || hasOAuthNewUser) {
        console.log('External return or OAuth detected, attempting session refresh...', {
          isExternalReturn,
          hasOAuthSuccess,
          hasOAuthNewUser,
          isPostOAuth
        })

        try {
          // For new OAuth users, try multiple refresh attempts with delays
          let refreshAttempts = hasOAuthNewUser ? 3 : 1
          let refreshSuccess = false

          for (let attempt = 1; attempt <= refreshAttempts; attempt++) {
            console.log(`Session refresh attempt ${attempt}/${refreshAttempts}`)

            const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession()
            if (refreshData?.session && !refreshError) {
              session = refreshData.session
              sessionError = null
              refreshSuccess = true
              console.log(`Session refreshed successfully on attempt ${attempt} for ${hasOAuthNewUser ? 'new OAuth user' : 'OAuth user'}`)
              break
            } else {
              console.log(`Refresh attempt ${attempt} failed:`, refreshError)
              if (attempt < refreshAttempts) {
                // Wait before next attempt for new users
                await new Promise(resolve => setTimeout(resolve, 500))
              }
            }
          }

          if (refreshSuccess) {
            // Clear OAuth flags if they were used
            if (hasOAuthSuccess) {
              supabaseResponse.cookies.delete('oauth_success')
            }
            if (hasOAuthNewUser) {
              supabaseResponse.cookies.delete('oauth_new_user')
            }
          } else {
            console.log('All session refresh attempts failed')
          }
        } catch (refreshError) {
          console.error('Session refresh error:', refreshError)
        }
      }
    }
  } catch (error) {
    console.error('Error in middleware session check:', error)
    sessionError = error
  }

  // Add session info to response headers for debugging
  if (session) {
    supabaseResponse.headers.set('X-User-ID', session.user.id)
    supabaseResponse.headers.set('X-Session-Exists', 'true')
  } else {
    supabaseResponse.headers.set('X-Session-Exists', 'false')
  }

  // Ensure proper cache headers for auth-related routes
  supabaseResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
  supabaseResponse.headers.set('Pragma', 'no-cache')
  supabaseResponse.headers.set('Expires', '0')

  return { response: supabaseResponse, session }
}
