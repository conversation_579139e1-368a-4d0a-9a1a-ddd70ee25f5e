import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ''

export const createClient = async () => {
  let cookieStore
  
  try {
    cookieStore = await cookies()
  } catch (error) {
    console.error('Failed to access cookies:', error)
    // Fallback: create client without cookie access
    return createServerClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        cookies: {
          getAll() {
            return []
          },
          setAll() {
            // No-op if cookies are not available
          },
        },
      }
    )
  }

  return createServerClient(
    supabaseUrl,
    supabase<PERSON>nonKey,
    {
      cookies: {
        getAll() {
          try {
            return cookieStore.getAll().map(({ name, value }) => ({
              name,
              value,
            }))
          } catch (error) {
            console.error('Error accessing cookies:', error)
            return []
          }
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              // Enhanced cookie options for better persistence
              const enhancedOptions = {
                ...options,
                httpOnly: false, // Allow client access
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax' as const, // Better for external navigation
                maxAge: options?.maxAge || 60 * 60 * 24 * 7, // 7 days default
                path: '/',
              }
              
              cookieStore.set(name, value, enhancedOptions)
            })
          } catch (error) {
            console.error('Error setting cookies:', error)
          }
        },
      },
    }
  )
}

// Create a service role client for admin operations
export const createAdminClient = async () => {
  // Only create admin client if service role key is available
  if (!supabaseServiceRoleKey) {
    console.error('SUPABASE_SERVICE_ROLE_KEY is not defined')
    // Return regular client as fallback
    return createClient()
  }

  let cookieStore
  
  try {
    cookieStore = await cookies()
  } catch (error) {
    console.error('Failed to access cookies in admin client:', error)
    // Fallback: create admin client without cookie access
    return createServerClient(
      supabaseUrl,
      supabaseServiceRoleKey,
      {
        cookies: {
          getAll() {
            return []
          },
          setAll() {
            // No-op if cookies are not available
          },
        },
      }
    )
  }

  return createServerClient(
    supabaseUrl,
    supabaseServiceRoleKey,
    {
      cookies: {
        getAll() {
          try {
            return cookieStore.getAll().map(({ name, value }) => ({
              name,
              value,
            }))
          } catch (error) {
            console.error('Error accessing cookies:', error)
            return []
          }
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              // Enhanced cookie options for admin client
              const enhancedOptions = {
                ...options,
                httpOnly: false,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax' as const,
                maxAge: options?.maxAge || 60 * 60 * 24 * 7,
                path: '/',
              }
              
              cookieStore.set(name, value, enhancedOptions)
            })
          } catch (error) {
            console.error('Error setting cookies:', error)
          }
        },
      },
    }
  )
}
