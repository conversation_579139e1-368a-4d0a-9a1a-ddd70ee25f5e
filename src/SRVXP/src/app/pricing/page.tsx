"use client";

import { useState, useEffect } from "react";
import { Check } from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/zaply/ui/button";
import { PriceToggle } from "@/components/zaply/core/price-toggle";
import { DynamicDashboardLayout } from "@/components/dynamic/dashboard-layout";
import { T } from "@/components/t";
import { translationKeys } from "@/lib/translations";
import { useLanguage } from "@/lib/LanguageContext";
import { Suspense } from "react";
import { supabase } from "@/lib/supabase/client";

// Stripe price IDs for subscription plans
const PLANS = {
  INDIVIDUAL: {
    MONTHLY: 'price_1R0CkxElTneJ74CJARjKlS9U', // Individual Monthly - $7.95 CAD
    ANNUAL: 'price_1REN04ElTneJ74CJVejJzVDH'   // Individual Annual - $71.40 CAD
  },
  FAMILY: {
    MONTHLY: 'price_1R0ClKElTneJ74CJO45trpKh', // Family Monthly - $14.95 CAD
    ANNUAL: 'price_1REN0TElTneJ74CJDRnv2MXk'    // Family Annual - $134.40 CAD
  }
};

interface PricingCardProps {
  title: string | React.ReactNode;
  price: string;
  description: string | React.ReactNode;
  features: (string | React.ReactNode)[];
  annualSavings?: string | React.ReactNode;
  planId: string;
  userSubscription: any;
  isLoadingSubscription: boolean;
  isLoading: Record<string, boolean>;
  onSubscribe: (planId: string) => void;
  onManageSubscription: () => void;
}

const PricingCard = ({
  title,
  price,
  description,
  features,
  annualSavings,
  planId,
  userSubscription,
  isLoadingSubscription,
  isLoading,
  onSubscribe,
  onManageSubscription
}: PricingCardProps) => {
  const { language } = useLanguage();

  // Render button based on subscription status
  const renderButton = () => {
    if (isLoadingSubscription) {
      return (
        <Button disabled size="lg" className="w-full rounded-lg font-medium text-sm py-5 mb-6 group hover:shadow-md transition-all duration-300 bg-brandBlue text-white hover:bg-brandBlue/90">
          <T keyName={translationKeys.common.loading} />
        </Button>
      );
    }

    if (userSubscription) {
      return (
        <Button
          onClick={onManageSubscription}
          size="lg"
          className="w-full rounded-lg font-medium text-sm py-5 mb-6 group hover:shadow-md transition-all duration-300 bg-brandBlue text-white hover:bg-brandBlue/90"
        >
          <T keyName={translationKeys.landing.pricing.manageSubscription} />
        </Button>
      );
    }

    return (
      <Button
        onClick={() => onSubscribe(planId)}
        disabled={isLoading[planId]}
        size="lg"
        className="w-full rounded-lg font-medium text-sm py-5 mb-6 group hover:shadow-md transition-all duration-300 bg-brandBlue text-white hover:bg-brandBlue/90"
      >
        {isLoading[planId] ? (
          <T keyName={translationKeys.common.processing} />
        ) : (
          <>
            <T keyName={translationKeys.landing.pricing.choosePlan} />
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="ml-2 h-5 w-5 arrow-icon"
            >
              <path d="M5 12h14"/>
              <path d="m12 5 7 7-7 7"/>
            </svg>
          </>
        )}
      </Button>
    );
  };

  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm flex flex-col h-full">
      <div className="flex-grow">
        <h3 className="text-lg font-bold mb-2">{title}</h3>
        {/* Adding a key to the price div forces it to re-render when price changes */}
        <div
          key={`price-${price}`}
          className="text-4xl font-bold mb-5 animate-price-fade text-[#212244]"
        >
          {/* Extract just the price portion and always show monthly suffix */}
          {language === 'fr'
            ? price.split('/')[0]
            : price.split('/')[0]
          }
          <span className="text-xl font-normal">{language === 'fr' ? '/mois' : '/month'}</span>
        </div>

        {annualSavings && (
          <div className="text-green-600 text-xs font-medium mb-2">
            {annualSavings}
          </div>
        )}
        <p className="text-gray-600 text-sm mb-6">{description}</p>
      </div>

      {renderButton()}

      <div className="space-y-4">
        <h4 className="font-bold text-sm text-[#212244]"><T keyName={translationKeys.landing.pricing.included} /></h4>

        {features.map((feature, index) => (
          <div key={index} className="flex items-center">
            <div className="flex-shrink-0 w-4 h-4 mr-2">
              <Check className="w-full h-full text-brandBlue" strokeWidth={2} />
            </div>
            <span className="text-[#212244] text-sm">{feature}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

function PricingPageContent() {
  const [pricingPeriod, setPricingPeriod] = useState<"monthly" | "annual">("monthly");
  const [isMounted, setIsMounted] = useState(false);
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});
  const [userSubscription, setUserSubscription] = useState(null);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState(true);
  const { language } = useLanguage();
  const router = useRouter();
  const searchParams = useSearchParams();
  const returnUrl = typeof window !== 'undefined' ? `${window.location.origin}/account/subscription/success` : '';

  // Set isMounted to true after component mounts to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Fetch user subscription data
  useEffect(() => {
    async function fetchUserSubscription() {
      try {
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          setIsLoadingSubscription(false);
          return;
        }

        // Query the subscriptions table for this user
        const { data: subscription, error } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .eq('status', 'active')
          .single();

        if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
          console.error('Error fetching subscription:', error);
        }

        setUserSubscription(subscription || null);
        setIsLoadingSubscription(false);
      } catch (error) {
        console.error('Error checking subscription status:', error);
        setIsLoadingSubscription(false);
      }
    }

    fetchUserSubscription();
  }, []);

  // Check for a selected plan in URL parameters when component mounts
  useEffect(() => {
    const selectedPlan = searchParams.get('selected_plan');

    if (selectedPlan && !isLoadingSubscription && !userSubscription) {
      handleSubscribe(selectedPlan);
      // Clean up URL to prevent re-triggering on refresh
      if (typeof window !== 'undefined') {
        window.history.replaceState({}, '', '/pricing');
      }
    }
  }, [searchParams, isLoadingSubscription, userSubscription]);

  // Handle pricing period change
  const handlePricingPeriodChange = (period: "monthly" | "annual") => {
    setPricingPeriod(period);
  };

  // Handle subscription checkout
  const handleSubscribe = async (priceId: string) => {
    console.log(`Starting checkout process for plan ID: ${priceId}`);
    setIsLoading(prev => ({ ...prev, [priceId]: true }));

    try {
      // Get current user
      const { data: { user: currentUser } } = await supabase.auth.getUser();

      if (!currentUser) {
        // Redirect to login with the selected plan in URL parameters
        router.push(`/login?redirect=/pricing&selected_plan=${priceId}`);
        return;
      }

      // Call the Supabase function to create checkout
      const { data, error } = await supabase.functions.invoke('supabase-functions-create-checkout', {
        body: {
          price_id: priceId,
          user_id: currentUser.id,
          return_url: returnUrl
        },
        headers: {
          'X-Customer-Email': currentUser.email
        }
      });

      if (error) throw error;

      // Redirect to Stripe checkout
      if (data?.url) {
        console.log(`Redirecting to Stripe checkout: ${data.url}`);
        window.location.href = data.url;
      } else {
        console.error('No checkout URL returned in the response:', data);
        throw new Error('No checkout URL returned');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      // Show error message to user
      alert(`Error creating checkout session: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`);
    } finally {
      setIsLoading(prev => ({ ...prev, [priceId]: false }));
    }
  };

  // Navigate to subscription management page
  const handleManageSubscription = () => {
    router.push('/compte/abonnement');
  };

  // Calculate prices based on period and language
  const formatPrice = (amount: string) => {
    return language === 'fr' ? `${amount}$` : `$${amount}`;
  };

  // Monthly prices
  const monthlyIndividualPrice = language === 'fr' ? formatPrice("7,95") : formatPrice("7.95");
  const monthlyFamilyPrice = language === 'fr' ? formatPrice("14,95") : formatPrice("14.95");

  // Annual prices (30% discount)
  const annualIndividualPrice = language === 'fr' ? formatPrice("5,95") : formatPrice("5.95");
  const annualFamilyPrice = language === 'fr' ? formatPrice("11,20") : formatPrice("11.20");

  // Individual price savings calculation
  const individualSavings = pricingPeriod === "annual" ? <T keyName={translationKeys.landing.pricing.individual.annualSavings} /> : undefined;
  const familySavings = pricingPeriod === "annual" ? <T keyName={translationKeys.landing.pricing.family.annualSavings} /> : undefined;

  // Select the right price based on period
  const individualPrice = pricingPeriod === "monthly" ? monthlyIndividualPrice : annualIndividualPrice;
  const familyPrice = pricingPeriod === "monthly" ? monthlyFamilyPrice : annualFamilyPrice;

  // Full formatted price strings for use as keys in the PricingCard
  const formattedIndividualPrice = `${individualPrice}/suffix`;
  const formattedFamilyPrice = `${familyPrice}/suffix`;

  // Get the current plan ID based on selected period
  const getCurrentIndividualPlanId = () => {
    return pricingPeriod === "monthly" ? PLANS.INDIVIDUAL.MONTHLY : PLANS.INDIVIDUAL.ANNUAL;
  };

  const getCurrentFamilyPlanId = () => {
    return pricingPeriod === "monthly" ? PLANS.FAMILY.MONTHLY : PLANS.FAMILY.ANNUAL;
  };

  return (
    <div className="space-y-6">
      <div className="mt-4">
        {/* Price toggle for both mobile and desktop */}
        <div className="flex justify-center mb-8">
          <PriceToggle period={pricingPeriod} onChange={handlePricingPeriodChange} />
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <PricingCard
            key={`individual-${pricingPeriod}`}
            title={<T keyName={translationKeys.landing.pricing.individual.title} />}
            price={formattedIndividualPrice}
            description={<T keyName={translationKeys.landing.pricing.individual.description} />}
            features={[
              <T key="1" keyName={translationKeys.landing.pricing.feature1} />,
              <T key="2" keyName={translationKeys.landing.pricing.feature2} />,
              <T key="3" keyName={translationKeys.landing.pricing.feature3} />,
              <T key="4" keyName={translationKeys.landing.pricing.feature4} />
            ]}
            annualSavings={individualSavings}
            planId={getCurrentIndividualPlanId()}
            userSubscription={userSubscription}
            isLoadingSubscription={isLoadingSubscription}
            isLoading={isLoading}
            onSubscribe={handleSubscribe}
            onManageSubscription={handleManageSubscription}
          />

          <PricingCard
            key={`family-${pricingPeriod}`}
            title={<T keyName={translationKeys.landing.pricing.family.title} />}
            price={formattedFamilyPrice}
            description={<T keyName={translationKeys.landing.pricing.family.description} />}
            features={[
              <T key="1" keyName={translationKeys.landing.pricing.family.features} />,
              <T key="2" keyName={translationKeys.landing.pricing.feature2} />,
              <T key="3" keyName={translationKeys.landing.pricing.feature3} />,
              <T key="4" keyName={translationKeys.landing.pricing.feature4} />
            ]}
            annualSavings={familySavings}
            planId={getCurrentFamilyPlanId()}
            userSubscription={userSubscription}
            isLoadingSubscription={isLoadingSubscription}
            isLoading={isLoading}
            onSubscribe={handleSubscribe}
            onManageSubscription={handleManageSubscription}
          />
        </div>
      </div>
    </div>
  );
}

export default function PricingPage() {
  return (
    <DynamicDashboardLayout>
      <Suspense fallback={
        <div className="flex h-full w-full items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
        </div>
      }>
        <PricingPageContent />
      </Suspense>
    </DynamicDashboardLayout>
  );
}
