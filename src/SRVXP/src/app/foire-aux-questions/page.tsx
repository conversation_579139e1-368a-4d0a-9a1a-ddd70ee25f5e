import { Navbar } from "@/components/zaply/layout/Navbar";
import { Footer } from "@/components/zaply/layout/Footer";
import { Metadata } from "next";
import { FaqFullPage } from "@/components/zaply/sections/FaqFullPage";

export const metadata: Metadata = {
  title: "Foire aux questions | Sans rendez-vous express",
  description: "Trouvez les réponses à vos questions sur Sans rendez-vous express et notre service pour trouver des rendez-vous médicaux rapidement.",
};

export default function FaqPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        {/* FAQ Full Page - White Background */}
        <div className="bg-white w-full pt-4 pb-16">
          <div className="container mx-auto px-4 sm:px-6">
            <FaqFullPage />
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
