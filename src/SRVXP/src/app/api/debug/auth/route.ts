import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    console.log('=== AUTH DEBUG START ===')
    
    // Check cookies from the request
    const cookies = request.cookies.getAll()
    console.log('Request cookies:', cookies.map(c => ({ name: c.name, hasValue: !!c.value, partial: c.value?.substring(0, 20) + '...' })))
    
    // Create Supabase client
    const supabase = await createClient()
    console.log('Supabase client created successfully')
    
    // Try to get user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    console.log('Auth user result:', { 
      hasUser: !!user, 
      userId: user?.id,
      email: user?.email,
      error: authError?.message 
    })
    
    // Try to get session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    console.log('Session result:', { 
      hasSession: !!session, 
      sessionValid: !!session?.access_token,
      error: sessionError?.message 
    })
    
    let subscriptionInfo = null
    if (user) {
      // Check subscription data
      const { data: subscriptions, error: subsError } = await supabase
        .from('subscriptions')
        .select('customer_id, status, plan_type, billing_period')
        .eq('user_id', user.id)
      
      console.log('Subscription query result:', { 
        hasSubscriptions: !!subscriptions, 
        count: subscriptions?.length || 0,
        statuses: subscriptions?.map(s => s.status),
        error: subsError?.message 
      })
      
      subscriptionInfo = {
        hasSubscriptions: !!subscriptions,
        count: subscriptions?.length || 0,
        subscriptions: subscriptions || [],
        error: subsError?.message
      }
    }
    
    console.log('=== AUTH DEBUG END ===')
    
    return NextResponse.json({
      success: true,
      requestCookies: cookies.length,
      cookieNames: cookies.map(c => c.name),
      hasUser: !!user,
      hasSession: !!session,
      userEmail: user?.email,
      userId: user?.id,
      authError: authError?.message,
      sessionError: sessionError?.message,
      subscription: subscriptionInfo
    })
  } catch (error) {
    console.error('Debug auth error:', error)
    return NextResponse.json(
      { error: 'Debug failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 