import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'
import { createClient } from '@/lib/supabase/server'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-03-31.basil',
})

export async function GET(request: NextRequest) {
  try {
    console.log('=== STRIPE CUSTOMER DEBUG START ===')
    
    const supabase = await createClient()
    
    // Try to get user from session token in Authorization header
    const authHeader = request.headers.get('authorization')
    let user = null

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      const { data, error } = await supabase.auth.getUser(token)
      user = data?.user
      if (error) console.log('Auth error:', error.message)
    } else {
      const { data, error } = await supabase.auth.getUser()
      user = data?.user
      if (error) console.log('Auth error:', error.message)
    }
    
    if (!user || !user.email) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    console.log('User:', { id: user.id, email: user.email })

    // Check Supabase subscriptions
    const { data: supabaseSubscriptions, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id)

    console.log('Supabase subscriptions:', supabaseSubscriptions)
    if (subError) console.log('Supabase subscription error:', subError)

    // Check Stripe customers
    let stripeCustomers: Stripe.Customer[] = []
    let stripeSubscriptions: Stripe.Subscription[] = []
    
    try {
      const customerList = await stripe.customers.list({
        email: user.email,
        limit: 10,
      })
      stripeCustomers = customerList.data
      console.log('Stripe customers found:', stripeCustomers.length)

      // If we found customers, get their subscriptions
      for (const customer of stripeCustomers) {
        const subscriptions = await stripe.subscriptions.list({
          customer: customer.id,
          limit: 10,
        })
        stripeSubscriptions.push(...subscriptions.data)
      }
      
      console.log('Stripe subscriptions found:', stripeSubscriptions.length)
    } catch (stripeError) {
      console.error('Stripe error:', stripeError)
    }

    console.log('=== STRIPE CUSTOMER DEBUG END ===')

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
      },
      supabase: {
        subscriptions: supabaseSubscriptions || [],
        error: subError?.message,
      },
      stripe: {
        customers: stripeCustomers.map(c => ({
          id: c.id,
          email: c.email,
          created: c.created,
          metadata: c.metadata,
        })),
        subscriptions: stripeSubscriptions.map(s => ({
          id: s.id,
          customer: s.customer,
          status: s.status,
          items: s.items.data.map(item => ({
            price_id: item.price.id,
            product_id: item.price.product,
          })),
        })),
      },
    })
  } catch (error) {
    console.error('Debug error:', error)
    return NextResponse.json(
      { error: 'Debug failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 