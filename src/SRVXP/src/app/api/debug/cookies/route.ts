import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    console.log('=== COOKIE DEBUG START ===')
    
    // Get all cookies from the request
    const allCookies = request.cookies.getAll()
    console.log('Request cookies count:', allCookies.length)
    
    // Log Supabase-related cookies
    const supabaseCookies = allCookies.filter(c => 
      c.name.includes('supabase') || 
      c.name.includes('auth') ||
      c.name.includes('sb-')
    )
    console.log('Supabase cookies:', supabaseCookies.map(c => ({
      name: c.name,
      hasValue: !!c.value,
      valueLength: c.value?.length || 0
    })))
    
    // Try to get session using server client
    const supabase = await createClient()
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession()
    
    console.log('Server session check:', {
      hasSession: !!sessionData?.session,
      hasUser: !!sessionData?.session?.user,
      userEmail: sessionData?.session?.user?.email,
      error: sessionError?.message
    })
    
    // Response data
    const debugInfo = {
      timestamp: new Date().toISOString(),
      cookies: {
        total: allCookies.length,
        supabase: supabaseCookies.length,
        list: allCookies.map(c => ({
          name: c.name,
          hasValue: !!c.value,
          valueLength: c.value?.length || 0,
          preview: c.value?.substring(0, 20) + (c.value && c.value.length > 20 ? '...' : '')
        }))
      },
      session: {
        exists: !!sessionData?.session,
        userId: sessionData?.session?.user?.id,
        email: sessionData?.session?.user?.email,
        expiresAt: sessionData?.session?.expires_at,
        error: sessionError?.message
      },
      headers: {
        userAgent: request.headers.get('user-agent'),
        referer: request.headers.get('referer'),
        origin: request.headers.get('origin')
      }
    }
    
    console.log('=== COOKIE DEBUG END ===')
    
    return NextResponse.json(debugInfo, { status: 200 })
    
  } catch (error) {
    console.error('Cookie debug error:', error)
    return NextResponse.json({ 
      error: 'Debug failed', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
} 