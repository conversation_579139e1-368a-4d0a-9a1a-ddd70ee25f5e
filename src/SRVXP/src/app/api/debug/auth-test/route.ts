import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    console.log('=== AUTH TEST DEBUG START ===')
    
    // Check all cookies
    const allCookies = request.cookies.getAll()
    console.log('All request cookies:', allCookies.map(c => ({ 
      name: c.name, 
      hasValue: !!c.value, 
      length: c.value?.length || 0,
      preview: c.value?.substring(0, 20) + '...'
    })))
    
    // Filter for Supabase-related cookies
    const supabaseCookies = allCookies.filter(c => 
      c.name.includes('supabase') || 
      c.name.includes('auth') ||
      c.name.includes('session')
    )
    console.log('Supabase-related cookies:', supabaseCookies)
    
    // Create Supabase client
    const supabase = await createClient()
    console.log('Supabase client created successfully')
    
    // Check headers that might indicate external return
    const referrer = request.headers.get('referer') || request.headers.get('referrer')
    const xExternalReturn = request.headers.get('x-external-return')
    const userAgent = request.headers.get('user-agent')
    
    console.log('Request metadata:', {
      referrer,
      xExternalReturn,
      hasUserAgent: !!userAgent,
      url: request.url,
      searchParams: [...request.nextUrl.searchParams.entries()]
    })
    
    // Check if this looks like an external return
    const isExternalReturn = 
      request.nextUrl.searchParams.has('session_id') ||
      request.nextUrl.pathname.includes('/compte/abonnement') ||
      referrer?.includes('stripe.com') ||
      xExternalReturn === 'true'
    
    console.log('External return detection:', { 
      isExternalReturn,
      hasSessionId: request.nextUrl.searchParams.has('session_id'),
      isSubscriptionPage: request.nextUrl.pathname.includes('/compte/abonnement'),
      isStripeReferrer: referrer?.includes('stripe.com'),
      hasExternalHeader: xExternalReturn === 'true'
    })
    
    // Try to get user and session
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    console.log('Auth results:', {
      hasUser: !!user,
      hasSession: !!session,
      userError: userError?.message,
      sessionError: sessionError?.message,
      sessionValid: !!session?.access_token,
      sessionExpiry: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null
    })
    
    // If external return and no session, try refresh
    let refreshResult = null
    if (isExternalReturn && !session) {
      console.log('Attempting session refresh for external return...')
      
      try {
        const { data, error } = await supabase.auth.refreshSession()
        refreshResult = {
          success: !!data?.session,
          error: error?.message,
          hasNewSession: !!data?.session,
        }
        console.log('Refresh result:', refreshResult)
      } catch (refreshError) {
        refreshResult = {
          success: false,
          error: refreshError instanceof Error ? refreshError.message : 'Unknown refresh error'
        }
        console.error('Refresh error:', refreshError)
      }
    }
    
    console.log('=== AUTH TEST DEBUG END ===')
    
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      cookies: {
        total: allCookies.length,
        supabaseRelated: supabaseCookies.length,
        names: allCookies.map(c => c.name)
      },
      request: {
        url: request.url,
        referrer,
        isExternalReturn,
        searchParams: Object.fromEntries(request.nextUrl.searchParams.entries())
      },
      auth: {
        hasUser: !!user,
        hasSession: !!session,
        userEmail: user?.email,
        userId: user?.id,
        sessionValid: !!session?.access_token,
        sessionExpiry: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null,
        userError: userError?.message,
        sessionError: sessionError?.message
      },
      refresh: refreshResult,
      diagnosis: {
        cookiesPresent: supabaseCookies.length > 0,
        authWorking: !!user && !!session,
        needsRefresh: isExternalReturn && !session,
        recommendedAction: !user && !session ? 'login_required' : 
                          isExternalReturn && !session ? 'refresh_needed' :
                          'auth_working'
      }
    })
  } catch (error) {
    console.error('Auth test error:', error)
    return NextResponse.json(
      { 
        error: 'Auth test failed', 
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
} 