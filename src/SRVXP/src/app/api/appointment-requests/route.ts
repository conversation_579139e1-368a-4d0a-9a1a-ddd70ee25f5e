import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required', errorType: 'authentication_required' },
        { status: 401 }
      )
    }

    // Check if user has an active subscription
    const { data: subscriptions, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .in('plan_type', ['individual', 'family'])
      .maybeSingle()

    if (subscriptionError) {
      console.error('Error checking subscription:', subscriptionError)
    }

    if (!subscriptions) {
      return NextResponse.json(
        { error: 'Subscription required to create appointment requests', errorType: 'no_subscription' },
        { status: 403 }
      )
    }

    // Get request data
    const requestData = await request.json()

    // Validate required fields
    if (!requestData.request_type) {
      return NextResponse.json(
        { error: 'Request type is required' },
        { status: 400 }
      )
    }

    // Prepare data for insertion
    const appointmentRequest = {
      user_id: user.id,
      patient_id: requestData.patient_id || null,
      request_type: requestData.request_type,
      request_details: requestData.request_details || {},
      status: 'pending'
    }

    // Insert the appointment request
    const { data, error } = await supabase
      .from('appointment_requests')
      .insert(appointmentRequest)
      .select()
      .single()

    if (error) {
      console.error('Error creating appointment request:', error)

      // Handle specific errors
      if (error.message.includes('Free users cannot create appointment requests')) {
        return NextResponse.json(
          { error: 'Subscription required to create appointment requests', errorType: 'no_subscription' },
          { status: 403 }
        )
      }

      if (error.message.includes('Only family plan members can book for others')) {
        return NextResponse.json(
          { error: 'Family plan required to book for family members' },
          { status: 403 }
        )
      }

      if (error.message.includes('Patient does not belong to this user')) {
        return NextResponse.json(
          { error: 'Invalid patient selection' },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to create appointment request' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true, data })

  } catch (error) {
    console.error('Unexpected error creating appointment request:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get query parameters
    const url = new URL(request.url)
    const status = url.searchParams.get('status')
    const limit = parseInt(url.searchParams.get('limit') || '10')

    // Build query
    let query = supabase
      .from('appointment_requests')
      .select(`
        *,
        family_members(first_name, last_name, health_card, birth_date)
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(limit)

    // Add status filter if provided
    if (status) {
      query = query.eq('status', status)
    }

    // Execute query
    const { data, error } = await query

    if (error) {
      console.error('Error fetching appointment requests:', error)
      return NextResponse.json(
        { error: 'Failed to fetch appointment requests' },
        { status: 500 }
      )
    }

    return NextResponse.json({ data })

  } catch (error) {
    console.error('Unexpected error fetching appointment requests:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
