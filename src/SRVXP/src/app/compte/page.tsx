"use client"

import { useEffect } from "react"
import Link from "next/link"
import { CreditCard, FileText, Settings, User, Users, Loader2 } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { T } from "@/components/t"
import { translationKeys } from "@/lib/translations"
import { useUserProfile } from "@/hooks/use-user-profile"
import { useCombinedStore } from "@/stores/useAppStore"
import { Badge } from "@/components/ui/badge"

export default function ComptePage() {
  // Get data from the combined store
  const {
    profile,
    subscription,
    user,
    fetchProfile,
    fetchSubscription,
    isProfileLoading,
    isSubscriptionLoading
  } = useCombinedStore()

  // For backward compatibility, also use the hook
  const { fullName: hookFullName, initials: hookInitials } = useUserProfile()

  // Use store data if available, otherwise fall back to hook data
  const fullName = profile ? `${profile.firstName} ${profile.lastName}`.trim() : hookFullName
  const initials = profile ? `${profile.firstName?.charAt(0) || ''}${profile.lastName?.charAt(0) || ''}`.toUpperCase() : hookInitials

  // Fetch profile and subscription data when component mounts
  useEffect(() => {
    if (user?.id) {
      fetchProfile(user.id)
      fetchSubscription(user.id)
    }
  }, [user?.id, fetchProfile, fetchSubscription])

  // Determine subscription plan type and status
  const planType = subscription?.plan_type || 'individual'
  const isActive = subscription?.status === 'active'

  // Enhanced logging for subscription data verification
  useEffect(() => {
    if (subscription) {
      console.log('Subscription data:', {
        id: subscription.id,
        stripe_id: subscription.stripe_id,
        plan_type: subscription.plan_type,
        interval: subscription.interval,
        billing_period: subscription.billing_period,
        amount: subscription.amount,
        period_start: subscription.current_period_start ? new Date(subscription.current_period_start * 1000).toISOString() : null,
        period_end: subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : null,
        period_diff_days: subscription.current_period_start && subscription.current_period_end ?
          Math.round((subscription.current_period_end - subscription.current_period_start) / (60 * 60 * 24)) : null,
        // Add display logic verification
        display_plan: subscription.plan_type === 'family'
          ? (subscription.billing_period === 'annual' || (!subscription.billing_period && subscription.interval === 'year')
              ? 'familyPlanAnnual'
              : 'familyPlanMonthly')
          : (subscription.billing_period === 'annual' || (!subscription.billing_period && subscription.interval === 'year')
              ? 'individualPlanAnnual'
              : 'individualPlanMonthly')
      })
    }
  }, [subscription])

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">
            <T keyName={translationKeys.account.yourAccount} />
          </h1>
        </div>

        {/* User Account Info */}
        <Card className="overflow-hidden">
          <CardHeader className="bg-gray-50/50 dark:bg-transparent dark:border-b pb-4 pt-6">
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 min-h-[3rem] min-w-[3rem] rounded-full bg-blue-100 dark:bg-blue-950/70 flex items-center justify-center shrink-0">
                  {isProfileLoading ? (
                    <Loader2 className="h-6 w-6 text-blue-700 dark:text-blue-400 animate-spin" />
                  ) : initials ? (
                    <span className="text-lg font-semibold text-blue-700 dark:text-blue-400">{initials}</span>
                  ) : (
                    <User className="h-6 w-6 text-blue-700 dark:text-blue-400" />
                  )}
                </div>
                <div>
                  <CardTitle>{isProfileLoading ? 'Loading...' : fullName}</CardTitle>
                  {subscription && (
                    <CardDescription className="mt-1">
                      <T keyName={
                        // Determine plan display based on plan_type and billing period
                        // Primarily use billing_period, only fall back to interval if needed
                        subscription.plan_type === 'family'
                          ? (subscription.billing_period === 'annual' || (!subscription.billing_period && subscription.interval === 'year')
                              ? translationKeys.account.familyPlanAnnual
                              : translationKeys.account.familyPlanMonthly)
                          : (subscription.billing_period === 'annual' || (!subscription.billing_period && subscription.interval === 'year')
                              ? translationKeys.account.individualPlanAnnual
                              : translationKeys.account.individualPlanMonthly)
                      } />
                    </CardDescription>
                  )}
                </div>
              </div>
              <Badge className={isActive ? "bg-green-100 text-green-800 hover:bg-green-100" : "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"}>
                {isSubscriptionLoading ? (
                  <Loader2 className="h-3.5 w-3.5 animate-spin" />
                ) : (
                  <T keyName={isActive ? translationKeys.common.active : translationKeys.common.inactive} />
                )}
              </Badge>
            </div>
          </CardHeader>
        </Card>

        {/* Account Management Sections */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2">
          {/* Personal Information Management */}
          <Card className="flex flex-col min-h-[180px]">
            <CardHeader className="pb-2 flex flex-row items-start gap-4 flex-1">
              <div className="h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0">
                <FileText className="h-5 w-5 text-blue-700" />
              </div>
              <div>
                <CardTitle>
                  <T keyName={translationKeys.account.manageInformation} />
                </CardTitle>
                <CardDescription className="mt-1">
                  <T keyName={translationKeys.account.personalInfoDescription} />
                </CardDescription>
              </div>
            </CardHeader>
            <CardContent className="pt-2">
              <Button asChild variant="default" className="w-full">
                <Link href="/compte/profile">
                  <T keyName={translationKeys.account.modifyProfile} />
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Modify Subscription */}
          <Card className="flex flex-col min-h-[180px]">
            <CardHeader className="pb-2 flex flex-row items-start gap-4 flex-1">
              <div className="h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0">
                <CreditCard className="h-5 w-5 text-blue-700" />
              </div>
              <div>
                <CardTitle>
                  <T keyName={translationKeys.account.modifySubscription} />
                </CardTitle>
                <CardDescription className="mt-1">
                  <T keyName={
                    isActive
                      ? translationKeys.account.subscriptionDescription
                      : subscription?.status === 'cancelled' || subscription?.cancel_at_period_end
                        ? translationKeys.subscription.returnToPlans
                        : translationKeys.subscription.returnToPlans
                  } />
                </CardDescription>
              </div>
            </CardHeader>
            <CardContent className="pt-2">
              <Button asChild variant="default" className="w-full">
                <Link href={isActive ? "/compte/abonnement" : "/pricing"}>
                  <T keyName={
                    isActive
                      ? translationKeys.account.modifySubscription
                      : subscription?.status === 'cancelled' || subscription?.cancel_at_period_end
                        ? translationKeys.account.choosePlan
                        : translationKeys.account.subscribePlan
                  } />
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Preferences */}
          <Card className="flex flex-col min-h-[180px]">
            <CardHeader className="pb-2 flex flex-row items-start gap-4 flex-1">
              <div className="h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0">
                <Settings className="h-5 w-5 text-blue-700" />
              </div>
              <div>
                <CardTitle>
                  <T keyName={translationKeys.account.appearanceLanguage} />
                </CardTitle>
                <CardDescription className="mt-1">
                  <T keyName={translationKeys.account.appearanceDescription} />
                </CardDescription>
              </div>
            </CardHeader>
            <CardContent className="pt-2">
              <Button asChild variant="default" className="w-full">
                <Link href="/compte/preferences">
                  <T keyName={translationKeys.account.modifyPreferences} />
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* User Management or Profile Management (based on plan type) */}
          <Card className="flex flex-col min-h-[180px]">
            <CardHeader className="pb-2 flex flex-row items-start gap-4 flex-1">
              <div className="h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0">
                {planType === 'family' ? (
                  <Users className="h-5 w-5 text-blue-700" />
                ) : (
                  <User className="h-5 w-5 text-blue-700" />
                )}
              </div>
              <div>
                <CardTitle>
                  <T keyName={planType === 'family'
                    ? translationKeys.account.manageAccountUsers
                    : translationKeys.account.manageProfile} />
                </CardTitle>
                <CardDescription className="mt-1">
                  <T keyName={planType === 'family'
                    ? translationKeys.account.manageUsersDescription
                    : translationKeys.account.manageProfileDescription} />
                </CardDescription>
              </div>
            </CardHeader>
            <CardContent className="pt-2">
              <Button asChild variant="default" className="w-full">
                <Link href="/compte/utilisateurs">
                  <T keyName={planType === 'family'
                    ? translationKeys.account.manageUsers
                    : translationKeys.account.manageProfileButton} />
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
