"use client";

import { useAuth } from "@/lib/AuthContext";
import { useExternalNavigation } from "@/lib/hooks/useExternalNavigation";
import { useState, useEffect } from "react";

export default function TestAuthPage() {
  const { user, session, status, refresh, signIn } = useAuth();
  const { markStripeNavigation, detectStripeReturn } = useExternalNavigation();
  const [testResult, setTestResult] = useState("");
  const [cookieDebugInfo, setCookieDebugInfo] = useState<any>(null);

  // Function to fetch cookie debug info
  const fetchCookieDebug = async () => {
    try {
      const response = await fetch('/api/debug/cookies');
      const data = await response.json();
      setCookieDebugInfo(data);
    } catch (error) {
      console.error('Failed to fetch cookie debug info:', error);
    }
  };

  // Fetch debug info on mount
  useEffect(() => {
    fetchCookieDebug();
  }, []);

  const handleTestSignIn = async () => {
    setTestResult("Attempting to sign in...");
    try {
      const result = await signIn("<EMAIL>", "test123");
      if (result.success) {
        setTestResult("Sign in successful! Fetching updated cookie info...");
        await fetchCookieDebug();
      } else {
        setTestResult(`Sign in failed: ${result.error?.message}`);
      }
    } catch (error) {
      setTestResult(`Sign in error: ${error}`);
    }
  };

  const handleTestStripeReturn = () => {
    // Simulate marking a Stripe navigation
    markStripeNavigation();
    setTestResult("Marked Stripe navigation. Now refresh the page to simulate return.");
  };

  const handleTestRefresh = async () => {
    setTestResult("Refreshing auth state...");
    try {
      await refresh();
      setTestResult("Auth state refreshed successfully!");
      await fetchCookieDebug();
    } catch (error) {
      setTestResult(`Error refreshing: ${error}`);
    }
  };

  const handleTestDetection = () => {
    const isStripeReturn = detectStripeReturn();
    setTestResult(`Stripe return detection: ${isStripeReturn ? "YES" : "NO"}`);
  };

  const handleTestLocalStorage = () => {
    const items = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        const value = localStorage.getItem(key);
        items.push({ key, valueLength: value?.length || 0 });
      }
    }
    setTestResult(`localStorage items: ${items.length}. Items: ${JSON.stringify(items, null, 2)}`);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">Authentication Test Page</h1>
      
      <div className="bg-gray-100 p-4 rounded">
        <h2 className="text-xl font-semibold mb-2">Auth Status</h2>
        <p><strong>Status:</strong> {status}</p>
        <p><strong>User ID:</strong> {user?.id || "None"}</p>
        <p><strong>Email:</strong> {user?.email || "None"}</p>
        <p><strong>Session:</strong> {session ? "Active" : "None"}</p>
        <p><strong>Session Expires:</strong> {session?.expires_at ? new Date(session.expires_at * 1000).toLocaleString() : "N/A"}</p>
      </div>

      <div className="bg-blue-100 p-4 rounded space-y-2">
        <h2 className="text-xl font-semibold mb-2">Test Controls</h2>
        <div className="space-x-2 flex flex-wrap gap-2">
          <button 
            onClick={handleTestSignIn}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
          >
            Test Sign In
          </button>
          <button 
            onClick={handleTestStripeReturn}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Test Stripe Navigation
          </button>
          <button 
            onClick={handleTestRefresh}
            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
          >
            Test Refresh
          </button>
          <button 
            onClick={handleTestDetection}
            className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
          >
            Test Detection
          </button>
          <button 
            onClick={handleTestLocalStorage}
            className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600"
          >
            Check localStorage
          </button>
          <button 
            onClick={fetchCookieDebug}
            className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
          >
            Refresh Cookie Info
          </button>
        </div>
      </div>

      {testResult && (
        <div className="bg-yellow-100 p-4 rounded">
          <h3 className="font-semibold">Test Result:</h3>
          <pre className="text-sm whitespace-pre-wrap">{testResult}</pre>
        </div>
      )}

      {cookieDebugInfo && (
        <div className="bg-green-100 p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Cookie Debug Info</h2>
          <pre className="text-sm">
            {JSON.stringify(cookieDebugInfo, null, 2)}
          </pre>
        </div>
      )}

      <div className="bg-gray-100 p-4 rounded">
        <h2 className="text-xl font-semibold mb-2">Session Storage Debug</h2>
        <pre className="text-sm">
          {JSON.stringify({
            stripe_redirect: sessionStorage.getItem('stripe_redirect'),
            stripe_redirect_time: sessionStorage.getItem('stripe_redirect_time'),
            external_return_detected: sessionStorage.getItem('external_return_detected'),
          }, null, 2)}
        </pre>
      </div>

      <div className="bg-gray-100 p-4 rounded">
        <h2 className="text-xl font-semibold mb-2">Local Storage Debug</h2>
        <pre className="text-sm">
          {JSON.stringify({
            auth_cache: localStorage.getItem('auth_cache') ? 'Present' : 'None',
            session_backup: localStorage.getItem('session_backup') ? 'Present' : 'None',
            language: localStorage.getItem('language'),
          }, null, 2)}
        </pre>
      </div>
    </div>
  );
} 