"use client"

// Force light mode for auth pages

import Link from "next/link"
import Image from "next/image"
import { LanguageToggle } from "@/components/language-toggle"
import { useTranslation } from "@/components/t"

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { t, language } = useTranslation()

  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-gray-50 light relative">
      {/* Language selector - outside the white box */}
      <div className="absolute top-4 right-4">
        <LanguageToggle />
      </div>

      {/* Centered auth form - explicitly light theme */}
      <div className="w-full max-w-md bg-white p-8 rounded-lg shadow-sm text-gray-900">
        {/* Logo */}
        <div className="flex items-center justify-center mb-8">
          <Link href="/" className="flex items-center group">
            <div className="w-8 h-8 flex items-center justify-center transition-transform duration-300 group-hover:scale-110">
              <Image
                src="/zaply-images/srvxp_logorevised.svg"
                alt="Logo"
                width={32}
                height={32}
                priority
                suppressHydrationWarning
                className="text-brandBlue fill-current"
              />
            </div>
            <span className="ml-4 text-lg sm:text-xl font-bold text-[#212242]">Sans rendez-vous express</span>
          </Link>
        </div>

        {/* Children will be the auth forms */}
        {children}
      </div>

      {/* Sticky footer - outside the white box */}
      <div className="absolute bottom-4 left-4 flex items-center space-x-4 text-xs text-gray-400">
        <span>© Sans rendez-vous express</span>
        <Link href={language === 'en' ? "/politique-de-confidentialite-EN" : "/politique-de-confidentialite"} className="hover:text-gray-600 transition-colors">
          {t('landing.footer.privacyPolicy')}
        </Link>
      </div>
    </div>
  )
}
