import { createClient, createAdminClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

/**
 * Updates the user's profile data in Supabase after Google Auth
 */
async function updateUserProfileFromGoogleAuth(supabase: any, userId: string) {
  try {
    // Get the admin client for privileged operations
    const adminClient = await createAdminClient()

    // Get the user's metadata from auth.users
    const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(userId)

    if (userError || !userData) {
      console.error('Error fetching user data:', userError)
      return
    }

    const metadata = userData.user.user_metadata

    // Check if this is a Google Auth user
    const isGoogleAuth = metadata?.provider_id ||
                         metadata?.iss === 'https://accounts.google.com' ||
                         metadata?.sub?.startsWith('google')

    if (!isGoogleAuth) {
      console.log('Not a Google Auth user, skipping profile update')
      return
    }

    console.log('Processing Google Auth user profile update')

    // Extract name information from Google metadata
    // Google provides different fields depending on the authentication flow
    const firstName = metadata.given_name || metadata.first_name || metadata.name?.split(' ')[0] || ''
    const lastName = metadata.family_name || metadata.last_name || (metadata.name?.split(' ').slice(1).join(' ') || '')

    // Get the language from the cookie or default to French
    const cookieStore = await cookies()
    const languageCookie = cookieStore.get('NEXT_LOCALE')
    const language = languageCookie?.value || 'fr'

    console.log(`Extracted user info - First: "${firstName}", Last: "${lastName}", Language: "${language}"`)

    // Update the auth.users metadata to ensure consistency
    const { error: updateAuthError } = await adminClient.auth.admin.updateUserById(
      userId,
      {
        user_metadata: {
          ...metadata,
          first_name: firstName,
          last_name: lastName,
          language: language
        }
      }
    )

    if (updateAuthError) {
      console.error('Error updating auth user metadata:', updateAuthError)
    }

    // The database trigger should handle updating the users table,
    // but we'll do it explicitly as well to ensure it happens
    const { error: updateUserError } = await adminClient
      .from('users')
      .upsert({
        id: userId,
        user_id: userId,
        email: userData.user.email || '',
        first_name: firstName,
        last_name: lastName,
        language: language,
        avatar_url: metadata.avatar_url || metadata.picture || '',
        token_identifier: userData.user.email || '',
        created_at: userData.user.created_at,
        updated_at: new Date().toISOString()
      }, { onConflict: 'id' })

    if (updateUserError) {
      console.error('Error updating user profile:', updateUserError)
    } else {
      console.log('Successfully updated user profile with Google Auth data')
    }

  } catch (error) {
    console.error('Error in updateUserProfileFromGoogleAuth:', error)
  }
}

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const redirectTo = requestUrl.searchParams.get('redirectTo') || '/dashboard'
  const origin = requestUrl.origin

  console.log(`OAuth callback - Code: ${!!code}, RedirectTo: ${redirectTo}`)

  if (code) {
    try {
      // Create the Supabase client
      const supabase = await createClient()

      // Exchange the code for a session
      const { data, error } = await supabase.auth.exchangeCodeForSession(code)

      if (error) {
        console.error('Error exchanging code for session:', error)
        // Redirect to sign-in page with error
        const errorUrl = `${origin}/auth/sign-in?error=oauth_callback_failed`
        return NextResponse.redirect(errorUrl)
      }

      console.log('Successfully exchanged code for session')

      if (!data?.session?.user?.id) {
        console.error('No session or user ID after code exchange')
        const errorUrl = `${origin}/auth/sign-in?error=no_session`
        return NextResponse.redirect(errorUrl)
      }

      const session = data.session
      const user = session.user

      // Check if this is a new user registration vs existing user login
      const userCreatedAt = new Date(user.created_at).getTime()
      const now = Date.now()
      const timeSinceCreation = now - userCreatedAt
      const isNewUser = timeSinceCreation < (10 * 60 * 1000) // Created within last 10 minutes

      // Check for manual testing override
      const forceNewUserTest = requestUrl.searchParams.get('test_new_user') === 'true'
      const finalIsNewUser = isNewUser || forceNewUserTest

      console.log(`OAuth user analysis:`)
      console.log(`- User created at: ${user.created_at}`)
      console.log(`- Time since creation: ${Math.round(timeSinceCreation / 1000)} seconds`)
      console.log(`- Is new user (auto): ${isNewUser}`)
      console.log(`- Force new user test: ${forceNewUserTest}`)
      console.log(`- Final user type: ${finalIsNewUser ? 'NEW USER REGISTRATION' : 'EXISTING USER LOGIN'}`)

      // For new users, we need to ensure proper session establishment
      if (finalIsNewUser) {
        console.log('Processing new user registration - ensuring session establishment...')

        // Update user profile first
        await updateUserProfileFromGoogleAuth(supabase, user.id)

        // Wait a moment for database operations to complete
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Verify session is still valid after profile operations
        const { data: verifyData, error: verifyError } = await supabase.auth.getSession()
        if (verifyError || !verifyData?.session) {
          console.error('Session lost during new user setup:', verifyError)
          // Try to refresh the session
          const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession()
          if (refreshError || !refreshData?.session) {
            console.error('Failed to refresh session for new user:', refreshError)
            const errorUrl = `${origin}/auth/sign-in?error=session_lost`
            return NextResponse.redirect(errorUrl)
          }
          console.log('Session refreshed successfully for new user')
        }
      } else {
        // For existing users, just update profile without delays
        await updateUserProfileFromGoogleAuth(supabase, user.id)
      }

      // Create the redirect URL - respect the redirectTo parameter
      const redirectUrl = `${origin}${redirectTo}`
      console.log(`Redirecting OAuth user (${finalIsNewUser ? 'new' : 'existing'}) to: ${redirectUrl}`)

      const response = NextResponse.redirect(redirectUrl)

      // Set proper cookies for OAuth session persistence
      const cookieOptions = {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax' as const,
        maxAge: 60 * 60 * 24 * 30, // 30 days
        path: '/',
        domain: process.env.NODE_ENV === 'production' ? undefined : 'localhost',
      }

      // Set different flags for new vs existing users
      if (finalIsNewUser) {
        response.cookies.set('oauth_new_user', 'true', { ...cookieOptions, maxAge: 120 }) // 2 minutes
        response.cookies.set('oauth_success', 'true', { ...cookieOptions, maxAge: 120 })
      } else {
        response.cookies.set('oauth_success', 'true', { ...cookieOptions, maxAge: 60 })
      }

      response.cookies.delete('isLoggedOut')

      // Add session verification headers for debugging
      response.headers.set('X-OAuth-User-Type', finalIsNewUser ? 'new' : 'existing')
      response.headers.set('X-OAuth-User-ID', user.id)

      return response

    } catch (error) {
      console.error('Error in auth callback:', error)
      // Redirect to sign-in page with error
      const errorUrl = `${origin}/auth/sign-in?error=oauth_callback_error`
      return NextResponse.redirect(errorUrl)
    }
  }

  // No code provided - redirect to sign-in
  console.log('No OAuth code provided, redirecting to sign-in')
  const signInUrl = `${origin}/auth/sign-in`
  return NextResponse.redirect(signInUrl)
}
