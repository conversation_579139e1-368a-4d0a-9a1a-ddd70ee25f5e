"use client";

import Link from "next/link"
import {
  Users,
  User,
  ClipboardList,
  Search,
} from "lucide-react"

import { DynamicDashboardLayout } from "@/components/dynamic/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { T, useTranslation } from "@/components/t"
import { translationKeys } from "@/lib/translations"
import { Suspense, useEffect } from "react"
import { useLanguage } from "@/lib/LanguageContext"
import { useUserProfile } from "@/hooks/use-user-profile"
import { useCombinedStore } from "@/stores/useAppStore"

function PageContent() {
  // Get data from the combined store
  const {
    profile,
    subscription,
    pendingAppointments,
    completedAppointments,
    cancelledAppointments,
    fetchAllAppointmentHistory,
    getAppointmentSummary,
    isLoading
  } = useCombinedStore();

  // Fallback to useUserProfile for backward compatibility
  const { firstName, fullName } = useUserProfile();
  const { } = useTranslation(); // t is used via the T component

  // Fetch appointment history when component mounts
  useEffect(() => {
    fetchAllAppointmentHistory();
  }, [fetchAllAppointmentHistory]);

  // Get appointment summary
  const appointmentSummary = getAppointmentSummary();

  // Get current language and translation function
  const { t, language } = useTranslation();

  // Determine subscription plan type and status
  const planType = subscription?.plan_type || 'individual';
  const isActive = subscription?.status === 'active';

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">
          <T
            keyName={translationKeys.home.greeting}
            params={{ firstName: firstName }}
          />
        </h1>
        <p className="text-muted-foreground">
          <T keyName={translationKeys.home.welcome} />
        </p>
      </div>

      {/* Account info card */}
      <Card>
        <CardHeader className="py-4 flex flex-row items-center justify-between">
          <div>
            <CardTitle>
              <T keyName={translationKeys.account.yourAccount} />
            </CardTitle>
          </div>
          <Badge className={isActive ? "bg-green-100 text-green-800 hover:bg-green-100" : "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"}>
            <T keyName={isActive ? translationKeys.common.active : translationKeys.common.inactive} />
          </Badge>
        </CardHeader>
        <CardContent className="pb-6">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                <User className="h-5 w-5 text-blue-700" />
              </div>
              <div>
                <h3 className="font-medium">{profile?.firstName ? `${profile.firstName} ${profile.lastName}` : fullName}</h3>
                <p className="text-sm text-muted-foreground">
                  <T keyName={planType === 'family' ? translationKeys.account.familyPlan : translationKeys.account.individualPlan} />
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick access cards */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        <Card className="hover:shadow-md transition-shadow flex flex-col">
          <CardHeader className="p-4">
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0 w-5">
                <Search className="h-5 w-5 text-blue-600" />
              </div>
              <CardTitle className="text-base break-words hyphens-auto">
                <T keyName={translationKeys.home.findAppointmentTitle} />
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0 flex-1 flex flex-col">
            <CardDescription className="mb-3 flex-1">
              <T keyName={translationKeys.home.findAppointmentDesc} />
            </CardDescription>
            <Button asChild className="w-full mt-auto">
              <Link href="/trouver-rendez-vous">
                <T keyName={translationKeys.common.search} />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow flex flex-col">
          <CardHeader className="p-4">
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0 w-5">
                <ClipboardList className="h-5 w-5 text-blue-600" />
              </div>
              <CardTitle className="text-base break-words hyphens-auto">
                <T keyName={translationKeys.nav.appointments} />
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0 flex-1 flex flex-col">
            <CardDescription className="mb-3 flex-1">
              <T keyName={translationKeys.home.manageAppointmentsDesc} />
            </CardDescription>
            <Button asChild className="w-full mt-auto" variant="default">
              <Link href="/mes-rendez-vous">
                <T keyName={translationKeys.home.viewRequests} />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow flex flex-col">
          <CardHeader className="p-4">
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0 w-5">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <CardTitle className="text-base break-words hyphens-auto">
                {planType === 'family' ? (
                  <T keyName={translationKeys.account.manageUsers} />
                ) : (
                  <T keyName={translationKeys.home.manageProfileTitle} />
                )}
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0 flex-1 flex flex-col">
            <CardDescription className="mb-3 flex-1">
              {planType === 'family' ? (
                <T keyName={translationKeys.home.manageUsersDesc} />
              ) : (
                <T keyName={translationKeys.home.manageProfileDesc} />
              )}
            </CardDescription>
            <Button asChild className="w-full mt-auto" variant="default">
              <Link href="/compte/utilisateurs">
                {planType === 'family' ? (
                  <T keyName={translationKeys.account.manageUsers} />
                ) : (
                  <T keyName={translationKeys.home.manageProfileButton} />
                )}
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Appointment Summary Card */}
      <Card>
        <CardHeader className="py-4">
          <CardTitle>
            <T keyName={translationKeys.appointments.summary} />
          </CardTitle>
        </CardHeader>
        <CardContent className="pb-6">
          {isLoading ? (
            <div className="flex justify-center py-4">
              <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
            </div>
          ) : (
            <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  <T keyName={translationKeys.appointments.inProgress} />
                </p>
                <p className="text-2xl font-bold">{appointmentSummary.counts.in_progress}</p>
              </div>
              <div className="bg-green-50 p-3 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  <T keyName={translationKeys.appointments.completed} />
                </p>
                <p className="text-2xl font-bold">{appointmentSummary.counts.completed}</p>
              </div>
              <div className="bg-red-50 p-3 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  {language === 'fr' ? 'Annulés' : 'Cancelled'}
                </p>
                <p className="text-2xl font-bold">{appointmentSummary.counts.cancelled}</p>
              </div>
            </div>
          )}
          <div className="mt-4 flex justify-end">
            <Button asChild variant="outline" size="sm">
              <Link href="/mes-rendez-vous">
                {t(translationKeys.appointments.viewAll) || (language === 'fr' ? 'Voir tous les rendez-vous' : 'View all appointments')}
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function DashboardPage() {
  // Get the initialization function from the combined store
  const { initializeApp, isLoading } = useCombinedStore();

  // Initialize the app when the component mounts
  useEffect(() => {
    initializeApp();
  }, [initializeApp]);

  return (
    <DynamicDashboardLayout>
      {isLoading && (
        <div className="fixed inset-0 bg-background/80 z-50 flex items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
        </div>
      )}
      <Suspense fallback={
        <div className="flex h-full w-full items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
        </div>
      }>
        <PageContent />
      </Suspense>
    </DynamicDashboardLayout>
  )
}
