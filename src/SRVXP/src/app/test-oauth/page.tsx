"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestOAuthPage() {
  const { user, session, status, signInWithGoogle, signOut, refresh } = useAuth()
  const [logs, setLogs] = useState<string[]>([])
  const [cookieInfo, setCookieInfo] = useState<string>('')
  const [storageInfo, setStorageInfo] = useState<string>('')

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
    console.log(message)
  }

  const checkCookies = () => {
    if (typeof window !== 'undefined') {
      const cookies = document.cookie.split(';').map(c => c.trim()).filter(c => c)
      const authCookies = cookies.filter(c =>
        c.includes('supabase') ||
        c.includes('auth') ||
        c.includes('oauth') ||
        c.includes('session')
      )

      // Check for specific OAuth flags
      const hasOAuthSuccess = document.cookie.includes('oauth_success=true')
      const hasOAuthNewUser = document.cookie.includes('oauth_new_user=true')

      let cookieDetails = authCookies.join('\n') || 'No auth-related cookies found'
      if (hasOAuthSuccess || hasOAuthNewUser) {
        cookieDetails += `\n\n🔍 OAuth Flags:\n- oauth_success: ${hasOAuthSuccess}\n- oauth_new_user: ${hasOAuthNewUser}`
      }

      setCookieInfo(cookieDetails)
      addLog(`Found ${authCookies.length} auth-related cookies (OAuth success: ${hasOAuthSuccess}, OAuth new user: ${hasOAuthNewUser})`)
    }
  }

  const checkStorage = () => {
    if (typeof window !== 'undefined') {
      const authKeys = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && (key.includes('auth') || key.includes('supabase') || key.includes('session'))) {
          authKeys.push(`${key}: ${localStorage.getItem(key)?.substring(0, 100)}...`)
        }
      }
      setStorageInfo(authKeys.join('\n') || 'No auth-related localStorage items found')
      addLog(`Found ${authKeys.length} auth-related localStorage items`)
    }
  }

  const handleGoogleSignIn = async () => {
    addLog('Initiating Google OAuth...')
    try {
      const result = await signInWithGoogle()
      if (result.success) {
        addLog('Google OAuth initiated successfully')
      } else {
        addLog(`Google OAuth failed: ${result.error?.message}`)
      }
    } catch (error) {
      addLog(`Error during Google OAuth: ${error}`)
    }
  }

  const handleTestNewUserFlow = async () => {
    addLog('Testing new user OAuth flow (with test flag)...')
    try {
      // Get the current origin to ensure correct redirect URL
      const origin = window.location.origin;
      // Create the callback URL with test flag for new user simulation
      const callbackUrl = `${origin}/auth/callback?redirectTo=/dashboard&test_new_user=true`;

      addLog(`Callback URL with test flag: ${callbackUrl}`);

      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        'https://tfvswgreslsbctjrvdbd.supabase.co',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.5bNos8JS_77EUjWOxIGHpgNaYE28qQvO2g4qj0wRWiU'
      );

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: callbackUrl
        }
      });

      if (error) {
        addLog(`Test new user OAuth failed: ${error.message}`);
      } else {
        addLog('Test new user OAuth initiated successfully');
      }
    } catch (error) {
      addLog(`Error during test new user OAuth: ${error}`)
    }
  }

  const handleSignOut = async () => {
    addLog('Signing out...')
    try {
      await signOut()
      addLog('Sign out successful')
    } catch (error) {
      addLog(`Error during sign out: ${error}`)
    }
  }

  const handleRefresh = async () => {
    addLog('Refreshing auth state...')
    try {
      await refresh()
      addLog('Auth state refreshed')
    } catch (error) {
      addLog(`Error refreshing auth state: ${error}`)
    }
  }

  const simulateStripeNavigation = () => {
    addLog('Simulating Stripe navigation...')
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('stripe_redirect', 'true')
      sessionStorage.setItem('external_return', 'true')
      addLog('Stripe navigation flags set')
    }
  }

  const clearLogs = () => {
    setLogs([])
  }

  useEffect(() => {
    addLog('OAuth test page loaded')
    checkCookies()
    checkStorage()
  }, [])

  useEffect(() => {
    addLog(`Auth status changed: ${status}`)
    if (user) {
      addLog(`User: ${user.email}`)
    }
  }, [status, user])

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">🔍 Google OAuth Test</h1>
      
      <div className="grid gap-6">
        {/* Authentication Status */}
        <Card>
          <CardHeader>
            <CardTitle>🔐 Authentication Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <strong>Status:</strong> <span className={`px-2 py-1 rounded ${
                  status === 'authenticated' ? 'bg-green-100 text-green-800' :
                  status === 'loading' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>{status}</span>
              </div>
              {user && (
                <div>
                  <strong>User:</strong> {user.email}
                </div>
              )}
              {session && (
                <div>
                  <strong>Session:</strong> Active (expires: {new Date(session.expires_at! * 1000).toLocaleString()})
                </div>
              )}
              <div className="flex gap-2 flex-wrap">
                <Button onClick={handleGoogleSignIn} disabled={status === 'authenticated'}>
                  Test Google OAuth
                </Button>
                <Button onClick={handleTestNewUserFlow} disabled={status === 'authenticated'} variant="secondary">
                  Test New User Flow
                </Button>
                <Button onClick={handleSignOut} disabled={status !== 'authenticated'} variant="outline">
                  Sign Out
                </Button>
                <Button onClick={handleRefresh} variant="outline">
                  Refresh Auth
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Session Persistence Test */}
        <Card>
          <CardHeader>
            <CardTitle>🔄 Session Persistence Test</CardTitle>
            <CardDescription>Test session persistence after external navigation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button onClick={simulateStripeNavigation} variant="outline">
                Simulate Stripe Navigation
              </Button>
              <p className="text-sm text-gray-600">
                This simulates navigating to Stripe and back. After clicking, refresh the page to test session restoration.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Cookie Information */}
        <Card>
          <CardHeader>
            <CardTitle>🍪 Cookie Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button onClick={checkCookies} variant="outline">Refresh Cookies</Button>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40">
                {cookieInfo || 'No cookie information available'}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Local Storage */}
        <Card>
          <CardHeader>
            <CardTitle>💾 Local Storage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button onClick={checkStorage} variant="outline">Refresh Storage</Button>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40">
                {storageInfo || 'No storage information available'}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Test Log */}
        <Card>
          <CardHeader>
            <CardTitle>📝 Test Log</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button onClick={clearLogs} variant="outline">Clear Log</Button>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-60">
                {logs.join('\n') || 'No logs yet'}
              </pre>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
