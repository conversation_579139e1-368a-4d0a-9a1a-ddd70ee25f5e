"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Calendar as CalendarI<PERSON>, Clock, Info, User } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { cn, formatHealthCardNumber, isHealthCardComplete } from "@/lib/utils"
import { format, isBefore, startOfDay } from "date-fns"
import { fr } from "date-fns/locale"
import { useFamilyMembers, FamilyMember } from "@/lib/FamilyMembersContext"
import { useAuth } from "@/lib/AuthContext"
import { createAppointmentRequest } from "@/lib/appointment-requests/api"
import Image from "next/image"
import { T } from "@/components/t"
import { translationKeys } from "@/lib/translations"
import { useTranslation } from "@/components/t"

export default function TrouverRendezVousPage() {
  const { familyMembers } = useFamilyMembers()
  const [date, setDate] = useState<Date>()
  const [time, setTime] = useState<string>("")
  const [postalCode, setPostalCode] = useState<string>("")
  const [postalError, setPostalError] = useState<string>("")
  const [postalWarning, setPostalWarning] = useState<string>("")
  const [today, setToday] = useState<Date>(startOfDay(new Date()))
  const [selectedMember, setSelectedMember] = useState<string>("")
  const [healthCardLastDigits, setHealthCardLastDigits] = useState<string>("")
  const [cardSequenceNumber, setCardSequenceNumber] = useState<string>("")
  const [selectedFamilyMember, setSelectedFamilyMember] = useState<FamilyMember | null>(null)
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [showSearchResult, setShowSearchResult] = useState(false)
  const [formSubmitted, setFormSubmitted] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string>('')
  const { t, language } = useTranslation()

  // Format postal code to match "A1A 1A1" pattern
  const formatPostalCode = (value: string) => {
    // Remove non-alphanumeric characters
    let formatted = value.replace(/[^a-zA-Z0-9]/g, "").toUpperCase()

    // Limit to 6 characters
    formatted = formatted.slice(0, 6)

    // Insert space after the 3rd character
    if (formatted.length > 3) {
      formatted = formatted.slice(0, 3) + " " + formatted.slice(3)
    }

    // Check if postal code is valid and complete (A1A 1A1)
    const isFullyValidPostalCode = /^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/.test(formatted)

    // Show warning if not fully valid and has at least one character
    if (formatted.length > 0 && !isFullyValidPostalCode) {
      setPostalWarning(t(translationKeys.findAppointment.postalFormatWarning))
    } else {
      setPostalWarning("")
    }

    // Only set error for form validation if the field is filled but invalid
    if (formatted.length > 0 && !isFullyValidPostalCode && formatted.length >= 7) {
      setPostalError(t(translationKeys.findAppointment.invalidPostalFormat))
    } else {
      setPostalError("")
    }

    return formatted
  }

  const handlePostalCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPostalCode(e.target.value)
    setPostalCode(formatted)
  }

  const handleHealthCardLastDigitsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatHealthCardNumber(e.target.value, 8);
    setHealthCardLastDigits(formatted);

    // Clear error when user types
    if (formErrors.healthCardLastDigits && isHealthCardComplete(formatted)) {
      setFormErrors({...formErrors, healthCardLastDigits: ""});
    }
  }

  const handleSequenceNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Keep only digits, max 2
    const onlyDigits = e.target.value.replace(/\D/g, "").substring(0, 2)
    setCardSequenceNumber(onlyDigits)
  }

  // Update selected family member when the id changes
  useEffect(() => {
    if (selectedMember) {
      const member = familyMembers.find(m => m.id.toString() === selectedMember)
      setSelectedFamilyMember(member || null)

      // Reset health card last digits and sequence number when changing person
      setHealthCardLastDigits("")
      setCardSequenceNumber("")

      // Clear related errors
      const newErrors = {...formErrors}
      delete newErrors.healthCardLastDigits
      setFormErrors(newErrors)
    } else {
      setSelectedFamilyMember(null)
    }
  }, [selectedMember, familyMembers])

  // Validate the form and return true if valid
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}

    if (!date) {
      errors.date = t(translationKeys.findAppointment.selectDateError)
    }

    if (!time) {
      errors.time = t(translationKeys.findAppointment.selectTimeError)
    }

    if (!postalCode) {
      errors.postalCode = t(translationKeys.findAppointment.enterPostalError)
    } else if (postalError) {
      errors.postalCode = postalError
    } else if (!/^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/.test(postalCode)) {
      errors.postalCode = t(translationKeys.findAppointment.invalidPostalError)
    }

    if (!selectedMember) {
      errors.selectedMember = t(translationKeys.findAppointment.selectPersonError)
    }

    // Validate health card info if a family member is selected
    if (selectedMember && !isHealthCardComplete(healthCardLastDigits)) {
      errors.healthCardLastDigits = t(translationKeys.findAppointment.healthCardDigitsError);
    }

    if (selectedMember && cardSequenceNumber.length !== 2) {
      errors.cardSequenceNumber = t(translationKeys.findAppointment.sequenceNumberError);
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  async function handleSearch(e: React.FormEvent) {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    setSubmitError('')

    try {
      // Prepare the appointment request data
      const requestData = {
        request_type: 'general_appointment',
        patient_id: selectedFamilyMember?.supabaseId || null,
        request_details: {
          postalCode,
          date: date ? format(date, 'yyyy-MM-dd') : '',
          time,
          healthCardLastDigits: selectedFamilyMember ? healthCardLastDigits : '',
          cardSequenceNumber: selectedFamilyMember ? cardSequenceNumber : '',
          patientName: selectedFamilyMember ? `${selectedFamilyMember.firstName} ${selectedFamilyMember.lastName}` : ''
        }
      }

      // Submit the appointment request
      const result = await createAppointmentRequest(requestData)

      if (result.success) {
        // Set form submitted state to true
        setFormSubmitted(true)
      } else {
        // Check for specific error types
        if (result.errorType === 'no_subscription' || result.errorType === 'authentication_required') {
          setSubmitError(t(translationKeys.findAppointment.noSubscription))
        } else {
          setSubmitError(result.error || 'Failed to submit appointment request')
        }
      }
    } catch (error) {
      console.error('Error submitting appointment request:', error)
      setSubmitError('An unexpected error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const disablePastDates = (date: Date) => {
    return isBefore(date, today)
  }

  // Get valid family members (non-empty first or last name)
  const validFamilyMembers = familyMembers.filter(
    member => member.firstName || member.lastName
  )

  // Button disabled state
  const isFormValid = (): boolean => {
    // Check if postal code matches the A1A 1A1 format
    const isValidPostalCode = /^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/.test(postalCode);

    return !!(
      date &&
      time &&
      isValidPostalCode &&
      selectedMember &&
      (selectedMember ? isHealthCardComplete(healthCardLastDigits) && cardSequenceNumber.length === 2 : true)
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">
            <T keyName={translationKeys.findAppointment.title} />
          </h1>
          <p className="text-muted-foreground">
            <T keyName={translationKeys.findAppointment.description} />
          </p>
        </div>

        <Card>
          {!formSubmitted ? (
            <>
              <CardHeader>
                <CardTitle>
                  <T keyName={translationKeys.findAppointment.searchCriteria} />
                </CardTitle>
                <CardDescription>
                  <T keyName={translationKeys.findAppointment.requiredFields} />
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSearch} className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="family-member">
                      <T keyName={translationKeys.findAppointment.appointmentFor} />
                    </Label>
                    <Select
                      value={selectedMember}
                      onValueChange={(value) => {
                        setSelectedMember(value)
                        if (formErrors.selectedMember) {
                          const newErrors = {...formErrors}
                          delete newErrors.selectedMember
                          setFormErrors(newErrors)
                        }
                      }}
                    >
                      <SelectTrigger className={cn("w-full", formErrors.selectedMember && "border-red-500")}>
                        <SelectValue placeholder={t(translationKeys.findAppointment.selectPerson)} />
                      </SelectTrigger>
                      <SelectContent>
                        {validFamilyMembers.map((member) => (
                          <SelectItem key={member.id} value={member.id.toString()}>
                            <div className="flex items-center gap-2">
                              <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                                <User className="h-3 w-3 text-blue-700" />
                              </div>
                              <span>{member.firstName} {member.lastName}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {formErrors.selectedMember && (
                      <p className="text-xs text-red-500">{formErrors.selectedMember}</p>
                    )}
                    <p className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                      <Info className="h-3 w-3" />
                      <span>
                        {language === "fr" ? (
                          <>
                            Les personnes dans cette liste sont gérées dans la section <Link href="/compte/utilisateurs" className="text-primary hover:underline">"Gérer les utilisateurs du compte"</Link>
                          </>
                        ) : (
                          <>
                            The people listed here are managed in the <Link href="/compte/utilisateurs" className="text-primary hover:underline">"Manage account users"</Link> section
                          </>
                        )}
                      </span>
                    </p>
                  </div>

                  {selectedFamilyMember && (
                    <div className="space-y-4 p-4 border rounded-md bg-muted/40">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                          <User className="h-3 w-3 text-blue-700" />
                        </div>
                        <h3 className="font-medium">
                          <T keyName={translationKeys.findAppointment.healthCardOf} /> {selectedFamilyMember.firstName} {selectedFamilyMember.lastName}
                        </h3>
                      </div>

                      <div className="grid gap-6 sm:grid-cols-2">
                        <div className="space-y-2">
                          <Label htmlFor="health-card-last-digits">
                            <T keyName={translationKeys.findAppointment.lastDigits} />
                          </Label>
                          <div className="flex items-center gap-1">
                            <div className="bg-muted rounded px-2 py-1.5 font-mono text-sm border">
                              {selectedFamilyMember.healthCard}
                            </div>
                            <Input
                              id="health-card-last-digits"
                              placeholder="xxxx-xxxx"
                              value={healthCardLastDigits}
                              onChange={handleHealthCardLastDigitsChange}
                              required={!!selectedMember}
                              className={`flex-grow font-mono ${formErrors.healthCardLastDigits ? "border-red-500" : healthCardLastDigits.length > 0 && healthCardLastDigits.length < 8 ? "border-orange-500" : ""}`}
                            />
                          </div>
                          {formErrors.healthCardLastDigits ? (
                            <p className="text-xs text-red-500">{formErrors.healthCardLastDigits}</p>
                          ) : healthCardLastDigits.length > 0 && healthCardLastDigits.length < 8 && (
                            <p className="text-xs text-orange-500">
                              <T keyName={translationKeys.findAppointment.enterEightDigits} />
                            </p>
                          )}
                          <p className="text-xs text-muted-foreground">
                            <T keyName={translationKeys.findAppointment.format} />
                          </p>
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Label htmlFor="sequence-number">
                              <T keyName={translationKeys.findAppointment.sequenceNumber} />
                            </Label>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-6 w-6 p-0 rounded-md">
                                  <Info className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors" />
                                  <span className="sr-only">
                                    <T keyName={translationKeys.findAppointment.sequenceInfo} />
                                  </span>
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="p-0 max-w-[450px] w-auto border-0 bg-transparent shadow-none">
                                <div className="overflow-hidden rounded-lg border bg-background shadow-lg">
                                  <div className="p-2">
                                    <Image
                                      src="/ramq.png"
                                      alt="Carte RAMQ"
                                      width={400}
                                      height={250}
                                      className="rounded-md"
                                    />
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                          </div>
                          <Input
                            id="sequence-number"
                            placeholder="01-99"
                            value={cardSequenceNumber}
                            onChange={handleSequenceNumberChange}
                            required
                            className={`font-mono ${formErrors.cardSequenceNumber ? "border-red-500" : ""}`}
                          />
                          {formErrors.cardSequenceNumber ? (
                            <p className="text-xs text-red-500">{formErrors.cardSequenceNumber}</p>
                          ) : (
                            <p className="text-xs text-muted-foreground">
                              <T keyName={translationKeys.findAppointment.enterTwoDigits} />
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="space-y-1">
                    <Label htmlFor="postal-code">
                      <T keyName={translationKeys.findAppointment.postalCode} />
                    </Label>
                    <Input
                      id="postal-code"
                      placeholder={t(translationKeys.findAppointment.postalExample)}
                      value={postalCode}
                      onChange={handlePostalCodeChange}
                      required
                      maxLength={7}
                      className={formErrors.postalCode ? "border-red-500" : postalWarning ? "border-orange-500" : ""}
                    />
                    {formErrors.postalCode ? (
                      <p className="text-sm text-red-500 mt-1">{formErrors.postalCode}</p>
                    ) : postalWarning ? (
                      <p className="text-xs text-orange-500">{postalWarning}</p>
                    ) : null}
                    <p className="text-xs text-muted-foreground mt-1">
                      <T keyName={translationKeys.findAppointment.postalCodeDescription} />
                    </p>
                  </div>

                  <div className="grid gap-6 sm:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="date">
                        <T keyName={translationKeys.findAppointment.fromDate} />
                      </Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            id="date"
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !date && "text-muted-foreground",
                              formErrors.date && "border-red-500"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {date ? format(date, 'PPP', { locale: fr }) : t(translationKeys.findAppointment.selectDate)}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={date}
                            onSelect={(date) => {
                              setDate(date)
                              if (formErrors.date) {
                                const newErrors = {...formErrors}
                                delete newErrors.date
                                setFormErrors(newErrors)
                              }
                            }}
                            initialFocus
                            locale={fr}
                            disabled={disablePastDates}
                            fromDate={today}
                          />
                        </PopoverContent>
                      </Popover>
                      {formErrors.date && (
                        <p className="text-xs text-red-500">{formErrors.date}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="time">
                        <T keyName={translationKeys.findAppointment.appointmentTime} />
                      </Label>
                      <Select
                        value={time}
                        onValueChange={(value) => {
                          setTime(value)
                          if (formErrors.time) {
                            const newErrors = {...formErrors}
                            delete newErrors.time
                            setFormErrors(newErrors)
                          }
                        }}
                      >
                        <SelectTrigger className={cn("w-full", formErrors.time && "border-red-500")}>
                          <SelectValue placeholder={t(translationKeys.findAppointment.chooseTime)}>
                            {time ? (
                              <div className="flex items-center">
                                <Clock className="mr-2 h-4 w-4" />
                                {time}
                              </div>
                            ) : t(translationKeys.findAppointment.chooseTime)}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={t(translationKeys.findAppointment.asap)}>
                            {t(translationKeys.findAppointment.asap)}
                          </SelectItem>
                          <SelectItem value={t(translationKeys.findAppointment.morning)}>
                            {t(translationKeys.findAppointment.morning)}
                          </SelectItem>
                          <SelectItem value={t(translationKeys.findAppointment.afternoon)}>
                            {t(translationKeys.findAppointment.afternoon)}
                          </SelectItem>
                          <SelectItem value={t(translationKeys.findAppointment.evening)}>
                            {t(translationKeys.findAppointment.evening)}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      {formErrors.time && (
                        <p className="text-xs text-red-500">{formErrors.time}</p>
                      )}
                    </div>
                  </div>

                  {submitError && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm mb-4">
                      <div dangerouslySetInnerHTML={{ __html: submitError }} />
                    </div>
                  )}

                  <Button
                    type="submit"
                    className="w-full sm:w-auto"
                    size="lg"
                    disabled={!isFormValid() || isSubmitting}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center gap-2">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <T keyName={translationKeys.common.submitting} />
                      </span>
                    ) : (
                      <T keyName={translationKeys.findAppointment.submitRequest} />
                    )}
                  </Button>
                </form>
              </CardContent>
            </>
          ) : (
            <CardContent>
              <div className="p-8 flex flex-col items-center justify-center space-y-4">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h3 className="text-xl font-medium text-center">
                  <T keyName={translationKeys.findAppointment.thankYou} />
                </h3>
                <p className="text-muted-foreground text-center max-w-md">
                  <T keyName={translationKeys.findAppointment.confirmationMessage} />
                </p>
                <div className="flex gap-3 mt-2">
                  <Button
                    variant="secondary"
                    asChild
                  >
                    <Link href="/mes-rendez-vous">
                      <T keyName={translationKeys.findAppointment.viewRequests} />
                    </Link>
                  </Button>
                </div>
                <Button
                  variant="outline"
                  onClick={() => {
                    setFormSubmitted(false)
                    // Reset form fields if needed
                    setDate(undefined)
                    setTime("")
                    setPostalCode("")
                    setSelectedMember("")
                    setHealthCardLastDigits("")
                    setCardSequenceNumber("")
                    setFormErrors({})
                  }}
                  className="mt-4"
                >
                  <T keyName={translationKeys.common.newRequest} />
                </Button>
              </div>
            </CardContent>
          )}
        </Card>
      </div>
    </DashboardLayout>
  )
}
