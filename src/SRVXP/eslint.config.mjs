import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    ignores: [
      // Ignore all files in the .next directory
      ".next/**",
      // Ignore other build/generated files
      "node_modules/**",
      "out/**",
      "dist/**",
      // Specifically ignore problematic files
      ".next/static/chunks/polyfills.js",
      ".next/static/chunks/webpack-runtime.js"
    ]
  },
  {
    rules: {
      "@typescript-eslint/no-unused-vars": "off",
      "react/no-unescaped-entities": "off",
      // Allow certain unused expressions in specific contexts
      "@typescript-eslint/no-unused-expressions": ["error", {
        "allowShortCircuit": true,
        "allowTernary": true,
        "allowTaggedTemplates": true
      }]
    },
  },
];

export default eslintConfig;
