#!/usr/bin/env node

/**
 * Test script to simulate new user OAuth flow
 * This script will test the new user registration flow with our fixes
 */

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://tfvswgreslsbctjrvdbd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRmdnN3Z3Jlc2xzYmN0anJ2ZGJkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIzMjEyODUsImV4cCI6MjA1Nzg5NzI4NX0.5bNos8JS_77EUjWOxIGHpgNaYE28qQvO2g4qj0wRWiU';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testNewUserOAuthFlow() {
  console.log('🧪 Testing New User OAuth Flow...\n');

  try {
    // Test the OAuth URL generation with test flag
    console.log('1. Testing OAuth URL generation with new user test flag...');
    
    const callbackUrl = 'http://localhost:3002/auth/callback?redirectTo=/dashboard&test_new_user=true';
    console.log('Callback URL:', callbackUrl);
    
    // This would normally redirect to Google, but we can test the URL structure
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: callbackUrl
      }
    });

    if (error) {
      console.log('❌ OAuth URL generation failed:', error.message);
      return;
    }

    console.log('✅ OAuth URL generation successful');
    console.log('OAuth URL:', data.url);
    
    // Check if the callback URL is properly included
    if (data.url && data.url.includes('test_new_user=true')) {
      console.log('✅ Test flag properly included in OAuth flow');
    } else {
      console.log('⚠️ Test flag may not be properly included');
    }

    console.log('\n📋 Next Steps:');
    console.log('1. Open the test page: http://localhost:3002/test-oauth');
    console.log('2. Click "Test New User Flow" button');
    console.log('3. Complete Google OAuth');
    console.log('4. Check server logs for "NEW USER REGISTRATION" message');
    console.log('5. Verify immediate authentication without re-login');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testNewUserOAuthFlow().then(() => {
  console.log('\n🏁 New user OAuth flow test completed');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Test script failed:', error);
  process.exit(1);
});
