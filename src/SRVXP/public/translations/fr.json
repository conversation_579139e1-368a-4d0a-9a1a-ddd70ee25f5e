{"auth.signIn": "Connexion à votre compte", "auth.signUp": "<PERSON><PERSON><PERSON> votre compte", "auth.email": "<PERSON><PERSON><PERSON>", "auth.password": "Mot de passe", "auth.confirmPassword": "Confirmer le mot de passe", "auth.forgotPassword": "Mot de passe oublié ?", "auth.resetPassword": "Réinitialisation du mot de passe", "auth.enterCredentials": "Entrez vos identifiants pour vous connecter", "auth.createAccount": "<PERSON><PERSON><PERSON> votre compte", "auth.alreadyHaveAccount": "Vous avez déjà un compte ?", "auth.noAccount": "Vous n'avez pas de compte ?", "auth.passwordReset": "Réinitialisation du mot de passe", "auth.enterEmailForReset": "Saisissez l'adresse de courriel associée à votre compte, et nous vous enverrons un lien pour réinitialiser votre mot de passe.", "auth.passwordResetSent": "Un e-mail de réinitialisation a été envoyé", "auth.sendResetEmail": "<PERSON><PERSON><PERSON>", "auth.backToSignIn": "Revenir à la page de connexion", "auth.successfulReset": "Votre mot de passe a été réinitialisé avec succès", "auth.firstName": "Prénom", "auth.lastName": "Nom", "auth.createPassword": "Mot de passe", "auth.passwordHint": "Le mot de passe doit comprendre au moins 8 caractères avec des lettres et des chiffres", "auth.agreeTerms": "J'accepte les", "auth.termsAndConditions": "conditions d'utilisation", "auth.enterDetails": "Entrez vos informations pour créer un compte", "auth.confirmEmail": "Veuillez vérifier votre e-mail pour activer votre compte", "auth.emailInUse": "Cet e-mail est déjà associé à un compte. Connectez-vous ou réinitialisez votre mot de passe", "auth.sidebarTitle": "Trouvez des rendez-vous médicaux rapidement", "auth.sidebarDescription": "Sans rendez-vous express vous aide à trouver des rendez-vous médicaux dans des cliniques sans rendez-vous près de chez vous sous 24 à 48 heures.", "auth.benefit1": "Accès à des rendez-vous d'urgence le jour même", "auth.benefit2": "Recherche de cliniques dans un rayon de 50 km", "auth.benefit3": "Service disponible partout au Québec", "auth.errors.invalidCredentials": "Email ou mot de passe incorrect", "auth.errors.passwordsDontMatch": "Les mots de passe ne correspondent pas", "auth.errors.emailInUse": "Cet email est déjà utilisé", "auth.errors.invalidEmail": "Format d'email invalide", "auth.errors.passwordTooShort": "Le mot de passe doit faire au moins 8 caractères", "auth.errors.generalError": "Une erreur est survenue. Veuillez réessayer.", "auth.errors.agreeTerms": "<PERSON><PERSON> accepter les conditions d'utilisation", "auth.errors.generic": "Une erreur est survenue lors de l'inscription", "auth.continueWithGoogle": "Continuer avec Google", "auth.orContinueWith": "Ou", "auth.errors.socialAuthFailed": "L'authentification avec le fournisseur social a échoué. Veuillez réessayer.", "common.save": "<PERSON><PERSON><PERSON><PERSON>", "common.cancel": "Annuler", "common.confirm": "Confirmer", "common.delete": "<PERSON><PERSON><PERSON><PERSON>", "common.loading": "Chargement", "common.search": "<PERSON><PERSON><PERSON>", "common.logout": "Se déconnecter", "common.loggingOut": "Déconnexion...", "common.close": "<PERSON><PERSON><PERSON> le menu", "common.active": "Actif", "common.inactive": "Inactif", "common.submit": "Envoyer", "common.submitting": "Envoi en cours...", "common.processing": "Traitement en cours...", "common.newRequest": "Nouvelle demande", "common.required": "obligatoire", "common.yes": "O<PERSON>", "common.no": "Non", "common.continue": "continuer", "common.manage": "<PERSON><PERSON><PERSON>", "common.modify": "Modifier", "common.back": "Retour", "common.saved": "Enregistré", "common.saveChanges": "Enregistrer les modifications", "nav.home": "Accueil", "nav.dashboard": "Tableau de bord", "nav.appointments": "Mes rendez-vous", "nav.findAppointment": "Trouver un rendez-vous", "nav.calendar": "<PERSON><PERSON><PERSON>", "nav.help": "Aide", "nav.account": "<PERSON><PERSON><PERSON>", "nav.mobileNavigation": "Navigation mobile", "nav.needHelp": "Besoin d'aide?", "account.profile": "Profil", "account.preferences": "Préférences", "account.subscription": "Abonnement", "account.users": "Utilisateurs", "account.manageUsers": "<PERSON><PERSON><PERSON> les utilisateurs", "account.yourAccount": "Votre compte", "account.individualPlan": "Plan individuel", "account.individualPlanMonthly": "Plan individuel - 7,95$ / mois", "account.individualPlanAnnual": "Plan individuel annuel", "account.familyPlan": "Plan familial", "account.familyPlanMonthly": "Plan familial - 14,95$ / mois", "account.familyPlanAnnual": "Plan familial annuel", "account.manageInformation": "Gérer vos renseignements", "account.personalInfoDescription": "Modifiez vos informations personnelles et coordonnées", "account.modifyProfile": "Modifier vos renseignements", "account.modifySubscription": "Modifier votre abonnement", "account.subscribePlan": "Abonnez-vous", "account.choosePlan": "Choisir un forfait", "account.subscriptionDescription": "Changez de plan ou modifiez vos informations de paiement", "account.manageSubscription": "<PERSON><PERSON>rer l'abonnement", "account.appearanceLanguage": "Langue de communication", "account.appearanceDescription": "Personnaliser la langue de votre portail client", "account.modifyPreferences": "Modifier les préférences", "account.manageAccountUsers": "<PERSON><PERSON><PERSON> les utilisateurs du compte", "account.manageUsersDescription": "<PERSON><PERSON><PERSON><PERSON> ou modifiez les membres de votre famille sur votre compte", "account.manageProfile": "<PERSON><PERSON><PERSON> votre profil", "account.manageProfileDescription": "Consultez ou mettez à jour votre carte d'assurance maladie", "account.manageProfileButton": "<PERSON><PERSON><PERSON> le profil", "account.editPersonalInfo": "Modifiez vos informations personnelles", "account.firstName": "Prénom", "account.lastName": "Nom de famille", "account.email": "<PERSON><PERSON><PERSON>", "account.phone": "Téléphone", "account.emailCannotBeEmpty": "Le courriel doit être ajouté", "account.invalidEmail": "Le courriel que vous avez saisi est invalide", "account.firstNameRequired": "Veuillez saisir votre prénom", "account.lastNameRequired": "Veuillez saisir votre nom de famille", "home.greeting": "<PERSON><PERSON><PERSON>, {{firstName}}!", "home.welcome": "Bienvenue dans votre tableau de bord", "home.findAppointmentTitle": "Trouver un rendez-vous", "home.findAppointmentDesc": "Trouvez un rendez-vous médical près de chez vous", "home.viewRequests": "Voir les demandes", "home.manageAppointmentsDesc": "Consultez et gérez vos demandes de rendez-vous médicaux", "home.manageUsersDesc": "<PERSON><PERSON><PERSON><PERSON> ou modifiez les membres de votre famille sur votre compte", "home.manageProfileTitle": "<PERSON><PERSON><PERSON> votre profil", "home.manageProfileDesc": "Ajoutez les renseignements de votre carte d'assurance maladie", "home.manageProfileButton": "<PERSON><PERSON><PERSON> le profil", "help.needHelp": "Besoin d'aide?", "help.helpDescription": "Obtenez de l'aide pour l'utilisation de notre plateforme", "help.faq": "Questions fréquentes", "help.faqDescription": "Consultez nos réponses aux questions fréquemment posées", "help.howToBookAppointment": "Comment prendre un rendez-vous?", "help.howToBookDescription": "Cliquez sur \"Trouver un rendez-vous\" dans le menu, puis entrez votre code postal et vos disponibilités pour voir les options près de chez vous.", "help.howToCancelAppointment": "Comment annuler un rendez-vous?", "help.howToCancelDescription": "Sur la page \"Mes rendez-vous\", trouvez votre rendez-vous et cliquez sur le bouton \"Annuler\".", "help.howToChangePlan": "Comment changer mon forfait?", "help.howToChangePlanDescription": "Accédez à la page \"Votre compte\" et cliquez sur \"Gérer l'abonnement\" pour voir les options de forfait disponibles.", "help.customerSupport": "Support client", "help.supportDescription": "Notre équipe est disponible pour vous aider", "help.email": "<PERSON><PERSON><PERSON>", "help.supportEmail": "<EMAIL>", "help.responseTime": "Nous répondons dans les 24 heures", "help.contactSupport": "<PERSON>er le support", "preferences.managePreferences": "Gérer vos préférences", "preferences.languageAppearance": "Langue de communication", "preferences.customizeInterface": "Personnaliser la langue de votre portail client", "preferences.preferredLanguage": "Langue préférée", "preferences.french": "Français", "preferences.english": "English", "preferences.languageDescription": "Cette préférence s'applique à l'interface du portail et aux courriels que vous recevez de notre part.", "preferences.appTheme": "Thème de l'application", "preferences.light": "<PERSON>", "preferences.dark": "Sombre", "preferences.themeDescription": "Choisis<PERSON>z le thème visuel qui vous convient le mieux.", "preferences.saveChanges": "Sauvegarder les changements", "preferences.saving": "Enregistrement...", "preferences.changesSaved": "Vos préférences ont été enregistrées avec succès.", "preferences.errorSaving": "Une erreur est survenue lors de l'enregistrement de vos préférences.", "users.manageAccountUsers": "<PERSON><PERSON><PERSON> les utilisateurs du compte", "users.manageProfile": "<PERSON><PERSON><PERSON> votre profil", "users.familyMembers": "Membres de la famille", "users.userProfile": "Votre profil d'utilisateur", "users.familyMembersDescription": "<PERSON><PERSON><PERSON> jusqu'à 5 profils pour votre famille. Ajoutez les renseignements de leur carte d'assurance maladie pour faciliter la prise de rendez-vous.", "users.userProfileDescription": "Ajoutez les renseignements de votre carte d'assurance maladie pour faciliter la prise de rendez-vous.", "users.addYourInfo": "Ajouter vos renseignements", "users.addYourInfoPrompt": "Cliquez sur l'icône de modification pour ajouter vos renseignements.", "users.cancel": "Annuler", "users.firstName": "Prénom", "users.lastName": "Nom", "users.healthCardPrefix": "4 premiers caractères de la carte d'assurance maladie", "users.healthCardDescription": "Ces 4 caractères seront utilisés pour la prise de rendez-vous", "users.birthDate": "Date de naissance", "users.save": "<PERSON><PERSON><PERSON><PERSON>", "users.edit": "Modifier", "users.healthCard": "Carte d'assurance maladie:", "users.addMember": "Ajouter un membre", "users.editMemberPrompt": "Cliquez sur l'icône de modification pour ajouter les informations de ce membre.", "users.selectDate": "Sélectionnez une date", "users.validationError": "Veuillez saisir le prénom, le nom, les 4 caractères de la carte et la date de naissance pour sauvegarder.", "subscription.modifySubscription": "Modifier votre abonnement", "subscription.individualPlan": "Plan individuel", "subscription.monthlyCost": "7,95$ / mois", "subscription.benefits": "Ce que vous obtenez:", "subscription.unlimitedAccess": "Accès illimité à la recherche de rendez-vous", "subscription.emailNotifications": "Notifications par courriel pour vos rendez-vous", "subscription.familyProfiles": "Gestion de jusqu'à 5 profils pour votre famille", "subscription.modifyPlan": "Modifier l'abonnement", "subscription.cancelPlan": "Annuler l'abonnement", "subscription.paymentHistory": "Historique de paiement", "subscription.monthlySubscription": "Abonnement mensuel", "subscription.march": "14 mars 2025", "subscription.february": "14 février 2025", "subscription.january": "14 janvier 2025", "subscription.cost": "7,95$", "subscription.changePlan": "Changer de plan d'abonnement", "subscription.changePlanDescription": "Vous êtes actuellement sur le plan individuel à 7,95$ par mois. En confirmant, vous passerez au plan familial à 14,95$ par mois à partir du prochain cycle de facturation.", "subscription.confirmChange": "Confirmer le changement", "subscription.cancelConfirmation": "Êtes-vous certain de vouloir annuler votre abonnement?", "subscription.cancelWarning": "En annulant votre abonnement, vous perdrez l'accès à tous les services premium à la fin de votre période de facturation.", "subscription.yesCancel": "<PERSON><PERSON>, je veux annuler", "subscription.noCancel": "Non, pas maintenant", "subscription.status": "Statut de l'abonnement", "subscription.verifyingPayment": "Vérification de votre paiement d'abonnement", "subscription.success": "Abonnement réussi !", "subscription.successMessage": "Votre abonnement a été activé avec succès.", "subscription.details": "Détails de l'abonnement", "subscription.plan": "Forfait", "subscription.billing": "Facturation", "subscription.amount": "<PERSON><PERSON>", "subscription.currentPeriod": "Période actuelle", "subscription.nextSteps": "Que souhaitez-vous faire maintenant ?", "subscription.goToDashboard": "<PERSON>er au tableau de bord", "subscription.manageAccount": "<PERSON><PERSON><PERSON> le compte", "subscription.error": "Une erreur est survenue", "subscription.errorMessage": "Nous n'avons pas pu vérifier votre abonnement. Veuillez réessayer.", "subscription.needHelp": "Besoin d'aide ?", "subscription.returnToPlans": "Retourner aux forfaits", "subscription.contactSupport": "<PERSON>er le support", "subscription.canceledCheckout": "Vous avez annulé le processus de paiement. Votre abonnement n'a pas été activé.", "subscription.processingSubscription": "Votre abonnement est en cours de traitement. Veuillez vérifier à nouveau dans quelques minutes.", "subscription.noSessionId": "Aucun identifiant de session trouvé. Veuillez réessayer.", "subscription.notLoggedIn": "Vous devez être connecté pour vérifier votre abonnement.", "appointments.title": "Mes rendez-vous", "appointments.description": "<PERSON><PERSON><PERSON> vos demandes de rendez-vous médicaux", "appointments.requestsTitle": "De<PERSON><PERSON> de rendez-vous", "appointments.all": "Tous", "appointments.inProgress": "En cours", "appointments.completed": "Complétés", "appointments.cancelled": "<PERSON><PERSON><PERSON>", "appointments.noRequests": "Aucune demande de rendez-vous", "appointments.noRequestsInProgress": "Vous n'avez aucune demande de rendez-vous en cours", "appointments.noRequestsCompleted": "Vous n'avez aucune demande de rendez-vous complétée", "appointments.noRequestsCancelled": "Vous n'avez aucune demande de rendez-vous annulée", "appointments.postalCode": "Code postal", "appointments.sentOn": "<PERSON><PERSON><PERSON>", "appointments.pending": "En attente", "appointments.done": "<PERSON><PERSON><PERSON><PERSON>", "appointments.cancelAppointment": "Annuler", "appointments.cancelConfirmation": "Confirmation d'annulation", "appointments.cancelConfirmationText": "Êtes-vous certain de vouloir annuler cette demande de rendez-vous?", "appointments.noContinue": "Non, continuer", "appointments.yesCancel": "<PERSON><PERSON>, annuler", "appointments.viewAll": "Voir tous les rendez-vous", "findAppointment.title": "Trouver un rendez-vous", "findAppointment.description": "Recherchez un rendez-vous médical près de chez vous", "findAppointment.searchCriteria": "Entrez vos critères de recherche", "findAppointment.requiredFields": "Tous les champs marqués d'un astérisque (*) sont obligatoires", "findAppointment.appointmentFor": "Le rendez-vous est pour *", "findAppointment.selectPerson": "Sélectionnez une personne", "findAppointment.managedInUsersSection": "Les personnes dans cette liste sont gérées dans la section \"G<PERSON>rer les utilisateurs du compte\"", "findAppointment.healthCard": "Carte d'assurance maladie", "findAppointment.healthCardOf": "Carte d'assurance maladie de", "findAppointment.lastDigits": "8 derniers caractères du numéro d'assurance maladie *", "findAppointment.enterEightDigits": "Veuillez entrer les 8 chiffres complets", "findAppointment.format": "Format: xxxx-xxxx (8 chiffres au total)", "findAppointment.sequenceNumber": "Numéro séquentiel de la carte d'assurance maladie *", "findAppointment.sequenceInfo": "Afficher information sur le numéro séquentiel", "findAppointment.enterTwoDigits": "<PERSON>eu<PERSON>z entrer deux chiffres", "findAppointment.postalCode": "Code postal *", "findAppointment.postalExample": "ex: K1A 0L8", "findAppointment.invalidPostalFormat": "Format du code postal invalide. Utilisez le format 'A1A 1A1'", "findAppointment.postalFormatWarning": "Veuillez saisir un code postal valide (format : A1A 1A1)", "findAppointment.postalCodeDescription": "Insc<PERSON>z le vôtre ou celui près duquel vous souhaitez trouver un rendez-vous", "findAppointment.fromDate": "À partir de cette date *", "findAppointment.selectDate": "Sélectionnez une date", "findAppointment.appointmentTime": "Moment du rendez-vous *", "findAppointment.chooseTime": "Choisissez un moment", "findAppointment.morning": "<PERSON><PERSON><PERSON>", "findAppointment.afternoon": "Après-midi", "findAppointment.evening": "Soirée", "findAppointment.asap": "Dès que possible", "findAppointment.submitRequest": "Envoyer la demande", "findAppointment.thankYou": "Merci! Nous avons acheminé votre demande de rendez-vous.", "findAppointment.confirmationMessage": "Vous recevrez les informations concernant votre rendez-vous dès sa confirmation.", "findAppointment.viewRequests": "Voir mes demandes", "findAppointment.selectDateError": "<PERSON><PERSON><PERSON>z sélectionner une date", "findAppointment.selectTimeError": "<PERSON><PERSON><PERSON>z sélectionner un moment", "findAppointment.enterPostalError": "Veuillez entrer un code postal", "findAppointment.invalidPostalError": "Le code postal doit être au format A1A 1A1", "findAppointment.selectPersonError": "<PERSON><PERSON><PERSON>z sélectionner une personne", "findAppointment.healthCardDigitsError": "Veuillez entrer les 8 chiffres complets", "findAppointment.sequenceNumberError": "<PERSON>eu<PERSON>z entrer deux chiffres", "findAppointment.noSubscription": "Aucun abonnement. <a href='/pricing' class='text-blue-600 hover:underline'>Cliquez ici pour acheter un forfait.</a>", "landing.hero.title": "Obtenez un rendez-vous avec un médecin dès aujourd'hui.", "landing.hero.subtitle": "Simplifiez votre accès à des soins de santé. Sans rendez-vous express vous aide à trouver des rendez-vous médicaux dans des cliniques au Québec.", "landing.hero.findAppointment": "Trouver un rendez-vous", "landing.hero.learnMore": "En savoir plus", "landing.hero.imageAlt": "Interface de rendez-vous médicaux", "landing.features.sameDay.title": "Des rendez-vous le jour même", "landing.features.sameDay.description": "Notre service vous aide à trouver un rendez-vous dans une clinique pour le jour même ou d'ici 24 à 48 heures en moyenne.", "landing.features.nearbyClinic.title": "Des cliniques à proximité", "landing.features.nearbyClinic.description": "Obtenez des rendez-vous à proximité, ou étendez votre recherche jusqu'à 50 km si vous avez la possibilité de vous déplacer.", "landing.features.anywhereInQuebec.title": "Disponible partout au Québec", "landing.features.anywhereInQuebec.description": "Où que vous soyez au Québec, notre service est accessible partout pour répondre à vos besoins.", "landing.howItWorks.title": "Soins médicaux sans attente", "landing.howItWorks.customAppointments.title": "Des rendez-vous selon vos disponibilités", "landing.howItWorks.customAppointments.description": "Choisissez une heure et un lieu qui vous conviennent pour consulter un médecin.", "landing.howItWorks.easyManagement.title": "<PERSON><PERSON><PERSON> facilement vos rendez-vous", "landing.howItWorks.easyManagement.description": "<PERSON><PERSON><PERSON><PERSON>, modifiez ou annulez vos rendez-vous médicaux en quelques clics, sans appel téléphonique ni attente.", "landing.howItWorks.imageAlt": "Comment ça fonctionne", "landing.pricing.title": "Forfaits abordables", "landing.pricing.description": "Notre service de prise de rendez-vous médicaux est conçu pour tous les besoins. Choisissez le forfait qui vous convient le mieux selon vos besoins.", "landing.pricing.period.monthly": "<PERSON><PERSON><PERSON>", "landing.pricing.period.annually": "<PERSON><PERSON>", "landing.pricing.individual.title": "Plan individuel", "landing.pricing.individual.description": "Idéal pour les personnes ayant besoin de consultations médicales occasionnelles.", "landing.pricing.individual.features": "Réservations illimitées pour une personne", "landing.pricing.individual.annualSavings": "Économisez 23$ par année", "landing.pricing.family.title": "Plan familial", "landing.pricing.family.description": "Pour les familles qui doivent prendre de nombreux rendez-vous médicaux.", "landing.pricing.family.features": "Réservations illimitées pour 5 personnes", "landing.pricing.family.annualSavings": "Économisez 45$ par année", "landing.pricing.choosePlan": "Choisir ce forfait", "landing.pricing.manageSubscription": "<PERSON><PERSON>rer l'abonnement", "landing.pricing.included": "Ce qui est inclus:", "landing.pricing.feature1": "Réservations illimitées pour une personne", "landing.pricing.feature2": "Recherche de rendez-vous rapide", "landing.pricing.feature3": "Notifications par courriel", "landing.pricing.feature4": "Support prioritaire", "landing.faq.title": "Questions courantes", "landing.faq.viewFullFaq": "Consulter la FAQ", "landing.faq.questions.0.question": "Qu'est-ce que Sans rendez-vous express propose exactement?", "landing.faq.questions.0.answer": "Sans rendez-vous express est un service qui vous aide à trouver rapidement des rendez-vous médicaux dans des cliniques sans rendez-vous près de chez vous. Notre plateforme connecte les patients avec des médecins disponibles pour des consultations d'ici 24 à 48 heures.", "landing.faq.questions.1.question": "Quel est le délai d'attente pour obtenir un rendez-vous?", "landing.faq.questions.1.answer": "Chez Sans rendez-vous express, nous nous efforçons de trouver un rendez-vous avec un médecin le plus rapidement possible pour tous nos utilisateurs. En général, la plupart des rendez-vous sont trouvés en moins de 48 heures. Pour les grandes municipalités, la majorité des rendez-vous sont trouvés en moins de 24 heures.", "landing.faq.questions.2.question": "Quelles sont les informations nécessaires pour prendre un rendez-vous?", "landing.faq.questions.2.answer": "Pour prendre un rendez-vous, vous devrez fournir vos informations de base (nom, prénom, date de naissance), vos coordonnées (téléphone, email) et votre numéro d'assurance maladie. Ces informations nous permettent de vous trouver le rendez-vous le plus approprié.", "landing.faq.questions.3.question": "Comment est-ce que les rendez-vous sont réservés?", "landing.faq.questions.3.answer": "Après avoir soumis votre demande, notre système recherche automatiquement les disponibilités dans les cliniques participantes selon vos critères. Une fois qu'un rendez-vous correspondant est trouvé, vous recevez une confirmation par email et/ou SMS avec tous les détails du rendez-vous.", "landing.faq.questions.4.question": "Qui peut utiliser ce service?", "landing.faq.questions.4.answer": "Notre service est accessible à tous les résidents du Québec possédant une carte d'assurance maladie valide. Nous servons les adultes et les enfants, que ce soit pour des problèmes de santé ponctuels ou des suivis médicaux réguliers.", "landing.cta.title": "Ne tardez pas à consulter un médecin.", "landing.cta.subtitle": "Laissez-nous vous trouvez un rendez-vous médical aujourd'hui.", "landing.cta.buttonText": "<PERSON><PERSON><PERSON> un rendez-vous", "landing.cta.imageAlt": "Médecin et patient", "landing.navbar.title": "Sans rendez-vous express", "landing.navbar.signIn": "Connexion", "landing.navbar.signUp": "S'inscrire", "landing.navbar.service": "Le service", "landing.navbar.pricing": "<PERSON><PERSON><PERSON>", "landing.navbar.faq": "FAQ", "landing.footer.description": "Sans rendez-vous express est un service indépendant qui aide les gens à trouver des rendez-vous médicaux rapidement au Québec.", "landing.footer.contactUs": "Contactez-nous", "landing.footer.privacyPolicy": "Politique de confidentialité", "landing.footer.termsOfUse": "Conditions d'utilisation", "landing.footer.termsOfSale": "Conditions générales de vente", "landing.footer.copyright": "© 2025 Sans rendez-vous express. Tous droits réservés."}