{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/account/subscription/success", "regex": "^/account/subscription/success(?:/)?$", "routeKeys": {}, "namedRegex": "^/account/subscription/success(?:/)?$"}, {"page": "/aide", "regex": "^/aide(?:/)?$", "routeKeys": {}, "namedRegex": "^/aide(?:/)?$"}, {"page": "/auth/callback", "regex": "^/auth/callback(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/callback(?:/)?$"}, {"page": "/auth/forgot-password", "regex": "^/auth/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/forgot\\-password(?:/)?$"}, {"page": "/auth/sign-in", "regex": "^/auth/sign\\-in(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/sign\\-in(?:/)?$"}, {"page": "/auth/sign-up", "regex": "^/auth/sign\\-up(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/sign\\-up(?:/)?$"}, {"page": "/compte", "regex": "^/compte(?:/)?$", "routeKeys": {}, "namedRegex": "^/compte(?:/)?$"}, {"page": "/compte/abonnement", "regex": "^/compte/abonnement(?:/)?$", "routeKeys": {}, "namedRegex": "^/compte/abonnement(?:/)?$"}, {"page": "/compte/preferences", "regex": "^/compte/preferences(?:/)?$", "routeKeys": {}, "namedRegex": "^/compte/preferences(?:/)?$"}, {"page": "/compte/profile", "regex": "^/compte/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/compte/profile(?:/)?$"}, {"page": "/compte/utilisateurs", "regex": "^/compte/utilisateurs(?:/)?$", "routeKeys": {}, "namedRegex": "^/compte/utilisateurs(?:/)?$"}, {"page": "/conditions-generales-de-vente", "regex": "^/conditions\\-generales\\-de\\-vente(?:/)?$", "routeKeys": {}, "namedRegex": "^/conditions\\-generales\\-de\\-vente(?:/)?$"}, {"page": "/conditions-generales-de-vente-EN", "regex": "^/conditions\\-generales\\-de\\-vente\\-EN(?:/)?$", "routeKeys": {}, "namedRegex": "^/conditions\\-generales\\-de\\-vente\\-EN(?:/)?$"}, {"page": "/conditions-utilisation", "regex": "^/conditions\\-utilisation(?:/)?$", "routeKeys": {}, "namedRegex": "^/conditions\\-utilisation(?:/)?$"}, {"page": "/conditions-utilisation-EN", "regex": "^/conditions\\-utilisation\\-EN(?:/)?$", "routeKeys": {}, "namedRegex": "^/conditions\\-utilisation\\-EN(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/foire-aux-questions", "regex": "^/foire\\-aux\\-questions(?:/)?$", "routeKeys": {}, "namedRegex": "^/foire\\-aux\\-questions(?:/)?$"}, {"page": "/foire-aux-questions-EN", "regex": "^/foire\\-aux\\-questions\\-EN(?:/)?$", "routeKeys": {}, "namedRegex": "^/foire\\-aux\\-questions\\-EN(?:/)?$"}, {"page": "/mes-rendez-vous", "regex": "^/mes\\-rendez\\-vous(?:/)?$", "routeKeys": {}, "namedRegex": "^/mes\\-rendez\\-vous(?:/)?$"}, {"page": "/politique-de-confidentialite", "regex": "^/politique\\-de\\-confidentialite(?:/)?$", "routeKeys": {}, "namedRegex": "^/politique\\-de\\-confidentialite(?:/)?$"}, {"page": "/politique-de-confidentialite-EN", "regex": "^/politique\\-de\\-confidentialite\\-EN(?:/)?$", "routeKeys": {}, "namedRegex": "^/politique\\-de\\-confidentialite\\-EN(?:/)?$"}, {"page": "/pricing", "regex": "^/pricing(?:/)?$", "routeKeys": {}, "namedRegex": "^/pricing(?:/)?$"}, {"page": "/trouver-rendez-vous", "regex": "^/trouver\\-rendez\\-vous(?:/)?$", "routeKeys": {}, "namedRegex": "^/trouver\\-rendez\\-vous(?:/)?$"}, {"page": "/zaply-landing", "regex": "^/zaply\\-landing(?:/)?$", "routeKeys": {}, "namedRegex": "^/zaply\\-landing(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}