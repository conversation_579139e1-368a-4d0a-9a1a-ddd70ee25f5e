"use strict";exports.id=411,exports.ids=[411],exports.modules={60374:(e,t,a)=>{a.d(t,{yk:()=>_});var n=a(30547),r=a(44e3),s=a(70292),o=a(80411),i=a(54386),l=a(64782);let c={profile:null,isLoading:!1,isSaving:!1,error:null,saveError:null,saveSuccess:!1,lastFetched:null},p=(0,r.L)((0,n.v)()((0,o.Zr)((e,t)=>({...c,fetchProfile:async a=>{try{e({isLoading:!0,error:null});let{profile:n,lastFetched:r}=t(),s=Date.now();if(n&&r&&s-r<3e5)return e({isLoading:!1}),n;let{data:o,error:l}=await i.N.auth.getUser();if(l)throw l;let c=null;try{let{data:e,error:t}=await i.N.from("users").select("*").eq("id",a).single();t||(c=e)}catch(e){console.warn("Error getting public user data:",e)}let p=o.user.user_metadata||{},g=p.first_name||"",u=p.last_name||"";g||(g=c?.first_name||""),u||(u=c?.last_name||"");let m=`${g} ${u}`.trim()||"User",d=`${g.charAt(0)}${u.charAt(0)}`.toUpperCase()||"??",f={firstName:g,lastName:u,email:o.user.email||"",phone:p.phone||"",avatar:null,initials:d,fullName:m};return e({profile:f,isLoading:!1,lastFetched:Date.now()}),f}catch(t){return e({isLoading:!1,error:t instanceof Error?t:Error("Unknown error fetching profile")}),null}},updateProfile:async(a,n)=>{try{e({isSaving:!0,saveError:null,saveSuccess:!1});let r=s.kH.getState().session,o={},c=!1;if(void 0!==n.firstName&&(o.first_name=n.firstName,c=!0),void 0!==n.lastName&&(o.last_name=n.lastName,c=!0),void 0!==n.phone&&(o.phone=n.phone,c=!0),c){let{error:e}=await i.N.auth.updateUser({data:o});if(e)throw Error(`Error updating user metadata: ${e.message}`)}if(void 0!==n.email&&r?.user?.email!==n.email){let{error:e}=await i.N.auth.updateUser({email:n.email});if(e)throw Error(`Error updating email: ${e.message}`)}try{let{data:e,error:t}=await i.N.from("users").select("id").eq("id",a).maybeSingle();if(t&&!t.message.includes("does not exist")&&console.warn(`Error checking users table: ${t.message}`),e){let e={};if(void 0!==n.firstName&&(e.first_name=n.firstName),void 0!==n.lastName&&(e.last_name=n.lastName),void 0!==n.phone&&(e.phone=n.phone),Object.keys(e).length>0){e.updated_at=new Date().toISOString();let{error:t}=await i.N.from("users").update(e).eq("id",a);t&&console.warn(`Error updating users table: ${t.message}`)}}}catch(e){console.warn("Error updating users table:",e)}let p=t().profile;if(p){let t={...p,firstName:void 0!==n.firstName?n.firstName:p.firstName,lastName:void 0!==n.lastName?n.lastName:p.lastName,email:void 0!==n.email?n.email:p.email,phone:void 0!==n.phone?n.phone:p.phone,fullName:`${n.firstName||p.firstName} ${n.lastName||p.lastName}`.trim(),initials:`${(n.firstName||p.firstName).charAt(0)}${(n.lastName||p.lastName).charAt(0)}`.toUpperCase()};e({profile:t})}return(0,l.gm)("user-profile-store"),e({isSaving:!1,saveSuccess:!0}),setTimeout(()=>{e({saveSuccess:!1})},3e3),!0}catch(t){return e({isSaving:!1,saveError:t instanceof Error?t:Error("Unknown error updating profile"),saveSuccess:!1}),!1}},setProfile:t=>e({profile:t}),setIsLoading:t=>e({isLoading:t}),setIsSaving:t=>e({isSaving:t}),setError:t=>e({error:t}),setSaveError:t=>e({saveError:t}),setSaveSuccess:t=>e({saveSuccess:t}),clearErrors:()=>e({error:null,saveError:null}),reset:()=>e(c)}),(0,l.LX)("user-profile-store",{storage:(0,o.KU)(()=>(0,l.oV)()),partialize:e=>({profile:e.profile,lastFetched:e.lastFetched}),version:1,onRehydrateStorage:()=>e=>{if(e?.profile&&e.lastFetched&&Date.now()-e.lastFetched>3e5){let e=s.kH.getState().user?.id;e&&setTimeout(()=>{p.getState().fetchProfile(e)},0)}}})))),g={theme:"light",language:"fr"},u={preferences:g,isLoading:!1,isSaving:!1,error:null,saveError:null,saveSuccess:!1,lastFetched:null},m=(0,r.L)((0,n.v)()((0,o.Zr)((e,t)=>({...u,fetchPreferences:async a=>{try{e({isLoading:!0,error:null});let{preferences:n,lastFetched:r}=t(),s=Date.now();if(r&&s-r<18e5)return e({isLoading:!1}),n;let{data:o,error:l}=await i.N.from("users").select("language, theme").eq("id",a).maybeSingle();if(l)throw l;let c={language:o?.language||g.language,theme:o?.theme||g.theme};return e({preferences:c,isLoading:!1,lastFetched:Date.now()}),c}catch(t){return e({isLoading:!1,error:t instanceof Error?t:Error("Unknown error fetching preferences")}),g}},updatePreferences:async(a,n)=>{try{e({isSaving:!0,saveError:null,saveSuccess:!1});let{error:r}=await i.N.from("users").update({...n.language&&{language:n.language},...n.theme&&{theme:n.theme},updated_at:new Date().toISOString()}).eq("id",a);if(r)throw r;let s=t().preferences;return e({preferences:{...s,...n},isSaving:!1,saveSuccess:!0}),n.language&&(localStorage.setItem("language",n.language),"undefined"!=typeof document&&(document.documentElement.lang=n.language)),setTimeout(()=>{e({saveSuccess:!1})},3e3),!0}catch(t){return e({isSaving:!1,saveError:t instanceof Error?t:Error("Unknown error updating preferences"),saveSuccess:!1}),!1}},setTheme:a=>{let n=s.kH.getState().user?.id;n?t().updatePreferences(n,{theme:a}):e(e=>({preferences:{...e.preferences,theme:a}}))},setLanguage:a=>{let n=s.kH.getState().user?.id;n?t().updatePreferences(n,{language:a}):(e(e=>({preferences:{...e.preferences,language:a}})),localStorage.setItem("language",a),"undefined"!=typeof document&&(document.documentElement.lang=a))},setPreferences:t=>e({preferences:t}),setIsLoading:t=>e({isLoading:t}),setIsSaving:t=>e({isSaving:t}),setError:t=>e({error:t}),setSaveError:t=>e({saveError:t}),setSaveSuccess:t=>e({saveSuccess:t}),clearErrors:()=>e({error:null,saveError:null}),reset:()=>e(u)}),(0,l.LX)("preferences-store",{storage:(0,o.KU)(()=>(0,l.oV)()),partialize:e=>({preferences:e.preferences,lastFetched:e.lastFetched}),version:1}))));var d=a(12349);let f={language:"fr",translations:{},isLoading:!0,error:null,lastFetched:null},h=(0,r.L)((0,n.v)()((0,o.Zr)((e,t)=>({...f,setLanguage:async a=>{try{e({isLoading:!0,error:null});let n=s.kH.getState().user?.id;n?await m.getState().updatePreferences(n,{language:a}):m.getState().setLanguage(a),e({language:a}),localStorage.setItem("language",a),"undefined"!=typeof document&&(document.documentElement.lang=a),await t().loadTranslations()}catch(t){e({isLoading:!1,error:t instanceof Error?t:Error(`Failed to set language to ${a}`)}),console.error(`Error setting language to ${a}:`,t)}},translate:e=>t().translations[e]||e,loadTranslations:async()=>{try{let a=t().language;e({isLoading:!0,error:null});let n=await (0,d.Zi)(a);e({translations:n,isLoading:!1,lastFetched:Date.now()})}catch(t){e({isLoading:!1,error:t instanceof Error?t:Error("Failed to load translations")}),console.error("Error loading translations:",t)}},setTranslations:t=>e({translations:t}),setIsLoading:t=>e({isLoading:t}),setError:t=>e({error:t}),clearError:()=>e({error:null}),reset:()=>e(f)}),(0,l.LX)("language-store",{storage:(0,o.KU)(()=>(0,l.oV)()),partialize:e=>({language:e.language,lastFetched:e.lastFetched}),version:1,onRehydrateStorage:()=>e=>{if(e){let t=m.getState().preferences.language;t!==e.language&&h.setState({language:t}),setTimeout(()=>{h.getState().loadTranslations()},0)}}})))),S={currentPage:1,totalPages:0,totalCount:0,hasMore:!1,pageSize:10},A={appointmentRequests:[],completedAppointments:[],cancelledAppointments:[],requestsPagination:{...S},completedPagination:{...S},cancelledPagination:{...S},isLoading:!1,error:null,lastFetched:null},q=(0,r.L)((0,n.v)()((0,o.Zr)((e,t)=>({...A,fetchAppointmentRequests:async a=>{try{e(e=>({isLoading:!0,error:null}));let n=t(),r=a?.page||n.requestsPagination.currentPage,o=a?.pageSize||n.requestsPagination.pageSize,l=a?.status,c=(r-1)*o,p=s.kH.getState().user?.id;if(!p)throw Error("User not authenticated");let g=i.N.from("appointment_requests").select(`
                *,
                family_members(first_name, last_name, health_card, birth_date)
              `,{count:"exact"}).eq("user_id",p).order("created_at",{ascending:!1}).range(c,c+o-1);l&&(g=g.eq("status",l)),a?.startDate&&(g=g.gte("created_at",a.startDate)),a?.endDate&&(g=g.lte("created_at",a.endDate));let{data:u,error:m,count:d}=await g;if(m)throw m;let f=d||0,h=Math.ceil(f/o),S=r<h;return e(e=>({appointmentRequests:u,requestsPagination:{currentPage:r,totalPages:h,totalCount:f,hasMore:S,pageSize:o},isLoading:!1,lastFetched:Date.now()})),u}catch(t){return e({isLoading:!1,error:t instanceof Error?t:Error("Unknown error fetching appointment requests")}),console.error("Error fetching appointment requests:",t),[]}},fetchCompletedAppointments:async a=>{try{e(e=>({isLoading:!0,error:null}));let n=t(),r=a?.page||n.completedPagination.currentPage,o=a?.pageSize||n.completedPagination.pageSize,l=(r-1)*o,c=s.kH.getState().user?.id;if(!c)throw Error("User not authenticated");let p=i.N.from("completed_appointments").select(`
                *,
                appointment_request:appointment_request_id(*)
              `,{count:"exact"}).order("completed_at",{ascending:!1}).range(l,l+o-1);p=p.filter("appointment_request.user_id","eq",c),a?.startDate&&(p=p.gte("completed_at",a.startDate)),a?.endDate&&(p=p.lte("completed_at",a.endDate));let{data:g,error:u,count:m}=await p;if(u)throw u;let d=m||0,f=Math.ceil(d/o),h=r<f;return e(e=>({completedAppointments:g,completedPagination:{currentPage:r,totalPages:f,totalCount:d,hasMore:h,pageSize:o},isLoading:!1,lastFetched:Date.now()})),g}catch(t){return e({isLoading:!1,error:t instanceof Error?t:Error("Unknown error fetching completed appointments")}),console.error("Error fetching completed appointments:",t),[]}},fetchCancelledAppointments:async a=>{try{e(e=>({isLoading:!0,error:null}));let n=t(),r=a?.page||n.cancelledPagination.currentPage,o=a?.pageSize||n.cancelledPagination.pageSize,l=(r-1)*o,c=s.kH.getState().user?.id;if(!c)throw Error("User not authenticated");let p=i.N.from("cancelled_appointments").select(`
                *,
                appointment_request:appointment_request_id(*)
              `,{count:"exact"}).order("cancelled_at",{ascending:!1}).range(l,l+o-1);p=p.filter("appointment_request.user_id","eq",c),a?.startDate&&(p=p.gte("cancelled_at",a.startDate)),a?.endDate&&(p=p.lte("cancelled_at",a.endDate));let{data:g,error:u,count:m}=await p;if(u)throw u;let d=m||0,f=Math.ceil(d/o),h=r<f;return e(e=>({cancelledAppointments:g,cancelledPagination:{currentPage:r,totalPages:f,totalCount:d,hasMore:h,pageSize:o},isLoading:!1,lastFetched:Date.now()})),g}catch(t){return e({isLoading:!1,error:t instanceof Error?t:Error("Unknown error fetching cancelled appointments")}),console.error("Error fetching cancelled appointments:",t),[]}},fetchAllAppointmentHistory:async()=>{try{e({isLoading:!0,error:null});let{lastFetched:a}=t(),n=Date.now();if(a&&n-a<3e5){e({isLoading:!1});return}let r={page:1,pageSize:10};await Promise.all([t().fetchAppointmentRequests(r),t().fetchCompletedAppointments(r),t().fetchCancelledAppointments(r)]),e({lastFetched:Date.now()})}catch(t){e({isLoading:!1,error:t instanceof Error?t:Error("Unknown error fetching appointment history")}),console.error("Error fetching appointment history:",t)}},createAppointmentRequest:async t=>{try{e({isLoading:!0,error:null});let a=await fetch("/api/appointment-requests",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),n=await a.json();if(!a.ok)throw Error(n.error||"Failed to create appointment request");let r=n.data;return e(e=>({appointmentRequests:[r,...e.appointmentRequests],isLoading:!1})),{success:!0,data:r}}catch(a){let t=a instanceof Error?a:Error("Unknown error creating appointment request");return e({isLoading:!1,error:t}),console.error("Error creating appointment request:",a),{success:!1,error:t.message}}},cancelAppointmentRequest:async(a,n)=>{try{e({isLoading:!0,error:null});let{error:r}=await i.N.from("appointment_requests").update({status:"cancelled"}).eq("id",a);if(r)throw r;let{error:s}=await i.N.from("cancelled_appointments").insert({appointment_request_id:a,cancellation_reason:n||"Cancelled by user",cancelled_by:"user"});return s&&console.error("Error creating cancelled appointment record:",s),e(e=>({appointmentRequests:e.appointmentRequests.map(e=>e.id===a?{...e,status:"cancelled"}:e),isLoading:!1})),t().fetchCancelledAppointments(),{success:!0}}catch(a){let t=a instanceof Error?a:Error("Unknown error cancelling appointment request");return e({isLoading:!1,error:t}),console.error("Error cancelling appointment request:",a),{success:!1,error:t.message}}},fetchNextPage:async e=>{let a,n;let r=t();switch(e){case"requests":a=r.requestsPagination.currentPage,n=r.requestsPagination.hasMore;break;case"completed":a=r.completedPagination.currentPage,n=r.completedPagination.hasMore;break;case"cancelled":a=r.cancelledPagination.currentPage,n=r.cancelledPagination.hasMore}if(n){let n=a+1;await t().goToPage(e,n)}},fetchPreviousPage:async e=>{let a;let n=t();switch(e){case"requests":a=n.requestsPagination.currentPage;break;case"completed":a=n.completedPagination.currentPage;break;case"cancelled":a=n.cancelledPagination.currentPage}if(a>1){let n=a-1;await t().goToPage(e,n)}},goToPage:async(e,a)=>{let n={page:a};switch(e){case"requests":await t().fetchAppointmentRequests(n);break;case"completed":await t().fetchCompletedAppointments(n);break;case"cancelled":await t().fetchCancelledAppointments(n)}},setPageSize:(a,n)=>{switch(a){case"requests":e(e=>({requestsPagination:{...e.requestsPagination,pageSize:n,currentPage:1}})),t().fetchAppointmentRequests({pageSize:n,page:1});break;case"completed":e(e=>({completedPagination:{...e.completedPagination,pageSize:n,currentPage:1}})),t().fetchCompletedAppointments({pageSize:n,page:1});break;case"cancelled":e(e=>({cancelledPagination:{...e.cancelledPagination,pageSize:n,currentPage:1}})),t().fetchCancelledAppointments({pageSize:n,page:1})}},setAppointmentRequests:(t,a)=>e(e=>({appointmentRequests:t,requestsPagination:a?{...e.requestsPagination,...a}:e.requestsPagination})),setCompletedAppointments:(t,a)=>e(e=>({completedAppointments:t,completedPagination:a?{...e.completedPagination,...a}:e.completedPagination})),setCancelledAppointments:(t,a)=>e(e=>({cancelledAppointments:t,cancelledPagination:a?{...e.cancelledPagination,...a}:e.cancelledPagination})),setIsLoading:t=>e({isLoading:t}),setError:t=>e({error:t}),clearError:()=>e({error:null}),invalidateCache:()=>{e({lastFetched:null}),t().fetchAllAppointmentHistory()},reset:()=>e(A)}),(0,l.LX)("appointment-history-store",{storage:(0,o.KU)(()=>(0,l.oV)()),partialize:e=>({appointmentRequests:e.appointmentRequests,completedAppointments:e.completedAppointments,cancelledAppointments:e.cancelledAppointments,requestsPagination:e.requestsPagination,completedPagination:e.completedPagination,cancelledPagination:e.cancelledPagination,lastFetched:e.lastFetched}),version:3,onRehydrateStorage:()=>e=>{if(e){let{lastFetched:t}=e,a=Date.now();(!t||a-t>3e5)&&setTimeout(()=>{s.kH.getState().user?.id&&q.getState().fetchAllAppointmentHistory()},0)}}}))));var P=a(18318);let y=(0,r.L)((0,n.v)()((e,t)=>({signIn:async(e,t)=>s.kH.getState().signIn(e,t),signInWithGoogle:async()=>s.kH.getState().signInWithGoogle(),signOut:async e=>s.kH.getState().signOut(e),fetchProfile:async e=>p.getState().fetchProfile(e),updateProfile:async(e,t)=>p.getState().updateProfile(e,t),fetchPreferences:async e=>m.getState().fetchPreferences(e),updatePreferences:async(e,t)=>m.getState().updatePreferences(e,t),setTheme:e=>{m.getState().setTheme(e)},setLanguage:e=>{m.getState().setLanguage(e),h.getState().setLanguage(e)},translate:e=>h.getState().translate(e),loadTranslations:async()=>h.getState().loadTranslations(),fetchAppointmentRequests:async e=>q.getState().fetchAppointmentRequests(e),fetchCompletedAppointments:async e=>q.getState().fetchCompletedAppointments(e),fetchCancelledAppointments:async e=>q.getState().fetchCancelledAppointments(e),fetchAllAppointmentHistory:async()=>q.getState().fetchAllAppointmentHistory(),createAppointmentRequest:async e=>q.getState().createAppointmentRequest(e),cancelAppointmentRequest:async(e,t)=>q.getState().cancelAppointmentRequest(e,t),fetchNextPage:async e=>q.getState().fetchNextPage(e),fetchPreviousPage:async e=>q.getState().fetchPreviousPage(e),goToPage:async(e,t)=>q.getState().goToPage(e,t),setPageSize:(e,t)=>{q.getState().setPageSize(e,t)},filterByDateRange:(e,t)=>q.getState().fetchAllAppointmentHistory({startDate:e,endDate:t}),filterByStatus:e=>{let t=q.getState();return"completed"===e?t.completedAppointments.map(e=>e.appointment_request).filter(Boolean):"cancelled"===e?t.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean):t.appointmentRequests.filter(t=>t.status===e)},filterByType:e=>{let t=q.getState();return[...t.appointmentRequests,...t.completedAppointments.map(e=>e.appointment_request).filter(Boolean),...t.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)].filter(t=>t.request_type===e)},getAppointmentCountsByStatus:()=>{let e=q.getState();return{pending:e.appointmentRequests.filter(e=>"pending"===e.status).length,in_progress:e.appointmentRequests.filter(e=>"in_progress"===e.status).length,completed:e.completedAppointments.length,cancelled:e.cancelledAppointments.length,total:e.appointmentRequests.length+e.completedAppointments.length+e.cancelledAppointments.length}},getAppointmentCountsByMonth:(e=new Date().getFullYear())=>{let t=q.getState(),a=Array(12).fill(0).map(()=>({pending:0,in_progress:0,completed:0,cancelled:0,total:0}));return t.appointmentRequests.forEach(t=>{let n=new Date(t.created_at);if(n.getFullYear()===e){let e=n.getMonth();"pending"===t.status?a[e].pending++:"in_progress"===t.status&&a[e].in_progress++,a[e].total++}}),t.completedAppointments.forEach(t=>{if(t.completed_at){let n=new Date(t.completed_at);if(n.getFullYear()===e){let e=n.getMonth();a[e].completed++,a[e].total++}}}),t.cancelledAppointments.forEach(t=>{if(t.cancelled_at){let n=new Date(t.cancelled_at);if(n.getFullYear()===e){let e=n.getMonth();a[e].cancelled++,a[e].total++}}}),a},getAppointmentSummary:()=>{let e=q.getState(),t=e.appointmentRequests.filter(e=>"pending"===e.status).length,a=e.appointmentRequests.filter(e=>"in_progress"===e.status).length,n=e.completedAppointments.length,r=e.cancelledAppointments.length,s=t+a+n+r,o=[...e.appointmentRequests,...e.completedAppointments.map(e=>e.appointment_request).filter(Boolean),...e.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)].sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime());return{counts:{pending:t,in_progress:a,completed:n,cancelled:r,total:s},completionRate:s>0?n/s*100:0,cancellationRate:s>0?r/s*100:0,mostRecentAppointment:o.length>0?o[0]:null,totalAppointments:s}},groupByStatus:()=>{let e=q.getState();return{pending:e.appointmentRequests.filter(e=>"pending"===e.status),in_progress:e.appointmentRequests.filter(e=>"in_progress"===e.status),completed:e.completedAppointments.map(e=>e.appointment_request).filter(Boolean),cancelled:e.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)}},groupByMonth:(e=new Date().getFullYear())=>{let t=q.getState(),a=[...t.appointmentRequests,...t.completedAppointments.map(e=>e.appointment_request).filter(Boolean),...t.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)],n={};return a.forEach(t=>{let a=new Date(t.created_at);if(a.getFullYear()===e){let e=a.getMonth();n[e]||(n[e]=[]),n[e].push(t)}}),n},groupByType:()=>{let e=q.getState(),t=[...e.appointmentRequests,...e.completedAppointments.map(e=>e.appointment_request).filter(Boolean),...e.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)],a={};return t.forEach(e=>{let t=e.request_type||"unknown";a[t]||(a[t]=[]),a[t].push(e)}),a},sortByDate:(e,t=!1)=>[...e].sort((e,a)=>{let n=new Date(e.created_at).getTime(),r=new Date(a.created_at).getTime();return t?n-r:r-n}),getAppointmentsInDateRange:(e,t)=>{let a=q.getState();return[...a.appointmentRequests,...a.completedAppointments.map(e=>e.appointment_request).filter(Boolean),...a.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)].filter(a=>{let n=new Date(a.created_at);return n>=e&&n<=t})},getAppointmentsForDay:e=>{let t=q.getState(),a=[...t.appointmentRequests,...t.completedAppointments.map(e=>e.appointment_request).filter(Boolean),...t.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)],n=e.getFullYear(),r=e.getMonth(),s=e.getDate();return a.filter(e=>{let t=new Date(e.created_at);return t.getFullYear()===n&&t.getMonth()===r&&t.getDate()===s})},getUpcomingAppointments:()=>{let e=q.getState(),t=new Date;return e.appointmentRequests.filter(e=>!!(("pending"===e.status||"in_progress"===e.status)&&e.request_details?.date&&e.request_details?.time)&&new Date(`${e.request_details.date}T${e.request_details.time}`)>t).sort((e,t)=>new Date(`${e.request_details.date}T${e.request_details.time}`).getTime()-new Date(`${t.request_details.date}T${t.request_details.time}`).getTime())},fetchSubscription:async e=>P.S.getState().fetchSubscription(e),updateSubscription:async e=>P.S.getState().updateSubscription(e),cancelSubscription:async e=>P.S.getState().cancelSubscription(e),initializeApp:async()=>{try{await s.kH.getState().refresh();let e=s.kH.getState().user;if(e){let t=e.id;await Promise.all([p.getState().fetchProfile(t),m.getState().fetchPreferences(t),P.S.getState().fetchSubscription(t),q.getState().fetchAllAppointmentHistory()])}await h.getState().loadTranslations();return}catch(e){throw console.error("Error initializing app:",e),e}}}))),_=()=>{let e=(0,s.kH)(e=>e.user),t=(0,s.kH)(e=>e.status),a=y(e=>e.signIn),n=y(e=>e.signInWithGoogle),r=y(e=>e.signOut),o=p(e=>e.profile),i=y(e=>e.updateProfile),l=y(e=>e.fetchProfile),c=m(e=>e.preferences),g=m(e=>e.theme),u=y(e=>e.updatePreferences),d=y(e=>e.fetchPreferences),f=y(e=>e.setTheme),S=h(e=>e.language),A=h(e=>e.translations),_=y(e=>e.setLanguage),w=y(e=>e.translate),E=(0,P.S)(e=>e.subscription),v=y(e=>e.fetchSubscription),L=y(e=>e.updateSubscription),N=y(e=>e.cancelSubscription),D=q(e=>e.appointmentRequests),R=q(e=>e.completedAppointments),k=q(e=>e.cancelledAppointments),F=D.filter(e=>"pending"===e.status),T=D.filter(e=>"in_progress"===e.status),$=q(e=>e.requestsPagination),B=q(e=>e.completedPagination),C=q(e=>e.cancelledPagination),U=y(e=>e.fetchAppointmentRequests),b=y(e=>e.fetchCompletedAppointments),z=y(e=>e.fetchCancelledAppointments),H=y(e=>e.fetchAllAppointmentHistory),I=y(e=>e.createAppointmentRequest),M=y(e=>e.cancelAppointmentRequest),x=y(e=>e.fetchNextPage),O=y(e=>e.fetchPreviousPage),Y=y(e=>e.goToPage),V=y(e=>e.setPageSize),Z=y(e=>e.filterByDateRange),K=y(e=>e.filterByStatus),X=y(e=>e.filterByType),j=y(e=>e.getAppointmentCountsByStatus),G=y(e=>e.getAppointmentCountsByMonth),W=y(e=>e.getAppointmentSummary),J=y(e=>e.groupByStatus),Q=y(e=>e.groupByMonth),ee=y(e=>e.groupByType),et=y(e=>e.sortByDate),ea=y(e=>e.getAppointmentsInDateRange),en=y(e=>e.getAppointmentsForDay),er=y(e=>e.getUpcomingAppointments),es=(0,s.kH)(e=>"loading"===e.status),eo=p(e=>e.isLoading),ei=m(e=>e.isLoading),el=h(e=>e.isLoading),ec=q(e=>e.isLoading),ep=(0,P.S)(e=>e.isLoading);return{user:e,status:t,isAuthenticated:"authenticated"===t,signIn:a,signInWithGoogle:n,signOut:r,profile:o,updateProfile:i,fetchProfile:l,preferences:c,theme:g,updatePreferences:u,fetchPreferences:d,setTheme:f,language:S,translations:A,setLanguage:_,translate:w,subscription:E,fetchSubscription:v,updateSubscription:L,cancelSubscription:N,appointmentRequests:D,completedAppointments:R,cancelledAppointments:k,pendingAppointments:F,inProgressAppointments:T,requestsPagination:$,completedPagination:B,cancelledPagination:C,fetchAppointmentRequests:U,fetchCompletedAppointments:b,fetchCancelledAppointments:z,fetchAllAppointmentHistory:H,createAppointmentRequest:I,cancelAppointmentRequest:M,fetchNextPage:x,fetchPreviousPage:O,goToPage:Y,setPageSize:V,filterByDateRange:Z,filterByStatus:K,filterByType:X,getAppointmentCountsByStatus:j,getAppointmentCountsByMonth:G,getAppointmentSummary:W,groupByStatus:J,groupByMonth:Q,groupByType:ee,sortByDate:et,getAppointmentsInDateRange:ea,getAppointmentsForDay:en,getUpcomingAppointments:er,isLoading:es||eo||ei||el||ec||ep,isAuthLoading:es,isProfileLoading:eo,isPreferencesLoading:ei,isLanguageLoading:el,isAppointmentHistoryLoading:ec,isSubscriptionLoading:ep,initializeApp:y(e=>e.initializeApp)}}},97124:(e,t,a)=>{a.d(t,{s:()=>l});var n=a(4473),r=a(40869),s=a(65563),o=a(63250);let i={};function l(){let e=async e=>{try{let t=await (0,s.V)(e),a=t.firstName||"",n=t.lastName||"",r={firstName:a,lastName:n,avatar:null,initials:`${a.charAt(0)}${n.charAt(0)}`.toUpperCase()||"??",role:"user",fullName:a||n?`${a} ${n}`.trim():"User",phone:t.phone||""};return u(r),S&&(localStorage.setItem(S,JSON.stringify(r)),i[S]=r),r}catch(e){throw console.error("Error loading user profile:",e),e}},{user:t,status:a}=(0,r.A)(),{notifyProfileUpdate:l,lastUpdatedUserId:c,lastUpdateTimestamp:p}=(0,o.F)(),[g,u]=(0,n.useState)(null),[m,d]=(0,n.useState)(!0),[f,h]=(0,n.useState)(null),S=(0,n.useMemo)(()=>t?.email?`user_profile_${t.email}`:t?.id?`user_profile_id_${t.id}`:null,[t?.email,t?.id]),A=g?.firstName||"",q=g?.lastName||"",P=t?.email||"",y=A,_=q;if(!A&&!q&&P){let e=P.split("@")[0].split(".");y=e[0]?e[0].charAt(0).toUpperCase()+e[0].slice(1):"",_=e[1]?e[1].charAt(0).toUpperCase()+e[1].slice(1):""}let w=g?.fullName||(A||q?`${A} ${q}`.trim():y||_?`${y} ${_}`.trim():"User"),E=g?.initials||(A||q?`${A.charAt(0)}${q.charAt(0)}`.toUpperCase():y||_?`${y.charAt(0)}${_.charAt(0)}`.toUpperCase():"??"),v=g?.phone||"";return{profile:g,isLoading:m,error:f,initials:E,fullName:w,firstName:A||y,lastName:q||_,phone:v,invalidateCache:(t,n,r=!0)=>{let s=`user_profile_${n}`,o=`user_profile_id_${t}`;localStorage.removeItem(s),localStorage.removeItem(o),s in i&&delete i[s],o in i&&delete i[o],r&&t&&"authenticated"===a&&(e(t),l&&l(t))},reloadProfile:t=>e(t),isFromCache:!!S&&(!!i[S]||!!localStorage.getItem(S))}}}};