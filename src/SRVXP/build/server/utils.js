"use strict";exports.id=738,exports.ids=[738],exports.modules={12349:(e,n,t)=>{t.d(n,{Zi:()=>i,re:()=>a});let r={en:null,fr:null};async function i(e){if(r[e])return r[e];try{let n=await fetch(`/translations/${e}.json`);if(!n.ok)throw Error(`Failed to load ${e} translations: ${n.statusText}`);let t=await n.json();return r[e]=t,t}catch(n){return console.error(`Error loading ${e} translations:`,n),{}}}function a(e,n){return`${e}.${n}`}},18897:(e,n,t)=>{t.d(n,{w:()=>i});var r=t(4473);let i=(e={})=>{let[n,t]=(0,r.useState)(!1),i=(0,r.useRef)(null),{threshold:a=.1,rootMargin:o="0px"}=e;return(0,r.useEffect)(()=>{let e=new IntersectionObserver(([n])=>{n.isIntersecting&&(t(!0),i.current&&e.unobserve(i.current))},{threshold:a,rootMargin:o}),n=i.current;return n&&e.observe(n),()=>{n&&e.unobserve(n)}},[a,o]),{ref:i,isIntersecting:n}}},25911:(e,n,t)=>{t.d(n,{cn:()=>a});var r=t(75233),i=t(54621);function a(...e){return(0,i.QP)((0,r.$)(e))}},29816:(e,n,t)=>{t.d(n,{T$:()=>s});var r=t(28392),i=t(75721),a=t(4473);function o(){return(0,r.jsx)("div",{className:"flex h-24 w-full items-center justify-center",children:(0,r.jsx)("div",{className:"h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-primary"})})}function s(e,{ssr:n=!1,loading:t=o,displayName:c}={}){let l=(0,i.default)(e,{loading:t,ssr:n}),p=e=>(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("loading",{}),children:(0,r.jsx)(l,{...e})});return c&&(p.displayName=`Dynamic(${c})`),p}},31186:(e,n,t)=>{t.d(n,{U:()=>c,Z:()=>l});var r=t(81712),i=t(84824);let a="https://tfvswgreslsbctjrvdbd.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.5bNos8JS_77EUjWOxIGHpgNaYE28qQvO2g4qj0wRWiU",s=process.env.SUPABASE_SERVICE_ROLE_KEY||"",c=async()=>{let e;try{e=await (0,i.UL)()}catch(e){return console.error("Failed to access cookies:",e),(0,r.createServerClient)(a,o,{cookies:{getAll:()=>[],setAll(){}}})}return(0,r.createServerClient)(a,o,{cookies:{getAll(){try{return e.getAll().map(({name:e,value:n})=>({name:e,value:n}))}catch(e){return console.error("Error accessing cookies:",e),[]}},setAll(n){try{n.forEach(({name:n,value:t,options:r})=>{let i={...r,httpOnly:!1,secure:!0,sameSite:"lax",maxAge:r?.maxAge||604800,path:"/"};e.set(n,t,i)})}catch(e){console.error("Error setting cookies:",e)}}}})},l=async()=>{let e;if(!s)return console.error("SUPABASE_SERVICE_ROLE_KEY is not defined"),c();try{e=await (0,i.UL)()}catch(e){return console.error("Failed to access cookies in admin client:",e),(0,r.createServerClient)(a,s,{cookies:{getAll:()=>[],setAll(){}}})}return(0,r.createServerClient)(a,s,{cookies:{getAll(){try{return e.getAll().map(({name:e,value:n})=>({name:e,value:n}))}catch(e){return console.error("Error accessing cookies:",e),[]}},setAll(n){try{n.forEach(({name:n,value:t,options:r})=>{let i={...r,httpOnly:!1,secure:!0,sameSite:"lax",maxAge:r?.maxAge||604800,path:"/"};e.set(n,t,i)})}catch(e){console.error("Error setting cookies:",e)}}}})}},31953:(e,n,t)=>{t.d(n,{o:()=>i});function r(e,n){return`${e}.${n}`}let i={errors:{tryAgainLater:r("errors","tryAgainLater"),saveFailed:r("errors","saveFailed"),notFound:r("errors","notFound"),unauthorized:r("errors","unauthorized"),generalError:r("errors","generalError")},common:{save:r("common","save"),cancel:r("common","cancel"),confirm:r("common","confirm"),delete:r("common","delete"),loading:r("common","loading"),search:r("common","search"),logout:r("common","logout"),loggingOut:r("common","loggingOut"),close:r("common","close"),active:r("common","active"),inactive:r("common","inactive"),submit:r("common","submit"),submitting:r("common","submitting"),processing:r("common","processing"),newRequest:r("common","newRequest"),required:r("common","required"),yes:r("common","yes"),no:r("common","no"),continue:r("common","continue"),manage:r("common","manage"),modify:r("common","modify"),back:r("common","back"),saved:r("common","saved"),saving:r("common","saving"),saveChanges:r("common","saveChanges"),errorOccurred:r("common","errorOccurred")},auth:{signIn:r("auth","signIn"),signUp:r("auth","signUp"),email:r("auth","email"),password:r("auth","password"),confirmPassword:r("auth","confirmPassword"),forgotPassword:r("auth","forgotPassword"),resetPassword:r("auth","resetPassword"),enterCredentials:r("auth","enterCredentials"),createAccount:r("auth","createAccount"),alreadyHaveAccount:r("auth","alreadyHaveAccount"),noAccount:r("auth","noAccount"),passwordReset:r("auth","passwordReset"),passwordResetInstructions:r("auth","passwordResetInstructions"),passwordResetSent:r("auth","passwordResetSent"),successfulReset:r("auth","successfulReset"),firstName:r("auth","firstName"),lastName:r("auth","lastName"),errors:{invalidCredentials:r("auth.errors","invalidCredentials"),passwordsDontMatch:r("auth.errors","passwordsDontMatch"),emailInUse:r("auth.errors","emailInUse"),invalidEmail:r("auth.errors","invalidEmail"),passwordTooShort:r("auth.errors","passwordTooShort"),generalError:r("auth.errors","generalError")}},nav:{home:r("nav","home"),dashboard:r("nav","dashboard"),appointments:r("nav","appointments"),findAppointment:r("nav","findAppointment"),calendar:r("nav","calendar"),help:r("nav","help"),account:r("nav","account"),mobileNavigation:r("nav","mobileNavigation"),needHelp:r("nav","needHelp")},account:{profile:r("account","profile"),preferences:r("account","preferences"),subscription:r("account","subscription"),users:r("account","users"),manageUsers:r("account","manageUsers"),yourAccount:r("account","yourAccount"),individualPlan:r("account","individualPlan"),individualPlanMonthly:r("account","individualPlanMonthly"),individualPlanAnnual:r("account","individualPlanAnnual"),familyPlan:r("account","familyPlan"),familyPlanMonthly:r("account","familyPlanMonthly"),familyPlanAnnual:r("account","familyPlanAnnual"),manageInformation:r("account","manageInformation"),personalInfoDescription:r("account","personalInfoDescription"),modifyProfile:r("account","modifyProfile"),modifySubscription:r("account","modifySubscription"),subscriptionDescription:r("account","subscriptionDescription"),manageSubscription:r("account","manageSubscription"),appearanceLanguage:r("account","appearanceLanguage"),appearanceDescription:r("account","appearanceDescription"),modifyPreferences:r("account","modifyPreferences"),manageAccountUsers:r("account","manageAccountUsers"),manageUsersDescription:r("account","manageUsersDescription"),manageProfile:r("account","manageProfile"),manageProfileDescription:r("account","manageProfileDescription"),manageProfileButton:r("account","manageProfileButton"),subscribePlan:r("account","subscribePlan"),choosePlan:r("account","choosePlan"),editPersonalInfo:r("account","editPersonalInfo"),firstName:r("account","firstName"),lastName:r("account","lastName"),email:r("account","email"),phone:r("account","phone"),invalidEmail:r("account","invalidEmail"),invalidPhone:r("account","invalidPhone"),emailCannotBeEmpty:r("account","emailCannotBeEmpty"),firstNameRequired:r("account","firstNameRequired"),lastNameRequired:r("account","lastNameRequired"),emailVerificationSent:r("account","emailVerificationSent")},home:{greeting:r("home","greeting"),welcome:r("home","welcome"),findAppointmentTitle:r("home","findAppointmentTitle"),findAppointmentDesc:r("home","findAppointmentDesc"),viewRequests:r("home","viewRequests"),manageAppointmentsDesc:r("home","manageAppointmentsDesc"),manageUsersDesc:r("home","manageUsersDesc"),manageProfileTitle:r("home","manageProfileTitle"),manageProfileDesc:r("home","manageProfileDesc"),manageProfileButton:r("home","manageProfileButton")},appointments:{title:r("appointments","title"),description:r("appointments","description"),requestsTitle:r("appointments","requestsTitle"),all:r("appointments","all"),inProgress:r("appointments","inProgress"),completed:r("appointments","completed"),noRequests:r("appointments","noRequests"),noRequestsInProgress:r("appointments","noRequestsInProgress"),noRequestsCompleted:r("appointments","noRequestsCompleted"),noRequestsCancelled:r("appointments","noRequestsCancelled"),postalCode:r("appointments","postalCode"),sentOn:r("appointments","sentOn"),pending:r("appointments","pending"),done:r("appointments","done"),cancelAppointment:r("appointments","cancelAppointment"),cancelConfirmation:r("appointments","cancelConfirmation"),cancelConfirmationText:r("appointments","cancelConfirmationText"),noContinue:r("appointments","noContinue"),yesCancel:r("appointments","yesCancel"),viewAll:r("appointments","viewAll")},meta:{title:r("meta","title"),description:r("meta","description")},subscription:{modifySubscription:r("subscription","modifySubscription"),individualPlan:r("subscription","individualPlan"),monthlyCost:r("subscription","monthlyCost"),benefits:r("subscription","benefits"),unlimitedAccess:r("subscription","unlimitedAccess"),emailNotifications:r("subscription","emailNotifications"),familyProfiles:r("subscription","familyProfiles"),modifyPlan:r("subscription","modifyPlan"),cancelPlan:r("subscription","cancelPlan"),paymentHistory:r("subscription","paymentHistory"),monthlySubscription:r("subscription","monthlySubscription"),march:r("subscription","march"),february:r("subscription","february"),january:r("subscription","january"),cost:r("subscription","cost"),changePlan:r("subscription","changePlan"),changePlanDescription:r("subscription","changePlanDescription"),confirmChange:r("subscription","confirmChange"),cancelConfirmation:r("subscription","cancelConfirmation"),cancelWarning:r("subscription","cancelWarning"),yesCancel:r("subscription","yesCancel"),noCancel:r("subscription","noCancel"),status:r("subscription","status"),verifyingPayment:r("subscription","verifyingPayment"),success:r("subscription","success"),successMessage:r("subscription","successMessage"),details:r("subscription","details"),plan:r("subscription","plan"),billing:r("subscription","billing"),amount:r("subscription","amount"),currentPeriod:r("subscription","currentPeriod"),nextSteps:r("subscription","nextSteps"),goToDashboard:r("subscription","goToDashboard"),manageAccount:r("subscription","manageAccount"),error:r("subscription","error"),errorMessage:r("subscription","errorMessage"),needHelp:r("subscription","needHelp"),returnToPlans:r("subscription","returnToPlans"),contactSupport:r("subscription","contactSupport"),canceledCheckout:r("subscription","canceledCheckout"),processingSubscription:r("subscription","processingSubscription"),noSessionId:r("subscription","noSessionId"),notLoggedIn:r("subscription","notLoggedIn")},preferences:{managePreferences:r("preferences","managePreferences"),languageAppearance:r("preferences","languageAppearance"),customizeInterface:r("preferences","customizeInterface"),preferredLanguage:r("preferences","preferredLanguage"),french:r("preferences","french"),english:r("preferences","english"),languageDescription:r("preferences","languageDescription"),appTheme:r("preferences","appTheme"),light:r("preferences","light"),dark:r("preferences","dark"),themeDescription:r("preferences","themeDescription"),saveChanges:r("preferences","saveChanges"),saving:r("preferences","saving"),changesSaved:r("preferences","changesSaved"),errorSaving:r("preferences","errorSaving")},users:{manageAccountUsers:r("users","manageAccountUsers"),manageProfile:r("users","manageProfile"),familyMembers:r("users","familyMembers"),userProfile:r("users","userProfile"),familyMembersDescription:r("users","familyMembersDescription"),userProfileDescription:r("users","userProfileDescription"),addYourInfo:r("users","addYourInfo"),addYourInfoPrompt:r("users","addYourInfoPrompt"),cancel:r("users","cancel"),firstName:r("users","firstName"),lastName:r("users","lastName"),healthCardPrefix:r("users","healthCardPrefix"),healthCardDescription:r("users","healthCardDescription"),birthDate:r("users","birthDate"),save:r("users","save"),edit:r("users","edit"),healthCard:r("users","healthCard"),addMember:r("users","addMember"),editMemberPrompt:r("users","editMemberPrompt"),selectDate:r("users","selectDate"),validationError:r("users","validationError")},help:{needHelp:r("help","needHelp"),helpDescription:r("help","helpDescription"),faq:r("help","faq"),faqDescription:r("help","faqDescription"),howToBookAppointment:r("help","howToBookAppointment"),howToBookDescription:r("help","howToBookDescription"),howToCancelAppointment:r("help","howToCancelAppointment"),howToCancelDescription:r("help","howToCancelDescription"),howToChangePlan:r("help","howToChangePlan"),howToChangePlanDescription:r("help","howToChangePlanDescription"),customerSupport:r("help","customerSupport"),supportDescription:r("help","supportDescription"),email:r("help","email"),supportEmail:r("help","supportEmail"),responseTime:r("help","responseTime"),contactSupport:r("help","contactSupport")},landing:{hero:{title:r("landing.hero","title"),subtitle:r("landing.hero","subtitle"),findAppointment:r("landing.hero","findAppointment"),learnMore:r("landing.hero","learnMore"),imageAlt:r("landing.hero","imageAlt")},features:{sameDay:{title:r("landing.features.sameDay","title"),description:r("landing.features.sameDay","description")},nearbyClinic:{title:r("landing.features.nearbyClinic","title"),description:r("landing.features.nearbyClinic","description")},anywhereInQuebec:{title:r("landing.features.anywhereInQuebec","title"),description:r("landing.features.anywhereInQuebec","description")}},howItWorks:{title:r("landing.howItWorks","title"),customAppointments:{title:r("landing.howItWorks.customAppointments","title"),description:r("landing.howItWorks.customAppointments","description")},easyManagement:{title:r("landing.howItWorks.easyManagement","title"),description:r("landing.howItWorks.easyManagement","description")},imageAlt:r("landing.howItWorks","imageAlt")},pricing:{title:r("landing.pricing","title"),description:r("landing.pricing","description"),period:{monthly:r("landing.pricing.period","monthly"),annually:r("landing.pricing.period","annually")},individual:{title:r("landing.pricing.individual","title"),description:r("landing.pricing.individual","description"),features:r("landing.pricing.individual","features"),annualSavings:r("landing.pricing.individual","annualSavings")},family:{title:r("landing.pricing.family","title"),description:r("landing.pricing.family","description"),features:r("landing.pricing.family","features"),annualSavings:r("landing.pricing.family","annualSavings")},choosePlan:r("landing.pricing","choosePlan"),included:r("landing.pricing","included"),manageSubscription:r("landing.pricing","manageSubscription"),feature1:r("landing.pricing","feature1"),feature2:r("landing.pricing","feature2"),feature3:r("landing.pricing","feature3"),feature4:r("landing.pricing","feature4")},faq:{title:r("landing.faq","title"),viewFullFaq:r("landing.faq","viewFullFaq"),questions:[{question:r("landing.faq.questions.0","question"),answer:r("landing.faq.questions.0","answer")},{question:r("landing.faq.questions.1","question"),answer:r("landing.faq.questions.1","answer")},{question:r("landing.faq.questions.2","question"),answer:r("landing.faq.questions.2","answer")},{question:r("landing.faq.questions.3","question"),answer:r("landing.faq.questions.3","answer")},{question:r("landing.faq.questions.4","question"),answer:r("landing.faq.questions.4","answer")}]},cta:{title:r("landing.cta","title"),subtitle:r("landing.cta","subtitle"),buttonText:r("landing.cta","buttonText"),imageAlt:r("landing.cta","imageAlt")},navbar:{title:r("landing.navbar","title"),signIn:r("landing.navbar","signIn"),signUp:r("landing.navbar","signUp"),service:r("landing.navbar","service"),pricing:r("landing.navbar","pricing"),faq:r("landing.navbar","faq")},footer:{description:r("landing.footer","description"),contactUs:r("landing.footer","contactUs"),privacyPolicy:r("landing.footer","privacyPolicy"),termsOfUse:r("landing.footer","termsOfUse"),termsOfSale:r("landing.footer","termsOfSale"),copyright:r("landing.footer","copyright")}},findAppointment:{title:r("findAppointment","title"),description:r("findAppointment","description"),searchCriteria:r("findAppointment","searchCriteria"),requiredFields:r("findAppointment","requiredFields"),appointmentFor:r("findAppointment","appointmentFor"),selectPerson:r("findAppointment","selectPerson"),managedInUsersSection:r("findAppointment","managedInUsersSection"),healthCard:r("findAppointment","healthCard"),healthCardOf:r("findAppointment","healthCardOf"),lastDigits:r("findAppointment","lastDigits"),enterEightDigits:r("findAppointment","enterEightDigits"),format:r("findAppointment","format"),sequenceNumber:r("findAppointment","sequenceNumber"),sequenceInfo:r("findAppointment","sequenceInfo"),enterTwoDigits:r("findAppointment","enterTwoDigits"),postalCode:r("findAppointment","postalCode"),postalExample:r("findAppointment","postalExample"),invalidPostalFormat:r("findAppointment","invalidPostalFormat"),postalFormatWarning:r("findAppointment","postalFormatWarning"),postalCodeDescription:r("findAppointment","postalCodeDescription"),fromDate:r("findAppointment","fromDate"),selectDate:r("findAppointment","selectDate"),appointmentTime:r("findAppointment","appointmentTime"),chooseTime:r("findAppointment","chooseTime"),morning:r("findAppointment","morning"),afternoon:r("findAppointment","afternoon"),evening:r("findAppointment","evening"),asap:r("findAppointment","asap"),submitRequest:r("findAppointment","submitRequest"),thankYou:r("findAppointment","thankYou"),confirmationMessage:r("findAppointment","confirmationMessage"),viewRequests:r("findAppointment","viewRequests"),selectDateError:r("findAppointment","selectDateError"),selectTimeError:r("findAppointment","selectTimeError"),enterPostalError:r("findAppointment","enterPostalError"),invalidPostalError:r("findAppointment","invalidPostalError"),selectPersonError:r("findAppointment","selectPersonError"),healthCardDigitsError:r("findAppointment","healthCardDigitsError"),sequenceNumberError:r("findAppointment","sequenceNumberError"),noSubscription:r("findAppointment","noSubscription")}}},40869:(e,n,t)=>{t.d(n,{A:()=>s,O:()=>p});var r=t(28392),i=t(4473),a=t(54386);let o=(0,i.createContext)({user:null,session:null,status:"loading",signIn:async()=>({success:!1}),signInWithGoogle:async()=>({success:!1}),signOut:async()=>{},refresh:async()=>{}}),s=()=>(0,i.useContext)(o),c="auth_cache",l="auth_cache_expiry";function p({children:e}){let[n,t]=(0,i.useState)(null),[s,p]=(0,i.useState)(null),[u,m]=(0,i.useState)("loading"),d=(0,i.useCallback)(()=>!1,[]),g=(0,i.useCallback)(e=>{},[]),f=(0,i.useCallback)(async()=>null,[]),h=(0,i.useCallback)(async()=>{try{m("loading");let e=d();if(e){console.log("External return detected, attempting session restoration..."),sessionStorage.removeItem("external_return_detected");let e=await f();if(e){p(e),t(e.user),m("authenticated"),localStorage.setItem(c,JSON.stringify({session:e,user:e.user})),localStorage.setItem(l,(Date.now()+18e5).toString());return}}let n=null,r=null,i=await a.N.auth.getSession();if(n=i.data,((r=i.error)||!n?.session)&&e){console.log("First session attempt failed, trying refresh...");let e=await a.N.auth.refreshSession();!e.error&&e.data.session&&(n={session:e.data.session},r=null)}if(r)throw r;n?.session?(p(n.session),t(n.session.user),m("authenticated"),g(n.session)):(p(null),t(null),m("unauthenticated"))}catch(e){m("unauthenticated"),p(null),t(null),console.error("Error refreshing auth state:",e)}},[d,f,g]),b=(0,i.useCallback)(async(e,n)=>{try{let{data:r,error:i}=await a.N.auth.signInWithPassword({email:e,password:n});if(i)throw i;if(r.session){p(r.session),t(r.session.user),m("authenticated"),g(r.session),localStorage.setItem(c,JSON.stringify({session:r.session,user:r.session.user})),localStorage.setItem(l,(Date.now()+18e5).toString());let e=[];for(let n=0;n<sessionStorage.length;n++){let t=sessionStorage.key(n);t&&(t.includes("auth")||t.includes("logout")||t.includes("supabase"))&&e.push(t)}e.forEach(e=>sessionStorage.removeItem(e))}return{success:!0}}catch(e){return console.error("Sign in error:",e),{success:!1,error:e}}},[g]),v=(0,i.useCallback)(async()=>{try{let e=window.location.origin,n=`${e}/auth/callback?redirectTo=/dashboard`;console.log(`Setting up Google auth with callback URL: ${n}`);let{data:t,error:r}=await a.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:n}});if(r)throw r;return{success:!0}}catch(e){return console.error("Google sign in error:",e),{success:!1,error:e}}},[]),y=(0,i.useCallback)(async(e="/")=>{try{await a.N.auth.signOut(),p(null),t(null),m("unauthenticated"),localStorage.removeItem(c),localStorage.removeItem(l),localStorage.removeItem("session_backup"),localStorage.removeItem("supabase.auth.token"),localStorage.removeItem("theme"),n?.id&&localStorage.removeItem(`user_preferences_${n.id}`),sessionStorage.setItem("isLoggedOut","true"),window.location.href=e}catch(n){console.error("Sign out error:",n),p(null),t(null),m("unauthenticated"),window.location.href=e}},[n]);return(0,r.jsx)(o.Provider,{value:{user:n,session:s,status:u,signIn:b,signInWithGoogle:v,signOut:y,refresh:h},children:e})}},45590:(e,n,t)=>{t.d(n,{Y9:()=>i});var r=t(54386);let i=async(e="/")=>{try{await r.N.auth.signOut(),sessionStorage.setItem("isLoggedOut","true"),localStorage.removeItem("supabase.auth.token"),window.location.href=e}catch(n){console.error("Error during complete sign out:",n),window.location.href=e}}},54386:(e,n,t)=>{t.d(n,{N:()=>r});let r=(0,t(16121).UU)("https://tfvswgreslsbctjrvdbd.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.5bNos8JS_77EUjWOxIGHpgNaYE28qQvO2g4qj0wRWiU")},59356:(e,n,t)=>{t.d(n,{o:()=>i});var r=t(12349);let i={errors:{tryAgainLater:(0,r.re)("errors","tryAgainLater"),saveFailed:(0,r.re)("errors","saveFailed"),notFound:(0,r.re)("errors","notFound"),unauthorized:(0,r.re)("errors","unauthorized"),generalError:(0,r.re)("errors","generalError")},common:{save:(0,r.re)("common","save"),cancel:(0,r.re)("common","cancel"),confirm:(0,r.re)("common","confirm"),delete:(0,r.re)("common","delete"),loading:(0,r.re)("common","loading"),search:(0,r.re)("common","search"),logout:(0,r.re)("common","logout"),loggingOut:(0,r.re)("common","loggingOut"),close:(0,r.re)("common","close"),active:(0,r.re)("common","active"),inactive:(0,r.re)("common","inactive"),submit:(0,r.re)("common","submit"),submitting:(0,r.re)("common","submitting"),processing:(0,r.re)("common","processing"),newRequest:(0,r.re)("common","newRequest"),required:(0,r.re)("common","required"),yes:(0,r.re)("common","yes"),no:(0,r.re)("common","no"),continue:(0,r.re)("common","continue"),manage:(0,r.re)("common","manage"),modify:(0,r.re)("common","modify"),back:(0,r.re)("common","back"),saved:(0,r.re)("common","saved"),saving:(0,r.re)("common","saving"),saveChanges:(0,r.re)("common","saveChanges"),errorOccurred:(0,r.re)("common","errorOccurred")},auth:{signIn:(0,r.re)("auth","signIn"),signUp:(0,r.re)("auth","signUp"),email:(0,r.re)("auth","email"),password:(0,r.re)("auth","password"),confirmPassword:(0,r.re)("auth","confirmPassword"),forgotPassword:(0,r.re)("auth","forgotPassword"),resetPassword:(0,r.re)("auth","resetPassword"),enterCredentials:(0,r.re)("auth","enterCredentials"),createAccount:(0,r.re)("auth","createAccount"),alreadyHaveAccount:(0,r.re)("auth","alreadyHaveAccount"),noAccount:(0,r.re)("auth","noAccount"),passwordReset:(0,r.re)("auth","passwordReset"),passwordResetInstructions:(0,r.re)("auth","passwordResetInstructions"),passwordResetSent:(0,r.re)("auth","passwordResetSent"),successfulReset:(0,r.re)("auth","successfulReset"),firstName:(0,r.re)("auth","firstName"),lastName:(0,r.re)("auth","lastName"),errors:{invalidCredentials:(0,r.re)("auth.errors","invalidCredentials"),passwordsDontMatch:(0,r.re)("auth.errors","passwordsDontMatch"),emailInUse:(0,r.re)("auth.errors","emailInUse"),invalidEmail:(0,r.re)("auth.errors","invalidEmail"),passwordTooShort:(0,r.re)("auth.errors","passwordTooShort"),generalError:(0,r.re)("auth.errors","generalError")}},nav:{home:(0,r.re)("nav","home"),dashboard:(0,r.re)("nav","dashboard"),appointments:(0,r.re)("nav","appointments"),findAppointment:(0,r.re)("nav","findAppointment"),calendar:(0,r.re)("nav","calendar"),help:(0,r.re)("nav","help"),account:(0,r.re)("nav","account"),mobileNavigation:(0,r.re)("nav","mobileNavigation"),needHelp:(0,r.re)("nav","needHelp")},account:{profile:(0,r.re)("account","profile"),preferences:(0,r.re)("account","preferences"),subscription:(0,r.re)("account","subscription"),users:(0,r.re)("account","users"),manageUsers:(0,r.re)("account","manageUsers"),yourAccount:(0,r.re)("account","yourAccount"),individualPlan:(0,r.re)("account","individualPlan"),individualPlanMonthly:(0,r.re)("account","individualPlanMonthly"),individualPlanAnnual:(0,r.re)("account","individualPlanAnnual"),familyPlan:(0,r.re)("account","familyPlan"),familyPlanMonthly:(0,r.re)("account","familyPlanMonthly"),familyPlanAnnual:(0,r.re)("account","familyPlanAnnual"),manageInformation:(0,r.re)("account","manageInformation"),personalInfoDescription:(0,r.re)("account","personalInfoDescription"),modifyProfile:(0,r.re)("account","modifyProfile"),modifySubscription:(0,r.re)("account","modifySubscription"),subscriptionDescription:(0,r.re)("account","subscriptionDescription"),manageSubscription:(0,r.re)("account","manageSubscription"),appearanceLanguage:(0,r.re)("account","appearanceLanguage"),appearanceDescription:(0,r.re)("account","appearanceDescription"),modifyPreferences:(0,r.re)("account","modifyPreferences"),manageAccountUsers:(0,r.re)("account","manageAccountUsers"),manageUsersDescription:(0,r.re)("account","manageUsersDescription"),manageProfile:(0,r.re)("account","manageProfile"),manageProfileDescription:(0,r.re)("account","manageProfileDescription"),manageProfileButton:(0,r.re)("account","manageProfileButton"),subscribePlan:(0,r.re)("account","subscribePlan"),choosePlan:(0,r.re)("account","choosePlan"),editPersonalInfo:(0,r.re)("account","editPersonalInfo"),firstName:(0,r.re)("account","firstName"),lastName:(0,r.re)("account","lastName"),email:(0,r.re)("account","email"),phone:(0,r.re)("account","phone"),invalidEmail:(0,r.re)("account","invalidEmail"),invalidPhone:(0,r.re)("account","invalidPhone"),emailCannotBeEmpty:(0,r.re)("account","emailCannotBeEmpty"),firstNameRequired:(0,r.re)("account","firstNameRequired"),lastNameRequired:(0,r.re)("account","lastNameRequired"),emailVerificationSent:(0,r.re)("account","emailVerificationSent")},home:{greeting:(0,r.re)("home","greeting"),welcome:(0,r.re)("home","welcome"),findAppointmentTitle:(0,r.re)("home","findAppointmentTitle"),findAppointmentDesc:(0,r.re)("home","findAppointmentDesc"),viewRequests:(0,r.re)("home","viewRequests"),manageAppointmentsDesc:(0,r.re)("home","manageAppointmentsDesc"),manageUsersDesc:(0,r.re)("home","manageUsersDesc"),manageProfileTitle:(0,r.re)("home","manageProfileTitle"),manageProfileDesc:(0,r.re)("home","manageProfileDesc"),manageProfileButton:(0,r.re)("home","manageProfileButton")},appointments:{title:(0,r.re)("appointments","title"),description:(0,r.re)("appointments","description"),requestsTitle:(0,r.re)("appointments","requestsTitle"),all:(0,r.re)("appointments","all"),inProgress:(0,r.re)("appointments","inProgress"),completed:(0,r.re)("appointments","completed"),noRequests:(0,r.re)("appointments","noRequests"),noRequestsInProgress:(0,r.re)("appointments","noRequestsInProgress"),noRequestsCompleted:(0,r.re)("appointments","noRequestsCompleted"),noRequestsCancelled:(0,r.re)("appointments","noRequestsCancelled"),postalCode:(0,r.re)("appointments","postalCode"),sentOn:(0,r.re)("appointments","sentOn"),pending:(0,r.re)("appointments","pending"),done:(0,r.re)("appointments","done"),cancelAppointment:(0,r.re)("appointments","cancelAppointment"),cancelConfirmation:(0,r.re)("appointments","cancelConfirmation"),cancelConfirmationText:(0,r.re)("appointments","cancelConfirmationText"),noContinue:(0,r.re)("appointments","noContinue"),yesCancel:(0,r.re)("appointments","yesCancel"),viewAll:(0,r.re)("appointments","viewAll")},meta:{title:(0,r.re)("meta","title"),description:(0,r.re)("meta","description")},subscription:{modifySubscription:(0,r.re)("subscription","modifySubscription"),individualPlan:(0,r.re)("subscription","individualPlan"),monthlyCost:(0,r.re)("subscription","monthlyCost"),benefits:(0,r.re)("subscription","benefits"),unlimitedAccess:(0,r.re)("subscription","unlimitedAccess"),emailNotifications:(0,r.re)("subscription","emailNotifications"),familyProfiles:(0,r.re)("subscription","familyProfiles"),modifyPlan:(0,r.re)("subscription","modifyPlan"),cancelPlan:(0,r.re)("subscription","cancelPlan"),paymentHistory:(0,r.re)("subscription","paymentHistory"),monthlySubscription:(0,r.re)("subscription","monthlySubscription"),march:(0,r.re)("subscription","march"),february:(0,r.re)("subscription","february"),january:(0,r.re)("subscription","january"),cost:(0,r.re)("subscription","cost"),changePlan:(0,r.re)("subscription","changePlan"),changePlanDescription:(0,r.re)("subscription","changePlanDescription"),confirmChange:(0,r.re)("subscription","confirmChange"),cancelConfirmation:(0,r.re)("subscription","cancelConfirmation"),cancelWarning:(0,r.re)("subscription","cancelWarning"),yesCancel:(0,r.re)("subscription","yesCancel"),noCancel:(0,r.re)("subscription","noCancel"),status:(0,r.re)("subscription","status"),verifyingPayment:(0,r.re)("subscription","verifyingPayment"),success:(0,r.re)("subscription","success"),successMessage:(0,r.re)("subscription","successMessage"),details:(0,r.re)("subscription","details"),plan:(0,r.re)("subscription","plan"),billing:(0,r.re)("subscription","billing"),amount:(0,r.re)("subscription","amount"),currentPeriod:(0,r.re)("subscription","currentPeriod"),nextSteps:(0,r.re)("subscription","nextSteps"),goToDashboard:(0,r.re)("subscription","goToDashboard"),manageAccount:(0,r.re)("subscription","manageAccount"),error:(0,r.re)("subscription","error"),errorMessage:(0,r.re)("subscription","errorMessage"),needHelp:(0,r.re)("subscription","needHelp"),returnToPlans:(0,r.re)("subscription","returnToPlans"),contactSupport:(0,r.re)("subscription","contactSupport"),canceledCheckout:(0,r.re)("subscription","canceledCheckout"),processingSubscription:(0,r.re)("subscription","processingSubscription"),noSessionId:(0,r.re)("subscription","noSessionId"),notLoggedIn:(0,r.re)("subscription","notLoggedIn")},preferences:{managePreferences:(0,r.re)("preferences","managePreferences"),languageAppearance:(0,r.re)("preferences","languageAppearance"),customizeInterface:(0,r.re)("preferences","customizeInterface"),preferredLanguage:(0,r.re)("preferences","preferredLanguage"),french:(0,r.re)("preferences","french"),english:(0,r.re)("preferences","english"),languageDescription:(0,r.re)("preferences","languageDescription"),appTheme:(0,r.re)("preferences","appTheme"),light:(0,r.re)("preferences","light"),dark:(0,r.re)("preferences","dark"),themeDescription:(0,r.re)("preferences","themeDescription"),saveChanges:(0,r.re)("preferences","saveChanges"),saving:(0,r.re)("preferences","saving"),changesSaved:(0,r.re)("preferences","changesSaved"),errorSaving:(0,r.re)("preferences","errorSaving")},users:{manageAccountUsers:(0,r.re)("users","manageAccountUsers"),manageProfile:(0,r.re)("users","manageProfile"),familyMembers:(0,r.re)("users","familyMembers"),userProfile:(0,r.re)("users","userProfile"),familyMembersDescription:(0,r.re)("users","familyMembersDescription"),userProfileDescription:(0,r.re)("users","userProfileDescription"),addYourInfo:(0,r.re)("users","addYourInfo"),addYourInfoPrompt:(0,r.re)("users","addYourInfoPrompt"),cancel:(0,r.re)("users","cancel"),firstName:(0,r.re)("users","firstName"),lastName:(0,r.re)("users","lastName"),healthCardPrefix:(0,r.re)("users","healthCardPrefix"),healthCardDescription:(0,r.re)("users","healthCardDescription"),birthDate:(0,r.re)("users","birthDate"),save:(0,r.re)("users","save"),edit:(0,r.re)("users","edit"),healthCard:(0,r.re)("users","healthCard"),addMember:(0,r.re)("users","addMember"),editMemberPrompt:(0,r.re)("users","editMemberPrompt"),selectDate:(0,r.re)("users","selectDate"),validationError:(0,r.re)("users","validationError")},help:{needHelp:(0,r.re)("help","needHelp"),helpDescription:(0,r.re)("help","helpDescription"),faq:(0,r.re)("help","faq"),faqDescription:(0,r.re)("help","faqDescription"),howToBookAppointment:(0,r.re)("help","howToBookAppointment"),howToBookDescription:(0,r.re)("help","howToBookDescription"),howToCancelAppointment:(0,r.re)("help","howToCancelAppointment"),howToCancelDescription:(0,r.re)("help","howToCancelDescription"),howToChangePlan:(0,r.re)("help","howToChangePlan"),howToChangePlanDescription:(0,r.re)("help","howToChangePlanDescription"),customerSupport:(0,r.re)("help","customerSupport"),supportDescription:(0,r.re)("help","supportDescription"),email:(0,r.re)("help","email"),supportEmail:(0,r.re)("help","supportEmail"),responseTime:(0,r.re)("help","responseTime"),contactSupport:(0,r.re)("help","contactSupport")},landing:{hero:{title:(0,r.re)("landing.hero","title"),subtitle:(0,r.re)("landing.hero","subtitle"),findAppointment:(0,r.re)("landing.hero","findAppointment"),learnMore:(0,r.re)("landing.hero","learnMore"),imageAlt:(0,r.re)("landing.hero","imageAlt")},features:{sameDay:{title:(0,r.re)("landing.features.sameDay","title"),description:(0,r.re)("landing.features.sameDay","description")},nearbyClinic:{title:(0,r.re)("landing.features.nearbyClinic","title"),description:(0,r.re)("landing.features.nearbyClinic","description")},anywhereInQuebec:{title:(0,r.re)("landing.features.anywhereInQuebec","title"),description:(0,r.re)("landing.features.anywhereInQuebec","description")}},howItWorks:{title:(0,r.re)("landing.howItWorks","title"),customAppointments:{title:(0,r.re)("landing.howItWorks.customAppointments","title"),description:(0,r.re)("landing.howItWorks.customAppointments","description")},easyManagement:{title:(0,r.re)("landing.howItWorks.easyManagement","title"),description:(0,r.re)("landing.howItWorks.easyManagement","description")},imageAlt:(0,r.re)("landing.howItWorks","imageAlt")},pricing:{title:(0,r.re)("landing.pricing","title"),description:(0,r.re)("landing.pricing","description"),period:{monthly:(0,r.re)("landing.pricing.period","monthly"),annually:(0,r.re)("landing.pricing.period","annually")},individual:{title:(0,r.re)("landing.pricing.individual","title"),description:(0,r.re)("landing.pricing.individual","description"),features:(0,r.re)("landing.pricing.individual","features"),annualSavings:(0,r.re)("landing.pricing.individual","annualSavings")},family:{title:(0,r.re)("landing.pricing.family","title"),description:(0,r.re)("landing.pricing.family","description"),features:(0,r.re)("landing.pricing.family","features"),annualSavings:(0,r.re)("landing.pricing.family","annualSavings")},choosePlan:(0,r.re)("landing.pricing","choosePlan"),included:(0,r.re)("landing.pricing","included"),manageSubscription:(0,r.re)("landing.pricing","manageSubscription"),feature1:(0,r.re)("landing.pricing","feature1"),feature2:(0,r.re)("landing.pricing","feature2"),feature3:(0,r.re)("landing.pricing","feature3"),feature4:(0,r.re)("landing.pricing","feature4")},faq:{title:(0,r.re)("landing.faq","title"),viewFullFaq:(0,r.re)("landing.faq","viewFullFaq"),questions:[{question:(0,r.re)("landing.faq.questions.0","question"),answer:(0,r.re)("landing.faq.questions.0","answer")},{question:(0,r.re)("landing.faq.questions.1","question"),answer:(0,r.re)("landing.faq.questions.1","answer")},{question:(0,r.re)("landing.faq.questions.2","question"),answer:(0,r.re)("landing.faq.questions.2","answer")},{question:(0,r.re)("landing.faq.questions.3","question"),answer:(0,r.re)("landing.faq.questions.3","answer")},{question:(0,r.re)("landing.faq.questions.4","question"),answer:(0,r.re)("landing.faq.questions.4","answer")}]},cta:{title:(0,r.re)("landing.cta","title"),subtitle:(0,r.re)("landing.cta","subtitle"),buttonText:(0,r.re)("landing.cta","buttonText"),imageAlt:(0,r.re)("landing.cta","imageAlt")},navbar:{title:(0,r.re)("landing.navbar","title"),signIn:(0,r.re)("landing.navbar","signIn"),signUp:(0,r.re)("landing.navbar","signUp"),service:(0,r.re)("landing.navbar","service"),pricing:(0,r.re)("landing.navbar","pricing"),faq:(0,r.re)("landing.navbar","faq")},footer:{description:(0,r.re)("landing.footer","description"),contactUs:(0,r.re)("landing.footer","contactUs"),privacyPolicy:(0,r.re)("landing.footer","privacyPolicy"),termsOfUse:(0,r.re)("landing.footer","termsOfUse"),termsOfSale:(0,r.re)("landing.footer","termsOfSale"),copyright:(0,r.re)("landing.footer","copyright")}},findAppointment:{title:(0,r.re)("findAppointment","title"),description:(0,r.re)("findAppointment","description"),searchCriteria:(0,r.re)("findAppointment","searchCriteria"),requiredFields:(0,r.re)("findAppointment","requiredFields"),appointmentFor:(0,r.re)("findAppointment","appointmentFor"),selectPerson:(0,r.re)("findAppointment","selectPerson"),managedInUsersSection:(0,r.re)("findAppointment","managedInUsersSection"),healthCard:(0,r.re)("findAppointment","healthCard"),healthCardOf:(0,r.re)("findAppointment","healthCardOf"),lastDigits:(0,r.re)("findAppointment","lastDigits"),enterEightDigits:(0,r.re)("findAppointment","enterEightDigits"),format:(0,r.re)("findAppointment","format"),sequenceNumber:(0,r.re)("findAppointment","sequenceNumber"),sequenceInfo:(0,r.re)("findAppointment","sequenceInfo"),enterTwoDigits:(0,r.re)("findAppointment","enterTwoDigits"),postalCode:(0,r.re)("findAppointment","postalCode"),postalExample:(0,r.re)("findAppointment","postalExample"),invalidPostalFormat:(0,r.re)("findAppointment","invalidPostalFormat"),postalFormatWarning:(0,r.re)("findAppointment","postalFormatWarning"),postalCodeDescription:(0,r.re)("findAppointment","postalCodeDescription"),fromDate:(0,r.re)("findAppointment","fromDate"),selectDate:(0,r.re)("findAppointment","selectDate"),appointmentTime:(0,r.re)("findAppointment","appointmentTime"),chooseTime:(0,r.re)("findAppointment","chooseTime"),morning:(0,r.re)("findAppointment","morning"),afternoon:(0,r.re)("findAppointment","afternoon"),evening:(0,r.re)("findAppointment","evening"),asap:(0,r.re)("findAppointment","asap"),submitRequest:(0,r.re)("findAppointment","submitRequest"),thankYou:(0,r.re)("findAppointment","thankYou"),confirmationMessage:(0,r.re)("findAppointment","confirmationMessage"),viewRequests:(0,r.re)("findAppointment","viewRequests"),selectDateError:(0,r.re)("findAppointment","selectDateError"),selectTimeError:(0,r.re)("findAppointment","selectTimeError"),enterPostalError:(0,r.re)("findAppointment","enterPostalError"),invalidPostalError:(0,r.re)("findAppointment","invalidPostalError"),selectPersonError:(0,r.re)("findAppointment","selectPersonError"),healthCardDigitsError:(0,r.re)("findAppointment","healthCardDigitsError"),sequenceNumberError:(0,r.re)("findAppointment","sequenceNumberError"),noSubscription:(0,r.re)("findAppointment","noSubscription")}}},61237:(e,n,t)=>{t.d(n,{LanguageProvider:()=>i});var r=t(60544);(0,r.registerClientReference)(function(){throw Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/lib/LanguageContext.tsx","useLanguage");let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/lib/LanguageContext.tsx","LanguageProvider")},63250:(e,n,t)=>{t.d(n,{F:()=>s,o:()=>o});var r=t(28392),i=t(4473);let a=(0,i.createContext)({notifyProfileUpdate:()=>{},lastUpdatedUserId:null,lastUpdateTimestamp:0});function o({children:e}){let[n,t]=(0,i.useState)(null),[o,s]=(0,i.useState)(0);return(0,r.jsx)(a.Provider,{value:{notifyProfileUpdate:e=>{t(e),s(Date.now())},lastUpdatedUserId:n,lastUpdateTimestamp:o},children:e})}let s=()=>(0,i.useContext)(a)},63907:(e,n,t)=>{t.d(n,{E:()=>s,cn:()=>a,d:()=>o});var r=t(45817),i=t(33061);function a(...e){return(0,i.QP)((0,r.$)(e))}function o(e,n=8){let t=e.replace(/\D/g,"").substring(0,n);return t.length<=4?t:`${t.slice(0,4)}-${t.slice(4)}`}function s(e,n=8){return e.replace(/\D/g,"").length===n}},65563:(e,n,t)=>{t.d(n,{V:()=>a,e:()=>i});var r=t(54386);async function i(e,n,t){if(n.email&&n.email!==t?.user?.email&&!t)throw Error("Auth session is required to update email");try{let e=[],i={},a=!1;if(void 0!==n.firstName&&(i.first_name=n.firstName,a=!0),void 0!==n.lastName&&(i.last_name=n.lastName,a=!0),void 0!==n.phone&&(i.phone=n.phone,a=!0),a){let{data:n,error:t}=await r.N.auth.updateUser({data:i});if(t)throw Error(`Error updating user metadata: ${t.message}`);e.push("metadata")}if(void 0!==n.email&&t?.user?.email!==n.email){let{data:t,error:i}=await r.N.auth.updateUser({email:n.email});if(i)throw Error(`Error updating email: ${i.message}`);e.push("email")}return{success:e.length>0||0===Object.keys(n).length,updates:e}}catch(e){throw console.error("Error updating user profile:",e),e}}async function a(e){try{let{data:n,error:t}=await r.N.auth.getUser();if(t)throw t;let i=null;try{let{data:n,error:t}=await r.N.from("users").select("*").eq("id",e).single();t||(i=n)}catch(e){console.warn("Error getting public user data:",e)}let a=n.user.user_metadata||{},o=a.first_name||"",s=a.last_name||"";return o||(o=i?.first_name||""),s||(s=i?.last_name||""),{id:e,email:n.user.email,firstName:o,lastName:s,phone:a.phone||"",updatedAt:i?.updated_at||new Date().toISOString(),...i||{}}}catch(e){throw console.error("Error getting user profile:",e),e}}},66438:(e,n,t)=>{t.d(n,{U:()=>o});var r=t(25580);let i="https://tfvswgreslsbctjrvdbd.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.5bNos8JS_77EUjWOxIGHpgNaYE28qQvO2g4qj0wRWiU";function o(){return(0,r.UU)(i,a)}(0,r.UU)(i,a)},70371:(e,n,t)=>{t.d(n,{X:()=>a});var r=t(4473),i=t(40869);let a=()=>{let{refresh:e}=(0,i.A)(),n=(0,r.useCallback)(()=>!1,[]),t=(0,r.useCallback)(()=>{},[]),a=(0,r.useCallback)(()=>{},[]);return(0,r.useEffect)(()=>{let r=()=>{"visible"===document.visibilityState&&n()&&(console.log("Stripe return detected, refreshing auth state..."),a(),setTimeout(()=>{e()},500))},i=()=>{n()&&(console.log("Window focus with Stripe return detected, refreshing auth state..."),a(),setTimeout(()=>{e()},500))},o=()=>{window.location.pathname.includes("/compte/abonnement")&&t()};return document.addEventListener("visibilitychange",r),window.addEventListener("focus",i),window.addEventListener("beforeunload",o),n()&&(console.log("Stripe return detected on mount, refreshing auth state..."),a(),setTimeout(()=>{e()},100)),()=>{document.removeEventListener("visibilitychange",r),window.removeEventListener("focus",i),window.removeEventListener("beforeunload",o)}},[n,t,a,e]),{detectStripeReturn:n,markStripeNavigation:t,clearStripeNavigation:a}}},73760:(e,n,t)=>{t.d(n,{cn:()=>a});var r=t(45817),i=t(33061);function a(...e){return(0,i.QP)((0,r.$)(e))}},83808:(e,n,t)=>{t.d(n,{r:()=>u,h:()=>m});var r=t(28392),i=t(4473),a=t(40869),o=t(54386);async function s(e,n,t){try{let r={user_id:e,first_name:n.firstName,last_name:n.lastName,health_card:n.healthCard,birth_date:n.birthDate?.toISOString()||null,position:t};if(n.supabaseId){let{error:e}=await o.N.from("family_members").update(r).eq("id",n.supabaseId);if(e)throw e;return{success:!0,id:n.supabaseId}}{let{data:e,error:n}=await o.N.from("family_members").insert(r).select("id").single();if(n)throw n;return{success:!0,id:e.id}}}catch(e){return console.error("Error saving family member:",e),{success:!1}}}async function c(e){try{let{error:n}=await o.N.from("family_members").delete().eq("id",e);if(n)throw n;return{success:!0}}catch(e){return console.error("Error deleting family member:",e),{success:!1}}}let l=[{id:1,firstName:"F\xe9lix",lastName:"Tremblay",healthCard:"TREF",birthDate:new Date(1985,3,12),editing:!1},{id:2,firstName:"Marie",lastName:"Tremblay",healthCard:"TREM",birthDate:new Date(1987,8,23),editing:!1},{id:3,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1},{id:4,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1},{id:5,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1}],p=(0,i.createContext)({familyMembers:l,setFamilyMembers:()=>{},updateFamilyMember:()=>{},updateTempFamilyMember:()=>{},toggleEditing:()=>{},saveMemberChanges:()=>!1,deleteMember:()=>{},isLoading:!1,error:null});function u({children:e}){let{user:n,status:t}=(0,a.A)(),[o,u]=(0,i.useState)(l),[m,d]=(0,i.useState)(!0),[g,f]=(0,i.useState)(null);return(0,r.jsx)(p.Provider,{value:{familyMembers:o,setFamilyMembers:u,updateFamilyMember:(e,n)=>{u(t=>t.map(t=>t.id===e?{...t,...n}:t))},updateTempFamilyMember:(e,n)=>{u(t=>t.map(t=>t.id===e?{...t,tempFirstName:"tempFirstName"in n?n.tempFirstName:t.tempFirstName,tempLastName:"tempLastName"in n?n.tempLastName:t.tempLastName,tempHealthCard:"tempHealthCard"in n?n.tempHealthCard:t.tempHealthCard,tempBirthDate:"tempBirthDate"in n?n.tempBirthDate:t.tempBirthDate}:t))},toggleEditing:e=>{u(n=>n.map(n=>n.id===e?n.editing?{...n,editing:!1,tempFirstName:void 0,tempLastName:void 0,tempHealthCard:void 0,tempBirthDate:void 0}:{...n,editing:!0,tempFirstName:n.firstName,tempLastName:n.lastName,tempHealthCard:n.healthCard,tempBirthDate:n.birthDate}:n))},saveMemberChanges:e=>{let t=o.find(n=>n.id===e);if(!t)return!1;let r=void 0!==t.tempFirstName?t.tempFirstName:t.firstName,i=void 0!==t.tempLastName?t.tempLastName:t.lastName,a=void 0!==t.tempHealthCard?t.tempHealthCard:t.healthCard,c=void 0!==t.tempBirthDate?t.tempBirthDate:t.birthDate;return!!r&&!!i&&!!a&&4===a.length&&!!c&&(u(t=>{let r=t.map(t=>{if(t.id===e&&t.editing){let e={...t,editing:!1,firstName:void 0!==t.tempFirstName?t.tempFirstName:t.firstName,lastName:void 0!==t.tempLastName?t.tempLastName:t.lastName,healthCard:void 0!==t.tempHealthCard?t.tempHealthCard:t.healthCard,birthDate:void 0!==t.tempBirthDate?t.tempBirthDate:t.birthDate,tempFirstName:void 0,tempLastName:void 0,tempHealthCard:void 0,tempBirthDate:void 0};return n?.id&&(e.firstName||e.lastName||e.healthCard||e.birthDate)&&s(n.id,e,t.id).then(e=>{e.success&&e.id&&u(n=>n.map(n=>n.id===t.id?{...n,supabaseId:e.id}:n))}).catch(e=>console.error("Error saving member:",e)),e}return t});if(r.length<5){let e=r.map(e=>e.id);for(let n=1;n<=5;n++)e.includes(n)||r.push({id:n,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1});r.sort((e,n)=>e.id-n.id)}return r}),!0)},deleteMember:e=>{let t=o.find(n=>n.id===e);if(!t||!t.supabaseId){u(n=>n.map(n=>n.id===e?{...n,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1,tempFirstName:void 0,tempLastName:void 0,tempHealthCard:void 0,tempBirthDate:void 0}:n));return}n?.id&&c(t.supabaseId).then(n=>{n.success&&u(n=>n.map(n=>n.id===e?{...n,firstName:"",lastName:"",healthCard:"",birthDate:void 0,supabaseId:void 0,editing:!1,tempFirstName:void 0,tempLastName:void 0,tempHealthCard:void 0,tempBirthDate:void 0}:n))}).catch(e=>console.error("Error deleting member:",e))},isLoading:m,error:g},children:e})}function m(){return(0,i.useContext)(p)}},86669:(e,n,t)=>{async function r(e){try{let n=await fetch("/api/appointment-requests",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),t=await n.json();if(!n.ok)return{success:!1,error:t.error||"Failed to create appointment request",errorType:t.errorType};return{success:!0,data:t.data}}catch(e){return console.error("Error creating appointment request:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}t.d(n,{hb:()=>r}),t(54386)},98209:(e,n,t)=>{t.d(n,{LanguageProvider:()=>u,o:()=>p});var r=t(28392),i=t(4473),a=t(12349),o=t(98388),s=t(54386),c=t(40869);let l=(0,i.createContext)({language:"fr",translations:{},setLanguage:()=>{},translate:e=>e,isLoading:!0}),p=()=>(0,i.useContext)(l);function u({children:e}){let[n,t]=(0,i.useState)("fr"),[p,u]=(0,i.useState)({}),[m,d]=(0,i.useState)(!0),{user:g,status:f}=(0,c.A)(),h=async e=>{console.log("Language context: changing to",e),d(!0);try{t(e),localStorage.setItem("language",e),document.documentElement.lang=e;let n=await (0,a.Zi)(e);if(u(n),"authenticated"===f&&g)try{await (0,o.b)(g.id,{language:e});let{error:n}=await s.N.auth.updateUser({data:{language:e}});n?console.error("Error updating user metadata with language preference:",n):console.log("User metadata updated with language preference:",e)}catch(e){console.error("Error saving language preference to Supabase:",e)}console.log("Language context updated successfully to",e)}catch(n){console.error(`Failed to load ${e} translations:`,n)}finally{d(!1)}};return(0,r.jsx)(l.Provider,{value:{language:n,translations:p,setLanguage:h,translate:e=>p[e]||e,isLoading:m},children:e})}},98388:(e,n,t)=>{t.d(n,{b:()=>i,o:()=>a});var r=t(54386);async function i(e,n){try{let{error:t}=await r.N.from("users").update({language:n.language,theme:n.theme,updated_at:new Date().toISOString()}).eq("id",e);if(t)throw t;return{success:!0}}catch(e){return console.error("Error saving user preferences:",e),{success:!1,error:e}}}async function a(e){try{let{data:n,error:t}=await r.N.from("users").select("language, theme").eq("id",e).maybeSingle();if(t)throw t;if(!n||!n.language&&!n.theme)return{language:"fr",theme:"light"};return{language:n.language||"fr",theme:n.theme||"light"}}catch(e){return console.error("Error getting user preferences:",e),null}}}};