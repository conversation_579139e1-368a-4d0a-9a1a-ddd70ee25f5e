exports.id=988,exports.ids=[988],exports.modules={18318:(e,r,t)=>{"use strict";t.d(r,{S:()=>c,f:()=>d});var s=t(30547),n=t(80411),o=t(54386),i=t(44e3),a=t(64782),l=t(70292);let u={subscription:null,isLoading:!1,error:null,lastFetched:null},c=(0,i.L)((0,s.v)()((0,n.Zr)((e,r)=>({...u,fetchSubscription:async t=>{try{e({isLoading:!0,error:null});let{subscription:s,lastFetched:n}=r(),i=Date.now();if(s&&n&&i-n<3e5)return e({isLoading:!1}),s;let{data:a,error:l}=await o.N.from("subscriptions").select("*").eq("user_id",t).eq("status","active").in("plan_type",["individual","family"]).maybeSingle();if(l)throw l;return a&&console.log("Subscription billing info:",{interval:a.interval,billing_period:a.billing_period,derived_billing_period:a.billing_period||("month"===a.interval?"monthly":"year"===a.interval?"annual":null)}),e({subscription:a,isLoading:!1,lastFetched:Date.now()}),a}catch(r){return e({isLoading:!1,error:r instanceof Error?r:Error("Unknown error fetching subscription")}),console.error("Error fetching subscription:",r),null}},updateSubscription:async t=>{try{e({isLoading:!0,error:null});let s=r().subscription;if(!s)throw Error("No subscription to update");let{error:n}=await o.N.from("subscriptions").update(t).eq("id",s.id);if(n)throw n;return e({subscription:{...s,...t},isLoading:!1}),!0}catch(r){return e({isLoading:!1,error:r instanceof Error?r:Error("Unknown error updating subscription")}),console.error("Error updating subscription:",r),!1}},cancelSubscription:async t=>{try{e({isLoading:!0,error:null});let s=await fetch("/api/subscriptions/cancel",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({subscriptionId:t})}),n=await s.json();if(!s.ok)throw Error(n.error||"Failed to cancel subscription");let o=r().subscription;return o?e({subscription:{...o,cancel_at_period_end:!0,canceled_at:Math.floor(Date.now()/1e3)},isLoading:!1}):e({isLoading:!1}),!0}catch(r){return e({isLoading:!1,error:r instanceof Error?r:Error("Unknown error cancelling subscription")}),console.error("Error cancelling subscription:",r),!1}},setSubscription:r=>e({subscription:r}),setIsLoading:r=>e({isLoading:r}),setError:r=>e({error:r}),clearError:()=>e({error:null}),reset:()=>e(u)}),(0,a.LX)("subscription-store",{storage:(0,n.KU)(()=>(0,a.oV)()),partialize:e=>({subscription:e.subscription,lastFetched:e.lastFetched}),version:1,onRehydrateStorage:()=>e=>{if(e){let{lastFetched:r}=e,t=Date.now();(!r||t-r>3e5)&&setTimeout(()=>{let e=l.kH.getState().user?.id;e&&c.getState().fetchSubscription(e)},0)}}})))),d=()=>{let e=c(e=>e.subscription);c(e=>e.isLoading),c(e=>e.error);let r=!!e&&"active"===e.status,t=e?.plan_type||null,s=e?.billing_period||(e?.interval==="month"?"monthly":e?.interval==="year"?"annual":null),n=e?.status||null,o=e?.current_period_start?new Date(1e3*e.current_period_start):null,i=e?.current_period_end?new Date(1e3*e.current_period_end):null,a=e?.cancel_at_period_end||!1,l=e?.amount?e.amount/100:null,u=e?.currency||null,d=null!==l&&u?new Intl.NumberFormat("en-US",{style:"currency",currency:u.toUpperCase()}).format(l):null,h=i?Math.ceil((i.getTime()-Date.now())/864e5):null;return{hasSubscription:r,planType:t,billingPeriod:s,status:n,currentPeriodStart:o,currentPeriodEnd:i,cancelAtPeriodEnd:a,amount:l,currency:u,formattedAmount:d,daysUntilRenewal:h,isTrialing:"trialing"===n,isPastDue:"past_due"===n,isCancelled:"cancelled"===n||a}}},27548:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,56495,23)),Promise.resolve().then(t.t.bind(t,33395,23)),Promise.resolve().then(t.t.bind(t,9287,23)),Promise.resolve().then(t.t.bind(t,80150,23)),Promise.resolve().then(t.t.bind(t,65138,23)),Promise.resolve().then(t.t.bind(t,93558,23)),Promise.resolve().then(t.t.bind(t,66989,23)),Promise.resolve().then(t.t.bind(t,38197,23))},44e3:(e,r,t)=>{"use strict";function s(e){for(let r of(e.use={},Object.keys(e.getState())))Object.defineProperty(e.use,r,{get:()=>()=>e(e=>e[r])});return e}t.d(r,{L:()=>s})},48825:(e,r,t)=>{Promise.resolve().then(t.bind(t,67993))},61849:(e,r,t)=>{Promise.resolve().then(t.bind(t,68757))},64782:(e,r,t)=>{"use strict";t.d(r,{LX:()=>n,gm:()=>s,oV:()=>o});let s=(e,r=localStorage)=>{r.removeItem(e)},n=(e,r)=>({name:e,storage:r?.storage,partialize:r?.partialize,merge:r?.merge,onRehydrateStorage:r?.onRehydrateStorage,version:r?.version||1,migrate:r?.migrate}),o=()=>{let e={};return{getItem:r=>e[r]||null,setItem:(r,t)=>{e[r]=t},removeItem:r=>{delete e[r]}}}},69404:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,17803,23)),Promise.resolve().then(t.t.bind(t,23783,23)),Promise.resolve().then(t.t.bind(t,7979,23)),Promise.resolve().then(t.t.bind(t,13642,23)),Promise.resolve().then(t.t.bind(t,55486,23)),Promise.resolve().then(t.t.bind(t,59098,23)),Promise.resolve().then(t.t.bind(t,31289,23)),Promise.resolve().then(t.t.bind(t,61169,23))},70292:(e,r,t)=>{"use strict";t.d(r,{kH:()=>u});var s=t(30547),n=t(80411),o=t(54386),i=t(44e3),a=t(64782);let l={user:null,session:null,status:"loading",error:null,lastRefreshed:null},u=(0,i.L)((0,s.v)()((0,n.Zr)((e,r)=>({...l,signIn:async(r,t)=>{try{e({status:"loading",error:null});let{data:s,error:n}=await o.N.auth.signInWithPassword({email:r,password:t});if(n)return e({status:"unauthenticated",error:n}),{success:!1,error:n};if(s.session){e({user:s.session.user,session:s.session,status:"authenticated",lastRefreshed:Date.now()});let r=[];for(let e=0;e<sessionStorage.length;e++){let t=sessionStorage.key(e);t&&(t.includes("auth")||t.includes("logout")||t.includes("supabase"))&&r.push(t)}r.forEach(e=>sessionStorage.removeItem(e))}return{success:!0}}catch(t){let r=t instanceof Error?t:Error("Unknown error during sign in");return e({status:"unauthenticated",error:r}),{success:!1,error:r}}},signInWithGoogle:async()=>{try{e({status:"loading",error:null});let r=window.location.origin,t=`${r}/auth/callback?redirectTo=/dashboard`;console.log(`Setting up Google auth with callback URL: ${t}`);let{data:s,error:n}=await o.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:t}});if(n)return e({status:"unauthenticated",error:n}),{success:!1,error:n};return{success:!0}}catch(t){let r=t instanceof Error?t:Error("Unknown error during Google sign in");return e({status:"unauthenticated",error:r}),{success:!1,error:r}}},signOut:async(t="/")=>{try{e({status:"loading",error:null}),await o.N.auth.signOut(),e({user:null,session:null,status:"unauthenticated",error:null,lastRefreshed:null}),localStorage.removeItem("supabase.auth.token"),localStorage.removeItem("theme");let t=r().user?.id;t&&(localStorage.removeItem(`user_preferences_${t}`),(0,a.gm)("user-store"),(0,a.gm)("preferences-store"),(0,a.gm)("subscription-store"),(0,a.gm)("appointment-history-store"))}catch(r){console.error("Error during sign out:",r),e({user:null,session:null,status:"unauthenticated",error:r instanceof Error?r:Error("Unknown error during sign out"),lastRefreshed:null})}},refresh:async()=>{try{let t=r().status;"authenticated"!==t&&e({status:"loading",error:null});let{data:s,error:n}=await o.N.auth.getSession();if(n)throw n;s.session?e({user:s.session.user,session:s.session,status:"authenticated",lastRefreshed:Date.now()}):e({user:null,session:null,status:"unauthenticated",lastRefreshed:Date.now()})}catch(r){console.error("Error refreshing auth state:",r),e({user:null,session:null,status:"unauthenticated",error:r instanceof Error?r:Error("Unknown error during refresh"),lastRefreshed:Date.now()})}},setUser:r=>e({user:r}),setSession:r=>e({session:r}),setStatus:r=>e({status:r}),setError:r=>e({error:r}),clearError:()=>e({error:null}),reset:()=>e(l)}),(0,a.LX)("user-store",{storage:(0,n.KU)(()=>(0,a.oV)()),partialize:e=>({user:e.user,session:e.session,status:e.status,lastRefreshed:e.lastRefreshed}),version:1,onRehydrateStorage:()=>e=>{}}))))}};