1:"$Sreact.fragment"
2:I[15945,["7957","static/chunks/routes-d340d2550dd9e8db.js","5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","7177","static/chunks/app/layout-af3f1353f13967be.js"],"default"]
3:I[25704,[],""]
4:I[73566,[],""]
5:I[2067,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","5763","static/chunks/app/conditions-utilisation-EN/page-2af3504c4ad5cbea.js"],"Navbar"]
6:I[10244,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","5763","static/chunks/app/conditions-utilisation-EN/page-2af3504c4ad5cbea.js"],"Footer"]
7:I[52329,[],"OutletBoundary"]
a:I[52329,[],"ViewportBoundary"]
c:I[52329,[],"MetadataBoundary"]
e:I[71639,[],""]
:HL["/_next/static/media/569ce4b8f30dc480-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/93f479601ee12b01-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/234b042d6f756282.css","style"]
:HL["/_next/static/css/f30152c0704fba31.css","style"]
0:{"P":null,"b":"OmbAXfhZLD7aTmFcXGoiH","p":"","c":["","conditions-utilisation-EN"],"i":false,"f":[[["",{"children":["conditions-utilisation-EN",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/234b042d6f756282.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/f30152c0704fba31.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"fr","className":"__variable_5cfdac __variable_9a8899","suppressHydrationWarning":true,"children":["$","body",null,{"className":"antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"$undefined",[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],{"children":["conditions-utilisation-EN",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen flex flex-col","children":[["$","$L5",null,{}],["$","main",null,{"className":"flex-grow bg-white","children":["$","div",null,{"className":"max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-6 py-12","children":[["$","div",null,{"className":"mb-4 text-gray-500","children":["$","p",null,{"children":"Last updated on March 24, 2025"}]}],["$","h1",null,{"className":"text-4xl sm:text-5xl xl:text-[60px] leading-tight font-bold text-[#212244] mb-16 max-w-[1000px]","children":"Terms and Conditions"}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"General"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"Before using the service of Sans rendez-vous express Inc. (hereinafter referred to as \"SRVXP\"), please take the time to carefully read the following Terms and Conditions (hereinafter referred to as the \"Terms\"). These govern your use of our website www.sansrendezvousexpress.ca (hereinafter the \"Platform\") and the service offered by SRVXP, a company legally registered in Quebec, Canada."}],["$","p",null,{"children":"By accessing and using the Platform, you, the end user or the organization you represent (hereinafter referred to as \"you\", \"the User\" or \"the Users\"), agree to be bound by these Terms as well as our Privacy Policy."}],["$","p",null,{"children":"SRVXP provides you, through its Platform, the opportunity to make a one-time appointment at a walk-in clinic in Quebec (hereinafter referred to as the \"Service\")."}],["$","p",null,{"children":"This contract legally binds you, the User, and SRVXP. Both parties may enforce these Terms and take necessary actions to ensure their compliance."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Acceptance of the Terms"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"By accessing and using the Platform, you agree to be bound by these Terms as well as our Privacy Policy. If you do not accept these Terms, please do not use our Platform."}],["$","p",null,{"children":"By accessing and using the Platform, you, the end user or the organization you represent (hereinafter referred to as \"you\", \"the User\" or \"the Users\"), agree to be bound by these Terms as well as our Privacy Policy. If you do not accept these Terms, or their periodically updated versions, please do not use our Platform."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Platform Availability"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"Our Platform is generally accessible at all times. However, there may be temporary service interruptions due to maintenance, updates, or technical issues. SRVXP is not responsible for these interruptions and attempts to schedule updates during periods of low traffic to minimize the impact on Users."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Platform Compatibility and Performance"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"We cannot guarantee that our platform will perform optimally on all devices and configurations. Additionally, like any online service, it may experience slowdowns and malfunctions related to the use of the internet and electronic communications."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Platform Use"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"When you use our Service, we may ask for information related to your health insurance card. This information is crucial for us to provide you with the requested Service, in accordance with current laws and regulations as well as our Terms. You have the right to refuse to provide us with this information. However, please note that in such cases, we might not be able to offer you the Service in question."}],["$","p",null,{"children":"It is prohibited to use the Platform in a way that could:"}],["$","ul",null,{"className":"list-disc pl-6 space-y-2","children":[["$","li",null,{"children":"Cause technical damage to its infrastructure"}],["$","li",null,{"children":"Interfere with its proper functioning"}],["$","li",null,{"children":"Degrade the quality of the Service for other Users by overloading it"}]]}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Content Modification"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"SRVXP reserves the right to modify, add, or remove any content on the Platform, except for the personal data of Users. We are not responsible for errors, typos, or bugs that may occur."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"User Content Ownership"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"Each User remains the owner of the content published from their account. SRVXP will only delete this content at the User's request or if it violates our Terms. We may also delete certain personal data to enhance security, in accordance with our Privacy Policy."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Intellectual Property"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"All elements of the Platform (texts, graphics, logos, videos, etc.) are the exclusive property of SRVXP and are protected by intellectual property laws. You are not authorized to reproduce or use these elements without our prior written consent."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"External Links"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"Our Platform may contain links to third-party websites. We have no control over the content of these sites and disclaim any responsibility for their content or any damages that may result from their use."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Limitation of Liability"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"SRVXP strives to provide accurate and up-to-date information on its Platform, but cannot guarantee its completeness or accuracy. Except in cases where the Quebec Consumer Protection Act applies, SRVXP cannot be held liable for any direct or indirect damages resulting from the use of the Platform."}],["$","p",null,{"children":"In the event of non-compliance with your obligations as a User, you agree to ward off SRVXP against any claims or legal actions that may arise."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Measures in Case of Non-Compliance"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"If a User violates these Terms, or if we have reasonable grounds to believe that their actions threaten our Platform, our other Users, or third parties, we reserve the right to limit or block the User in question's access to certain features or sections of our website, temporarily or permanently, depending on the severity of the breach."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Priority to French"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"In the event of a conflict in the interpretation of these Terms compared with the French version, the French version shall prevail."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Updating Terms"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"These Terms may be periodically updated to remain compliant with laws and to reflect changes in the management of our site as well as the expectations of our site's Users. We advise our Users to regularly review these Terms to stay informed of any updates. If necessary, we will notify Users of changes via email or we will post a notice on our site. If you continue to use the Platform, this will indicate that you accept the new Terms."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Interpretation Rules"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"The titles of the various sections of the Terms are provided solely for reference and clarity. They should not be considered when interpreting or applying the provisions contained in these Terms."}],["$","p",null,{"children":"In the Terms, words written in the singular also include their plural form when the context requires it, and vice versa. Similarly, words indicating a gender, male or female, encompass the other gender when necessary for a correct understanding of the text."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Divisibility"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"If, at any time, a clause in the Terms is declared non-compliant or invalid according to the applicable laws, this clause will then be considered null and void and will be excluded from the Terms. The other clauses will remain in force and will not be affected by this invalidity, and the rest of the Terms will continue to be considered valid."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Governing Law"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"These Terms are governed by the laws applicable in the province of Quebec, Canada. Any dispute will be subject to the exclusive jurisdiction of the courts of the city of Montreal."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Contacting Us"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":["For any questions regarding these Terms, you can contact us by email at ",["$","a",null,{"href":"mailto:<EMAIL>","className":"text-blue-600 hover:underline","children":"<EMAIL>"}]," or by mail at the following address:"]}],["$","p",null,{"children":["PO BOX 99900 VG 550 446",["$","br",null,{}],"RPO VILLENEUVE",["$","br",null,{}],"MONTREAL QC",["$","br",null,{}],"H2T 0A6"]}]]}]]}]]}]}],["$","$L6",null,{}]]}],"$undefined",null,["$","$L7",null,{"children":["$L8","$L9",null]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","52fRkrUqbo879jpzdSUo-",{"children":[["$","$La",null,{"children":"$Lb"}],["$","meta",null,{"name":"next-size-adjust","content":""}]]}],["$","$Lc",null,{"children":"$Ld"}]]}],false]],"m":"$undefined","G":["$e","$undefined"],"s":false,"S":true}
b:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
8:null
9:null
d:[["$","title","0",{"children":"Terms and Conditions | Sans rendez-vous express"}],["$","meta","1",{"name":"description","content":"Terms and Conditions for Sans rendez-vous express."}]]
