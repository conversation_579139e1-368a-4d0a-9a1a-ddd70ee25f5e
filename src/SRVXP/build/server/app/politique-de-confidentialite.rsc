1:"$Sreact.fragment"
2:I[15945,["7957","static/chunks/routes-d340d2550dd9e8db.js","5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","7177","static/chunks/app/layout-af3f1353f13967be.js"],"default"]
3:I[25704,[],""]
4:I[73566,[],""]
5:I[2067,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","3642","static/chunks/app/politique-de-confidentialite/page-9b4d2a7059273bb4.js"],"Navbar"]
6:I[10244,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","3642","static/chunks/app/politique-de-confidentialite/page-9b4d2a7059273bb4.js"],"Footer"]
7:I[52329,[],"OutletBoundary"]
a:I[52329,[],"ViewportBoundary"]
c:I[52329,[],"MetadataBoundary"]
e:I[71639,[],""]
:HL["/_next/static/media/569ce4b8f30dc480-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/93f479601ee12b01-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/234b042d6f756282.css","style"]
:HL["/_next/static/css/f30152c0704fba31.css","style"]
0:{"P":null,"b":"OmbAXfhZLD7aTmFcXGoiH","p":"","c":["","politique-de-confidentialite"],"i":false,"f":[[["",{"children":["politique-de-confidentialite",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/234b042d6f756282.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/f30152c0704fba31.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"fr","className":"__variable_5cfdac __variable_9a8899","suppressHydrationWarning":true,"children":["$","body",null,{"className":"antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"$undefined",[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],{"children":["politique-de-confidentialite",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen flex flex-col","children":[["$","$L5",null,{}],["$","main",null,{"className":"flex-grow bg-white","children":["$","div",null,{"className":"max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-6 py-12","children":[["$","div",null,{"className":"mb-4 text-gray-500","children":["$","p",null,{"children":"Dernière mise à jour le 24 mars, 2025"}]}],["$","h1",null,{"className":"text-4xl sm:text-5xl xl:text-[60px] leading-tight font-bold text-[#212244] mb-16 max-w-[1000px]","children":"Politique de confidentialité"}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Général"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"En utilisant notre site www.sansrendezvousexpress.ca (ci-après la \"Plateforme\"), vous acceptez les termes de cette politique de confidentialité (ci-après la \"Politique\")."}],["$","p",null,{"children":"Le but de cette Politique est de renseigner les utilisateurs de notre Plateforme sur les types de données personnelles que nous collectons et la manière dont elles sont utilisées."}],["$","p",null,{"children":"Sans rendez-vous express Inc. (ci-après \"SRVE\") se conforme strictement à la loi 25 du Québec en matière de protection des données personnelles. SRVE est une entreprise légalement enregistrée au Québec, Canada."}],["$","p",null,{"children":"Cette Politique explique également notre engagement et les actions que nous entreprenons pour assurer la protection de vos informations personnelles."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Acceptation de cette Politique"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"En accédant et en utilisant la Plateforme, vous, l'utilisateur final ou l'organisation que vous représentez (ci-après désigné par \"vous\", \"l'Utilisateur\" ou \"les Utilisateurs\"), acceptez d'être lié par la présente Politique, ou leurs versions mises à jour périodiquement, ainsi que par nos Conditions d'utilisation. Si vous n'acceptez pas cette Politique, veuillez ne pas utiliser notre Plateforme."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Collecte des renseignements personnels"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"Lors de votre visite sur la Plateforme, nous collectons automatiquement des informations sur votre appareil et votre interaction avec notre site via l'outil Google Analytics."}],["$","p",null,{"children":"Cela inclut les données suivantes:"}],["$","ul",null,{"className":"list-disc pl-6 space-y-2","children":[["$","li",null,{"children":"Adresse IP"}],["$","li",null,{"children":"Lieu"}],["$","li",null,{"children":"Détails matériels et logiciels"}],["$","li",null,{"children":"Contenu que l'utilisateur consulte sur notre site"}],["$","li",null,{"children":"Liens sur lesquels utilisateur un utilisateur clique en allant sur le site"}],["$","li",null,{"children":"Données personnelles récoltées non automatiquement"}]]}],["$","p",null,{"className":"mt-6","children":"Vos données personnelles sont collectées via:"}],["$","ul",null,{"className":"list-disc pl-6 space-y-2","children":[["$","li",null,{"children":"Formulaire de contact"}],["$","li",null,{"children":"Formulaire de demande de rendez-vous médical"}]]}],["$","p",null,{"className":"mt-6","children":"Cela inclut les données suivantes:"}],["$","ul",null,{"className":"list-disc pl-6 space-y-2","children":[["$","li",null,{"children":"Prénom"}],["$","li",null,{"children":"Nom"}],["$","li",null,{"children":"Numéro d'assurance maladie"}],["$","li",null,{"children":"Numéro séquentiel de la carte d'assurance maladie"}],["$","li",null,{"children":"Date de naissance"}],["$","li",null,{"children":"Sexe"}],["$","li",null,{"children":"Code postal proche du rendez-vous"}],["$","li",null,{"children":"Raison de la consultation"}],["$","li",null,{"children":"Courriel"}],["$","li",null,{"children":"Téléphone mobile"}],["$","li",null,{"children":"Téléphone à la maison"}],["$","li",null,{"children":"Information de paiement"}]]}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Utilisation des témoins (cookies)"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"La Plateforme utilise des témoins de navigation (cookies) ainsi que des pixels de Facebook et Google pour vous offrir une expérience personnalisée."}],["$","p",null,{"children":"Les témoins de navigation sont des fichiers textuels qui sont transmis à votre navigateur lorsque vous accédez à un site Web. Les témoins de Google Analytics transmettent des statistiques anonymes de consultation. Ils permettent, entre autres, de connaître le nombre de visites sur une page et le temps moyen de ces visites."}],["$","p",null,{"children":"Les pixels Facebook et Google sont du code JavaScript permettant de détecter, notamment, le nombre de visites sur une page, et ce, de façon anonyme. Les pixels ne sont pas placés sur votre ordinateur; ils demeurent sur le site consulté. Ils sont utilisés pour recueillir des statistiques, analyser les performances du site et faire de la publicité ciblée par centres d'intérêt."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Utilisation des données"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"Veuillez noter que nous ne recueillons que les données nécessaires pour atteindre les objectifs décrits dans la Politique. Nous ne collecterons pas d'informations supplémentaires sans vous en informer à l'avance."}],["$","p",null,{"children":"Nous utilisons ces données pour:"}],["$","ul",null,{"className":"list-disc pl-6 space-y-2","children":[["$","li",null,{"children":"Trouver des rendez-vous médical"}],["$","li",null,{"children":"Envoyer des offres promotionnelles et des communications ciblées via notre infolettre"}],["$","li",null,{"children":"Faire des analyses statistiques pour améliorer le site"}],["$","li",null,{"children":"Faire un suivi des demandes de rendez-vous médical"}],["$","li",null,{"children":"Faire un suivi des messages reçu à travers le formulaire de contact"}],["$","li",null,{"children":"Dans le cadre d'un protocole de sécurité des données"}]]}],["$","p",null,{"children":"Les données des utilisateurs peuvent être consultées, traitées ou collectées au Canada."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Durée d'exploitation des données personnelles"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"Nous conservons pour une période indéterminée vos informations de contact et les 4 premiers caractères de votre numéro d'assurance maladie pour faciliter la prise de rendez-vous. Vous pouvez nous contacter si vous souhaitez apporter des modifications, supprimer ou anonymiser vos données personnelles."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Mesures de sécurité"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"Vos données sont stockées dans un environnement sécurisé et contrôlé. La Plateforme utilise des protocoles SSL et des mesures de sécurité robustes avec double authentification pour prévenir tout accès non autorisé."}],["$","p",null,{"children":"Nous nous engageons à ne pas vendre ou partager vos données avec des tiers sauf si la loi l'exige ou en cas de procédure judiciaire."}],["$","p",null,{"children":"Nous pouvons divulguer à tout membre de notre organisation les données utilisateur dont il a raisonnablement besoin pour réaliser les objectifs énoncés dans la présente Politique."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Gestion des incidents de sécurité"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"En cas de fuite de vos données personnelles, nous nous engageons à vous en tenir informé ainsi que la Commission d'Accès à l'Information du Québec."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Droit des utilisateurs"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"Si vous êtes résident du Québec, conformément à la loi 25, vous bénéficiez de droits spécifiques concernant vos données personnelles:"}],["$","ul",null,{"className":"list-disc pl-6 space-y-2","children":[["$","li",null,{"children":"Droit d'être informé"}],["$","li",null,{"children":"Droit d'accès"}],["$","li",null,{"children":"Droit à la rectification"}],["$","li",null,{"children":"Droit à la portabilité des données"}]]}],["$","p",null,{"className":"mt-6","children":"Vous avez le droit de vous opposer à l'utilisation de vos données et de les retirer. Pour exercer ces droits, contactez notre responsable de la protection des données personnelles:"}],["$","p",null,{"className":"mt-6 font-semibold","children":"Par courrier:"}],["$","p",null,{"children":["Sans rendez-vous express - Responsable de la protection des renseignements personnels",["$","br",null,{}],"CP 99900 VG 550 446",["$","br",null,{}],"COP Villeneuve",["$","br",null,{}],"Montréal (Québec)",["$","br",null,{}],"H2T 0A6"]}],["$","p",null,{"className":"mt-6 font-semibold","children":"Par courriel:"}],["$","p",null,{"children":["$","a",null,{"href":"mailto:<EMAIL>","className":"text-blue-600 hover:underline","children":"<EMAIL>"}]}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Droit d'accès et de rectification"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"Vous pouvez consulter, modifier ou supprimer vos données. Pour ce faire, utilisez les mêmes coordonnées mentionnées ci-dessus."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Consentement et retrait"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"Il est possible de naviguer sur notre site de manière anonyme. Toutefois, l'accès à certaines fonctionnalités peut nécessiter la collecte de données personnelles. Vous avez toujours l'option de ne pas fournir ces informations."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Mineurs"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"La loi 25 précise que les personnes de moins de 14 ans sont considérées comme des mineurs aux fins de la collecte de données. Leurs renseignements personnels ne pourront être recueillis auprès de celui-ci sans le consentement du titulaire de l'autorité parentale ou du tuteur, sauf lorsque cette collecte sera manifestement au bénéfice de ce mineur."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Liens externes"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"Notre Plateforme peut contenir des liens vers des sites web tiers. Nous n'avons aucun contrôle sur le contenu de ces sites et déclinons toute responsabilité quant à leur contenu ou aux dommages pouvant résulter de leur utilisation."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Priorité au français"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"En cas de conflit d'interprétation de la Politique par rapport à celui en anglais, cette version française prévaudra."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Modification de la Politique"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"Nous pouvons être amenés à modifier occasionnellement la Politique. Vous serez informés de ces changements lors de votre prochaine visite. Si vous continuez à utiliser la Plateforme, cela vaudra acceptation de la Politique."}],["$","p",null,{"children":"Nous conseillons à nos utilisateurs de consulter régulièrement la Politique pour rester informés des éventuelles mises à jour. Si nécessaire, nous notifierons les utilisateurs des modifications par courriel ou nous publierons un avis sur notre site. Si vous continuez à utiliser la Plateforme, cela indiquera que vous acceptez la nouvelle Politique."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Règles d'interprétation"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"Les titres des différentes sections de la Politique sont fournis uniquement à des fins de référence et de clarté. Ils ne doivent pas être pris en compte lors de l'interprétation ou de l'application des dispositions contenues dans ce document."}],["$","p",null,{"children":"Dans la Politique, les mots écrits au singulier incluent également leur forme plurielle lorsque le contexte le requiert, et inversement. De même, les mots désignant un genre, masculin ou féminin, englobent l'autre genre lorsque cela est nécessaire pour une compréhension correcte du texte."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Divisibilité"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"Si, à un moment quelconque, une clause de ce document est déclarée non conforme ou invalide selon les lois en vigueur, cette clause sera alors considérée comme nulle et sera exclue de ce document. Les autres clauses resteront en vigueur et ne seront pas affectées par cette invalidité, et le reste du document continuera à être considéré comme valide."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Loi applicable"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"Cette Politique est régie par les lois applicables dans la province du Québec, Canada. Tout litige sera soumis à la compétence exclusive des tribunaux de la ville de Montréal."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Nous contacter"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":["Pour toute question ou plainte relative à la Politique, vous pouvez nous contacter à ",["$","a",null,{"href":"mailto:<EMAIL>","className":"text-blue-600 hover:underline","children":"<EMAIL>"}],"."]}]}]]}]]}]}],["$","$L6",null,{}]]}],"$undefined",null,["$","$L7",null,{"children":["$L8","$L9",null]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","bIf1Z1PbXMReCw6HbbYQD",{"children":[["$","$La",null,{"children":"$Lb"}],["$","meta",null,{"name":"next-size-adjust","content":""}]]}],["$","$Lc",null,{"children":"$Ld"}]]}],false]],"m":"$undefined","G":["$e","$undefined"],"s":false,"S":true}
b:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
8:null
9:null
d:[["$","title","0",{"children":"Politique de confidentialité | Sans rendez-vous express"}],["$","meta","1",{"name":"description","content":"Politique de confidentialité pour Sans rendez-vous express."}]]
