1:"$Sreact.fragment"
2:I[15945,["7957","static/chunks/routes-d340d2550dd9e8db.js","5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","7177","static/chunks/app/layout-af3f1353f13967be.js"],"default"]
3:I[25704,[],""]
4:I[73566,[],""]
5:I[2067,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","354","static/chunks/app/politique-de-confidentialite-EN/page-2a3beb4a6eb70a4d.js"],"Navbar"]
6:I[10244,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","354","static/chunks/app/politique-de-confidentialite-EN/page-2a3beb4a6eb70a4d.js"],"Footer"]
7:I[52329,[],"OutletBoundary"]
a:I[52329,[],"ViewportBoundary"]
c:I[52329,[],"MetadataBoundary"]
e:I[71639,[],""]
:HL["/_next/static/media/569ce4b8f30dc480-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/93f479601ee12b01-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/234b042d6f756282.css","style"]
:HL["/_next/static/css/f30152c0704fba31.css","style"]
0:{"P":null,"b":"OmbAXfhZLD7aTmFcXGoiH","p":"","c":["","politique-de-confidentialite-EN"],"i":false,"f":[[["",{"children":["politique-de-confidentialite-EN",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/234b042d6f756282.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/f30152c0704fba31.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"fr","className":"__variable_5cfdac __variable_9a8899","suppressHydrationWarning":true,"children":["$","body",null,{"className":"antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"$undefined",[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],{"children":["politique-de-confidentialite-EN",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen flex flex-col","children":[["$","$L5",null,{}],["$","main",null,{"className":"flex-grow bg-white","children":["$","div",null,{"className":"max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-6 py-12","children":[["$","div",null,{"className":"mb-4 text-gray-500","children":["$","p",null,{"children":"Last updated on March 24, 2025"}]}],["$","h1",null,{"className":"text-4xl sm:text-5xl xl:text-[60px] leading-tight font-bold text-[#212244] mb-16 max-w-[1000px]","children":"Privacy Policy"}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"General"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"By using our website www.sansrendezvousexpress.ca (hereinafter referred to as the \"Platform\"), you agree to the terms of this Privacy Policy (hereinafter referred to as the \"Policy\")."}],["$","p",null,{"children":"The purpose of this Policy is to inform users of our Platform about the types of personal data we collect and how they are used."}],["$","p",null,{"children":"Sans rendez-vous express Inc. (hereinafter referred to as \"SRVXP\") strictly adheres to Quebec's Law 25 concerning the protection of personal data. SRVXP is a company legally registered in Quebec, Canada."}],["$","p",null,{"children":"This Policy also outlines our commitment and the measures we take to ensure the protection of your personal information."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Acceptance of the Policy"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"By accessing and using the Platform, you, the end user or the organization you represent (hereinafter referred to as \"you\", \"the User\" or \"the Users\"), agree to be bound by this Policy as well as our Terms and Conditions. If you do not accept this Policy, please do not use our Platform."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Collection of Personal Information"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"During your visit to the Platform, we automatically collect information about your device and your interaction with our site via Google Analytics."}],["$","p",null,{"children":"This includes the following data:"}],["$","ul",null,{"className":"list-disc pl-6 space-y-2","children":[["$","li",null,{"children":"IP Address"}],["$","li",null,{"children":"Location"}],["$","li",null,{"children":"Hardware and software details"}],["$","li",null,{"children":"Content that the User views on our site"}],["$","li",null,{"children":"Links that a User clicks on while visiting the site"}],["$","li",null,{"children":"Non-Automatically Collected Personal Data"}]]}],["$","p",null,{"className":"mt-6","children":"Your personal data is collected through:"}],["$","ul",null,{"className":"list-disc pl-6 space-y-2","children":[["$","li",null,{"children":"Contact form"}],["$","li",null,{"children":"Medical appointment request form"}]]}],["$","p",null,{"className":"mt-6","children":"This includes the following data:"}],["$","ul",null,{"className":"list-disc pl-6 space-y-2","children":[["$","li",null,{"children":"First name"}],["$","li",null,{"children":"Last name"}],["$","li",null,{"children":"Quebec Health insurance number"}],["$","li",null,{"children":"Sequential number of the Quebec Health Insurance Card"}],["$","li",null,{"children":"Date of birth"}],["$","li",null,{"children":"Gender"}],["$","li",null,{"children":"Postal code near the appointment location"}],["$","li",null,{"children":"Reason for the consultation"}],["$","li",null,{"children":"Email"}],["$","li",null,{"children":"Mobile phone"}],["$","li",null,{"children":"Home phone"}],["$","li",null,{"children":"Payment information"}]]}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Use of Cookies"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"The Platform uses navigation cookies as well as Facebook and Google pixels to offer you a personalized experience."}],["$","p",null,{"children":"Navigation cookies are text files that are transmitted to your browser when you access a website. Google Analytics cookies transmit anonymous browsing statistics. They allow, among other things, to know the number of visits to a page and the average time of these visits."}],["$","p",null,{"children":"Facebook and Google pixels are JavaScript code that allows for the detection of, among other things, the number of visits to a page, anonymously. The pixels are not placed on your computer; they remain on the visited site. They are used to collect statistics, analyze site performance, and conduct targeted advertising based on interests."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Data Usage"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"Please note that we only collect data necessary to achieve the objectives described in the Policy. We will not collect additional information without informing you in advance."}],["$","p",null,{"children":"We use this data to:"}],["$","ul",null,{"className":"list-disc pl-6 space-y-2","children":[["$","li",null,{"children":"Find medical appointments"}],["$","li",null,{"children":"Send promotional offers and targeted communications via our newsletter"}],["$","li",null,{"children":"Conduct statistical analyses to improve the site"}],["$","li",null,{"children":"Track requests for medical appointments"}],["$","li",null,{"children":"Follow up on messages received through the contact form"}],["$","li",null,{"children":"As part of a data security protocol"}]]}],["$","p",null,{"children":"User data may be accessed, processed, or collected in Canada."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Personal Data Retention Period"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"We store personal data for a duration of 7 days, after which it is permanently deleted. However, the first 8 characters of the Users' health insurance numbers are preserved for a period of 5 years to ensure that the free trial of the service is not used more than once."}],["$","p",null,{"children":"We will ensure that Users are notified if their data is retained longer than this period. You can contact us if you wish to make changes, delete, or anonymize your personal data."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Security Incident Management"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"In the event of a leak of your personal data, we commit to keeping you informed as well as notifying the Quebec's Access to Information Commission."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"User Rights"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"If you are a resident of Quebec, in accordance with Law 25, you have specific rights regarding your personal data:"}],["$","ul",null,{"className":"list-disc pl-6 space-y-2","children":[["$","li",null,{"children":"Right to be informed"}],["$","li",null,{"children":"Right of access"}],["$","li",null,{"children":"Right to rectification"}],["$","li",null,{"children":"Right to data portability"}]]}],["$","p",null,{"className":"mt-6","children":"You have the right to object to the use of your data and to withdraw it. To exercise these rights, contact our data protection officer:"}],["$","p",null,{"className":"mt-6 font-semibold","children":"By mail:"}],["$","p",null,{"children":["Sans rendez-vous express - Data Protection Officer",["$","br",null,{}],"PO BOX 99900 VG 550 446",["$","br",null,{}],"RPO VILLENEUVE",["$","br",null,{}],"MONTREAL QC",["$","br",null,{}],"H2T 0A6"]}],["$","p",null,{"className":"mt-6 font-semibold","children":"By email:"}],["$","p",null,{"children":["$","a",null,{"href":"mailto:<EMAIL>","className":"text-blue-600 hover:underline","children":"<EMAIL>"}]}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Right of Access and Rectification"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"You can view, modify, or delete your data. To do so, use the same contact details mentioned above."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Consent and Withdrawal"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"It is possible to browse our site anonymously. However, access to certain features may require the collection of personal data. You always have the option not to provide this information."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Minors"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"Law 25 specifies that individuals under the age of 14 are considered minors for the purposes of data collection. Their personal information cannot be collected from them without the consent of the person holding parental authority or the guardian, except when the collection is clearly for the benefit of the minor."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"External Links"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"Our Platform may contain links to third-party websites. We have no control over the content of these sites and disclaim any responsibility for their content or any damages that may result from their use."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Priority to French"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"In the event of a conflict in the interpretation of the Policy compared with the French version, the French version shall prevail."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Updating the Policy"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"This Policy may be periodically updated to remain compliant with laws and to reflect changes in the management of our site as well as the expectations of our site's Users."}],["$","p",null,{"children":"We advise our Users to regularly review this Policy to stay informed of any updates. If necessary, we will notify Users of changes via email or we will post a notice on our site. If you continue to use the Platform, this will indicate that you accept the new Policy."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Interpretation Rules"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":[["$","p",null,{"children":"The titles of the various sections of the Policy are provided solely for reference and clarity. They should not be considered when interpreting or applying the provisions contained in this Policy."}],["$","p",null,{"children":"In the Policy, words written in the singular also include their plural form when the context requires it, and vice versa. Similarly, words indicating a gender, male or female, encompass the other gender when necessary for a correct understanding of the text."}]]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Divisibility"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"If, at any time, a clause in the Policy is declared non-compliant or invalid according to the applicable laws, this clause will then be considered null and void and will be excluded from the Policy. The other clauses will remain in force and will not be affected by this invalidity, and the rest of the Policy will continue to be considered valid."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Governing Law"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":"This Policy is governed by the laws applicable in the province of Quebec, Canada. Any dispute will be subject to the exclusive jurisdiction of the courts of the city of Montreal."}]}]]}],["$","section",null,{"className":"mb-12","children":[["$","h2",null,{"className":"text-2xl sm:text-3xl font-bold text-[#212244] mb-6","children":"Contacting Us"}],["$","div",null,{"className":"text-gray-700 space-y-6 max-w-[1000px]","children":["$","p",null,{"children":["For any questions regarding this Policy, please contact us by email at ",["$","a",null,{"href":"mailto:<EMAIL>","className":"text-blue-600 hover:underline","children":"<EMAIL>"}],"."]}]}]]}]]}]}],["$","$L6",null,{}]]}],"$undefined",null,["$","$L7",null,{"children":["$L8","$L9",null]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","Qrj_C8evzzLF7_48-GWqv",{"children":[["$","$La",null,{"children":"$Lb"}],["$","meta",null,{"name":"next-size-adjust","content":""}]]}],["$","$Lc",null,{"children":"$Ld"}]]}],false]],"m":"$undefined","G":["$e","$undefined"],"s":false,"S":true}
b:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
8:null
9:null
d:[["$","title","0",{"children":"Privacy Policy | Sans rendez-vous express"}],["$","meta","1",{"name":"description","content":"Privacy Policy for Sans rendez-vous express."}]]
