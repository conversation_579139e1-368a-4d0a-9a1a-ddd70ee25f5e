1:"$Sreact.fragment"
2:I[15945,["7957","static/chunks/routes-d340d2550dd9e8db.js","5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","7177","static/chunks/app/layout-af3f1353f13967be.js"],"default"]
3:I[25704,[],""]
4:I[73566,[],""]
5:I[91303,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","8974","static/chunks/app/page-9b5ac2cdfd60c602.js"],"LanguageProvider"]
6:I[2067,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","8974","static/chunks/app/page-9b5ac2cdfd60c602.js"],"Navbar"]
7:I[44868,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","8974","static/chunks/app/page-9b5ac2cdfd60c602.js"],"HeroSection"]
8:I[59955,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","8974","static/chunks/app/page-9b5ac2cdfd60c602.js"],"FeaturesSection"]
9:I[67547,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","8974","static/chunks/app/page-9b5ac2cdfd60c602.js"],"HowItWorksSection"]
a:I[67332,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","8974","static/chunks/app/page-9b5ac2cdfd60c602.js"],"PricingSection"]
b:I[92878,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","8974","static/chunks/app/page-9b5ac2cdfd60c602.js"],"FaqSection"]
c:I[5326,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","8974","static/chunks/app/page-9b5ac2cdfd60c602.js"],"CtaSection2"]
d:I[10244,["5179","static/chunks/components-ceb3155dfc198c1c.js","5738","static/chunks/utils-542d5cfa42eb2b0f.js","8974","static/chunks/app/page-9b5ac2cdfd60c602.js"],"Footer"]
e:I[52329,[],"OutletBoundary"]
11:I[52329,[],"ViewportBoundary"]
13:I[52329,[],"MetadataBoundary"]
15:I[71639,[],""]
:HL["/_next/static/media/569ce4b8f30dc480-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/93f479601ee12b01-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/234b042d6f756282.css","style"]
:HL["/_next/static/css/f30152c0704fba31.css","style"]
0:{"P":null,"b":"OmbAXfhZLD7aTmFcXGoiH","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/234b042d6f756282.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/f30152c0704fba31.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"fr","className":"__variable_5cfdac __variable_9a8899","suppressHydrationWarning":true,"children":["$","body",null,{"className":"antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"$undefined",[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L5",null,{"children":["$","div",null,{"className":"min-h-screen flex flex-col","children":[["$","$L6",null,{}],["$","main",null,{"className":"flex-grow","children":[["$","div",null,{"className":"bg-[#f8f9fb]","children":["$","div",null,{"className":"container mx-auto px-4 sm:px-6","children":["$","$L7",null,{}]}]}],["$","div",null,{"className":"bg-white w-full","children":["$","div",null,{"className":"container mx-auto px-4 sm:px-6","children":["$","$L8",null,{}]}]}],["$","div",null,{"className":"bg-white w-full","children":["$","div",null,{"className":"container mx-auto px-4 sm:px-6","children":["$","$L9",null,{}]}]}],["$","div",null,{"className":"bg-[#f8f9fb] w-full","children":["$","div",null,{"className":"container mx-auto px-4 sm:px-6","children":["$","$La",null,{}]}]}],["$","div",null,{"className":"bg-white w-full","children":["$","div",null,{"className":"container mx-auto px-4 sm:px-6","children":["$","$Lb",null,{}]}]}],["$","$Lc",null,{}]]}],["$","$Ld",null,{}]]}]}],"$undefined",null,["$","$Le",null,{"children":["$Lf","$L10",null]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","rh6FBo-TmFkz8vWCkZzSN",{"children":[["$","$L11",null,{"children":"$L12"}],["$","meta",null,{"name":"next-size-adjust","content":""}]]}],["$","$L13",null,{"children":"$L14"}]]}],false]],"m":"$undefined","G":["$15","$undefined"],"s":false,"S":true}
12:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
f:null
10:null
14:[["$","title","0",{"children":"Rendez-vous Médicaux"}],["$","meta","1",{"name":"description","content":"Gérez vos rendez-vous médicaux en ligne"}]]
