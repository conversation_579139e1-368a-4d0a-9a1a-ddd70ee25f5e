(()=>{var e={};e.id=322,e.ids=[322,908],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27548:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,56495,23)),Promise.resolve().then(t.t.bind(t,33395,23)),Promise.resolve().then(t.t.bind(t,9287,23)),Promise.resolve().then(t.t.bind(t,80150,23)),Promise.resolve().then(t.t.bind(t,65138,23)),Promise.resolve().then(t.t.bind(t,93558,23)),Promise.resolve().then(t.t.bind(t,66989,23)),Promise.resolve().then(t.t.bind(t,38197,23))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41935:(e,r,t)=>{Promise.resolve().then(t.bind(t,10007)),Promise.resolve().then(t.bind(t,90369)),Promise.resolve().then(t.bind(t,51407))},48825:(e,r,t)=>{Promise.resolve().then(t.bind(t,67993))},50183:(e,r,t)=>{Promise.resolve().then(t.bind(t,91632)),Promise.resolve().then(t.bind(t,88597)),Promise.resolve().then(t.bind(t,65187))},50498:()=>{},55591:e=>{"use strict";e.exports=require("https")},61849:(e,r,t)=>{Promise.resolve().then(t.bind(t,68757))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69404:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,17803,23)),Promise.resolve().then(t.t.bind(t,23783,23)),Promise.resolve().then(t.t.bind(t,7979,23)),Promise.resolve().then(t.t.bind(t,13642,23)),Promise.resolve().then(t.t.bind(t,55486,23)),Promise.resolve().then(t.t.bind(t,59098,23)),Promise.resolve().then(t.t.bind(t,31289,23)),Promise.resolve().then(t.t.bind(t,61169,23))},69561:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>m,tree:()=>d});var s=t(5388),i=t(33203),o=t(7979),n=t.n(o),a=t(47652),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d={children:["",{children:["aide",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,34173)),"/Users/<USER>/src/SRVXP/src/app/aide/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,89906)),"/Users/<USER>/src/SRVXP/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,60793,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,95028,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,25557,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/Users/<USER>/src/SRVXP/src/app/aide/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/aide/page",pathname:"/aide",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85364:e=>{"use strict";e.exports=require("ws")},90976:(e,r,t)=>{"use strict";t.d(r,{w:()=>o}),t(4473);var s=t(80507),i=t(40869);function o(){let{theme:e,setTheme:r}=(0,s.D)(),{user:t,status:o}=(0,i.A)();return{theme:e}}t(98388)},97124:(e,r,t)=>{"use strict";t.d(r,{s:()=>l});var s=t(4473),i=t(40869),o=t(65563),n=t(63250);let a={};function l(){let e=async e=>{try{let r=await (0,o.V)(e),t=r.firstName||"",s=r.lastName||"",i={firstName:t,lastName:s,avatar:null,initials:`${t.charAt(0)}${s.charAt(0)}`.toUpperCase()||"??",role:"user",fullName:t||s?`${t} ${s}`.trim():"User",phone:r.phone||""};return m(i),b&&(localStorage.setItem(b,JSON.stringify(i)),a[b]=i),i}catch(e){throw console.error("Error loading user profile:",e),e}},{user:r,status:t}=(0,i.A)(),{notifyProfileUpdate:l,lastUpdatedUserId:d,lastUpdateTimestamp:u}=(0,n.F)(),[p,m]=(0,s.useState)(null),[c,h]=(0,s.useState)(!0),[v,P]=(0,s.useState)(null),b=(0,s.useMemo)(()=>r?.email?`user_profile_${r.email}`:r?.id?`user_profile_id_${r.id}`:null,[r?.email,r?.id]),x=p?.firstName||"",f=p?.lastName||"",_=r?.email||"",g=x,$=f;if(!x&&!f&&_){let e=_.split("@")[0].split(".");g=e[0]?e[0].charAt(0).toUpperCase()+e[0].slice(1):"",$=e[1]?e[1].charAt(0).toUpperCase()+e[1].slice(1):""}let q=p?.fullName||(x||f?`${x} ${f}`.trim():g||$?`${g} ${$}`.trim():"User"),A=p?.initials||(x||f?`${x.charAt(0)}${f.charAt(0)}`.toUpperCase():g||$?`${g.charAt(0)}${$.charAt(0)}`.toUpperCase():"??"),S=p?.phone||"";return{profile:p,isLoading:c,error:v,initials:A,fullName:q,firstName:x||g,lastName:f||$,phone:S,invalidateCache:(r,s,i=!0)=>{let o=`user_profile_${s}`,n=`user_profile_id_${r}`;localStorage.removeItem(o),localStorage.removeItem(n),o in a&&delete a[o],n in a&&delete a[n],i&&r&&"authenticated"===t&&(e(r),l&&l(r))},reloadProfile:r=>e(r),isFromCache:!!b&&(!!a[b]||!!localStorage.getItem(b))}}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[96,957,738,560],()=>t(69561));module.exports=s})();