(()=>{var e={};e.id=6,e.ids=[6],e.modules={1671:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>o,serverHooks:()=>c,workAsyncStorage:()=>a,workUnitAsyncStorage:()=>n});var s=t(25066),i=t(33203),u=t(46514),p=t(95303);let o=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/subscription/portal/route",pathname:"/api/subscription/portal",filename:"route",bundlePath:"app/api/subscription/portal/route"},resolvedPagePath:"/Users/<USER>/src/SRVXP/src/app/api/subscription/portal/route.ts",nextConfigOutput:"",userland:p}),{workAsyncStorage:a,workUnitAsyncStorage:n,serverHooks:c}=o;function x(){return(0,u.patchFetch)({workAsyncStorage:a,workUnitAsyncStorage:n})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38907:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},85364:e=>{"use strict";e.exports=require("ws")},92051:()=>{},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[96,957,738],()=>t(1671));module.exports=s})();