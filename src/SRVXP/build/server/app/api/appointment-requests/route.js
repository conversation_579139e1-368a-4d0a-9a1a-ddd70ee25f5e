(()=>{var e={};e.id=60,e.ids=[60],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},21479:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>d,routeModule:()=>o,serverHooks:()=>c,workAsyncStorage:()=>a,workUnitAsyncStorage:()=>n});var s=t(25066),i=t(33203),p=t(46514),u=t(15717);let o=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/appointment-requests/route",pathname:"/api/appointment-requests",filename:"route",bundlePath:"app/api/appointment-requests/route"},resolvedPagePath:"/Users/<USER>/src/SRVXP/src/app/api/appointment-requests/route.ts",nextConfigOutput:"",userland:u}),{workAsyncStorage:a,workUnitAsyncStorage:n,serverHooks:c}=o;function d(){return(0,p.patchFetch)({workAsyncStorage:a,workUnitAsyncStorage:n})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38907:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85364:e=>{"use strict";e.exports=require("ws")},92051:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[96,957,738],()=>t(21479));module.exports=s})();