(()=>{var e={};e.id=157,e.ids=[157],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31797:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>c,routeModule:()=>a,serverHooks:()=>d,workAsyncStorage:()=>o,workUnitAsyncStorage:()=>n});var s=r(25066),u=r(33203),i=r(46514),p=r(12946);let a=new s.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/debug/auth-test/route",pathname:"/api/debug/auth-test",filename:"route",bundlePath:"app/api/debug/auth-test/route"},resolvedPagePath:"/Users/<USER>/src/SRVXP/src/app/api/debug/auth-test/route.ts",nextConfigOutput:"",userland:p}),{workAsyncStorage:o,workUnitAsyncStorage:n,serverHooks:d}=a;function c(){return(0,i.patchFetch)({workAsyncStorage:o,workUnitAsyncStorage:n})}},38907:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85364:e=>{"use strict";e.exports=require("ws")},92051:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[96,957,738],()=>r(31797));module.exports=s})();