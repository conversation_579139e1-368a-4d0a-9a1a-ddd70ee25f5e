(()=>{var e={};e.id=865,e.ids=[865],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},23485:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>d,routeModule:()=>p,serverHooks:()=>c,workAsyncStorage:()=>a,workUnitAsyncStorage:()=>n});var s=t(25066),i=t(33203),u=t(46514),o=t(39614);let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/debug/session-check/route",pathname:"/api/debug/session-check",filename:"route",bundlePath:"app/api/debug/session-check/route"},resolvedPagePath:"/Users/<USER>/src/SRVXP/src/app/api/debug/session-check/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:a,workUnitAsyncStorage:n,serverHooks:c}=p;function d(){return(0,u.patchFetch)({workAsyncStorage:a,workUnitAsyncStorage:n})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38907:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85364:e=>{"use strict";e.exports=require("ws")},92051:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[96,957,738],()=>t(23485));module.exports=s})();