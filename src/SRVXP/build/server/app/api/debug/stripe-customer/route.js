(()=>{var e={};e.id=66,e.ids=[66],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},20795:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>d,routeModule:()=>o,serverHooks:()=>n,workAsyncStorage:()=>a,workUnitAsyncStorage:()=>c});var s=t(25066),i=t(33203),u=t(46514),p=t(77771);let o=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/debug/stripe-customer/route",pathname:"/api/debug/stripe-customer",filename:"route",bundlePath:"app/api/debug/stripe-customer/route"},resolvedPagePath:"/Users/<USER>/src/SRVXP/src/app/api/debug/stripe-customer/route.ts",nextConfigOutput:"",userland:p}),{workAsyncStorage:a,workUnitAsyncStorage:c,serverHooks:n}=o;function d(){return(0,u.patchFetch)({workAsyncStorage:a,workUnitAsyncStorage:c})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38907:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},85364:e=>{"use strict";e.exports=require("ws")},92051:()=>{},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[96,957,738],()=>t(20795));module.exports=s})();