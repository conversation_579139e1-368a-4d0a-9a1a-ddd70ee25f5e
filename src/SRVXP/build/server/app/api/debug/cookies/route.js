(()=>{var e={};e.id=355,e.ids=[355],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38907:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63061:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>c,routeModule:()=>u,serverHooks:()=>d,workAsyncStorage:()=>n,workUnitAsyncStorage:()=>p});var s=t(25066),o=t(33203),i=t(46514),a=t(88160);let u=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/debug/cookies/route",pathname:"/api/debug/cookies",filename:"route",bundlePath:"app/api/debug/cookies/route"},resolvedPagePath:"/Users/<USER>/src/SRVXP/src/app/api/debug/cookies/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:n,workUnitAsyncStorage:p,serverHooks:d}=u;function c(){return(0,i.patchFetch)({workAsyncStorage:n,workUnitAsyncStorage:p})}},92051:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[96,957],()=>t(63061));module.exports=s})();