(()=>{var e={};e.id=980,e.ids=[980],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38907:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48687:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>c,routeModule:()=>a,serverHooks:()=>d,workAsyncStorage:()=>o,workUnitAsyncStorage:()=>n});var s=t(25066),u=t(33203),i=t(46514),p=t(53741);let a=new s.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/debug/auth/route",pathname:"/api/debug/auth",filename:"route",bundlePath:"app/api/debug/auth/route"},resolvedPagePath:"/Users/<USER>/src/SRVXP/src/app/api/debug/auth/route.ts",nextConfigOutput:"",userland:p}),{workAsyncStorage:o,workUnitAsyncStorage:n,serverHooks:d}=a;function c(){return(0,i.patchFetch)({workAsyncStorage:o,workUnitAsyncStorage:n})}},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85364:e=>{"use strict";e.exports=require("ws")},92051:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[96,957,738],()=>t(48687));module.exports=s})();