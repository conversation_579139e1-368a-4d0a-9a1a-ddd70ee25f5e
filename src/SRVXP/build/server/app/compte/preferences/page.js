(()=>{var e={};e.id=218,e.ids=[218],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7277:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>l,pages:()=>c,routeModule:()=>d,tree:()=>p});var s=t(5388),n=t(33203),o=t(7979),i=t.n(o),a=t(47652),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);t.d(r,u);let p={children:["",{children:["compte",{children:["preferences",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,92869)),"/Users/<USER>/src/SRVXP/src/app/compte/preferences/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,89906)),"/Users/<USER>/src/SRVXP/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,60793,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,95028,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,25557,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/src/SRVXP/src/app/compte/preferences/page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},d=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/compte/preferences/page",pathname:"/compte/preferences",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19360:(e,r,t)=>{Promise.resolve().then(t.bind(t,54353))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50498:()=>{},55591:e=>{"use strict";e.exports=require("https")},55808:(e,r,t)=>{Promise.resolve().then(t.bind(t,92869))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65751:(e,r,t)=>{"use strict";t.d(r,{H:()=>a});var s=t(4473),n=t(80507),o=t(40869),i=t(98388);function a(){let{user:e,status:r}=(0,o.A)(),{setTheme:t}=(0,n.D)(),[a,u]=(0,s.useState)(null),[p,c]=(0,s.useState)(!0),[l,d]=(0,s.useState)(!1),[x,m]=(0,s.useState)(null),[f,g]=(0,s.useState)(!1);return{preferences:a,isLoading:p,isSaving:l,saveError:x,saveSuccess:f,updatePreferences:async r=>{if(e){console.log("Updating user preferences:",r),d(!0),m(null),g(!1);try{u(r),localStorage.setItem("language",r.language),console.log("Local language preferences updated");let t=await (0,i.b)(e.id,r);if(!t.success)throw t.error||Error("Failed to save preferences");console.log("Preferences saved successfully to Supabase"),g(!0),setTimeout(()=>{g(!1)},3e3)}catch(e){console.error("Error saving preferences:",e),m(e),g(!1)}finally{d(!1)}}}}}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85364:e=>{"use strict";e.exports=require("ws")},90976:(e,r,t)=>{"use strict";t.d(r,{w:()=>o}),t(4473);var s=t(80507),n=t(40869);function o(){let{theme:e,setTheme:r}=(0,s.D)(),{user:t,status:o}=(0,n.A)();return{theme:e}}t(98388)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[96,957,738,560,988,411],()=>t(7277));module.exports=s})();