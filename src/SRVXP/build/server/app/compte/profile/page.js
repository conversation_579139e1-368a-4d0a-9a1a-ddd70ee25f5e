(()=>{var e={};e.id=97,e.ids=[97],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16399:(e,r,t)=>{Promise.resolve().then(t.bind(t,77286))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50498:()=>{},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85364:e=>{"use strict";e.exports=require("ws")},88375:(e,r,t)=>{Promise.resolve().then(t.bind(t,78386))},90976:(e,r,t)=>{"use strict";t.d(r,{w:()=>o}),t(4473);var s=t(80507),i=t(40869);function o(){let{theme:e,setTheme:r}=(0,s.D)(),{user:t,status:o}=(0,i.A)();return{theme:e}}t(98388)},97953:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>l,pages:()=>d,routeModule:()=>c,tree:()=>u});var s=t(5388),i=t(33203),o=t(7979),n=t.n(o),p=t(47652),a={};for(let e in p)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>p[e]);t.d(r,a);let u={children:["",{children:["compte",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,78386)),"/Users/<USER>/src/SRVXP/src/app/compte/profile/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,89906)),"/Users/<USER>/src/SRVXP/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,60793,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,95028,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,25557,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/src/SRVXP/src/app/compte/profile/page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/compte/profile/page",pathname:"/compte/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[96,957,738,560,988,411],()=>t(97953));module.exports=s})();