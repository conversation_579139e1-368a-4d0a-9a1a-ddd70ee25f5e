exports.id=957,exports.ids=[957],exports.modules={4344:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var a=t(57484),r=t(85815),n=t(8152);let i={title:"Conditions g\xe9n\xe9rales de vente | Sans rendez-vous express",description:"Conditions g\xe9n\xe9rales de vente pour Sans rendez-vous express."};function l(){return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(r.Navbar,{}),(0,a.jsx)("main",{className:"flex-grow bg-white",children:(0,a.jsxs)("div",{className:"max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-6 py-12",children:[(0,a.jsx)("div",{className:"mb-4 text-gray-500",children:(0,a.jsx)("p",{children:"Derni\xe8re mise \xe0 jour le 24 mars, 2025"})}),(0,a.jsx)("h1",{className:"text-4xl sm:text-5xl xl:text-[60px] leading-tight font-bold text-[#212244] mb-16 max-w-[1000px]",children:"Conditions g\xe9n\xe9rales de vente"}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"G\xe9n\xe9ral"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:'Les pr\xe9sentes conditions g\xe9n\xe9rales de ventes (ci-apr\xe8s "Conditions") r\xe9gissent l\'utilisation du site www.sansrendezvousexpress.ca (ci-apr\xe8s "Plateforme"). Ce site appartient et est g\xe9r\xe9 par Sans rendez-vous express Inc (ci apr\xe8s "SRVE").'}),(0,a.jsx)("p",{children:'Avant de naviguer sur notre Plateforme, veuillez prendre connaissance des conditions g\xe9n\xe9rales de vente \xe9nonc\xe9es ci-dessous. En acc\xe9dant \xe0 ce site et en l\'utilisant, vous, l\'utilisateur final ou l\'organisation que vous repr\xe9sentez (ci-apr\xe8s d\xe9sign\xe9 par "vous", "l\'Utilisateur" ou "les Utilisateurs") reconnaissez avoir lu, compris et accept\xe9 les Conditions dans leur int\xe9gralit\xe9 et \xe0 les respecter en tout temps.'})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Utilisation acceptable"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"En tant qu'utilisateur de la Plateforme, vous vous engagez \xe0 faire usage de notre site de mani\xe8re l\xe9gale et \xe0 ne pas exploiter ce site \xe0 des fins ill\xe9gales, notamment :"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"Enfreindre les droits d'autres utilisateurs du site"}),(0,a.jsx)("li",{children:"Enfreindre les droits de propri\xe9t\xe9 intellectuelle des d\xe9tenteurs du site ou de tiers associ\xe9s au site"}),(0,a.jsx)("li",{children:"Pirater le compte d'un autre utilisateur du site"}),(0,a.jsx)("li",{children:"Agir de toute fa\xe7on qui pourrait \xeatre consid\xe9r\xe9e comme frauduleuse"}),(0,a.jsx)("li",{children:"Participer \xe0 toute activit\xe9 ill\xe9gale sur le site"}),(0,a.jsx)("li",{children:"Afficher tout mat\xe9riel qui peut \xeatre jug\xe9 inappropri\xe9 ou offensant"})]}),(0,a.jsx)("p",{children:"Si nous consid\xe9rons que votre utilisation de ce site est ill\xe9gale ou enfreint nos conditions d'utilisation ou nos conditions g\xe9n\xe9rales de vente, nous nous r\xe9servons le droit de restreindre, suspendre ou mettre fin \xe0 votre acc\xe8s au site. Nous nous r\xe9servons \xe9galement le droit d'entreprendre toute action l\xe9gale requise pour vous interdire l'acc\xe8s \xe0 notre site."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Ventes des biens et services"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"Ces Conditions \xe9tablit les r\xe8gles concernant la vente du service propos\xe9 sur notre site internet soit la r\xe9servation de rendez-vous avec un m\xe9decin dans une clinique sans rendez-vous au Qu\xe9bec"}),(0,a.jsx)("p",{children:"Le service mentionn\xe9 pr\xe9c\xe9demment est celui qui est disponible sur notre site web au moment o\xf9 vous le consultez. Nous nous effor\xe7ons de vous fournir toutes les informations, d\xe9tails et images de notre service avec une grande pr\xe9cision et exactitude."}),(0,a.jsx)("p",{children:"Toutefois, nous ne sommes pas juridiquement responsables des informations, descriptions ou images, car nous ne pouvons assurer la pr\xe9cision totale de chaque produit ou service que nous offrons. En achetant ce service, vous reconnaissez le faire \xe0 vos propres risques."}),(0,a.jsx)("p",{children:"Le service sera factur\xe9 en totalit\xe9 apr\xe8s que la r\xe9servation du rendez-vous avec un m\xe9decin a \xe9t\xe9 effectu\xe9e."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Paiements"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"Le moyen de paiement que nous acceptons sur la Plateforme est par carte de cr\xe9dit bancaire."}),(0,a.jsx)("p",{children:"En nous transmettant vos informations de paiement, vous attestez que vous \xeates autoris\xe9 \xe0 utiliser le moyen de paiement s\xe9lectionn\xe9. De plus, en partageant ces d\xe9tails, vous nous donnez la permission de pr\xe9lever le montant d\xfb sur ce moyen de paiement."}),(0,a.jsx)("p",{children:"Si nous jugeons que votre paiement enfreint la loi, nos conditions d'utilisation ou nos conditions g\xe9n\xe9rales de vente, nous nous octroyons le droit d'annuler votre transaction."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Remboursement"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Tous les biens ou services vendus sur notre site sont non remboursables."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Limitation de responsabilit\xe9"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"SRVE s'efforce de fournir une information exacte et \xe0 jour sur sa Plateforme, mais ne peut garantir son exhaustivit\xe9 ou sa pr\xe9cision. En dehors des cas o\xf9 la Loi sur la protection du consommateur du Qu\xe9bec s'applique, nous ne pourrons \xeatre tenus responsables d'\xe9ventuels dommages directs ou indirects r\xe9sultant de l'utilisation de la Plateforme."}),(0,a.jsx)("p",{children:"En cas de non respect \xe0 vos obligations en tant que Utilisateur, vous vous engagez \xe0 pr\xe9munir SRVE contre toute r\xe9clamation ou action en justice qui pourrait en d\xe9couler."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Indemnit\xe9"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"En tant qu'utilisateur, vous d\xe9gagez SRVE de toute responsabilit\xe9 et vous vous engagez \xe0 couvrir tous les frais, actions en justice, dommages ou d\xe9penses r\xe9sultant de votre utilisation de ce site ou de votre non-respect des dispositions \xe9nonc\xe9s dans ces Conditions"})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Liens externes"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Notre Plateforme peut contenir des liens vers des sites web tiers. Nous n'avons aucun contr\xf4le sur le contenu de ces sites et d\xe9clinons toute responsabilit\xe9 quant \xe0 leur contenu ou aux dommages pouvant r\xe9sulter de leur utilisation."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Divisibilit\xe9"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Si, \xe0 un moment quelconque, une clause de ces Conditions est d\xe9clar\xe9e non conforme ou invalide selon les lois en vigueur, cette clause sera alors consid\xe9r\xe9e comme nulle et sera exclue de ces Conditions. Les autres clauses resteront en vigueur et ne seront pas affect\xe9es par cette invalidit\xe9, et le reste de ces Conditions continuera \xe0 \xeatre consid\xe9r\xe9 comme valide."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Modifications des Conditions"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Ces Conditions peuvent \xeatre mis \xe0 jour p\xe9riodiquement pour rester conformes aux lois et pour refl\xe9ter les modifications dans la gestion de notre site ainsi que les attentes des utilisateurs de notre site. Nous conseillons \xe0 nos utilisateurs de consulter r\xe9guli\xe8rement ces termes et conditions pour rester inform\xe9s des \xe9ventuelles mises \xe0 jour. Si n\xe9cessaire, nous notifierons les utilisateurs des modifications par courriel ou nous publierons un avis sur notre site. Si vous continuez \xe0 utiliser la Plateforme, cela indiquera que vous acceptez les nouvelles Conditions."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"R\xe8gles d'interpr\xe9tation"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"Les titres des diff\xe9rentes sections des Conditions sont fournis uniquement \xe0 des fins de r\xe9f\xe9rence et de clart\xe9. Ils ne doivent pas \xeatre pris en compte lors de l'interpr\xe9tation ou de l'application des dispositions contenues dans ces Conditions."}),(0,a.jsx)("p",{children:"Dans les Conditions, les mots \xe9crits au singulier incluent \xe9galement leur forme plurielle lorsque le contexte le requiert, et inversement. De m\xeame, les mots d\xe9signant un genre, masculin ou f\xe9minin, englobent l'autre genre lorsque cela est n\xe9cessaire pour une compr\xe9hension correcte du texte."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Priorit\xe9 au fran\xe7ais"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"En cas de conflit d'interpr\xe9tation des pr\xe9sentes Conditions par rapport \xe0 celui en anglais, cette version fran\xe7aise pr\xe9vaudra."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Lois applicables"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Les pr\xe9sentes Conditions sont r\xe9gies par les lois applicables dans la province du Qu\xe9bec, Canada. Tout litige sera soumis \xe0 la comp\xe9tence exclusive des tribunaux de la ville de Montr\xe9al."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Contact"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsxs)("p",{children:["Pour toute question relative \xe0 ces Conditions, vous pouvez nous contacter par courriel \xe0 ",(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"})," ou par la poste \xe0 l'adresse suivante:"]}),(0,a.jsxs)("p",{children:["CP 99900 VG 550 446",(0,a.jsx)("br",{}),"COP Villeneuve",(0,a.jsx)("br",{}),"Montr\xe9al (Qu\xe9bec)",(0,a.jsx)("br",{}),"H2T 0A6"]})]})]})]})}),(0,a.jsx)(n.Footer,{})]})}},4694:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/auth/forgot-password/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/auth/forgot-password/page.tsx","default")},7578:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o,metadata:()=>l});var a=t(57484),r=t(85815),n=t(8152),i=t(37337);let l={title:"Foire aux questions | Sans rendez-vous express",description:"Trouvez les r\xe9ponses \xe0 vos questions sur Sans rendez-vous express et notre service pour trouver des rendez-vous m\xe9dicaux rapidement."};function o(){return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(r.Navbar,{}),(0,a.jsx)("main",{className:"flex-grow",children:(0,a.jsx)("div",{className:"bg-white w-full pt-4 pb-16",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6",children:(0,a.jsx)(i.FaqFullPage,{})})})}),(0,a.jsx)(n.Footer,{})]})}},11177:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(57484),r=t(85815),n=t(8152),i=t(12922),l=t(84301),o=t(91717),c=t(1380),d=t(64594),m=t(72920),u=t(61237);function x(){return(0,a.jsx)(u.LanguageProvider,{children:(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(r.Navbar,{}),(0,a.jsxs)("main",{className:"flex-grow",children:[(0,a.jsx)("div",{className:"bg-[#f8f9fb]",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6",children:(0,a.jsx)(i.HeroSection,{})})}),(0,a.jsx)("div",{className:"bg-white w-full",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6",children:(0,a.jsx)(l.FeaturesSection,{})})}),(0,a.jsx)("div",{className:"bg-white w-full",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6",children:(0,a.jsx)(o.HowItWorksSection,{})})}),(0,a.jsx)("div",{className:"bg-[#f8f9fb] w-full",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6",children:(0,a.jsx)(c.PricingSection,{})})}),(0,a.jsx)("div",{className:"bg-white w-full",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6",children:(0,a.jsx)(d.FaqSection,{})})}),(0,a.jsx)(m.CtaSection2,{})]}),(0,a.jsx)(n.Footer,{})]})})}},11893:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(28392),r=t(4473),n=t(96326),i=t(21204),l=t(88597),o=t(59356),c=t(40869),d=t(98209),m=t(18318);let u={individual:{monthly:7.95,annual:71.4},family:{monthly:14.95,annual:134.4}};function x(){let{user:e}=(0,c.A)(),{translate:s}=(0,d.o)(),t=(0,n.useSearchParams)();t.get("session_id"),t.get("canceled");let[i,x]=(0,r.useState)(!0),[h,p]=(0,r.useState)(!1),[f,j]=(0,r.useState)(null);(0,m.S)(e=>e.fetchSubscription);let g=(0,m.f)();if(i)return(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[(0,a.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"}),(0,a.jsx)("p",{className:"mt-4 text-center text-lg",children:(0,a.jsx)(l.T,{keyName:o.o.common.loading})})]});if(h&&g.hasSubscription&&g.planType&&g.billingPeriod){var N,v;let e=(N=g.planType,v=g.billingPeriod,N in u&&v in u[N]?u[N][v]:g.amount||0),s=g.currentPeriodStart?((e,s)=>{let t=new Date(e),a=new Date(e);return"annual"===s?a.setFullYear(a.getFullYear()+1):a.setMonth(a.getMonth()+1),{start:t,end:a}})(g.currentPeriodStart,g.billingPeriod):{start:new Date,end:new Date},t="monthly"===g.billingPeriod?"Monthly":"Annual";return(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[(0,a.jsx)("div",{className:"rounded-full bg-green-100 p-3",children:(0,a.jsx)("svg",{className:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,a.jsx)("h2",{className:"mt-4 text-center text-2xl font-bold",children:(0,a.jsx)(l.T,{keyName:o.o.subscription.success})}),(0,a.jsx)("p",{className:"mt-2 text-center text-gray-600",children:(0,a.jsx)(l.T,{keyName:o.o.subscription.successMessage})}),(0,a.jsxs)("div",{className:"mt-8 w-full max-w-md rounded-lg border p-6",children:[(0,a.jsx)("h3",{className:"mb-4 text-lg font-medium",children:(0,a.jsx)(l.T,{keyName:o.o.subscription.details})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-500",children:[(0,a.jsx)(l.T,{keyName:o.o.subscription.plan}),":"]}),(0,a.jsx)("span",{className:"font-medium",children:g.planType.charAt(0).toUpperCase()+g.planType.slice(1)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-500",children:[(0,a.jsx)(l.T,{keyName:o.o.subscription.billing}),":"]}),(0,a.jsx)("span",{className:"font-medium",children:t})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-500",children:[(0,a.jsx)(l.T,{keyName:o.o.subscription.amount}),":"]}),(0,a.jsx)("span",{className:"font-medium",children:((e,s="CAD")=>new Intl.NumberFormat("en-CA",{style:"currency",currency:s.toUpperCase()}).format(e))(e,g.currency||"CAD")})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-500",children:[(0,a.jsx)(l.T,{keyName:o.o.subscription.currentPeriod}),":"]}),(0,a.jsxs)("span",{className:"font-medium",children:[s.start.toLocaleDateString()," - ",s.end.toLocaleDateString()]})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 text-center",children:[(0,a.jsx)("p",{className:"text-gray-600",children:(0,a.jsx)(l.T,{keyName:o.o.subscription.nextSteps})}),(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap justify-center gap-4",children:[(0,a.jsx)("a",{href:"/dashboard",className:"rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700",children:(0,a.jsx)(l.T,{keyName:o.o.subscription.goToDashboard})}),(0,a.jsx)("a",{href:"/compte",className:"rounded-md border border-gray-300 px-4 py-2 hover:bg-gray-50",children:(0,a.jsx)(l.T,{keyName:o.o.subscription.manageAccount})})]})]})]})}return(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[(0,a.jsx)("div",{className:"rounded-full bg-red-100 p-3",children:(0,a.jsx)("svg",{className:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,a.jsx)("h2",{className:"mt-4 text-center text-2xl font-bold",children:(0,a.jsx)(l.T,{keyName:o.o.subscription.error})}),(0,a.jsx)("p",{className:"mt-2 text-center text-gray-600",children:f||s(o.o.subscription.errorMessage)}),(0,a.jsxs)("div",{className:"mt-8 text-center",children:[(0,a.jsx)("p",{className:"text-gray-600",children:(0,a.jsx)(l.T,{keyName:o.o.subscription.needHelp})}),(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap justify-center gap-4",children:[(0,a.jsx)("a",{href:"/pricing",className:"rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700",children:(0,a.jsx)(l.T,{keyName:o.o.subscription.returnToPlans})}),(0,a.jsx)("a",{href:"/aide",className:"rounded-md border border-gray-300 px-4 py-2 hover:bg-gray-50",children:(0,a.jsx)(l.T,{keyName:o.o.subscription.contactSupport})})]})]})]})}function h(){return(0,a.jsx)(i.S,{children:(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"flex h-full w-full items-center justify-center",children:(0,a.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"})}),children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:(0,a.jsx)(l.T,{keyName:"subscription.status"})}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1.5",children:(0,a.jsx)(l.T,{keyName:"subscription.verifyingPayment"})})]}),(0,a.jsx)(x,{})]})})})}},12946:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GET:()=>n});var a=t(90987),r=t(31186);async function n(e){try{console.log("=== AUTH TEST DEBUG START ===");let s=e.cookies.getAll();console.log("All request cookies:",s.map(e=>({name:e.name,hasValue:!!e.value,length:e.value?.length||0,preview:e.value?.substring(0,20)+"..."})));let t=s.filter(e=>e.name.includes("supabase")||e.name.includes("auth")||e.name.includes("session"));console.log("Supabase-related cookies:",t);let n=await (0,r.U)();console.log("Supabase client created successfully");let i=e.headers.get("referer")||e.headers.get("referrer"),l=e.headers.get("x-external-return"),o=e.headers.get("user-agent");console.log("Request metadata:",{referrer:i,xExternalReturn:l,hasUserAgent:!!o,url:e.url,searchParams:[...e.nextUrl.searchParams.entries()]});let c=e.nextUrl.searchParams.has("session_id")||e.nextUrl.pathname.includes("/compte/abonnement")||i?.includes("stripe.com")||"true"===l;console.log("External return detection:",{isExternalReturn:c,hasSessionId:e.nextUrl.searchParams.has("session_id"),isSubscriptionPage:e.nextUrl.pathname.includes("/compte/abonnement"),isStripeReferrer:i?.includes("stripe.com"),hasExternalHeader:"true"===l});let{data:{user:d},error:m}=await n.auth.getUser(),{data:{session:u},error:x}=await n.auth.getSession();console.log("Auth results:",{hasUser:!!d,hasSession:!!u,userError:m?.message,sessionError:x?.message,sessionValid:!!u?.access_token,sessionExpiry:u?.expires_at?new Date(1e3*u.expires_at).toISOString():null});let h=null;if(c&&!u){console.log("Attempting session refresh for external return...");try{let{data:e,error:s}=await n.auth.refreshSession();h={success:!!e?.session,error:s?.message,hasNewSession:!!e?.session},console.log("Refresh result:",h)}catch(e){h={success:!1,error:e instanceof Error?e.message:"Unknown refresh error"},console.error("Refresh error:",e)}}return console.log("=== AUTH TEST DEBUG END ==="),a.NextResponse.json({timestamp:new Date().toISOString(),cookies:{total:s.length,supabaseRelated:t.length,names:s.map(e=>e.name)},request:{url:e.url,referrer:i,isExternalReturn:c,searchParams:Object.fromEntries(e.nextUrl.searchParams.entries())},auth:{hasUser:!!d,hasSession:!!u,userEmail:d?.email,userId:d?.id,sessionValid:!!u?.access_token,sessionExpiry:u?.expires_at?new Date(1e3*u.expires_at).toISOString():null,userError:m?.message,sessionError:x?.message},refresh:h,diagnosis:{cookiesPresent:t.length>0,authWorking:!!d&&!!u,needsRefresh:c&&!u,recommendedAction:d||u?c&&!u?"refresh_needed":"auth_working":"login_required"}})}catch(e){return console.error("Auth test error:",e),a.NextResponse.json({error:"Auth test failed",details:e instanceof Error?e.message:"Unknown error",timestamp:new Date().toISOString()},{status:500})}}},13408:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var a=t(28392),r=t(4473),n=t(42547),i=t.n(n),l=t(96326),o=t(54386),c=t(56552),d=t(77370),m=t(81076),u=t(88597),x=t(94096),h=t(51359),p=t(65187),f=t(40869);function j(){(0,l.useRouter)();let{t:e,language:s}=(0,u.B)(),{signInWithGoogle:t}=(0,f.A)(),[n,j]=(0,r.useState)(""),[g,N]=(0,r.useState)(""),[v,b]=(0,r.useState)(""),[y,w]=(0,r.useState)(""),[k,T]=(0,r.useState)(!1),[S,C]=(0,r.useState)(null),[P,A]=(0,r.useState)(!1),[q,E]=(0,r.useState)(!1),[R,U]=(0,r.useState)(null),_=async t=>{if(t.preventDefault(),A(!0),C(null),U(null),!k){C(e("auth.errors.agreeTerms")),A(!1);return}if(!v.trim()){C(e("account.firstNameRequired")),A(!1);return}if(!y.trim()){C(e("account.lastNameRequired")),A(!1);return}try{let{data:t,error:a}=await o.N.auth.signUp({email:n,password:g,options:{data:{first_name:v,last_name:y,language:s}}});if(a)throw a;t?.user?.identities?.length===0?U(e("auth.emailInUse")):U(e("auth.confirmEmail"))}catch(s){console.error("Error signing up:",s),C(s.message||e("auth.errors.generic"))}finally{A(!1)}},z=async()=>{E(!0),C(null);try{await t()}catch(s){console.error("Error signing up with Google:",s),C(e("auth.errors.socialAuthFailed")),E(!1)}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"space-y-2 text-center",children:(0,a.jsx)("h1",{className:"text-2xl font-bold",children:e("auth.createAccount")})}),S&&(0,a.jsx)(x.Fc,{variant:"destructive",children:(0,a.jsx)(x.TN,{children:S})}),R&&(0,a.jsx)(x.Fc,{children:(0,a.jsx)(x.TN,{children:R})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(c.$,{type:"button",variant:"outline",className:"w-full flex items-center justify-center gap-2",onClick:z,disabled:q,children:[q?(0,a.jsx)("span",{className:"w-5 h-5 border-2 border-gray-300 border-t-primary rounded-full animate-spin"}):(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",height:"24",viewBox:"0 0 24 24",width:"24",children:[(0,a.jsx)("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),(0,a.jsx)("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),(0,a.jsx)("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),(0,a.jsx)("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"}),(0,a.jsx)("path",{d:"M1 1h22v22H1z",fill:"none"})]}),(0,a.jsx)("span",{children:q?e("common.loading"):e("auth.continueWithGoogle")})]}),(0,a.jsxs)("div",{className:"relative flex items-center justify-center mt-6",children:[(0,a.jsx)(p.Separator,{className:"absolute w-full bg-gray-200"}),(0,a.jsx)("span",{className:"relative bg-white px-2 text-xs text-gray-500",children:e("auth.orContinueWith")})]})]}),(0,a.jsxs)("form",{onSubmit:_,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"firstName",children:e("auth.firstName")}),(0,a.jsx)(d.p,{id:"firstName",type:"text",placeholder:"fr"===s?"Pr\xe9nom":"First name",required:!0,value:v,onChange:e=>b(e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"lastName",children:e("auth.lastName")}),(0,a.jsx)(d.p,{id:"lastName",type:"text",placeholder:"fr"===s?"Nom":"Last name",required:!0,value:y,onChange:e=>w(e.target.value)})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"email",children:e("auth.email")}),(0,a.jsx)(d.p,{id:"email",type:"email",placeholder:"<EMAIL>",autoComplete:"email",required:!0,value:n,onChange:e=>j(e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"password",children:e("auth.createPassword")}),(0,a.jsx)(d.p,{id:"password",type:"password",autoComplete:"new-password",required:!0,value:g,onChange:e=>N(e.target.value)}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e("auth.passwordHint")})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)(h.S,{id:"terms",checked:k,onCheckedChange:e=>T(!0===e)}),(0,a.jsxs)(m.J,{htmlFor:"terms",className:"text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[e("auth.agreeTerms")," ",(0,a.jsx)(i(),{href:"en"===s?"/conditions-utilisation-EN":"/conditions-utilisation",className:"text-primary hover:underline",children:e("auth.termsAndConditions")})]})]}),(0,a.jsx)(c.$,{type:"submit",className:"w-full py-6",disabled:P,children:P?e("common.loading"):e("auth.signUp")})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e("auth.alreadyHaveAccount")," ",(0,a.jsx)(i(),{href:"/auth/sign-in",className:"text-primary hover:underline",children:e("auth.signIn")})]})})]})}},14289:(e,s,t)=>{"use strict";t.r(s),t.d(s,{POST:()=>o});var a=t(90987),r=t(6721),n=t(66438);let i=new r.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-03-31.basil"}),l=process.env.STRIPE_WEBHOOK_SECRET;async function o(e){let s;let t=await e.text(),r=e.headers.get("stripe-signature");try{s=i.webhooks.constructEvent(t,r,l)}catch(e){return console.error("Webhook signature verification failed:",e),a.NextResponse.json({error:"Invalid signature"},{status:400})}let o=(0,n.U)();try{switch(await o.from("webhook_events").insert({event_type:s.type,type:"stripe",stripe_event_id:s.id,data:s.data}),s.type){case"customer.subscription.created":await c(s.data.object);break;case"customer.subscription.updated":await d(s.data.object);break;case"customer.subscription.deleted":await m(s.data.object);break;case"invoice.payment_succeeded":await u(s.data.object);break;case"invoice.payment_failed":await x(s.data.object);break;default:console.log(`Unhandled event type: ${s.type}`)}return a.NextResponse.json({received:!0})}catch(e){return console.error("Error processing webhook:",e),a.NextResponse.json({error:"Webhook processing failed"},{status:500})}}async function c(e){var s;let t=(0,n.U)(),a=(e.metadata||{}).user_id;if(!a){console.error("No user_id in subscription metadata");return}let r=e.items.data[0]?.price.id,i=(s=r)?["price_1R0CkxElTneJ74CJARjKlS9U","price_1REN04ElTneJ74CJVejJzVDH"].includes(s)?"individual":["price_1R0ClKElTneJ74CJO45trpKh","price_1REN0TElTneJ74CJDRnv2MXk"].includes(s)?"family":"free":"free",l=e.items.data[0]?.price.recurring?.interval==="year"?"annual":"monthly";try{let{error:s}=await t.from("subscriptions").insert({stripe_id:e.id,user_id:a,customer_id:e.customer,price_id:r,stripe_price_id:r,plan_type:i,billing_period:l,status:e.status,current_period_start:e.current_period_start,current_period_end:e.current_period_end,cancel_at_period_end:e.cancel_at_period_end,amount:e.items.data[0]?.price.unit_amount||0,currency:e.items.data[0]?.price.currency||"cad",interval:e.items.data[0]?.price.recurring?.interval||"month",started_at:e.start_date||e.created,metadata:e.metadata});s&&console.error("Error creating subscription:",s)}catch(e){console.error("Error in handleSubscriptionCreated:",e)}}async function d(e){let s=(0,n.U)();try{let{error:t}=await s.from("subscriptions").update({status:e.status,current_period_start:e.current_period_start,current_period_end:e.current_period_end,cancel_at_period_end:e.cancel_at_period_end,canceled_at:e.canceled_at,ended_at:e.ended_at,updated_at:new Date().toISOString()}).eq("stripe_id",e.id);t?console.error("Error updating subscription:",t):console.log(`Subscription ${e.id} updated successfully`)}catch(e){console.error("Error in handleSubscriptionUpdated:",e)}}async function m(e){let s=(0,n.U)();try{let{error:t}=await s.from("subscriptions").update({status:"cancelled",ended_at:e.ended_at||Math.floor(Date.now()/1e3),updated_at:new Date().toISOString()}).eq("stripe_id",e.id);t?console.error("Error deleting subscription:",t):console.log(`Subscription ${e.id} deleted successfully`)}catch(e){console.error("Error in handleSubscriptionDeleted:",e)}}async function u(e){console.log(`Payment succeeded for invoice ${e.id}`)}async function x(e){console.log(`Payment failed for invoice ${e.id}`)}},15717:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GET:()=>i,POST:()=>n});var a=t(90987),r=t(31186);async function n(e){try{let s=await (0,r.U)(),{data:{user:t}}=await s.auth.getUser();if(!t)return a.NextResponse.json({error:"Authentication required",errorType:"authentication_required"},{status:401});let{data:n,error:i}=await s.from("subscriptions").select("*").eq("user_id",t.id).eq("status","active").in("plan_type",["individual","family"]).maybeSingle();if(i&&console.error("Error checking subscription:",i),!n)return a.NextResponse.json({error:"Subscription required to create appointment requests",errorType:"no_subscription"},{status:403});let l=await e.json();if(!l.request_type)return a.NextResponse.json({error:"Request type is required"},{status:400});let o={user_id:t.id,patient_id:l.patient_id||null,request_type:l.request_type,request_details:l.request_details||{},status:"pending"},{data:c,error:d}=await s.from("appointment_requests").insert(o).select().single();if(d){if(console.error("Error creating appointment request:",d),d.message.includes("Free users cannot create appointment requests"))return a.NextResponse.json({error:"Subscription required to create appointment requests",errorType:"no_subscription"},{status:403});if(d.message.includes("Only family plan members can book for others"))return a.NextResponse.json({error:"Family plan required to book for family members"},{status:403});if(d.message.includes("Patient does not belong to this user"))return a.NextResponse.json({error:"Invalid patient selection"},{status:400});return a.NextResponse.json({error:"Failed to create appointment request"},{status:500})}return a.NextResponse.json({success:!0,data:c})}catch(e){return console.error("Unexpected error creating appointment request:",e),a.NextResponse.json({error:"An unexpected error occurred"},{status:500})}}async function i(e){try{let s=await (0,r.U)(),{data:{user:t}}=await s.auth.getUser();if(!t)return a.NextResponse.json({error:"Authentication required"},{status:401});let n=new URL(e.url),i=n.searchParams.get("status"),l=parseInt(n.searchParams.get("limit")||"10"),o=s.from("appointment_requests").select(`
        *,
        family_members(first_name, last_name, health_card, birth_date)
      `).eq("user_id",t.id).order("created_at",{ascending:!1}).limit(l);i&&(o=o.eq("status",i));let{data:c,error:d}=await o;if(d)return console.error("Error fetching appointment requests:",d),a.NextResponse.json({error:"Failed to fetch appointment requests"},{status:500});return a.NextResponse.json({data:c})}catch(e){return console.error("Unexpected error fetching appointment requests:",e),a.NextResponse.json({error:"An unexpected error occurred"},{status:500})}}},17336:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/pricing/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/pricing/page.tsx","default")},17530:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/dashboard/page.tsx","default")},19228:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GET:()=>l});var a=t(31186),r=t(84824),n=t(90987);async function i(e,s){try{let e=await (0,a.Z)(),{data:t,error:n}=await e.auth.admin.getUserById(s);if(n||!t){console.error("Error fetching user data:",n);return}let i=t.user.user_metadata;if(!(i?.provider_id||i?.iss==="https://accounts.google.com"||i?.sub?.startsWith("google"))){console.log("Not a Google Auth user, skipping profile update");return}console.log("Processing Google Auth user profile update");let l=i.given_name||i.first_name||i.name?.split(" ")[0]||"",o=i.family_name||i.last_name||i.name?.split(" ").slice(1).join(" ")||"",c=(await (0,r.UL)()).get("NEXT_LOCALE"),d=c?.value||"fr";console.log(`Extracted user info - First: "${l}", Last: "${o}", Language: "${d}"`);let{error:m}=await e.auth.admin.updateUserById(s,{user_metadata:{...i,first_name:l,last_name:o,language:d}});m&&console.error("Error updating auth user metadata:",m);let{error:u}=await e.from("users").upsert({id:s,user_id:s,email:t.user.email||"",first_name:l,last_name:o,language:d,avatar_url:i.avatar_url||i.picture||"",token_identifier:t.user.email||"",created_at:t.user.created_at,updated_at:new Date().toISOString()},{onConflict:"id"});u?console.error("Error updating user profile:",u):console.log("Successfully updated user profile with Google Auth data")}catch(e){console.error("Error in updateUserProfileFromGoogleAuth:",e)}}async function l(e){let s=new URL(e.url),t=s.searchParams.get("code");if(s.searchParams.get("redirectTo"),t)try{let e=await (0,a.U)(),{data:s,error:r}=await e.auth.exchangeCodeForSession(t);r?console.error("Error exchanging code for session:",r):(console.log("Successfully exchanged code for session"),s?.session?.user?.id&&await i(e,s.session.user.id))}catch(e){console.error("Error in auth callback:",e)}let r=s.origin,l=`${r}/dashboard`;console.log(`Redirecting to: ${l}`);let o=n.NextResponse.redirect(l);return o.headers.set("Set-Cookie","ClearLogoutFlag=true; Max-Age=60; Path=/; HttpOnly; SameSite=Strict"),o}},20933:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var a=t(57484),r=t(85815),n=t(8152);let i={title:"Privacy Policy | Sans rendez-vous express",description:"Privacy Policy for Sans rendez-vous express."};function l(){return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(r.Navbar,{}),(0,a.jsx)("main",{className:"flex-grow bg-white",children:(0,a.jsxs)("div",{className:"max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-6 py-12",children:[(0,a.jsx)("div",{className:"mb-4 text-gray-500",children:(0,a.jsx)("p",{children:"Last updated on March 24, 2025"})}),(0,a.jsx)("h1",{className:"text-4xl sm:text-5xl xl:text-[60px] leading-tight font-bold text-[#212244] mb-16 max-w-[1000px]",children:"Privacy Policy"}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"General"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:'By using our website www.sansrendezvousexpress.ca (hereinafter referred to as the "Platform"), you agree to the terms of this Privacy Policy (hereinafter referred to as the "Policy").'}),(0,a.jsx)("p",{children:"The purpose of this Policy is to inform users of our Platform about the types of personal data we collect and how they are used."}),(0,a.jsx)("p",{children:'Sans rendez-vous express Inc. (hereinafter referred to as "SRVXP") strictly adheres to Quebec\'s Law 25 concerning the protection of personal data. SRVXP is a company legally registered in Quebec, Canada.'}),(0,a.jsx)("p",{children:"This Policy also outlines our commitment and the measures we take to ensure the protection of your personal information."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Acceptance of the Policy"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:'By accessing and using the Platform, you, the end user or the organization you represent (hereinafter referred to as "you", "the User" or "the Users"), agree to be bound by this Policy as well as our Terms and Conditions. If you do not accept this Policy, please do not use our Platform.'})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Collection of Personal Information"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"During your visit to the Platform, we automatically collect information about your device and your interaction with our site via Google Analytics."}),(0,a.jsx)("p",{children:"This includes the following data:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"IP Address"}),(0,a.jsx)("li",{children:"Location"}),(0,a.jsx)("li",{children:"Hardware and software details"}),(0,a.jsx)("li",{children:"Content that the User views on our site"}),(0,a.jsx)("li",{children:"Links that a User clicks on while visiting the site"}),(0,a.jsx)("li",{children:"Non-Automatically Collected Personal Data"})]}),(0,a.jsx)("p",{className:"mt-6",children:"Your personal data is collected through:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"Contact form"}),(0,a.jsx)("li",{children:"Medical appointment request form"})]}),(0,a.jsx)("p",{className:"mt-6",children:"This includes the following data:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"First name"}),(0,a.jsx)("li",{children:"Last name"}),(0,a.jsx)("li",{children:"Quebec Health insurance number"}),(0,a.jsx)("li",{children:"Sequential number of the Quebec Health Insurance Card"}),(0,a.jsx)("li",{children:"Date of birth"}),(0,a.jsx)("li",{children:"Gender"}),(0,a.jsx)("li",{children:"Postal code near the appointment location"}),(0,a.jsx)("li",{children:"Reason for the consultation"}),(0,a.jsx)("li",{children:"Email"}),(0,a.jsx)("li",{children:"Mobile phone"}),(0,a.jsx)("li",{children:"Home phone"}),(0,a.jsx)("li",{children:"Payment information"})]})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Use of Cookies"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"The Platform uses navigation cookies as well as Facebook and Google pixels to offer you a personalized experience."}),(0,a.jsx)("p",{children:"Navigation cookies are text files that are transmitted to your browser when you access a website. Google Analytics cookies transmit anonymous browsing statistics. They allow, among other things, to know the number of visits to a page and the average time of these visits."}),(0,a.jsx)("p",{children:"Facebook and Google pixels are JavaScript code that allows for the detection of, among other things, the number of visits to a page, anonymously. The pixels are not placed on your computer; they remain on the visited site. They are used to collect statistics, analyze site performance, and conduct targeted advertising based on interests."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Data Usage"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"Please note that we only collect data necessary to achieve the objectives described in the Policy. We will not collect additional information without informing you in advance."}),(0,a.jsx)("p",{children:"We use this data to:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"Find medical appointments"}),(0,a.jsx)("li",{children:"Send promotional offers and targeted communications via our newsletter"}),(0,a.jsx)("li",{children:"Conduct statistical analyses to improve the site"}),(0,a.jsx)("li",{children:"Track requests for medical appointments"}),(0,a.jsx)("li",{children:"Follow up on messages received through the contact form"}),(0,a.jsx)("li",{children:"As part of a data security protocol"})]}),(0,a.jsx)("p",{children:"User data may be accessed, processed, or collected in Canada."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Personal Data Retention Period"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"We store personal data for a duration of 7 days, after which it is permanently deleted. However, the first 8 characters of the Users' health insurance numbers are preserved for a period of 5 years to ensure that the free trial of the service is not used more than once."}),(0,a.jsx)("p",{children:"We will ensure that Users are notified if their data is retained longer than this period. You can contact us if you wish to make changes, delete, or anonymize your personal data."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Security Incident Management"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"In the event of a leak of your personal data, we commit to keeping you informed as well as notifying the Quebec's Access to Information Commission."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"User Rights"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"If you are a resident of Quebec, in accordance with Law 25, you have specific rights regarding your personal data:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"Right to be informed"}),(0,a.jsx)("li",{children:"Right of access"}),(0,a.jsx)("li",{children:"Right to rectification"}),(0,a.jsx)("li",{children:"Right to data portability"})]}),(0,a.jsx)("p",{className:"mt-6",children:"You have the right to object to the use of your data and to withdraw it. To exercise these rights, contact our data protection officer:"}),(0,a.jsx)("p",{className:"mt-6 font-semibold",children:"By mail:"}),(0,a.jsxs)("p",{children:["Sans rendez-vous express - Data Protection Officer",(0,a.jsx)("br",{}),"PO BOX 99900 VG 550 446",(0,a.jsx)("br",{}),"RPO VILLENEUVE",(0,a.jsx)("br",{}),"MONTREAL QC",(0,a.jsx)("br",{}),"H2T 0A6"]}),(0,a.jsx)("p",{className:"mt-6 font-semibold",children:"By email:"}),(0,a.jsx)("p",{children:(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"})})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Right of Access and Rectification"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"You can view, modify, or delete your data. To do so, use the same contact details mentioned above."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Consent and Withdrawal"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"It is possible to browse our site anonymously. However, access to certain features may require the collection of personal data. You always have the option not to provide this information."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Minors"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Law 25 specifies that individuals under the age of 14 are considered minors for the purposes of data collection. Their personal information cannot be collected from them without the consent of the person holding parental authority or the guardian, except when the collection is clearly for the benefit of the minor."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"External Links"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Our Platform may contain links to third-party websites. We have no control over the content of these sites and disclaim any responsibility for their content or any damages that may result from their use."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Priority to French"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"In the event of a conflict in the interpretation of the Policy compared with the French version, the French version shall prevail."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Updating the Policy"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"This Policy may be periodically updated to remain compliant with laws and to reflect changes in the management of our site as well as the expectations of our site's Users."}),(0,a.jsx)("p",{children:"We advise our Users to regularly review this Policy to stay informed of any updates. If necessary, we will notify Users of changes via email or we will post a notice on our site. If you continue to use the Platform, this will indicate that you accept the new Policy."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Interpretation Rules"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"The titles of the various sections of the Policy are provided solely for reference and clarity. They should not be considered when interpreting or applying the provisions contained in this Policy."}),(0,a.jsx)("p",{children:"In the Policy, words written in the singular also include their plural form when the context requires it, and vice versa. Similarly, words indicating a gender, male or female, encompass the other gender when necessary for a correct understanding of the text."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Divisibility"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"If, at any time, a clause in the Policy is declared non-compliant or invalid according to the applicable laws, this clause will then be considered null and void and will be excluded from the Policy. The other clauses will remain in force and will not be affected by this invalidity, and the rest of the Policy will continue to be considered valid."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Governing Law"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"This Policy is governed by the laws applicable in the province of Quebec, Canada. Any dispute will be subject to the exclusive jurisdiction of the courts of the city of Montreal."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Contacting Us"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsxs)("p",{children:["For any questions regarding this Policy, please contact us by email at ",(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"}),"."]})})]})]})}),(0,a.jsx)(n.Footer,{})]})}},21133:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var a=t(28392),r=t(42547),n=t.n(r),i=t(85391),l=t(82374),o=t(88597);function c({children:e}){let{t:s,language:t}=(0,o.B)();return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col justify-center items-center bg-gray-50 light relative",children:[(0,a.jsx)("div",{className:"absolute top-4 right-4",children:(0,a.jsx)(l.J,{})}),(0,a.jsxs)("div",{className:"w-full max-w-md bg-white p-8 rounded-lg shadow-sm text-gray-900",children:[(0,a.jsx)("div",{className:"flex items-center justify-center mb-8",children:(0,a.jsxs)(n(),{href:"/",className:"flex items-center group",children:[(0,a.jsx)("div",{className:"w-8 h-8 flex items-center justify-center transition-transform duration-300 group-hover:scale-110",children:(0,a.jsx)(i.default,{src:"/zaply-images/srvxp_logorevised.svg",alt:"Logo",width:32,height:32,priority:!0,suppressHydrationWarning:!0,className:"text-brandBlue fill-current"})}),(0,a.jsx)("span",{className:"ml-4 text-lg sm:text-xl font-bold text-[#212242]",children:"Sans rendez-vous express"})]})}),e]}),(0,a.jsxs)("div",{className:"absolute bottom-4 left-4 flex items-center space-x-4 text-xs text-gray-400",children:[(0,a.jsx)("span",{children:"\xa9 Sans rendez-vous express"}),(0,a.jsx)(n(),{href:"en"===t?"/politique-de-confidentialite-EN":"/politique-de-confidentialite",className:"hover:text-gray-600 transition-colors",children:s("landing.footer.privacyPolicy")})]})]})}},21152:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(57484),r=t(85815),n=t(8152),i=t(12922),l=t(84301),o=t(91717),c=t(1380),d=t(64594),m=t(72920),u=t(61237);function x(){return(0,a.jsx)(u.LanguageProvider,{children:(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(r.Navbar,{}),(0,a.jsxs)("main",{className:"flex-grow",children:[(0,a.jsx)("div",{className:"bg-[#f8f9fb]",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6",children:(0,a.jsx)(i.HeroSection,{})})}),(0,a.jsx)("div",{className:"bg-white w-full",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6",children:(0,a.jsx)(l.FeaturesSection,{})})}),(0,a.jsx)("div",{className:"bg-white w-full",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6",children:(0,a.jsx)(o.HowItWorksSection,{})})}),(0,a.jsx)("div",{className:"bg-[#f8f9fb] w-full",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6",children:(0,a.jsx)(c.PricingSection,{})})}),(0,a.jsx)("div",{className:"bg-white w-full",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6",children:(0,a.jsx)(d.FaqSection,{})})}),(0,a.jsx)(m.CtaSection2,{})]}),(0,a.jsx)(n.Footer,{})]})})}},21412:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/auth/sign-up/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/auth/sign-up/page.tsx","default")},21699:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var a=t(28392),r=t(4473),n=t(42547),i=t.n(n),l=t(48017),o=t(91878),c=t(41788),d=t(86439),m=t(82654),u=t(35070),x=t(19798),h=t(91632),p=t(56552),f=t(39790),j=t(77370),g=t(81076),N=t(63907),v=t(88125),b=t(71216),y=t(83808),w=t(88597),k=t(59356),T=t(98209),S=t(48072),C=t(60374);function P(){let{familyMembers:e,updateTempFamilyMember:s,toggleEditing:t,saveMemberChanges:n,deleteMember:P,isLoading:A,error:q}=(0,y.h)(),{language:E}=(0,T.o)(),R="fr"===E?b.fr:void 0,[U,_]=(0,r.useState)({}),[z,D]=(0,r.useState)({}),{subscription:L,fetchSubscription:I,user:F,isSubscriptionLoading:V}=(0,C.yk)(),$=L?.plan_type==="family",B=(e,t)=>{s(e,{tempHealthCard:t.toUpperCase().replace(/[^A-Z]/g,"").substring(0,4)})},M=e=>{let s=n(e);D(t=>({...t,[e]:!s})),s||setTimeout(()=>{D(s=>({...s,[e]:!1}))},3e3)},W=e=>{s(e,{tempBirthDate:void 0}),_(s=>({...s,[e]:(s[e]||0)+1})),D(s=>({...s,[e]:!1})),P(e)};return(0,a.jsx)(h.DashboardLayout,{children:(0,a.jsx)("div",{className:"space-y-6 mx-auto max-w-6xl",children:A||V?(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(l.A,{className:"h-8 w-8 animate-spin text-blue-500"}),(0,a.jsx)("span",{className:"ml-2 text-lg",children:(0,a.jsx)(w.T,{keyName:k.o.common.loading})})]}):q?(0,a.jsxs)("div",{className:"p-4 border border-red-200 rounded-md bg-red-50 text-red-700",children:[(0,a.jsxs)("p",{children:["Error: ",q.message]}),(0,a.jsx)("p",{children:(0,a.jsx)(w.T,{keyName:k.o.errors.tryAgainLater})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p.$,{asChild:!0,variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsxs)(i(),{href:"/compte",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:(0,a.jsx)(w.T,{keyName:k.o.common.back})})]})}),(0,a.jsx)("h1",{className:"text-3xl font-bold",children:(0,a.jsx)(w.T,{keyName:$?k.o.users.manageAccountUsers:k.o.users.manageProfile})})]}),(0,a.jsxs)(f.Zp,{children:[(0,a.jsxs)(f.aR,{children:[(0,a.jsx)(f.ZB,{children:(0,a.jsx)(w.T,{keyName:$?k.o.users.familyMembers:k.o.users.userProfile})}),(0,a.jsx)(f.BT,{children:(0,a.jsx)(w.T,{keyName:$?k.o.users.familyMembersDescription:k.o.users.userProfileDescription})})]}),(0,a.jsx)(f.Wu,{children:(0,a.jsx)("div",{className:"space-y-5",children:($?5===e.length?e:[void 0,void 0,void 0,void 0,void 0].map((s,t)=>e.find(e=>e.id===t+1)||{id:t+1,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1}):[e[0]||{id:1,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1}]).map(e=>(0,a.jsxs)("div",{className:"rounded-lg border bg-card p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0",children:(0,a.jsx)(c.A,{className:"h-5 w-5 text-blue-700"})}),(0,a.jsx)("div",{children:e.firstName||e.lastName?(0,a.jsx)("h3",{className:"font-medium",children:`${e.firstName} ${e.lastName}`}):(0,a.jsx)("h3",{className:"font-medium text-muted-foreground",children:(0,a.jsx)(w.T,{keyName:$?k.o.users.addMember:k.o.users.addYourInfo})})})]}),(0,a.jsx)(p.$,{variant:"ghost",size:"sm",onClick:()=>t(e.id),className:"flex items-center gap-1.5",children:e.editing?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:(0,a.jsx)(w.T,{keyName:k.o.users.cancel})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:(0,a.jsx)(w.T,{keyName:k.o.users.edit})})]})})]}),e.editing?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g.J,{htmlFor:`firstName-${e.id}`,children:(0,a.jsx)(w.T,{keyName:k.o.users.firstName})}),(0,a.jsx)(j.p,{id:`firstName-${e.id}`,value:e.tempFirstName,onChange:t=>s(e.id,{tempFirstName:t.target.value}),className:z[e.id]&&!e.tempFirstName?"border-red-500 focus-visible:ring-red-500":""})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g.J,{htmlFor:`lastName-${e.id}`,children:(0,a.jsx)(w.T,{keyName:k.o.users.lastName})}),(0,a.jsx)(j.p,{id:`lastName-${e.id}`,value:e.tempLastName,onChange:t=>s(e.id,{tempLastName:t.target.value}),className:z[e.id]&&!e.tempLastName?"border-red-500 focus-visible:ring-red-500":""})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g.J,{htmlFor:`healthCard-${e.id}`,children:(0,a.jsx)(w.T,{keyName:k.o.users.healthCardPrefix})}),(0,a.jsx)(j.p,{id:`healthCard-${e.id}`,value:e.tempHealthCard,onChange:s=>B(e.id,s.target.value),maxLength:4,className:(0,N.cn)("uppercase",z[e.id]&&(!e.tempHealthCard||e.tempHealthCard.length<4)?"border-red-500 focus-visible:ring-red-500":"")}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:(0,a.jsx)(w.T,{keyName:k.o.users.healthCardDescription})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g.J,{htmlFor:`birthDate-${e.id}`,children:(0,a.jsx)(w.T,{keyName:k.o.users.birthDate})}),(0,a.jsx)("div",{className:z[e.id]&&!e.tempBirthDate?"border rounded border-red-500":"",children:(0,a.jsx)(S.u,{date:e.tempBirthDate,onSelect:t=>s(e.id,{tempBirthDate:t}),locale:R,fromYear:1900,toYear:2025},`date-picker-${e.id}-${U[e.id]||0}`)})]})]}),z[e.id]&&(0,a.jsx)("div",{className:"text-sm text-red-500 bg-red-50 dark:bg-red-950 p-2 rounded border border-red-200 dark:border-red-800 mb-2",children:(0,a.jsx)(w.T,{keyName:k.o.users.validationError,fallback:"Please enter first name, last name, health card (4 characters), and birth date to save."})}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)(p.$,{variant:"outline",onClick:()=>W(e.id),className:"flex items-center gap-1 border-red-500 text-red-500 hover:bg-red-50 hover:text-red-500 dark:hover:bg-red-950 dark:border-red-400 dark:text-red-400 dark:hover:text-red-400",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)(w.T,{keyName:k.o.common.delete})]}),(0,a.jsxs)(p.$,{onClick:()=>M(e.id),className:"flex items-center gap-1",children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),(0,a.jsx)(w.T,{keyName:k.o.users.save})]})]})]}):e.firstName||e.lastName?(0,a.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-2 text-sm",children:[e.healthCard&&(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:(0,a.jsx)(w.T,{keyName:k.o.users.healthCard})}),(0,a.jsx)("span",{className:"mt-1",children:"TRAF-XXXX-XXXX"})]}),e.birthDate&&(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:(0,a.jsx)(w.T,{keyName:k.o.users.birthDate})}),(0,a.jsx)("span",{className:"mt-1",children:(0,v.GP)(e.birthDate,"PPP",{locale:R})})]})]}):(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:(0,a.jsx)(w.T,{keyName:$?k.o.users.editMemberPrompt:k.o.users.addYourInfoPrompt})})]},e.id))})})]})]})})})}},25559:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/compte/utilisateurs/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/compte/utilisateurs/page.tsx","default")},26500:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/trouver-rendez-vous/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/trouver-rendez-vous/page.tsx","default")},28690:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx","default")},29770:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var a=t(57484),r=t(85815),n=t(8152);let i={title:"General Terms and Conditions of Sale | Sans rendez-vous express",description:"General Terms and Conditions of Sale for Sans rendez-vous express."};function l(){return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(r.Navbar,{}),(0,a.jsx)("main",{className:"flex-grow bg-white",children:(0,a.jsxs)("div",{className:"max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-6 py-12",children:[(0,a.jsx)("div",{className:"mb-4 text-gray-500",children:(0,a.jsx)("p",{children:"Last updated on March 24, 2025"})}),(0,a.jsx)("h1",{className:"text-4xl sm:text-5xl xl:text-[60px] leading-tight font-bold text-[#212244] mb-16 max-w-[1000px]",children:"General Terms and Conditions of Sale"}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"General"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:'These General Terms and Conditions of Sale (hereinafter referred to as the "Conditions") govern the use of the website www.sansrendezvousexpress.ca (hereinafter referred to as the "Platform"). This site is owned and operated by Sans rendez-vous express Inc (hereinafter referred to as "SRVXP").'}),(0,a.jsx)("p",{children:'Before browsing our Platform, please read the Conditions set out below. By accessing and using this site, you, the end user or the organization you represent (hereinafter referred to as "you", "the User" or "the Users") acknowledge that you have read, understood, and accepted the Conditions in their entirety and agree to comply with them at all times.'})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Acceptable Use"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"As a User of the Platform, you agree to use our site in a lawful manner and not to exploit this site for illegal purposes, including:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"Infringing on the rights of other Users of the site"}),(0,a.jsx)("li",{children:"Infringing on the intellectual property rights of the site owners or third parties associated with the site"}),(0,a.jsx)("li",{children:"Hacking into another User's account"}),(0,a.jsx)("li",{children:"Acting in any way that could be considered fraudulent"}),(0,a.jsx)("li",{children:"Participating in any illegal activity on the site"}),(0,a.jsx)("li",{children:"Displaying any material that may be deemed inappropriate or offensive"})]}),(0,a.jsx)("p",{children:"If we consider that your use of this site is illegal or violates our Terms and Conditions or these Conditions, we reserve the right to restrict, suspend or terminate your access to the site. We also reserve the right to take any legal action required to prohibit your access to our site."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Sales of Goods and Services"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"These Conditions establish the rules regarding the sale of the service offered on our website, namely the booking of appointments with a doctor at a walk-in clinic in Quebec."}),(0,a.jsx)("p",{children:"The aforementioned service is the one that is available on our website at the time you consult it. We strive to provide you with all the information, details, and images of our service with great accuracy and precision."}),(0,a.jsx)("p",{children:"However, we are not legally responsible for the information, descriptions, or images, as we cannot ensure the total accuracy of each product or service we offer. By purchasing this service, you acknowledge that you do so at your own risk."}),(0,a.jsx)("p",{children:"The service will be billed in full after the appointment with a doctor has been booked."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Payments"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"The method of payment we accept on the Platform is by credit card."}),(0,a.jsx)("p",{children:"By providing us with your payment information, you certify that you are authorized to use the selected payment method. Additionally, by sharing these details, you give us permission to charge the due amount to this payment method."}),(0,a.jsx)("p",{children:"If we determine that your payment violates the law, our Terms and Conditions, or these Conditions, we reserve the right to cancel your transaction."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Refund Policy"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"All goods or services sold on our site are non-refundable."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Limitation of Liability"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"SRVXP endeavors to provide accurate and up-to-date information on its Platform, but cannot guarantee its completeness or accuracy. Except in cases where the Quebec Consumer Protection Act applies, SRVXP cannot be held liable for any direct or indirect damages resulting from the use of the Platform."}),(0,a.jsx)("p",{children:"In the event of non-compliance with your obligations as a User, you agree to ward off SRVXP against any claims or legal actions that may arise."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Indemnity"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"As a User, you release SRVXP from any liability and agree to cover all costs, legal actions, damages, or expenses resulting from your use of this site or your failure to comply with the provisions set out in these Conditions."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"External Links"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Our Platform may contain links to third-party websites. We have no control over the content of these sites and disclaim any responsibility for their content or any damages that may result from their use."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Priority to French"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"In the event of a conflict in the interpretation of these Conditions compared with the French version, the French version shall prevail."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Governing Law"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"These Conditions are governed by the laws applicable in the province of Quebec, Canada. Any dispute will be subject to the exclusive jurisdiction of the courts of the city of Montreal."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Divisibility"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"If, at any time, a clause in the Conditions is declared non-compliant or invalid according to the applicable laws, this clause will then be considered null and void and will be excluded from the Conditions. The other clauses will remain in force and will not be affected by this invalidity, and the rest of the Conditions will continue to be considered valid."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Updating the Conditions"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"These Conditions may be periodically updated to remain compliant with laws and to reflect changes in the management of our site as well as the expectations of our site's Users. We advise our Users to regularly review these Conditions to stay informed of any updates. If necessary, we will notify Users of changes via email or we will post a notice on our site. If you continue to use the Platform, this will indicate that you accept the new Conditions."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Interpretation Rules"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"The titles of the various sections of the Conditions are provided solely for reference and clarity. They should not be considered when interpreting or applying the provisions contained in these Conditions."}),(0,a.jsx)("p",{children:"In the Conditions, words written in the singular also include their plural form when the context requires it, and vice versa. Similarly, words indicating a gender, male or female, encompass the other gender when necessary for a correct understanding of the text."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Contacting Us"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsxs)("p",{children:["For any questions regarding these Conditions, you can contact us by email at ",(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"})," or by mail at the following address:"]}),(0,a.jsxs)("p",{children:["PO BOX 99900 VG 550 446",(0,a.jsx)("br",{}),"RPO VILLENEUVE",(0,a.jsx)("br",{}),"MONTREAL QC",(0,a.jsx)("br",{}),"H2T 0A6"]})]})]})]})}),(0,a.jsx)(n.Footer,{})]})}},30160:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/compte/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/compte/page.tsx","default")},32071:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(28392);function r({children:e}){return(0,a.jsx)("div",{className:"zaply-page",children:e})}},34173:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(57484),r=t(47591),n=t(48723),i=t(49816),l=t(10007),o=t(34498),c=t(40564),d=t(51407),m=t(90369),u=t(31953);function x(){return(0,a.jsx)(l.DashboardLayout,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:(0,a.jsx)(m.T,{keyName:u.o.help.needHelp})}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1.5",children:(0,a.jsx)(m.T,{keyName:u.o.help.helpDescription})})]}),(0,a.jsxs)("div",{className:"grid gap-6 grid-cols-1 lg:grid-cols-2",children:[(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{className:"flex flex-row items-start space-y-0 pb-2",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(r.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)(o.ZB,{children:(0,a.jsx)(m.T,{keyName:u.o.help.faq})})]})}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsx)(o.BT,{className:"mb-4",children:(0,a.jsx)(m.T,{keyName:u.o.help.faqDescription})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:(0,a.jsx)(m.T,{keyName:u.o.help.howToBookAppointment})}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:(0,a.jsx)(m.T,{keyName:u.o.help.howToBookDescription})})]}),(0,a.jsx)(d.Separator,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:(0,a.jsx)(m.T,{keyName:u.o.help.howToCancelAppointment})}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:(0,a.jsx)(m.T,{keyName:u.o.help.howToCancelDescription})})]}),(0,a.jsx)(d.Separator,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:(0,a.jsx)(m.T,{keyName:u.o.help.howToChangePlan})}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:(0,a.jsx)(m.T,{keyName:u.o.help.howToChangePlanDescription})})]})]})]})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{className:"flex flex-row items-start space-y-0 pb-2",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)(o.ZB,{children:(0,a.jsx)(m.T,{keyName:u.o.help.customerSupport})})]})}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsx)(o.BT,{className:"mb-4",children:(0,a.jsx)(m.T,{keyName:u.o.help.supportDescription})}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(i.A,{className:"h-5 w-5 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:(0,a.jsx)(m.T,{keyName:u.o.help.email})}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:(0,a.jsx)(m.T,{keyName:u.o.help.supportEmail})}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:(0,a.jsx)(m.T,{keyName:u.o.help.responseTime})})]})]})}),(0,a.jsx)(c.$,{className:"w-full mt-6",asChild:!0,children:(0,a.jsx)("a",{href:"mailto:<EMAIL>",children:(0,a.jsx)(m.T,{keyName:u.o.help.contactSupport})})})]})]})]})]})})}},35382:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var a=t(57484),r=t(85815),n=t(8152);let i={title:"Conditions d'utilisation | Sans rendez-vous express",description:"Conditions d'utilisation pour Sans rendez-vous express."};function l(){return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(r.Navbar,{}),(0,a.jsx)("main",{className:"flex-grow bg-white",children:(0,a.jsxs)("div",{className:"max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-6 py-12",children:[(0,a.jsx)("div",{className:"mb-4 text-gray-500",children:(0,a.jsx)("p",{children:"Derni\xe8re mise \xe0 jour le 24 mars, 2025"})}),(0,a.jsx)("h1",{className:"text-4xl sm:text-5xl xl:text-[60px] leading-tight font-bold text-[#212244] mb-16 max-w-[1000px]",children:"Conditions d'utilisation"}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"G\xe9n\xe9ral"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:'Avant d\'utiliser le service de Sans rendez-vous express Inc. (ci-apr\xe8s "SRVE"), veuillez prendre le temps de lire attentivement les conditions d\'utilisation (ci-apr\xe8s les "Conditions") suivantes. Ces derniers r\xe9gissent votre utilisation de notre site web www.sansrendezvousexpress.ca (ci-apr\xe8s la "Plateforme") et du service offert par SRVE, une entreprise l\xe9galement enregistr\xe9e au Qu\xe9bec, Canada.'}),(0,a.jsx)("p",{children:'En acc\xe9dant et en utilisant la Plateforme, vous, l\'utilisateur final ou l\'organisation que vous repr\xe9sentez (ci-apr\xe8s d\xe9sign\xe9 par "vous", "l\'Utilisateur" ou "les Utilisateurs"), acceptez d\'\xeatre li\xe9 par ces Conditions ainsi que par notre politique de confidentialit\xe9.'}),(0,a.jsx)("p",{children:'SRVE vous fournit, via sa Plateforme, la possibilit\xe9 de prendre un rendez-vous ponctuel dans une clinique sans rendez-vous au Qu\xe9bec (ci-apr\xe8s le "Service").'}),(0,a.jsx)("p",{children:"Ce contrat lie l\xe9galement vous, l'Utilisateur, et SRVE. Les deux parties peuvent faire appliquer ces termes et prendre les mesures n\xe9cessaires pour assurer leur respect."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Acceptation des conditions"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"En acc\xe9dant et en utilisant la Plateforme, vous acceptez d'\xeatre li\xe9 par les pr\xe9sentes Conditions, ou leurs versions mises \xe0 jour p\xe9riodiquement, ainsi que par notre Politique de confidentialit\xe9. Si vous n'acceptez pas ces conditions, veuillez ne pas utiliser notre Plateforme."}),(0,a.jsx)("p",{children:'En acc\xe9dant et en utilisant la Plateforme, vous, l\'utilisateur final ou l\'organisation que vous repr\xe9sentez (ci-apr\xe8s d\xe9sign\xe9 par "vous", "l\'Utilisateur" ou "les Utilisateurs"), acceptez d\'\xeatre li\xe9 par les pr\xe9sentes Conditions, ou leurs versions mises \xe0 jour p\xe9riodiquement, ainsi que par notre Politique de confidentialit\xe9. Si vous n\'acceptez pas ces Conditions, veuillez ne pas utiliser notre Plateforme.'})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Disponibilit\xe9 de la Plateforme"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Notre Plateforme est g\xe9n\xe9ralement accessible en tout temps. Cependant, il peut y avoir des interruptions temporaires de service pour des raisons de maintenance, de mises \xe0 jour ou de probl\xe8mes techniques. SRVE n'est pas responsable de ces interruptions et essaie de planifier les mises \xe0 jour durant les p\xe9riodes de faible affluence pour minimiser l'impact sur les Utilisateurs."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Compatibilit\xe9 et performance de la Plateforme"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Nous ne pouvons garantir que notre Plateforme fonctionnera de mani\xe8re optimale sur tous les appareils et configurations. De plus, comme tout service en ligne, elle peut \xeatre sujette \xe0 des ralentissements et des dysfonctionnements li\xe9s \xe0 l'utilisation d'Internet et des communications \xe9lectroniques."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Utilisation de la Plateforme"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"Lorsque vous utilisez notre Service, il est possible que nous vous demandions des informations relatives \xe0 votre carte d'assurance maladie. Ces informations sont essentielles pour que nous puissions vous fournir le Service demand\xe9, conform\xe9ment aux lois et r\xe8glements en vigueur ainsi qu'\xe0 nos Conditions. Vous avez le droit de refuser de nous communiquer ces informations. Cependant, veuillez noter que dans ce cas, nous pourrions ne pas \xeatre en mesure de vous offrir le Service en question."}),(0,a.jsx)("p",{children:"Il est interdit d'exploiter la Plateforme d'une fa\xe7on qui pourrait:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"Causer des dommages techniques \xe0 son infrastructure"}),(0,a.jsx)("li",{children:"Interf\xe9rer avec son bon fonctionnement"}),(0,a.jsx)("li",{children:"D\xe9grader la qualit\xe9 du Service pour les autres Utilisateurs en la surchargeant"})]})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Modification du contenu"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"SRVE se r\xe9serve le droit de modifier, ajouter ou supprimer tout contenu sur la Plateforme, \xe0 l'exception des donn\xe9es personnelles des Utilisateurs. Nous ne sommes pas responsables des erreurs, fautes de frappe ou bugs qui pourraient survenir."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Propri\xe9t\xe9 du contenu de l'Utilisateur"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Chaque Utilisateur reste propri\xe9taire du contenu publi\xe9 \xe0 partir de son compte. SRVE ne supprimera ce contenu que sur demande de l'Utilisateur ou s'il enfreint nos Conditions. Nous pouvons aussi supprimer certaines donn\xe9es personnelles pour renforcer la s\xe9curit\xe9, conform\xe9ment \xe0 notre politique de confidentialit\xe9."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Propri\xe9t\xe9 intellectuelle"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Tous les \xe9l\xe9ments de la Plateforme (textes, graphismes, logos, vid\xe9os, etc.) sont la propri\xe9t\xe9 exclusive de SRVE et sont prot\xe9g\xe9s par les lois sur la propri\xe9t\xe9 intellectuelle. Vous n'\xeates pas autoris\xe9 \xe0 les reproduire ou les utiliser sans notre accord \xe9crit pr\xe9alable."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Liens externes"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Notre Plateforme peut contenir des liens vers des sites web tiers. Nous n'avons aucun contr\xf4le sur le contenu de ces sites et d\xe9clinons toute responsabilit\xe9 quant \xe0 leur contenu ou aux dommages pouvant r\xe9sulter de leur utilisation."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Limitation de responsabilit\xe9"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"SRVE s'efforce de fournir une information exacte et \xe0 jour sur sa Plateforme, mais ne peut garantir son exhaustivit\xe9 ou sa pr\xe9cision. En dehors des cas o\xf9 la Loi sur la protection du consommateur du Qu\xe9bec s'applique, nous ne pourrons \xeatre tenus responsables d'\xe9ventuels dommages directs ou indirects r\xe9sultant de l'utilisation de la Plateforme."}),(0,a.jsx)("p",{children:"En cas de non respect \xe0 vos obligations en tant que Utilisateur, vous vous engagez \xe0 pr\xe9munir SRVE contre toute r\xe9clamation ou action en justice qui pourrait en d\xe9couler."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Mesures en cas de non-respect des Conditions"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Si un Utilisateur enfreint ces Conditions, ou si nous avons des motifs raisonnables de croire que ses actions menacent notre Plateforme, nos autres Utilisateurs ou des tiers, nous nous r\xe9servons le droit de limiter ou bloquer l'acc\xe8s de l'Utilisateur en question \xe0 certaines fonctionnalit\xe9s ou sections de notre site web, de mani\xe8re temporaire ou permanente, selon la gravit\xe9 de l'infraction."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Priorit\xe9 au fran\xe7ais"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"En cas de conflit d'interpr\xe9tation des pr\xe9sentes Conditions par rapport \xe0 celui en anglais, cette version fran\xe7aise pr\xe9vaudra."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Modification des conditions"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Ces Conditions peuvent \xeatre mis \xe0 jour p\xe9riodiquement pour rester conformes aux lois et pour refl\xe9ter les modifications dans la gestion de notre site ainsi que les attentes des utilisateurs de notre site. Nous conseillons \xe0 nos utilisateurs de consulter r\xe9guli\xe8rement ces termes et conditions pour rester inform\xe9s des \xe9ventuelles mises \xe0 jour. Si n\xe9cessaire, nous notifierons les utilisateurs des modifications par courriel ou nous publierons un avis sur notre site. Si vous continuez \xe0 utiliser la Plateforme, cela indiquera que vous acceptez les nouvelles Conditions."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"R\xe8gles d'interpr\xe9tation"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"Les titres des diff\xe9rentes sections des Conditions sont fournis uniquement \xe0 des fins de r\xe9f\xe9rence et de clart\xe9. Ils ne doivent pas \xeatre pris en compte lors de l'interpr\xe9tation ou de l'application des dispositions contenues dans ce document."}),(0,a.jsx)("p",{children:"Dans les Conditions, les mots \xe9crits au singulier incluent \xe9galement leur forme plurielle lorsque le contexte le requiert, et inversement. De m\xeame, les mots d\xe9signant un genre, masculin ou f\xe9minin, englobent l'autre genre lorsque cela est n\xe9cessaire pour une compr\xe9hension correcte du texte."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Divisibilit\xe9"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Si, \xe0 un moment quelconque, une clause de ce document est d\xe9clar\xe9e non conforme ou invalide selon les lois en vigueur, cette clause sera alors consid\xe9r\xe9e comme nulle et sera exclue de ce document. Les autres clauses resteront en vigueur et ne seront pas affect\xe9es par cette invalidit\xe9, et le reste du document continuera \xe0 \xeatre consid\xe9r\xe9 comme valide."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Loi applicable"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Les pr\xe9sentes Conditions sont r\xe9gies par les lois applicables dans la province du Qu\xe9bec, Canada. Tout litige sera soumis \xe0 la comp\xe9tence exclusive des tribunaux de la ville de Montr\xe9al."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Nous contacter"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsxs)("p",{children:["Pour toute question relative \xe0 ces Conditions, vous pouvez nous contacter par courriel \xe0 ",(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"})," ou par la poste \xe0 l'adresse suivante:"]}),(0,a.jsxs)("p",{children:["CP 99900 VG 550 446",(0,a.jsx)("br",{}),"COP Villeneuve",(0,a.jsx)("br",{}),"Montr\xe9al (Qu\xe9bec)",(0,a.jsx)("br",{}),"H2T 0A6"]})]})]})]})}),(0,a.jsx)(n.Footer,{})]})}},37302:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var a=t(28392),r=t(4473),n=t(42547),i=t.n(n),l=t(96326),o=t(56552),c=t(77370),d=t(81076),m=t(88597),u=t(94096),x=t(40869),h=t(65187);function p(){(0,l.useRouter)(),(0,l.useSearchParams)().get("redirectedFrom");let{t:e}=(0,m.B)(),{signIn:s,signInWithGoogle:t,status:n}=(0,x.A)(),[p,f]=(0,r.useState)(""),[j,g]=(0,r.useState)(""),[N,v]=(0,r.useState)(null),[b,y]=(0,r.useState)(!1),[w,k]=(0,r.useState)(!1),T=async t=>{t.preventDefault(),y(!0),v(null);try{let e=await s(p,j);if(e.success)window.location.href="/dashboard";else throw Error(e.error?.message||"Authentication failed")}catch(s){console.error("Error signing in:",s),v(e("auth.errors.invalidCredentials"))}finally{y(!1)}},S=async()=>{k(!0),v(null);try{await t()}catch(s){console.error("Error signing in with Google:",s),v(e("auth.errors.socialAuthFailed")),k(!1)}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"space-y-2 text-center",children:(0,a.jsx)("h1",{className:"text-2xl font-bold",children:e("auth.signIn")})}),N&&(0,a.jsx)(u.Fc,{variant:"destructive",children:(0,a.jsx)(u.TN,{children:N})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(o.$,{type:"button",variant:"outline",className:"w-full flex items-center justify-center gap-2",onClick:S,disabled:w,children:[w?(0,a.jsx)("span",{className:"w-5 h-5 border-2 border-gray-300 border-t-primary rounded-full animate-spin"}):(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",height:"24",viewBox:"0 0 24 24",width:"24",children:[(0,a.jsx)("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),(0,a.jsx)("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),(0,a.jsx)("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),(0,a.jsx)("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"}),(0,a.jsx)("path",{d:"M1 1h22v22H1z",fill:"none"})]}),(0,a.jsx)("span",{children:w?e("common.loading"):e("auth.continueWithGoogle")})]}),(0,a.jsxs)("div",{className:"relative flex items-center justify-center mt-6",children:[(0,a.jsx)(h.Separator,{className:"absolute w-full bg-gray-200"}),(0,a.jsx)("span",{className:"relative bg-white px-2 text-xs text-gray-500",children:e("auth.orContinueWith")})]})]}),(0,a.jsxs)("form",{onSubmit:T,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"email",children:e("auth.email")}),(0,a.jsx)(c.p,{id:"email",type:"email",placeholder:"<EMAIL>",autoComplete:"email",required:!0,value:p,onChange:e=>f(e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(d.J,{htmlFor:"password",children:e("auth.password")}),(0,a.jsx)(i(),{href:"/auth/forgot-password",className:"text-sm text-primary hover:underline",children:e("auth.forgotPassword")})]}),(0,a.jsx)(c.p,{id:"password",type:"password",autoComplete:"current-password",required:!0,value:j,onChange:e=>g(e.target.value)})]}),(0,a.jsx)(o.$,{type:"submit",className:"w-full py-6",disabled:b,children:b?e("common.loading"):e("auth.signIn")})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e("auth.noAccount")," ",(0,a.jsx)(i(),{href:"/auth/sign-up",className:"text-primary hover:underline",children:e("auth.createAccount")})]})})]})}},38505:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx","default")},39614:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GET:()=>n});var a=t(90987),r=t(31186);async function n(e){try{console.log("=== SESSION CHECK DEBUG START ===");let s=await (0,r.U)(),t=e.headers.get("authorization"),n=e.headers.get("cookie");console.log("Request details:",{hasAuthHeader:!!t,authHeaderPreview:t?t.substring(0,30)+"...":"none",hasCookies:!!n,cookiePreview:n?n.substring(0,50)+"...":"none",userAgent:e.headers.get("user-agent")?.substring(0,50)+"...",origin:e.headers.get("origin")});let i={};if(t&&t.startsWith("Bearer ")){let e=t.substring(7);console.log("Testing Bearer token auth...");let{data:a,error:r}=await s.auth.getUser(e);i.bearerAuth={success:!!a?.user,userId:a?.user?.id,email:a?.user?.email,error:r?.message}}console.log("Testing cookie auth...");let{data:l,error:o}=await s.auth.getUser();i.cookieAuth={success:!!l?.user,userId:l?.user?.id,email:l?.user?.email,error:o?.message},console.log("Testing session retrieval...");let{data:c,error:d}=await s.auth.getSession();return i.session={hasSession:!!c?.session,hasAccessToken:!!c?.session?.access_token,tokenPreview:c?.session?.access_token?c.session.access_token.substring(0,20)+"...":"none",error:d?.message},console.log("=== SESSION CHECK DEBUG END ==="),a.NextResponse.json({timestamp:new Date().toISOString(),results:i,recommendation:i.bearerAuth?.success?"Bearer auth working":i.cookieAuth?.success?"Cookie auth working":"No authentication method working"})}catch(e){return console.error("Session check error:",e),a.NextResponse.json({error:"Session check failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}},44672:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>A});var a=t(28392),r=t(4473),n=t(42547),i=t.n(n),l=t(41788),o=t(41435),c=t(26109),d=t(89085),m=t(91632),u=t(39790),x=t(56552),h=t(77370),p=t(81076),f=t(68366),j=t(18705),g=t(60164),N=t(63907),v=t(6121),b=t(88125),y=t(96067),w=t(71216),k=t(83808),T=t(86669),S=t(85391),C=t(88597),P=t(59356);function A(){let{familyMembers:e}=(0,k.h)(),[s,t]=(0,r.useState)(),[n,A]=(0,r.useState)(""),[q,E]=(0,r.useState)(""),[R,U]=(0,r.useState)(""),[_,z]=(0,r.useState)(""),[D,L]=(0,r.useState)((0,v.o)(new Date)),[I,F]=(0,r.useState)(""),[V,$]=(0,r.useState)(""),[B,M]=(0,r.useState)(""),[W,X]=(0,r.useState)(null),[G,H]=(0,r.useState)({}),[O,Z]=(0,r.useState)(!1),[J,Q]=(0,r.useState)(!1),[Y,K]=(0,r.useState)(!1),[ee,es]=(0,r.useState)(""),{t:et,language:ea}=(0,C.B)(),er=e=>{let s=e.replace(/[^a-zA-Z0-9]/g,"").toUpperCase();(s=s.slice(0,6)).length>3&&(s=s.slice(0,3)+" "+s.slice(3));let t=/^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/.test(s);return s.length>0&&!t?z(et(P.o.findAppointment.postalFormatWarning)):z(""),s.length>0&&!t&&s.length>=7?U(et(P.o.findAppointment.invalidPostalFormat)):U(""),s},en=()=>{let e={};return s||(e.date=et(P.o.findAppointment.selectDateError)),n||(e.time=et(P.o.findAppointment.selectTimeError)),q?R?e.postalCode=R:/^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/.test(q)||(e.postalCode=et(P.o.findAppointment.invalidPostalError)):e.postalCode=et(P.o.findAppointment.enterPostalError),I||(e.selectedMember=et(P.o.findAppointment.selectPersonError)),I&&!(0,N.E)(V)&&(e.healthCardLastDigits=et(P.o.findAppointment.healthCardDigitsError)),I&&2!==B.length&&(e.cardSequenceNumber=et(P.o.findAppointment.sequenceNumberError)),H(e),0===Object.keys(e).length};async function ei(e){if(e.preventDefault(),en()){K(!0),es("");try{let e={request_type:"general_appointment",patient_id:W?.supabaseId||null,request_details:{postalCode:q,date:s?(0,b.GP)(s,"yyyy-MM-dd"):"",time:n,healthCardLastDigits:W?V:"",cardSequenceNumber:W?B:"",patientName:W?`${W.firstName} ${W.lastName}`:""}},t=await (0,T.hb)(e);t.success?Q(!0):"no_subscription"===t.errorType||"authentication_required"===t.errorType?es(et(P.o.findAppointment.noSubscription)):es(t.error||"Failed to submit appointment request")}catch(e){console.error("Error submitting appointment request:",e),es("An unexpected error occurred. Please try again.")}finally{K(!1)}}}let el=e.filter(e=>e.firstName||e.lastName);return(0,a.jsx)(m.DashboardLayout,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.title})}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.description})})]}),(0,a.jsx)(u.Zp,{children:J?(0,a.jsx)(u.Wu,{children:(0,a.jsxs)("div",{className:"p-8 flex flex-col items-center justify-center space-y-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-green-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,a.jsx)("h3",{className:"text-xl font-medium text-center",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.thankYou})}),(0,a.jsx)("p",{className:"text-muted-foreground text-center max-w-md",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.confirmationMessage})}),(0,a.jsx)("div",{className:"flex gap-3 mt-2",children:(0,a.jsx)(x.$,{variant:"secondary",asChild:!0,children:(0,a.jsx)(i(),{href:"/mes-rendez-vous",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.viewRequests})})})}),(0,a.jsx)(x.$,{variant:"outline",onClick:()=>{Q(!1),t(void 0),A(""),E(""),F(""),$(""),M(""),H({})},className:"mt-4",children:(0,a.jsx)(C.T,{keyName:P.o.common.newRequest})})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(u.aR,{children:[(0,a.jsx)(u.ZB,{children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.searchCriteria})}),(0,a.jsx)(u.BT,{children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.requiredFields})})]}),(0,a.jsx)(u.Wu,{children:(0,a.jsxs)("form",{onSubmit:ei,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"family-member",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.appointmentFor})}),(0,a.jsxs)(g.l6,{value:I,onValueChange:e=>{if(F(e),G.selectedMember){let e={...G};delete e.selectedMember,H(e)}},children:[(0,a.jsx)(g.bq,{className:(0,N.cn)("w-full",G.selectedMember&&"border-red-500"),children:(0,a.jsx)(g.yv,{placeholder:et(P.o.findAppointment.selectPerson)})}),(0,a.jsx)(g.gC,{children:el.map(e=>(0,a.jsx)(g.eb,{value:e.id.toString(),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"h-3 w-3 text-blue-700"})}),(0,a.jsxs)("span",{children:[e.firstName," ",e.lastName]})]})},e.id))})]}),G.selectedMember&&(0,a.jsx)("p",{className:"text-xs text-red-500",children:G.selectedMember}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground flex items-center gap-1 mt-1",children:[(0,a.jsx)(o.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"fr"===ea?(0,a.jsxs)(a.Fragment,{children:["Les personnes dans cette liste sont g\xe9r\xe9es dans la section ",(0,a.jsx)(i(),{href:"/compte/utilisateurs",className:"text-primary hover:underline",children:'"G\xe9rer les utilisateurs du compte"'})]}):(0,a.jsxs)(a.Fragment,{children:["The people listed here are managed in the ",(0,a.jsx)(i(),{href:"/compte/utilisateurs",className:"text-primary hover:underline",children:'"Manage account users"'})," section"]})})]})]}),W&&(0,a.jsxs)("div",{className:"space-y-4 p-4 border rounded-md bg-muted/40",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("div",{className:"h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"h-3 w-3 text-blue-700"})}),(0,a.jsxs)("h3",{className:"font-medium",children:[(0,a.jsx)(C.T,{keyName:P.o.findAppointment.healthCardOf})," ",W.firstName," ",W.lastName]})]}),(0,a.jsxs)("div",{className:"grid gap-6 sm:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"health-card-last-digits",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.lastDigits})}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"bg-muted rounded px-2 py-1.5 font-mono text-sm border",children:W.healthCard}),(0,a.jsx)(h.p,{id:"health-card-last-digits",placeholder:"xxxx-xxxx",value:V,onChange:e=>{let s=(0,N.d)(e.target.value,8);$(s),G.healthCardLastDigits&&(0,N.E)(s)&&H({...G,healthCardLastDigits:""})},required:!!I,className:`flex-grow font-mono ${G.healthCardLastDigits?"border-red-500":V.length>0&&V.length<8?"border-orange-500":""}`})]}),G.healthCardLastDigits?(0,a.jsx)("p",{className:"text-xs text-red-500",children:G.healthCardLastDigits}):V.length>0&&V.length<8&&(0,a.jsx)("p",{className:"text-xs text-orange-500",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.enterEightDigits})}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.format})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p.J,{htmlFor:"sequence-number",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.sequenceNumber})}),(0,a.jsxs)(j.AM,{children:[(0,a.jsx)(j.Wv,{asChild:!0,children:(0,a.jsxs)(x.$,{variant:"ghost",size:"icon",className:"h-6 w-6 p-0 rounded-md",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"}),(0,a.jsx)("span",{className:"sr-only",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.sequenceInfo})})]})}),(0,a.jsx)(j.hl,{className:"p-0 max-w-[450px] w-auto border-0 bg-transparent shadow-none",children:(0,a.jsx)("div",{className:"overflow-hidden rounded-lg border bg-background shadow-lg",children:(0,a.jsx)("div",{className:"p-2",children:(0,a.jsx)(S.default,{src:"/ramq.png",alt:"Carte RAMQ",width:400,height:250,className:"rounded-md"})})})})]})]}),(0,a.jsx)(h.p,{id:"sequence-number",placeholder:"01-99",value:B,onChange:e=>{M(e.target.value.replace(/\D/g,"").substring(0,2))},required:!0,className:`font-mono ${G.cardSequenceNumber?"border-red-500":""}`}),G.cardSequenceNumber?(0,a.jsx)("p",{className:"text-xs text-red-500",children:G.cardSequenceNumber}):(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.enterTwoDigits})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(p.J,{htmlFor:"postal-code",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.postalCode})}),(0,a.jsx)(h.p,{id:"postal-code",placeholder:et(P.o.findAppointment.postalExample),value:q,onChange:e=>{E(er(e.target.value))},required:!0,maxLength:7,className:G.postalCode?"border-red-500":_?"border-orange-500":""}),G.postalCode?(0,a.jsx)("p",{className:"text-sm text-red-500 mt-1",children:G.postalCode}):_?(0,a.jsx)("p",{className:"text-xs text-orange-500",children:_}):null,(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.postalCodeDescription})})]}),(0,a.jsxs)("div",{className:"grid gap-6 sm:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"date",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.fromDate})}),(0,a.jsxs)(j.AM,{children:[(0,a.jsx)(j.Wv,{asChild:!0,children:(0,a.jsxs)(x.$,{id:"date",variant:"outline",className:(0,N.cn)("w-full justify-start text-left font-normal",!s&&"text-muted-foreground",G.date&&"border-red-500"),children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),s?(0,b.GP)(s,"PPP",{locale:w.fr}):et(P.o.findAppointment.selectDate)]})}),(0,a.jsx)(j.hl,{className:"w-auto p-0",children:(0,a.jsx)(f.V,{mode:"single",selected:s,onSelect:e=>{if(t(e),G.date){let e={...G};delete e.date,H(e)}},initialFocus:!0,locale:w.fr,disabled:e=>(0,y.Y)(e,D),fromDate:D})})]}),G.date&&(0,a.jsx)("p",{className:"text-xs text-red-500",children:G.date})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"time",children:(0,a.jsx)(C.T,{keyName:P.o.findAppointment.appointmentTime})}),(0,a.jsxs)(g.l6,{value:n,onValueChange:e=>{if(A(e),G.time){let e={...G};delete e.time,H(e)}},children:[(0,a.jsx)(g.bq,{className:(0,N.cn)("w-full",G.time&&"border-red-500"),children:(0,a.jsx)(g.yv,{placeholder:et(P.o.findAppointment.chooseTime),children:n?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"mr-2 h-4 w-4"}),n]}):et(P.o.findAppointment.chooseTime)})}),(0,a.jsxs)(g.gC,{children:[(0,a.jsx)(g.eb,{value:et(P.o.findAppointment.asap),children:et(P.o.findAppointment.asap)}),(0,a.jsx)(g.eb,{value:et(P.o.findAppointment.morning),children:et(P.o.findAppointment.morning)}),(0,a.jsx)(g.eb,{value:et(P.o.findAppointment.afternoon),children:et(P.o.findAppointment.afternoon)}),(0,a.jsx)(g.eb,{value:et(P.o.findAppointment.evening),children:et(P.o.findAppointment.evening)})]})]}),G.time&&(0,a.jsx)("p",{className:"text-xs text-red-500",children:G.time})]})]}),ee&&(0,a.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm mb-4",children:(0,a.jsx)("div",{dangerouslySetInnerHTML:{__html:ee}})}),(0,a.jsx)(x.$,{type:"submit",className:"w-full sm:w-auto",size:"lg",disabled:!(()=>{let e=/^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/.test(q);return!!(s&&n&&e&&I&&(!I||(0,N.E)(V)&&2===B.length))})()||Y,children:Y?(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)(C.T,{keyName:P.o.common.submitting})]}):(0,a.jsx)(C.T,{keyName:P.o.findAppointment.submitRequest})})]})})]})})]})})}},45469:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var a=t(57484),r=t(85815),n=t(8152);let i={title:"Politique de confidentialit\xe9 | Sans rendez-vous express",description:"Politique de confidentialit\xe9 pour Sans rendez-vous express."};function l(){return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(r.Navbar,{}),(0,a.jsx)("main",{className:"flex-grow bg-white",children:(0,a.jsxs)("div",{className:"max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-6 py-12",children:[(0,a.jsx)("div",{className:"mb-4 text-gray-500",children:(0,a.jsx)("p",{children:"Derni\xe8re mise \xe0 jour le 24 mars, 2025"})}),(0,a.jsx)("h1",{className:"text-4xl sm:text-5xl xl:text-[60px] leading-tight font-bold text-[#212244] mb-16 max-w-[1000px]",children:"Politique de confidentialit\xe9"}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"G\xe9n\xe9ral"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:'En utilisant notre site www.sansrendezvousexpress.ca (ci-apr\xe8s la "Plateforme"), vous acceptez les termes de cette politique de confidentialit\xe9 (ci-apr\xe8s la "Politique").'}),(0,a.jsx)("p",{children:"Le but de cette Politique est de renseigner les utilisateurs de notre Plateforme sur les types de donn\xe9es personnelles que nous collectons et la mani\xe8re dont elles sont utilis\xe9es."}),(0,a.jsx)("p",{children:'Sans rendez-vous express Inc. (ci-apr\xe8s "SRVE") se conforme strictement \xe0 la loi 25 du Qu\xe9bec en mati\xe8re de protection des donn\xe9es personnelles. SRVE est une entreprise l\xe9galement enregistr\xe9e au Qu\xe9bec, Canada.'}),(0,a.jsx)("p",{children:"Cette Politique explique \xe9galement notre engagement et les actions que nous entreprenons pour assurer la protection de vos informations personnelles."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Acceptation de cette Politique"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"En acc\xe9dant et en utilisant la Plateforme, vous, l'utilisateur final ou l'organisation que vous repr\xe9sentez (ci-apr\xe8s d\xe9sign\xe9 par \"vous\", \"l'Utilisateur\" ou \"les Utilisateurs\"), acceptez d'\xeatre li\xe9 par la pr\xe9sente Politique, ou leurs versions mises \xe0 jour p\xe9riodiquement, ainsi que par nos Conditions d'utilisation. Si vous n'acceptez pas cette Politique, veuillez ne pas utiliser notre Plateforme."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Collecte des renseignements personnels"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"Lors de votre visite sur la Plateforme, nous collectons automatiquement des informations sur votre appareil et votre interaction avec notre site via l'outil Google Analytics."}),(0,a.jsx)("p",{children:"Cela inclut les donn\xe9es suivantes:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"Adresse IP"}),(0,a.jsx)("li",{children:"Lieu"}),(0,a.jsx)("li",{children:"D\xe9tails mat\xe9riels et logiciels"}),(0,a.jsx)("li",{children:"Contenu que l'utilisateur consulte sur notre site"}),(0,a.jsx)("li",{children:"Liens sur lesquels utilisateur un utilisateur clique en allant sur le site"}),(0,a.jsx)("li",{children:"Donn\xe9es personnelles r\xe9colt\xe9es non automatiquement"})]}),(0,a.jsx)("p",{className:"mt-6",children:"Vos donn\xe9es personnelles sont collect\xe9es via:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"Formulaire de contact"}),(0,a.jsx)("li",{children:"Formulaire de demande de rendez-vous m\xe9dical"})]}),(0,a.jsx)("p",{className:"mt-6",children:"Cela inclut les donn\xe9es suivantes:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"Pr\xe9nom"}),(0,a.jsx)("li",{children:"Nom"}),(0,a.jsx)("li",{children:"Num\xe9ro d'assurance maladie"}),(0,a.jsx)("li",{children:"Num\xe9ro s\xe9quentiel de la carte d'assurance maladie"}),(0,a.jsx)("li",{children:"Date de naissance"}),(0,a.jsx)("li",{children:"Sexe"}),(0,a.jsx)("li",{children:"Code postal proche du rendez-vous"}),(0,a.jsx)("li",{children:"Raison de la consultation"}),(0,a.jsx)("li",{children:"Courriel"}),(0,a.jsx)("li",{children:"T\xe9l\xe9phone mobile"}),(0,a.jsx)("li",{children:"T\xe9l\xe9phone \xe0 la maison"}),(0,a.jsx)("li",{children:"Information de paiement"})]})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Utilisation des t\xe9moins (cookies)"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"La Plateforme utilise des t\xe9moins de navigation (cookies) ainsi que des pixels de Facebook et Google pour vous offrir une exp\xe9rience personnalis\xe9e."}),(0,a.jsx)("p",{children:"Les t\xe9moins de navigation sont des fichiers textuels qui sont transmis \xe0 votre navigateur lorsque vous acc\xe9dez \xe0 un site Web. Les t\xe9moins de Google Analytics transmettent des statistiques anonymes de consultation. Ils permettent, entre autres, de conna\xeetre le nombre de visites sur une page et le temps moyen de ces visites."}),(0,a.jsx)("p",{children:"Les pixels Facebook et Google sont du code JavaScript permettant de d\xe9tecter, notamment, le nombre de visites sur une page, et ce, de fa\xe7on anonyme. Les pixels ne sont pas plac\xe9s sur votre ordinateur; ils demeurent sur le site consult\xe9. Ils sont utilis\xe9s pour recueillir des statistiques, analyser les performances du site et faire de la publicit\xe9 cibl\xe9e par centres d'int\xe9r\xeat."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Utilisation des donn\xe9es"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"Veuillez noter que nous ne recueillons que les donn\xe9es n\xe9cessaires pour atteindre les objectifs d\xe9crits dans la Politique. Nous ne collecterons pas d'informations suppl\xe9mentaires sans vous en informer \xe0 l'avance."}),(0,a.jsx)("p",{children:"Nous utilisons ces donn\xe9es pour:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"Trouver des rendez-vous m\xe9dical"}),(0,a.jsx)("li",{children:"Envoyer des offres promotionnelles et des communications cibl\xe9es via notre infolettre"}),(0,a.jsx)("li",{children:"Faire des analyses statistiques pour am\xe9liorer le site"}),(0,a.jsx)("li",{children:"Faire un suivi des demandes de rendez-vous m\xe9dical"}),(0,a.jsx)("li",{children:"Faire un suivi des messages re\xe7u \xe0 travers le formulaire de contact"}),(0,a.jsx)("li",{children:"Dans le cadre d'un protocole de s\xe9curit\xe9 des donn\xe9es"})]}),(0,a.jsx)("p",{children:"Les donn\xe9es des utilisateurs peuvent \xeatre consult\xe9es, trait\xe9es ou collect\xe9es au Canada."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Dur\xe9e d'exploitation des donn\xe9es personnelles"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Nous conservons pour une p\xe9riode ind\xe9termin\xe9e vos informations de contact et les 4 premiers caract\xe8res de votre num\xe9ro d'assurance maladie pour faciliter la prise de rendez-vous. Vous pouvez nous contacter si vous souhaitez apporter des modifications, supprimer ou anonymiser vos donn\xe9es personnelles."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Mesures de s\xe9curit\xe9"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"Vos donn\xe9es sont stock\xe9es dans un environnement s\xe9curis\xe9 et contr\xf4l\xe9. La Plateforme utilise des protocoles SSL et des mesures de s\xe9curit\xe9 robustes avec double authentification pour pr\xe9venir tout acc\xe8s non autoris\xe9."}),(0,a.jsx)("p",{children:"Nous nous engageons \xe0 ne pas vendre ou partager vos donn\xe9es avec des tiers sauf si la loi l'exige ou en cas de proc\xe9dure judiciaire."}),(0,a.jsx)("p",{children:"Nous pouvons divulguer \xe0 tout membre de notre organisation les donn\xe9es utilisateur dont il a raisonnablement besoin pour r\xe9aliser les objectifs \xe9nonc\xe9s dans la pr\xe9sente Politique."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Gestion des incidents de s\xe9curit\xe9"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"En cas de fuite de vos donn\xe9es personnelles, nous nous engageons \xe0 vous en tenir inform\xe9 ainsi que la Commission d'Acc\xe8s \xe0 l'Information du Qu\xe9bec."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Droit des utilisateurs"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"Si vous \xeates r\xe9sident du Qu\xe9bec, conform\xe9ment \xe0 la loi 25, vous b\xe9n\xe9ficiez de droits sp\xe9cifiques concernant vos donn\xe9es personnelles:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"Droit d'\xeatre inform\xe9"}),(0,a.jsx)("li",{children:"Droit d'acc\xe8s"}),(0,a.jsx)("li",{children:"Droit \xe0 la rectification"}),(0,a.jsx)("li",{children:"Droit \xe0 la portabilit\xe9 des donn\xe9es"})]}),(0,a.jsx)("p",{className:"mt-6",children:"Vous avez le droit de vous opposer \xe0 l'utilisation de vos donn\xe9es et de les retirer. Pour exercer ces droits, contactez notre responsable de la protection des donn\xe9es personnelles:"}),(0,a.jsx)("p",{className:"mt-6 font-semibold",children:"Par courrier:"}),(0,a.jsxs)("p",{children:["Sans rendez-vous express - Responsable de la protection des renseignements personnels",(0,a.jsx)("br",{}),"CP 99900 VG 550 446",(0,a.jsx)("br",{}),"COP Villeneuve",(0,a.jsx)("br",{}),"Montr\xe9al (Qu\xe9bec)",(0,a.jsx)("br",{}),"H2T 0A6"]}),(0,a.jsx)("p",{className:"mt-6 font-semibold",children:"Par courriel:"}),(0,a.jsx)("p",{children:(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"})})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Droit d'acc\xe8s et de rectification"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Vous pouvez consulter, modifier ou supprimer vos donn\xe9es. Pour ce faire, utilisez les m\xeames coordonn\xe9es mentionn\xe9es ci-dessus."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Consentement et retrait"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Il est possible de naviguer sur notre site de mani\xe8re anonyme. Toutefois, l'acc\xe8s \xe0 certaines fonctionnalit\xe9s peut n\xe9cessiter la collecte de donn\xe9es personnelles. Vous avez toujours l'option de ne pas fournir ces informations."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Mineurs"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"La loi 25 pr\xe9cise que les personnes de moins de 14 ans sont consid\xe9r\xe9es comme des mineurs aux fins de la collecte de donn\xe9es. Leurs renseignements personnels ne pourront \xeatre recueillis aupr\xe8s de celui-ci sans le consentement du titulaire de l'autorit\xe9 parentale ou du tuteur, sauf lorsque cette collecte sera manifestement au b\xe9n\xe9fice de ce mineur."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Liens externes"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Notre Plateforme peut contenir des liens vers des sites web tiers. Nous n'avons aucun contr\xf4le sur le contenu de ces sites et d\xe9clinons toute responsabilit\xe9 quant \xe0 leur contenu ou aux dommages pouvant r\xe9sulter de leur utilisation."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Priorit\xe9 au fran\xe7ais"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"En cas de conflit d'interpr\xe9tation de la Politique par rapport \xe0 celui en anglais, cette version fran\xe7aise pr\xe9vaudra."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Modification de la Politique"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"Nous pouvons \xeatre amen\xe9s \xe0 modifier occasionnellement la Politique. Vous serez inform\xe9s de ces changements lors de votre prochaine visite. Si vous continuez \xe0 utiliser la Plateforme, cela vaudra acceptation de la Politique."}),(0,a.jsx)("p",{children:"Nous conseillons \xe0 nos utilisateurs de consulter r\xe9guli\xe8rement la Politique pour rester inform\xe9s des \xe9ventuelles mises \xe0 jour. Si n\xe9cessaire, nous notifierons les utilisateurs des modifications par courriel ou nous publierons un avis sur notre site. Si vous continuez \xe0 utiliser la Plateforme, cela indiquera que vous acceptez la nouvelle Politique."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"R\xe8gles d'interpr\xe9tation"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"Les titres des diff\xe9rentes sections de la Politique sont fournis uniquement \xe0 des fins de r\xe9f\xe9rence et de clart\xe9. Ils ne doivent pas \xeatre pris en compte lors de l'interpr\xe9tation ou de l'application des dispositions contenues dans ce document."}),(0,a.jsx)("p",{children:"Dans la Politique, les mots \xe9crits au singulier incluent \xe9galement leur forme plurielle lorsque le contexte le requiert, et inversement. De m\xeame, les mots d\xe9signant un genre, masculin ou f\xe9minin, englobent l'autre genre lorsque cela est n\xe9cessaire pour une compr\xe9hension correcte du texte."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Divisibilit\xe9"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Si, \xe0 un moment quelconque, une clause de ce document est d\xe9clar\xe9e non conforme ou invalide selon les lois en vigueur, cette clause sera alors consid\xe9r\xe9e comme nulle et sera exclue de ce document. Les autres clauses resteront en vigueur et ne seront pas affect\xe9es par cette invalidit\xe9, et le reste du document continuera \xe0 \xeatre consid\xe9r\xe9 comme valide."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Loi applicable"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Cette Politique est r\xe9gie par les lois applicables dans la province du Qu\xe9bec, Canada. Tout litige sera soumis \xe0 la comp\xe9tence exclusive des tribunaux de la ville de Montr\xe9al."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Nous contacter"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsxs)("p",{children:["Pour toute question ou plainte relative \xe0 la Politique, vous pouvez nous contacter \xe0 ",(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"}),"."]})})]})]})}),(0,a.jsx)(n.Footer,{})]})}},50339:()=>{},50468:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var a=t(28392),r=t(4473),n=t(44951),i=t(96326),l=t(4657),o=t(53543),c=t(21204),d=t(88597),m=t(59356),u=t(98209),x=t(54386);let h={INDIVIDUAL:{MONTHLY:"price_1R0CkxElTneJ74CJARjKlS9U",ANNUAL:"price_1REN04ElTneJ74CJVejJzVDH"},FAMILY:{MONTHLY:"price_1R0ClKElTneJ74CJO45trpKh",ANNUAL:"price_1REN0TElTneJ74CJDRnv2MXk"}},p=({title:e,price:s,description:t,features:r,annualSavings:i,planId:o,userSubscription:c,isLoadingSubscription:x,isLoading:h,onSubscribe:p,onManageSubscription:f})=>{let{language:j}=(0,u.o)();return(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-2",children:e}),(0,a.jsxs)("div",{className:"text-4xl font-bold mb-5 animate-price-fade text-[#212244]",children:[s.split("/")[0],(0,a.jsx)("span",{className:"text-xl font-normal",children:"fr"===j?"/mois":"/month"})]},`price-${s}`),i&&(0,a.jsx)("div",{className:"text-green-600 text-xs font-medium mb-2",children:i}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-6",children:t})]}),x?(0,a.jsx)(l.$,{disabled:!0,size:"lg",className:"w-full rounded-lg font-medium text-sm py-5 mb-6 group hover:shadow-md transition-all duration-300 bg-brandBlue text-white hover:bg-brandBlue/90",children:(0,a.jsx)(d.T,{keyName:m.o.common.loading})}):c?(0,a.jsx)(l.$,{onClick:f,size:"lg",className:"w-full rounded-lg font-medium text-sm py-5 mb-6 group hover:shadow-md transition-all duration-300 bg-brandBlue text-white hover:bg-brandBlue/90",children:(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.manageSubscription})}):(0,a.jsx)(l.$,{onClick:()=>p(o),disabled:h[o],size:"lg",className:"w-full rounded-lg font-medium text-sm py-5 mb-6 group hover:shadow-md transition-all duration-300 bg-brandBlue text-white hover:bg-brandBlue/90",children:h[o]?(0,a.jsx)(d.T,{keyName:m.o.common.processing}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.choosePlan}),(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-2 h-5 w-5 arrow-icon",children:[(0,a.jsx)("path",{d:"M5 12h14"}),(0,a.jsx)("path",{d:"m12 5 7 7-7 7"})]})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-bold text-sm text-[#212244]",children:(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.included})}),r.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-4 h-4 mr-2",children:(0,a.jsx)(n.A,{className:"w-full h-full text-brandBlue",strokeWidth:2})}),(0,a.jsx)("span",{className:"text-[#212244] text-sm",children:e})]},s))]})]})};function f(){let[e,s]=(0,r.useState)("monthly"),[t,n]=(0,r.useState)(!1),[l,c]=(0,r.useState)({}),[f,j]=(0,r.useState)(null),[g,N]=(0,r.useState)(!0),{language:v}=(0,u.o)(),b=(0,i.useRouter)();(0,i.useSearchParams)();let y=async e=>{console.log(`Starting checkout process for plan ID: ${e}`),c(s=>({...s,[e]:!0}));try{let{data:{user:s}}=await x.N.auth.getUser();if(!s){b.push(`/login?redirect=/pricing&selected_plan=${e}`);return}let{data:t,error:a}=await x.N.functions.invoke("supabase-functions-create-checkout",{body:{price_id:e,user_id:s.id,return_url:""},headers:{"X-Customer-Email":s.email}});if(a)throw a;if(t?.url)console.log(`Redirecting to Stripe checkout: ${t.url}`),window.location.href=t.url;else throw console.error("No checkout URL returned in the response:",t),Error("No checkout URL returned")}catch(e){console.error("Error creating checkout session:",e),alert(`Error creating checkout session: ${e instanceof Error?e.message:"Unknown error"}. Please try again.`)}finally{c(s=>({...s,[e]:!1}))}},w=()=>{b.push("/compte/abonnement")},k=e=>"fr"===v?`${e}$`:`$${e}`,T=k("fr"===v?"7,95":"7.95"),S=k("fr"===v?"14,95":"14.95"),C=k("fr"===v?"5,95":"5.95"),P=k("fr"===v?"11,20":"11.20"),A="annual"===e?(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.individual.annualSavings}):void 0,q="annual"===e?(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.family.annualSavings}):void 0,E=`${"monthly"===e?T:C}/suffix`,R=`${"monthly"===e?S:P}/suffix`;return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("div",{className:"flex justify-center mb-8",children:(0,a.jsx)(o.S,{period:e,onChange:e=>{s(e)}})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsx)(p,{title:(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.individual.title}),price:E,description:(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.individual.description}),features:[(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.feature1},"1"),(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.feature2},"2"),(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.feature3},"3"),(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.feature4},"4")],annualSavings:A,planId:"monthly"===e?h.INDIVIDUAL.MONTHLY:h.INDIVIDUAL.ANNUAL,userSubscription:f,isLoadingSubscription:g,isLoading:l,onSubscribe:y,onManageSubscription:w},`individual-${e}`),(0,a.jsx)(p,{title:(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.family.title}),price:R,description:(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.family.description}),features:[(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.family.features},"1"),(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.feature2},"2"),(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.feature3},"3"),(0,a.jsx)(d.T,{keyName:m.o.landing.pricing.feature4},"4")],annualSavings:q,planId:"monthly"===e?h.FAMILY.MONTHLY:h.FAMILY.ANNUAL,userSubscription:f,isLoadingSubscription:g,isLoading:l,onSubscribe:y,onManageSubscription:w},`family-${e}`)]})]})})}function j(){return(0,a.jsx)(c.S,{children:(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"flex h-full w-full items-center justify-center",children:(0,a.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"})}),children:(0,a.jsx)(f,{})})})}},52456:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/compte/abonnement/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/compte/abonnement/page.tsx","default")},53741:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GET:()=>n});var a=t(90987),r=t(31186);async function n(e){try{console.log("=== AUTH DEBUG START ===");let s=e.cookies.getAll();console.log("Request cookies:",s.map(e=>({name:e.name,hasValue:!!e.value,partial:e.value?.substring(0,20)+"..."})));let t=await (0,r.U)();console.log("Supabase client created successfully");let{data:{user:n},error:i}=await t.auth.getUser();console.log("Auth user result:",{hasUser:!!n,userId:n?.id,email:n?.email,error:i?.message});let{data:{session:l},error:o}=await t.auth.getSession();console.log("Session result:",{hasSession:!!l,sessionValid:!!l?.access_token,error:o?.message});let c=null;if(n){let{data:e,error:s}=await t.from("subscriptions").select("customer_id, status, plan_type, billing_period").eq("user_id",n.id);console.log("Subscription query result:",{hasSubscriptions:!!e,count:e?.length||0,statuses:e?.map(e=>e.status),error:s?.message}),c={hasSubscriptions:!!e,count:e?.length||0,subscriptions:e||[],error:s?.message}}return console.log("=== AUTH DEBUG END ==="),a.NextResponse.json({success:!0,requestCookies:s.length,cookieNames:s.map(e=>e.name),hasUser:!!n,hasSession:!!l,userEmail:n?.email,userId:n?.id,authError:i?.message,sessionError:o?.message,subscription:c})}catch(e){return console.error("Debug auth error:",e),a.NextResponse.json({error:"Debug failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}},54353:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(28392),r=t(4473),n=t(42547),i=t.n(n),l=t(91878),o=t(80566),c=t(34326),d=t(48017),m=t(91632),u=t(56552),x=t(39790),h=t(81076),p=t(60164),f=t(94096),j=t(88597),g=t(59356),N=t(98209),v=t(65751),b=t(60374);function y(){let{user:e,preferences:s,language:t,setLanguage:n,updatePreferences:y,isPreferencesLoading:w,fetchPreferences:k}=(0,b.yk)(),{language:T,setLanguage:S}=(0,N.o)(),{t:C}=(0,j.B)(),{preferences:P,isLoading:A,isSaving:q,saveError:E,saveSuccess:R,updatePreferences:U}=(0,v.H)(),_=t||T,[z,D]=(0,r.useState)(!1),[L,I]=(0,r.useState)(null),[F,V]=(0,r.useState)(!1),[$,B]=(0,r.useState)(_),[M,W]=(0,r.useState)(!1),X=async()=>{if(e?.id){console.log("Saving preferences:",{pendingLanguage:$}),D(!0),I(null),V(!1);try{if($!==_&&(n($),S($)),y)try{await y(e.id,{language:$}),W(!1),V(!0),setTimeout(()=>V(!1),3e3),console.log("Preferences saved successfully using store"),D(!1);return}catch(e){console.error("Error saving preferences with store:",e)}await U({language:$}),W(!1),V(!0),setTimeout(()=>V(!1),3e3),console.log("Preferences saved successfully using hook")}catch(e){console.error("Error saving preferences:",e),I(e instanceof Error?e:Error("Failed to save preferences")),B(_)}finally{D(!1)}}};return(0,a.jsx)(m.DashboardLayout,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(u.$,{asChild:!0,variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsxs)(i(),{href:"/compte",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:(0,a.jsx)(j.T,{keyName:g.o.common.back})})]})}),(0,a.jsx)("h1",{className:"text-3xl font-bold",children:(0,a.jsx)(j.T,{keyName:g.o.preferences.managePreferences})})]}),(0,a.jsxs)("div",{className:"grid gap-6",children:[F&&(0,a.jsxs)(f.Fc,{className:"bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),(0,a.jsx)(f.TN,{className:"text-green-600 dark:text-green-400",children:(0,a.jsx)(j.T,{keyName:g.o.preferences.changesSaved})})]}),L&&(0,a.jsx)(f.Fc,{variant:"destructive",children:(0,a.jsx)(f.TN,{children:L.message?.includes("user_preferences table doesn't exist")?"Une erreur de configuration du syst\xe8me s'est produite. Veuillez contacter l'administrateur.":L.message||(0,a.jsx)(j.T,{keyName:g.o.preferences.errorSaving})})}),(0,a.jsxs)(x.Zp,{children:[(0,a.jsx)(x.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,a.jsx)(c.A,{className:"h-5 w-5 text-blue-700"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x.ZB,{children:(0,a.jsx)(j.T,{keyName:g.o.preferences.preferredLanguage})}),(0,a.jsx)(x.BT,{className:"mt-1.5",children:(0,a.jsx)(j.T,{keyName:g.o.preferences.languageDescription})})]})]})}),(0,a.jsx)(x.Wu,{children:(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"language",children:(0,a.jsx)(j.T,{keyName:g.o.preferences.preferredLanguage})}),(0,a.jsxs)(p.l6,{value:$,onValueChange:e=>{("fr"===e||"en"===e)&&(console.log("Language selection changed to:",e),B(e))},children:[(0,a.jsx)(p.bq,{id:"language",children:(0,a.jsx)(p.yv,{placeholder:C(g.o.preferences.preferredLanguage)})}),(0,a.jsxs)(p.gC,{children:[(0,a.jsx)(p.eb,{value:"fr",children:(0,a.jsx)(j.T,{keyName:g.o.preferences.french})}),(0,a.jsx)(p.eb,{value:"en",children:(0,a.jsx)(j.T,{keyName:g.o.preferences.english})})]})]})]})})}),(0,a.jsxs)(x.wL,{className:"flex gap-2 justify-end",children:[M&&(0,a.jsx)(u.$,{variant:"outline",onClick:()=>{B(_),W(!1),console.log("Changes canceled, reverting to:",{language:_})},disabled:z,children:(0,a.jsx)(j.T,{keyName:g.o.common.cancel})}),(0,a.jsx)(u.$,{className:"min-w-32",onClick:X,disabled:z||!M,children:z?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,a.jsx)(j.T,{keyName:g.o.preferences.saving})]}):(0,a.jsx)(j.T,{keyName:g.o.preferences.saveChanges})})]})]})]})]})})}},56996:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o,metadata:()=>l});var a=t(57484),r=t(85815),n=t(8152),i=t(34792);let l={title:"Frequently Asked Questions | Sans rendez-vous express",description:"Find answers to your questions about Sans rendez-vous express and our service for quickly finding medical appointments."};function o(){return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(r.Navbar,{}),(0,a.jsx)("main",{className:"flex-grow",children:(0,a.jsx)("div",{className:"bg-white w-full pt-4 pb-16",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6",children:(0,a.jsx)(i.FaqFullPageEN,{})})})}),(0,a.jsx)(n.Footer,{})]})}},57851:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/zaply-landing/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/zaply-landing/layout.tsx","default")},65460:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/mes-rendez-vous/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/mes-rendez-vous/page.tsx","default")},67993:(e,s,t)=>{"use strict";t.d(s,{default:()=>u});var a=t(28392),r=t(4473),n=t(96326),i=t(83808),l=t(98209),o=t(40869),c=t(76990),d=t(63250),m=t(77162);function u({children:e}){let[s,t]=(0,r.useState)(!1),u=(0,n.usePathname)(),x="/"===u||"/landing"===u||u?.startsWith("/auth");return s?(0,a.jsx)(o.O,{children:(0,a.jsx)(d.o,{children:(0,a.jsx)(m.N,{attribute:"class",defaultTheme:x?"light":"system",enableSystem:!x,forcedTheme:x?"light":void 0,children:(0,a.jsx)(l.LanguageProvider,{children:(0,a.jsx)(c.$,{children:(0,a.jsx)(i.r,{children:e})})})})})}):null}},68757:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx","default")},76644:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(28392);t(4473);var r=t(42547),n=t.n(r),i=t(48017),l=t(41788),o=t(21289),c=t(72509),d=t(84006),m=t(65215),u=t(91632),x=t(39790),h=t(56552),p=t(88597),f=t(59356),j=t(97124),g=t(60374),N=t(5935);function v(){let{profile:e,subscription:s,user:t,fetchProfile:r,fetchSubscription:v,isProfileLoading:b,isSubscriptionLoading:y}=(0,g.yk)(),{fullName:w,initials:k}=(0,j.s)(),T=e?`${e.firstName} ${e.lastName}`.trim():w,S=e?`${e.firstName?.charAt(0)||""}${e.lastName?.charAt(0)||""}`.toUpperCase():k,C=s?.plan_type||"individual",P=s?.status==="active";return(0,a.jsx)(u.DashboardLayout,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)("h1",{className:"text-3xl font-bold",children:(0,a.jsx)(p.T,{keyName:f.o.account.yourAccount})})}),(0,a.jsx)(x.Zp,{className:"overflow-hidden",children:(0,a.jsx)(x.aR,{className:"bg-gray-50/50 dark:bg-transparent dark:border-b pb-4 pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"h-12 w-12 min-h-[3rem] min-w-[3rem] rounded-full bg-blue-100 dark:bg-blue-950/70 flex items-center justify-center shrink-0",children:b?(0,a.jsx)(i.A,{className:"h-6 w-6 text-blue-700 dark:text-blue-400 animate-spin"}):S?(0,a.jsx)("span",{className:"text-lg font-semibold text-blue-700 dark:text-blue-400",children:S}):(0,a.jsx)(l.A,{className:"h-6 w-6 text-blue-700 dark:text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x.ZB,{children:b?"Loading...":T}),s&&(0,a.jsx)(x.BT,{className:"mt-1",children:(0,a.jsx)(p.T,{keyName:"family"===s.plan_type?"annual"!==s.billing_period&&(s.billing_period||"year"!==s.interval)?f.o.account.familyPlanMonthly:f.o.account.familyPlanAnnual:"annual"!==s.billing_period&&(s.billing_period||"year"!==s.interval)?f.o.account.individualPlanMonthly:f.o.account.individualPlanAnnual})})]})]}),(0,a.jsx)(N.E,{className:P?"bg-green-100 text-green-800 hover:bg-green-100":"bg-yellow-100 text-yellow-800 hover:bg-yellow-100",children:y?(0,a.jsx)(i.A,{className:"h-3.5 w-3.5 animate-spin"}):(0,a.jsx)(p.T,{keyName:P?f.o.common.active:f.o.common.inactive})})]})})}),(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2",children:[(0,a.jsxs)(x.Zp,{className:"flex flex-col min-h-[180px]",children:[(0,a.jsxs)(x.aR,{className:"pb-2 flex flex-row items-start gap-4 flex-1",children:[(0,a.jsx)("div",{className:"h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0",children:(0,a.jsx)(o.A,{className:"h-5 w-5 text-blue-700"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x.ZB,{children:(0,a.jsx)(p.T,{keyName:f.o.account.manageInformation})}),(0,a.jsx)(x.BT,{className:"mt-1",children:(0,a.jsx)(p.T,{keyName:f.o.account.personalInfoDescription})})]})]}),(0,a.jsx)(x.Wu,{className:"pt-2",children:(0,a.jsx)(h.$,{asChild:!0,variant:"default",className:"w-full",children:(0,a.jsx)(n(),{href:"/compte/profile",children:(0,a.jsx)(p.T,{keyName:f.o.account.modifyProfile})})})})]}),(0,a.jsxs)(x.Zp,{className:"flex flex-col min-h-[180px]",children:[(0,a.jsxs)(x.aR,{className:"pb-2 flex flex-row items-start gap-4 flex-1",children:[(0,a.jsx)("div",{className:"h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0",children:(0,a.jsx)(c.A,{className:"h-5 w-5 text-blue-700"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x.ZB,{children:(0,a.jsx)(p.T,{keyName:f.o.account.modifySubscription})}),(0,a.jsx)(x.BT,{className:"mt-1",children:(0,a.jsx)(p.T,{keyName:P?f.o.account.subscriptionDescription:(s?.status==="cancelled"||s?.cancel_at_period_end,f.o.subscription.returnToPlans)})})]})]}),(0,a.jsx)(x.Wu,{className:"pt-2",children:(0,a.jsx)(h.$,{asChild:!0,variant:"default",className:"w-full",children:(0,a.jsx)(n(),{href:P?"/compte/abonnement":"/pricing",children:(0,a.jsx)(p.T,{keyName:P?f.o.account.modifySubscription:s?.status==="cancelled"||s?.cancel_at_period_end?f.o.account.choosePlan:f.o.account.subscribePlan})})})})]}),(0,a.jsxs)(x.Zp,{className:"flex flex-col min-h-[180px]",children:[(0,a.jsxs)(x.aR,{className:"pb-2 flex flex-row items-start gap-4 flex-1",children:[(0,a.jsx)("div",{className:"h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0",children:(0,a.jsx)(d.A,{className:"h-5 w-5 text-blue-700"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x.ZB,{children:(0,a.jsx)(p.T,{keyName:f.o.account.appearanceLanguage})}),(0,a.jsx)(x.BT,{className:"mt-1",children:(0,a.jsx)(p.T,{keyName:f.o.account.appearanceDescription})})]})]}),(0,a.jsx)(x.Wu,{className:"pt-2",children:(0,a.jsx)(h.$,{asChild:!0,variant:"default",className:"w-full",children:(0,a.jsx)(n(),{href:"/compte/preferences",children:(0,a.jsx)(p.T,{keyName:f.o.account.modifyPreferences})})})})]}),(0,a.jsxs)(x.Zp,{className:"flex flex-col min-h-[180px]",children:[(0,a.jsxs)(x.aR,{className:"pb-2 flex flex-row items-start gap-4 flex-1",children:[(0,a.jsx)("div",{className:"h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0",children:"family"===C?(0,a.jsx)(m.A,{className:"h-5 w-5 text-blue-700"}):(0,a.jsx)(l.A,{className:"h-5 w-5 text-blue-700"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x.ZB,{children:(0,a.jsx)(p.T,{keyName:"family"===C?f.o.account.manageAccountUsers:f.o.account.manageProfile})}),(0,a.jsx)(x.BT,{className:"mt-1",children:(0,a.jsx)(p.T,{keyName:"family"===C?f.o.account.manageUsersDescription:f.o.account.manageProfileDescription})})]})]}),(0,a.jsx)(x.Wu,{className:"pt-2",children:(0,a.jsx)(h.$,{asChild:!0,variant:"default",className:"w-full",children:(0,a.jsx)(n(),{href:"/compte/utilisateurs",children:(0,a.jsx)(p.T,{keyName:"family"===C?f.o.account.manageUsers:f.o.account.manageProfileButton})})})})]})]})]})})}},77286:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var a=t(28392),r=t(4473),n=t(42547),i=t.n(n),l=t(91878),o=t(41788),c=t(40664),d=t(74320),m=t(9723),u=t(44951),x=t(91632),h=t(56552),p=t(39790),f=t(77370),j=t(81076),g=t(46603),N=t(88597),v=t(59356),b=t(97124),y=t(65563),w=t(63250),k=t(60374),T=t(94096);function S(e){return!!e&&""!==e.trim()&&/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/.test(e)}function C(){let{profile:e,user:s,session:t,isProfileLoading:n,updateProfile:C,fetchProfile:P}=(0,k.yk)(),{profile:A,isLoading:q,initials:E,phone:R,invalidateCache:U,isFromCache:_}=(0,b.s)(),{notifyProfileUpdate:z}=(0,w.F)(),D=e?`${e.firstName?.charAt(0)||""}${e.lastName?.charAt(0)||""}`.toUpperCase():E;e?.phone;let[L,I]=(0,r.useState)({firstName:"",lastName:"",email:"",phone:""}),[F,V]=(0,r.useState)({firstName:"",lastName:""}),[$,B]=(0,r.useState)(!1),[M,W]=(0,r.useState)(!1),[X,G]=(0,r.useState)(null),[H,O]=(0,r.useState)(null),[Z,J]=(0,r.useState)(null),[Q,Y]=(0,r.useState)(null),{t:K}=(0,N.B)(),ee=(e,s)=>{"phone"===e&&(s=function(e){let s=e.replace(/\D/g,"");return s.length<=3?s:s.length<=6?`(${s.substring(0,3)}) ${s.substring(3)}`:`(${s.substring(0,3)}) ${s.substring(3,6)}-${s.substring(6,10)}`}(s)),"firstName"===e&&(O(null),""===s.trim()&&O(K("account.firstNameRequired"))),"lastName"===e&&(J(null),""===s.trim()&&J(K("account.lastNameRequired"))),"email"!==e||(Y(null),""===s.trim()?Y(K("account.emailCannotBeEmpty")):S(s)||Y(K("account.invalidEmail"))),I(t=>({...t,[e]:s}))},es=async()=>{if(s?.id){G(null),W(!0);try{if(!L.firstName.trim()){O(K("account.firstNameRequired")),W(!1);return}if(!L.lastName.trim()){J(K("account.lastNameRequired")),W(!1);return}if(!L.email.trim()){Y(K("account.emailCannotBeEmpty")),W(!1);return}if(L.email&&!S(L.email)){Y(K("account.invalidEmail")),W(!1);return}let{valid:e,formattedPhone:a}=function(e){if(!e||""===e.trim())return{valid:!0,formattedPhone:""};let s=e.replace(/\D/g,"");if(s.length<10||s.length>15)return{valid:!1,formattedPhone:e};let t=e;return 10===s.length?t=`(${s.substring(0,3)}) ${s.substring(3,6)}-${s.substring(6)}`:11===s.length&&"1"===s.charAt(0)&&(t=`+1 (${s.substring(1,4)}) ${s.substring(4,7)}-${s.substring(7)}`),{valid:!0,formattedPhone:t}}(L.phone);if(!e){G(K("account.invalidPhone")||"Please enter a valid phone number"),W(!1);return}a!==L.phone&&I(e=>({...e,phone:a}));let r={firstName:L.firstName,lastName:L.lastName,email:L.email,phone:a};if(C)try{if(await C(s.id,r)){V({firstName:L.firstName,lastName:L.lastName}),B(!0),setTimeout(()=>B(!1),3e3),L.email!==s.email&&G(K("account.emailVerificationSent")||"A verification email has been sent to your new email address. Please verify it to complete the update."),z(s.id);return}}catch(e){console.error("Error updating profile with store:",e)}let n=await (0,y.e)(s.id,r,t);n.success?(V({firstName:L.firstName,lastName:L.lastName}),s.email?(U(s.id,s.email,!0),z(s.id),n.updates.includes("email")?(B(!0),setTimeout(()=>B(!1),3e3),L.email!==s.email&&G(K("account.emailVerificationSent")||"A verification email has been sent to your new email address. Please verify it to complete the update.")):(B(!0),setTimeout(()=>B(!1),3e3))):(B(!0),setTimeout(()=>B(!1),3e3))):G(K("common.errorOccurred")||"An error occurred while saving your profile.")}catch(e){console.error("Error saving profile:",e),G(e instanceof Error?e.message:K("common.errorOccurred")||"An error occurred while saving your profile.")}finally{W(!1)}}};return n||q?(0,a.jsx)(x.DashboardLayout,{children:(0,a.jsxs)("div",{className:"space-y-6 mx-auto max-w-6xl",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)("h1",{className:"text-3xl font-bold",children:(0,a.jsx)(N.T,{keyName:v.o.account.modifyProfile})})}),(0,a.jsx)(p.Zp,{children:(0,a.jsx)(p.Wu,{className:"py-10",children:(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("div",{className:"animate-pulse text-center",children:[(0,a.jsx)("div",{className:"h-20 w-20 bg-gray-200 rounded-full mx-auto mb-4"}),(0,a.jsx)("div",{className:"h-6 w-40 bg-gray-200 rounded mx-auto mb-2"}),(0,a.jsx)("div",{className:"h-4 w-60 bg-gray-200 rounded mx-auto"})]})})})})]})}):(0,a.jsx)(x.DashboardLayout,{children:(0,a.jsxs)("div",{className:"space-y-6 mx-auto max-w-6xl",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.$,{asChild:!0,variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsxs)(i(),{href:"/compte",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:(0,a.jsx)(N.T,{keyName:v.o.common.back})})]})}),(0,a.jsx)("h1",{className:"text-3xl font-bold",children:(0,a.jsx)(N.T,{keyName:v.o.account.modifyProfile})})]}),(0,a.jsxs)(p.Zp,{children:[(0,a.jsx)(p.aR,{children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(g.eu,{className:"h-16 w-16 flex-shrink-0",children:[(0,a.jsx)(g.BK,{src:"",alt:"Avatar"}),(0,a.jsx)(g.q5,{className:"bg-blue-100 text-blue-700",children:D})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(p.ZB,{children:[F.firstName," ",F.lastName]}),(0,a.jsx)(p.BT,{className:"mt-2",children:(0,a.jsx)(N.T,{keyName:v.o.account.editPersonalInfo})})]})]})}),(0,a.jsx)(p.Wu,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(j.J,{htmlFor:"firstName",children:(0,a.jsx)(N.T,{keyName:v.o.account.firstName})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)(f.p,{id:"firstName",value:L.firstName,onChange:e=>ee("firstName",e.target.value),className:H?"border-orange-500":""})]}),H&&(0,a.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-orange-500"}),(0,a.jsx)("p",{className:"text-sm text-orange-500",children:H})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(j.J,{htmlFor:"lastName",children:(0,a.jsx)(N.T,{keyName:v.o.account.lastName})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)(f.p,{id:"lastName",value:L.lastName,onChange:e=>ee("lastName",e.target.value),className:Z?"border-orange-500":""})]}),Z&&(0,a.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-orange-500"}),(0,a.jsx)("p",{className:"text-sm text-orange-500",children:Z})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(j.J,{htmlFor:"email",children:(0,a.jsx)(N.T,{keyName:v.o.account.email})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)(f.p,{id:"email",type:"email",value:L.email,onChange:e=>ee("email",e.target.value),className:Q?"border-orange-500":""})]}),Q&&(0,a.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-orange-500"}),(0,a.jsx)("p",{className:"text-sm text-orange-500",children:Q})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(j.J,{htmlFor:"phone",children:(0,a.jsx)(N.T,{keyName:v.o.account.phone})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)(f.p,{id:"phone",type:"tel",value:L.phone,onChange:e=>ee("phone",e.target.value)})]})]})]})]})}),(0,a.jsxs)(p.wL,{className:"flex flex-col gap-4 w-full",children:[X&&(0,a.jsxs)(T.Fc,{className:"w-full border-orange-500 bg-orange-50/50 text-orange-800",children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)(T.TN,{children:X})]}),(0,a.jsx)("div",{className:"flex justify-end w-full",children:(0,a.jsxs)(h.$,{onClick:es,className:"flex items-center gap-1",disabled:M||!!Q||!!H||!!Z||!L.email.trim()||!L.firstName.trim()||!L.lastName.trim(),children:[$?(0,a.jsx)(u.A,{className:"h-4 w-4"}):null,M?(0,a.jsx)(N.T,{keyName:v.o.common.saving||"Saving..."}):$?(0,a.jsx)(N.T,{keyName:v.o.common.saved}):(0,a.jsx)(N.T,{keyName:v.o.common.saveChanges})]})})]})]})]})})}},77771:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GET:()=>l});var a=t(90987),r=t(6721),n=t(31186);let i=new r.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-03-31.basil"});async function l(e){try{console.log("=== STRIPE CUSTOMER DEBUG START ===");let s=await (0,n.U)(),t=e.headers.get("authorization"),r=null;if(t&&t.startsWith("Bearer ")){let e=t.substring(7),{data:a,error:n}=await s.auth.getUser(e);r=a?.user,n&&console.log("Auth error:",n.message)}else{let{data:e,error:t}=await s.auth.getUser();r=e?.user,t&&console.log("Auth error:",t.message)}if(!r||!r.email)return a.NextResponse.json({error:"Authentication required"},{status:401});console.log("User:",{id:r.id,email:r.email});let{data:l,error:o}=await s.from("subscriptions").select("*").eq("user_id",r.id);console.log("Supabase subscriptions:",l),o&&console.log("Supabase subscription error:",o);let c=[],d=[];try{for(let e of(c=(await i.customers.list({email:r.email,limit:10})).data,console.log("Stripe customers found:",c.length),c)){let s=await i.subscriptions.list({customer:e.id,limit:10});d.push(...s.data)}console.log("Stripe subscriptions found:",d.length)}catch(e){console.error("Stripe error:",e)}return console.log("=== STRIPE CUSTOMER DEBUG END ==="),a.NextResponse.json({user:{id:r.id,email:r.email},supabase:{subscriptions:l||[],error:o?.message},stripe:{customers:c.map(e=>({id:e.id,email:e.email,created:e.created,metadata:e.metadata})),subscriptions:d.map(e=>({id:e.id,customer:e.customer,status:e.status,items:e.items.data.map(e=>({price_id:e.price.id,product_id:e.price.product}))}))}})}catch(e){return console.error("Debug error:",e),a.NextResponse.json({error:"Debug failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}},78386:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/compte/profile/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/compte/profile/page.tsx","default")},84028:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(28392),r=t(4473),n=t(42547),i=t.n(n),l=t(91878),o=t(72509),c=t(82493),d=t(91632),m=t(56552),u=t(39790),x=t(88597),h=t(59356),p=t(40869),f=t(98209),j=t(18318),g=t(54386),N=t(70371),v=t(57891);let b={"family-annual":{fr:{title:"Plan familiale",price:"134,40$ / an",description:"Ce que vous obtenez:",features:["R\xe9servations illimit\xe9es pour 5 membres de votre famille","Notifications par courriel pour vos rendez-vous"]},en:{title:"Family plan",price:"$134.40 / year",description:"What you get:",features:["Unlimited bookings for 5 family members","Email notifications for your appointments"]}},"individual-annual":{fr:{title:"Plan individuel",price:"71,40$ / an",description:"Ce que vous obtenez:",features:["R\xe9servations illimit\xe9es pour une personne","Notifications par courriel pour vos rendez-vous"]},en:{title:"Individual plan",price:"$71.40 / year",description:"What you get:",features:["Unlimited bookings for one person","Email notifications for your appointments"]}},"family-monthly":{fr:{title:"Plan familiale",price:"14,95$ / mois",description:"Ce que vous obtenez:",features:["R\xe9servations illimit\xe9es pour 5 personnes","Notifications par courriel pour vos rendez-vous"]},en:{title:"Family plan",price:"$14.95 / month",description:"What you get:",features:["Unlimited bookings for 5 family members","Email notifications for your appointments"]}},"individual-monthly":{fr:{title:"Plan individuel",price:"7,95$ / mois",description:"Ce que vous obtenez:",features:["R\xe9servations illimit\xe9es pour une personne","Notifications par courriel pour vos rendez-vous"]},en:{title:"Individual plan",price:"$7.95 / month",description:"What you get:",features:["Unlimited bookings for one person","Email notifications for your appointments"]}}};function y(){let{user:e}=(0,p.A)(),{language:s}=(0,f.o)(),[t,n]=(0,r.useState)(!1),[y,w]=(0,r.useState)(null);(0,j.S)(e=>e.fetchSubscription);let k=(0,j.f)(),{markStripeNavigation:T}=(0,N.X)(),S=async()=>{n(!0),w(null);try{T();let{data:{session:e}}=await g.N.auth.getSession();console.log("Session status:",{hasSession:!!e,hasToken:!!e?.access_token});let t={"Content-Type":"application/json"};e?.access_token?(t.Authorization=`Bearer ${e.access_token}`,console.log("Added Authorization header")):console.log("No session token available");let a=await fetch(`/api/subscription/portal?locale=${s}`,{method:"GET",credentials:"include",headers:t}),r=await a.json();if(console.log("Portal API response:",{status:a.status,data:r}),!a.ok){if(503===a.status&&"Stripe Customer Portal not configured"===r.error)throw Error("Customer portal is not yet configured. Please contact support for assistance.");if(401===a.status)throw Error("Session expired. Please refresh the page and try again.");throw Error(r.error||r.details||"Failed to create portal session")}window.location.href=r.url}catch(e){console.error("Error creating portal session:",e),w(e instanceof Error?e.message:"Failed to open customer portal")}finally{n(!1)}},C=(e,s)=>`${e}-${s}`,P=(()=>{if(!k.hasSubscription||!k.planType||!k.billingPeriod)return null;let e=b[C(k.planType,k.billingPeriod)];return e?e[s]:null})();return k.hasSubscription||k.planType?k.hasSubscription?(0,a.jsx)(v.o,{children:(0,a.jsx)(d.DashboardLayout,{children:(0,a.jsxs)("div",{className:"space-y-6 mx-auto max-w-6xl",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.$,{asChild:!0,variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsxs)(i(),{href:"/compte",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:(0,a.jsx)(x.T,{keyName:h.o.common.back})})]})}),(0,a.jsx)("h1",{className:"text-3xl font-bold",children:(0,a.jsx)(x.T,{keyName:h.o.subscription.modifySubscription})})]}),(0,a.jsxs)(u.Zp,{children:[(0,a.jsx)(u.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(u.ZB,{children:P?.title||"Subscription Plan"}),(0,a.jsx)(u.BT,{children:P?.price||"Subscription Cost"})]}),(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,a.jsx)(o.A,{className:"h-5 w-5 text-blue-700"})})]})}),(0,a.jsx)(u.Wu,{className:"pb-2",children:(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:P?.description||"Benefits"}),(0,a.jsx)("ul",{className:"mt-2 grid gap-2 text-sm",children:P?.features.map((e,s)=>a.jsxs("li",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"h-1.5 w-1.5 rounded-full bg-blue-500"}),a.jsx("span",{children:e})]},s))||(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"h-1.5 w-1.5 rounded-full bg-blue-500"}),(0,a.jsx)("span",{children:(0,a.jsx)(x.T,{keyName:h.o.subscription.unlimitedAccess})})]})})]})})}),(0,a.jsxs)(u.wL,{className:"flex flex-col space-y-4 pt-4",children:[k.isCancelled&&(0,a.jsxs)("div",{className:"w-full p-4 bg-orange-50 border border-orange-200 rounded-lg dark:bg-orange-900/20 dark:border-orange-800",children:[(0,a.jsx)("p",{className:"text-orange-800 dark:text-orange-200 text-sm font-medium",children:"fr"===s?`Votre abonnement sera annul\xe9 le ${k.currentPeriodEnd?.toLocaleDateString("fr-CA")}`:`Your subscription will be cancelled on ${k.currentPeriodEnd?.toLocaleDateString("en-CA")}`}),(0,a.jsx)("p",{className:"text-orange-600 dark:text-orange-300 text-xs mt-1",children:"fr"===s?"Vous conserverez l'acc\xe8s jusqu'\xe0 cette date.":"You will retain access until this date."})]}),y&&(0,a.jsx)("div",{className:"w-full p-4 bg-red-50 border border-red-200 rounded-lg dark:bg-red-900/20 dark:border-red-800",children:(0,a.jsx)("p",{className:"text-red-800 dark:text-red-200 text-sm",children:y})}),(0,a.jsx)("div",{className:"w-full max-w-md mx-auto",children:(0,a.jsx)(m.$,{className:"w-full",onClick:S,disabled:t,children:t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-b-2 border-white mr-2"}),"fr"===s?"Ouverture...":"Opening..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"fr"===s?"G\xe9rer l'abonnement":"Manage Subscription"]})})}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground text-center max-w-md mx-auto",children:"fr"===s?"Vous serez redirig\xe9 vers le portail client Stripe pour g\xe9rer votre abonnement, modifier votre m\xe9thode de paiement ou annuler votre abonnement.":"You will be redirected to the Stripe customer portal to manage your subscription, update payment methods, or cancel your subscription."})]})]}),(0,a.jsxs)(u.Zp,{children:[(0,a.jsx)(u.aR,{children:(0,a.jsx)(u.ZB,{children:(0,a.jsx)(x.T,{keyName:h.o.subscription.paymentHistory})})}),(0,a.jsx)(u.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"flex justify-between border-b pb-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("p",{className:"font-medium",children:[P?.title," - ","monthly"===k.billingPeriod?"fr"===s?"Mensuel":"Monthly":"fr"===s?"Annuel":"Annual"]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:k.currentPeriodStart?.toLocaleDateString("fr"===s?"fr-CA":"en-CA")})]}),(0,a.jsx)("p",{className:"font-medium",children:P?.price})]})})})]})]})})}):(0,a.jsx)(v.o,{children:(0,a.jsx)(d.DashboardLayout,{children:(0,a.jsxs)("div",{className:"space-y-6 mx-auto max-w-6xl",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.$,{asChild:!0,variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsxs)(i(),{href:"/compte",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:(0,a.jsx)(x.T,{keyName:h.o.common.back})})]})}),(0,a.jsx)("h1",{className:"text-3xl font-bold",children:(0,a.jsx)(x.T,{keyName:h.o.subscription.modifySubscription})})]}),(0,a.jsx)(u.Zp,{children:(0,a.jsx)(u.Wu,{className:"p-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"fr"===s?"Aucun abonnement actif trouv\xe9.":"No active subscription found."}),(0,a.jsx)(m.$,{asChild:!0,children:(0,a.jsx)(i(),{href:"/pricing",children:"fr"===s?"Voir les plans":"View Plans"})})]})})})})]})})}):(0,a.jsx)(v.o,{children:(0,a.jsx)(d.DashboardLayout,{children:(0,a.jsxs)("div",{className:"space-y-6 mx-auto max-w-6xl",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.$,{asChild:!0,variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsxs)(i(),{href:"/compte",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:(0,a.jsx)(x.T,{keyName:h.o.common.back})})]})}),(0,a.jsx)("h1",{className:"text-3xl font-bold",children:(0,a.jsx)(x.T,{keyName:h.o.subscription.modifySubscription})})]}),(0,a.jsx)(u.Zp,{children:(0,a.jsx)(u.Wu,{className:"p-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,a.jsx)(x.T,{keyName:h.o.common.loading})})})})})})]})})})}},88160:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GET:()=>r});var a=t(90987);async function r(e){try{console.log("=== COOKIE DEBUG START ===");let s=e.cookies.getAll();console.log("All request cookies:",s);let t=s.filter(e=>e.name.includes("supabase")||e.name.includes("auth")||e.name.includes("sb-"));console.log("Supabase auth cookies:",t);let r=e.headers.get("cookie");console.log("Cookie header:",r);let n=e.headers.get("origin"),i=e.headers.get("referer"),l=e.headers.get("user-agent");return console.log("Request headers:",{origin:n,referer:i,userAgent:l?.substring(0,50)+"..."}),console.log("=== COOKIE DEBUG END ==="),a.NextResponse.json({success:!0,totalCookies:s.length,supabaseCookies:t.length,cookieNames:s.map(e=>e.name),supabaseCookieNames:t.map(e=>e.name),hasCookieHeader:!!r,cookieHeaderLength:r?.length||0,origin:n,referer:i})}catch(e){return console.error("Cookie debug error:",e),a.NextResponse.json({error:"Debug failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}},88464:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var a=t(57484),r=t(85815),n=t(8152);let i={title:"Terms and Conditions | Sans rendez-vous express",description:"Terms and Conditions for Sans rendez-vous express."};function l(){return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(r.Navbar,{}),(0,a.jsx)("main",{className:"flex-grow bg-white",children:(0,a.jsxs)("div",{className:"max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-6 py-12",children:[(0,a.jsx)("div",{className:"mb-4 text-gray-500",children:(0,a.jsx)("p",{children:"Last updated on March 24, 2025"})}),(0,a.jsx)("h1",{className:"text-4xl sm:text-5xl xl:text-[60px] leading-tight font-bold text-[#212244] mb-16 max-w-[1000px]",children:"Terms and Conditions"}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"General"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:'Before using the service of Sans rendez-vous express Inc. (hereinafter referred to as "SRVXP"), please take the time to carefully read the following Terms and Conditions (hereinafter referred to as the "Terms"). These govern your use of our website www.sansrendezvousexpress.ca (hereinafter the "Platform") and the service offered by SRVXP, a company legally registered in Quebec, Canada.'}),(0,a.jsx)("p",{children:'By accessing and using the Platform, you, the end user or the organization you represent (hereinafter referred to as "you", "the User" or "the Users"), agree to be bound by these Terms as well as our Privacy Policy.'}),(0,a.jsx)("p",{children:'SRVXP provides you, through its Platform, the opportunity to make a one-time appointment at a walk-in clinic in Quebec (hereinafter referred to as the "Service").'}),(0,a.jsx)("p",{children:"This contract legally binds you, the User, and SRVXP. Both parties may enforce these Terms and take necessary actions to ensure their compliance."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Acceptance of the Terms"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"By accessing and using the Platform, you agree to be bound by these Terms as well as our Privacy Policy. If you do not accept these Terms, please do not use our Platform."}),(0,a.jsx)("p",{children:'By accessing and using the Platform, you, the end user or the organization you represent (hereinafter referred to as "you", "the User" or "the Users"), agree to be bound by these Terms as well as our Privacy Policy. If you do not accept these Terms, or their periodically updated versions, please do not use our Platform.'})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Platform Availability"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Our Platform is generally accessible at all times. However, there may be temporary service interruptions due to maintenance, updates, or technical issues. SRVXP is not responsible for these interruptions and attempts to schedule updates during periods of low traffic to minimize the impact on Users."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Platform Compatibility and Performance"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"We cannot guarantee that our platform will perform optimally on all devices and configurations. Additionally, like any online service, it may experience slowdowns and malfunctions related to the use of the internet and electronic communications."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Platform Use"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"When you use our Service, we may ask for information related to your health insurance card. This information is crucial for us to provide you with the requested Service, in accordance with current laws and regulations as well as our Terms. You have the right to refuse to provide us with this information. However, please note that in such cases, we might not be able to offer you the Service in question."}),(0,a.jsx)("p",{children:"It is prohibited to use the Platform in a way that could:"}),(0,a.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,a.jsx)("li",{children:"Cause technical damage to its infrastructure"}),(0,a.jsx)("li",{children:"Interfere with its proper functioning"}),(0,a.jsx)("li",{children:"Degrade the quality of the Service for other Users by overloading it"})]})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Content Modification"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"SRVXP reserves the right to modify, add, or remove any content on the Platform, except for the personal data of Users. We are not responsible for errors, typos, or bugs that may occur."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"User Content Ownership"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Each User remains the owner of the content published from their account. SRVXP will only delete this content at the User's request or if it violates our Terms. We may also delete certain personal data to enhance security, in accordance with our Privacy Policy."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Intellectual Property"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"All elements of the Platform (texts, graphics, logos, videos, etc.) are the exclusive property of SRVXP and are protected by intellectual property laws. You are not authorized to reproduce or use these elements without our prior written consent."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"External Links"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"Our Platform may contain links to third-party websites. We have no control over the content of these sites and disclaim any responsibility for their content or any damages that may result from their use."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Limitation of Liability"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"SRVXP strives to provide accurate and up-to-date information on its Platform, but cannot guarantee its completeness or accuracy. Except in cases where the Quebec Consumer Protection Act applies, SRVXP cannot be held liable for any direct or indirect damages resulting from the use of the Platform."}),(0,a.jsx)("p",{children:"In the event of non-compliance with your obligations as a User, you agree to ward off SRVXP against any claims or legal actions that may arise."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Measures in Case of Non-Compliance"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"If a User violates these Terms, or if we have reasonable grounds to believe that their actions threaten our Platform, our other Users, or third parties, we reserve the right to limit or block the User in question's access to certain features or sections of our website, temporarily or permanently, depending on the severity of the breach."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Priority to French"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"In the event of a conflict in the interpretation of these Terms compared with the French version, the French version shall prevail."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Updating Terms"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"These Terms may be periodically updated to remain compliant with laws and to reflect changes in the management of our site as well as the expectations of our site's Users. We advise our Users to regularly review these Terms to stay informed of any updates. If necessary, we will notify Users of changes via email or we will post a notice on our site. If you continue to use the Platform, this will indicate that you accept the new Terms."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Interpretation Rules"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsx)("p",{children:"The titles of the various sections of the Terms are provided solely for reference and clarity. They should not be considered when interpreting or applying the provisions contained in these Terms."}),(0,a.jsx)("p",{children:"In the Terms, words written in the singular also include their plural form when the context requires it, and vice versa. Similarly, words indicating a gender, male or female, encompass the other gender when necessary for a correct understanding of the text."})]})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Divisibility"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"If, at any time, a clause in the Terms is declared non-compliant or invalid according to the applicable laws, this clause will then be considered null and void and will be excluded from the Terms. The other clauses will remain in force and will not be affected by this invalidity, and the rest of the Terms will continue to be considered valid."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Governing Law"}),(0,a.jsx)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:(0,a.jsx)("p",{children:"These Terms are governed by the laws applicable in the province of Quebec, Canada. Any dispute will be subject to the exclusive jurisdiction of the courts of the city of Montreal."})})]}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-[#212244] mb-6",children:"Contacting Us"}),(0,a.jsxs)("div",{className:"text-gray-700 space-y-6 max-w-[1000px]",children:[(0,a.jsxs)("p",{children:["For any questions regarding these Terms, you can contact us by email at ",(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"})," or by mail at the following address:"]}),(0,a.jsxs)("p",{children:["PO BOX 99900 VG 550 446",(0,a.jsx)("br",{}),"RPO VILLENEUVE",(0,a.jsx)("br",{}),"MONTREAL QC",(0,a.jsx)("br",{}),"H2T 0A6"]})]})]})]})}),(0,a.jsx)(n.Footer,{})]})}},89906:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,metadata:()=>c});var a=t(57484),r=t(47022),n=t.n(r),i=t(28080),l=t.n(i);t(50339);var o=t(68757);let c={title:"Rendez-vous M\xe9dicaux",description:"G\xe9rez vos rendez-vous m\xe9dicaux en ligne"};function d({children:e}){return(0,a.jsx)("html",{lang:"fr",className:`${n().variable} ${l().variable}`,suppressHydrationWarning:!0,children:(0,a.jsx)("body",{className:"antialiased",children:(0,a.jsx)(o.default,{children:e})})})}},91430:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(28392),r=t(42547),n=t.n(r),i=t(41788),l=t(61915),o=t(91742),c=t(65215),d=t(21204),m=t(39790),u=t(56552),x=t(5935),h=t(88597),p=t(59356),f=t(4473),j=t(97124),g=t(60374);function N(){let{profile:e,subscription:s,pendingAppointments:t,completedAppointments:r,cancelledAppointments:d,fetchAllAppointmentHistory:f,getAppointmentSummary:N,isLoading:v}=(0,g.yk)(),{firstName:b,fullName:y}=(0,j.s)(),{}=(0,h.B)(),w=N(),{t:k,language:T}=(0,h.B)(),S=s?.plan_type||"individual",C=s?.status==="active";return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:(0,a.jsx)(h.T,{keyName:p.o.home.greeting,params:{firstName:b}})}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,a.jsx)(h.T,{keyName:p.o.home.welcome})})]}),(0,a.jsxs)(m.Zp,{children:[(0,a.jsxs)(m.aR,{className:"py-4 flex flex-row items-center justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsx)(m.ZB,{children:(0,a.jsx)(h.T,{keyName:p.o.account.yourAccount})})}),(0,a.jsx)(x.E,{className:C?"bg-green-100 text-green-800 hover:bg-green-100":"bg-yellow-100 text-yellow-800 hover:bg-yellow-100",children:(0,a.jsx)(h.T,{keyName:C?p.o.common.active:p.o.common.inactive})})]}),(0,a.jsx)(m.Wu,{className:"pb-6",children:(0,a.jsx)("div",{className:"flex flex-col md:flex-row items-start md:items-center gap-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,a.jsx)(i.A,{className:"h-5 w-5 text-blue-700"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:e?.firstName?`${e.firstName} ${e.lastName}`:y}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:(0,a.jsx)(h.T,{keyName:"family"===S?p.o.account.familyPlan:p.o.account.individualPlan})})]})]})})})]}),(0,a.jsxs)("div",{className:"grid gap-4 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsxs)(m.Zp,{className:"hover:shadow-md transition-shadow flex flex-col",children:[(0,a.jsx)(m.aR,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-5",children:(0,a.jsx)(l.A,{className:"h-5 w-5 text-blue-600"})}),(0,a.jsx)(m.ZB,{className:"text-base break-words hyphens-auto",children:(0,a.jsx)(h.T,{keyName:p.o.home.findAppointmentTitle})})]})}),(0,a.jsxs)(m.Wu,{className:"p-4 pt-0 flex-1 flex flex-col",children:[(0,a.jsx)(m.BT,{className:"mb-3 flex-1",children:(0,a.jsx)(h.T,{keyName:p.o.home.findAppointmentDesc})}),(0,a.jsx)(u.$,{asChild:!0,className:"w-full mt-auto",children:(0,a.jsx)(n(),{href:"/trouver-rendez-vous",children:(0,a.jsx)(h.T,{keyName:p.o.common.search})})})]})]}),(0,a.jsxs)(m.Zp,{className:"hover:shadow-md transition-shadow flex flex-col",children:[(0,a.jsx)(m.aR,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-5",children:(0,a.jsx)(o.A,{className:"h-5 w-5 text-blue-600"})}),(0,a.jsx)(m.ZB,{className:"text-base break-words hyphens-auto",children:(0,a.jsx)(h.T,{keyName:p.o.nav.appointments})})]})}),(0,a.jsxs)(m.Wu,{className:"p-4 pt-0 flex-1 flex flex-col",children:[(0,a.jsx)(m.BT,{className:"mb-3 flex-1",children:(0,a.jsx)(h.T,{keyName:p.o.home.manageAppointmentsDesc})}),(0,a.jsx)(u.$,{asChild:!0,className:"w-full mt-auto",variant:"default",children:(0,a.jsx)(n(),{href:"/mes-rendez-vous",children:(0,a.jsx)(h.T,{keyName:p.o.home.viewRequests})})})]})]}),(0,a.jsxs)(m.Zp,{className:"hover:shadow-md transition-shadow flex flex-col",children:[(0,a.jsx)(m.aR,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-5",children:(0,a.jsx)(c.A,{className:"h-5 w-5 text-blue-600"})}),(0,a.jsx)(m.ZB,{className:"text-base break-words hyphens-auto",children:"family"===S?(0,a.jsx)(h.T,{keyName:p.o.account.manageUsers}):(0,a.jsx)(h.T,{keyName:p.o.home.manageProfileTitle})})]})}),(0,a.jsxs)(m.Wu,{className:"p-4 pt-0 flex-1 flex flex-col",children:[(0,a.jsx)(m.BT,{className:"mb-3 flex-1",children:"family"===S?(0,a.jsx)(h.T,{keyName:p.o.home.manageUsersDesc}):(0,a.jsx)(h.T,{keyName:p.o.home.manageProfileDesc})}),(0,a.jsx)(u.$,{asChild:!0,className:"w-full mt-auto",variant:"default",children:(0,a.jsx)(n(),{href:"/compte/utilisateurs",children:"family"===S?(0,a.jsx)(h.T,{keyName:p.o.account.manageUsers}):(0,a.jsx)(h.T,{keyName:p.o.home.manageProfileButton})})})]})]})]}),(0,a.jsxs)(m.Zp,{children:[(0,a.jsx)(m.aR,{className:"py-4",children:(0,a.jsx)(m.ZB,{children:(0,a.jsx)(h.T,{keyName:p.o.appointments.summary})})}),(0,a.jsxs)(m.Wu,{className:"pb-6",children:[v?(0,a.jsx)("div",{className:"flex justify-center py-4",children:(0,a.jsx)("div",{className:"h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-primary"})}):(0,a.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:(0,a.jsx)(h.T,{keyName:p.o.appointments.inProgress})}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:w.counts.in_progress})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-3 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:(0,a.jsx)(h.T,{keyName:p.o.appointments.completed})}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:w.counts.completed})]}),(0,a.jsxs)("div",{className:"bg-red-50 p-3 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"fr"===T?"Annul\xe9s":"Cancelled"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:w.counts.cancelled})]})]}),(0,a.jsx)("div",{className:"mt-4 flex justify-end",children:(0,a.jsx)(u.$,{asChild:!0,variant:"outline",size:"sm",children:(0,a.jsx)(n(),{href:"/mes-rendez-vous",children:k(p.o.appointments.viewAll)||("fr"===T?"Voir tous les rendez-vous":"View all appointments")})})})]})]})]})}function v(){let{initializeApp:e,isLoading:s}=(0,g.yk)();return(0,a.jsxs)(d.S,{children:[s&&(0,a.jsx)("div",{className:"fixed inset-0 bg-background/80 z-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"})}),(0,a.jsx)(f.Suspense,{fallback:(0,a.jsx)("div",{className:"flex h-full w-full items-center justify-center",children:(0,a.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"})}),children:(0,a.jsx)(N,{})})]})}},91954:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(28392),r=t(4473),n=t(42547),i=t.n(n),l=t(54386),o=t(56552),c=t(77370),d=t(81076),m=t(88597),u=t(94096);function x(){let{t:e}=(0,m.B)(),[s,t]=(0,r.useState)(""),[n,x]=(0,r.useState)(null),[h,p]=(0,r.useState)(!1),[f,j]=(0,r.useState)(!1),g=async t=>{t.preventDefault(),j(!0),x(null),p(!1);try{let{error:e}=await l.N.auth.resetPasswordForEmail(s,{redirectTo:`${window.location.origin}/auth/reset-password`});if(e)throw e;p(!0)}catch(s){console.error("Error resetting password:",s),x(s.message||e("auth.errors.generic"))}finally{j(!1)}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2 text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:e("auth.resetPassword")}),(0,a.jsx)("p",{className:"text-gray-500",children:e("auth.enterEmailForReset")})]}),n&&(0,a.jsx)(u.Fc,{variant:"destructive",children:(0,a.jsx)(u.TN,{children:n})}),h?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(u.Fc,{children:(0,a.jsx)(u.TN,{children:e("auth.resetEmailSent")})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(i(),{href:"/auth/sign-in",children:(0,a.jsx)(o.$,{variant:"outline",children:e("auth.backToSignIn")})})})]}):(0,a.jsxs)("form",{onSubmit:g,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"email",children:e("auth.email")}),(0,a.jsx)(c.p,{id:"email",type:"email",placeholder:"<EMAIL>",required:!0,value:s,onChange:e=>t(e.target.value)})]}),(0,a.jsx)(o.$,{type:"submit",className:"w-full py-6",disabled:f,children:f?e("common.loading"):e("auth.sendResetEmail")}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(i(),{href:"/auth/sign-in",className:"text-sm text-primary hover:underline",children:e("auth.backToSignIn")})})]})]})}},92481:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/account/subscription/success/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/account/subscription/success/page.tsx","default")},92869:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(60544).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/compte/preferences/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/SRVXP/src/app/compte/preferences/page.tsx","default")},93256:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var a=t(28392),r=t(4473),n=t(40664),i=t(41435),l=t(70773),o=t(86439),c=t(89085),d=t(91632),m=t(39790),u=t(56552),x=t(5935),h=t(57408),p=t(4436),f=t(88125),j=t(71216),g=t(62736),N=t(88597),v=t(59356),b=t(60374);function y({type:e,pagination:s}){let{goToPage:t,fetchNextPage:r,fetchPreviousPage:n}=(0,b.yk)(),{currentPage:i,totalPages:l,hasMore:o}=s;return l<=1?null:(0,a.jsx)(g.dK,{className:"mt-4",children:(0,a.jsxs)(g.Iu,{children:[(0,a.jsx)(g.cU,{children:(0,a.jsx)(g.Eb,{onClick:()=>n(e),className:i<=1?"pointer-events-none opacity-50":"cursor-pointer"})}),(()=>{let e=[];if(l<=5)for(let s=1;s<=l;s++)e.push(s);else{e.push(1);let s=Math.max(2,i-1),t=Math.min(l-1,i+1);i<=2?t=Math.min(l-1,4):i>=l-1&&(s=Math.max(2,l-3)),s>2&&e.push("ellipsis");for(let a=s;a<=t;a++)e.push(a);t<l-1&&e.push("ellipsis"),l>1&&e.push(l)}return e})().map((s,r)=>"ellipsis"===s?(0,a.jsx)(g.cU,{children:(0,a.jsx)(g.M_,{})},`ellipsis-${r}`):(0,a.jsx)(g.cU,{children:(0,a.jsx)(g.n$,{isActive:s===i,onClick:()=>t(e,s),children:s})},s)),(0,a.jsx)(g.cU,{children:(0,a.jsx)(g.WA,{onClick:()=>r(e),className:o?"cursor-pointer":"pointer-events-none opacity-50"})})]})})}function w(){let[e,s]=(0,r.useState)("tous"),[t,g]=(0,r.useState)(null),{t:w,language:k}=(0,N.B)(),{appointmentRequests:T,completedAppointments:S,cancelledAppointments:C,requestsPagination:P,completedPagination:A,cancelledPagination:q,fetchAppointmentRequests:E,fetchCompletedAppointments:R,fetchCancelledAppointments:U,fetchAllAppointmentHistory:_,cancelAppointmentRequest:z,isLoading:D,error:L}=(0,b.yk)(),I=()=>"tous"===e?[...T,...S.map(e=>e.appointment_request),...C.map(e=>e.appointment_request)]:"en-cours"===e?T:"completes"===e?S.map(e=>e.appointment_request):"annules"===e?C.map(e=>e.appointment_request):[],F=async e=>{try{let s=await z(e);s.success?(_(),g(null)):console.error("Failed to cancel appointment:",s.error)}catch(e){console.error("Error cancelling appointment:",e)}};return(0,a.jsx)(d.DashboardLayout,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:(0,a.jsx)(N.T,{keyName:v.o.appointments.title})}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1.5",children:(0,a.jsx)(N.T,{keyName:v.o.appointments.description})})]}),(0,a.jsxs)(m.Zp,{children:[(0,a.jsxs)(m.aR,{className:"pb-3 flex flex-col gap-4",children:[(0,a.jsx)(m.ZB,{children:(0,a.jsx)(N.T,{keyName:v.o.appointments.requestsTitle})}),(0,a.jsx)(p.tU,{defaultValue:"tous",value:e,onValueChange:e=>{s(e),"en-cours"===e?E({page:1}):"completes"===e?R({page:1}):"annules"===e?U({page:1}):_()},className:"w-full",children:(0,a.jsxs)(p.j7,{className:"w-full sm:w-auto",children:[(0,a.jsx)(p.Xi,{value:"tous",className:"flex-1 sm:flex-initial",children:(0,a.jsx)(N.T,{keyName:v.o.appointments.all})}),(0,a.jsx)(p.Xi,{value:"en-cours",className:"flex-1 sm:flex-initial",children:(0,a.jsx)(N.T,{keyName:v.o.appointments.inProgress})}),(0,a.jsx)(p.Xi,{value:"completes",className:"flex-1 sm:flex-initial",children:(0,a.jsx)(N.T,{keyName:v.o.appointments.completed})}),(0,a.jsx)(p.Xi,{value:"annules",className:"flex-1 sm:flex-initial",children:"fr"===k?"Annul\xe9s":"Cancelled"})]})})]}),(0,a.jsx)(m.Wu,{children:D?(0,a.jsx)("div",{className:"flex justify-center py-8",children:(0,a.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"})}):L?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,a.jsx)(n.A,{className:"h-12 w-12 text-destructive opacity-80 mb-3"}),(0,a.jsx)("h3",{className:"font-medium text-lg",children:(0,a.jsx)(N.T,{keyName:v.o.common.error})}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1",children:L.message||(0,a.jsx)(N.T,{keyName:v.o.common.errorMessage})}),(0,a.jsx)(u.$,{onClick:()=>_(),className:"mt-4",children:(0,a.jsx)(N.T,{keyName:v.o.common.retry})})]}):0===I().length?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,a.jsx)(i.A,{className:"h-12 w-12 text-muted-foreground opacity-20 mb-3"}),(0,a.jsx)("h3",{className:"font-medium text-lg",children:(0,a.jsx)(N.T,{keyName:v.o.appointments.noRequests})}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1",children:"en-cours"===e?(0,a.jsx)(N.T,{keyName:v.o.appointments.noRequestsInProgress}):"completes"===e?(0,a.jsx)(N.T,{keyName:v.o.appointments.noRequestsCompleted}):"annules"===e?w(v.o.appointments.noRequestsCancelled)||("fr"===k?"Vous n'avez aucune demande de rendez-vous annul\xe9e":"You have no cancelled appointment requests"):(0,a.jsx)(N.T,{keyName:v.o.appointments.noRequests})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"space-y-4",children:I().map(e=>(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between p-4 rounded-lg border bg-card text-card-foreground",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h3",{className:"font-medium",children:e.family_member?e.family_member.first_name+" "+e.family_member.last_name:"Patient"}),(0,a.jsx)(x.E,{variant:"completed"===e.status?"outline":"cancelled"===e.status?"destructive":"secondary",className:"ml-2",children:"completed"===e.status?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.A,{className:"h-3.5 w-3.5 mr-1 text-green-600"}),(0,a.jsx)("span",{children:(0,a.jsx)(N.T,{keyName:v.o.appointments.done})})]}):"cancelled"===e.status?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"h-3.5 w-3.5 mr-1"}),(0,a.jsx)("span",{children:(0,a.jsx)(N.T,{keyName:v.o.appointments.cancelled})})]}):(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-3.5 w-3.5 mr-1"}),(0,a.jsx)("span",{children:(0,a.jsx)(N.T,{keyName:v.o.appointments.pending})})]})})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6 text-xs text-muted-foreground",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)(N.T,{keyName:v.o.appointments.requestType}),": ",e.request_type]}),e.request_details?.postalCode&&(0,a.jsxs)("span",{children:[(0,a.jsx)(N.T,{keyName:v.o.appointments.postalCode}),": ",e.request_details.postalCode]}),(0,a.jsxs)("span",{children:[(0,a.jsx)(N.T,{keyName:v.o.appointments.sentOn}),": ",(0,f.GP)(new Date(e.created_at),"d MMMM yyyy '\xe0' HH:mm",{locale:j.fr})]})]})]}),"pending"===e.status&&(0,a.jsx)("div",{className:"mt-3 sm:mt-0",children:(0,a.jsxs)(h.lG,{open:t===e.id,onOpenChange:s=>g(s?e.id:null),children:[(0,a.jsx)(h.zM,{asChild:!0,children:(0,a.jsxs)(u.$,{variant:"destructive",size:"sm",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)(N.T,{keyName:v.o.appointments.cancelAppointment})]})}),(0,a.jsxs)(h.Cf,{className:"w-[90%] mx-auto sm:max-w-md p-6 pt-10 rounded-md",children:[(0,a.jsxs)(h.c7,{className:"space-y-3",children:[(0,a.jsx)(h.L3,{className:"text-xl font-bold text-center",children:(0,a.jsx)(N.T,{keyName:v.o.appointments.cancelConfirmation})}),(0,a.jsx)(h.rr,{className:"text-base text-left",children:(0,a.jsx)(N.T,{keyName:v.o.appointments.cancelConfirmationText})})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 w-full pt-6",children:[(0,a.jsx)(u.$,{variant:"outline",className:"w-full order-1 sm:order-none",onClick:()=>g(null),children:(0,a.jsx)(N.T,{keyName:v.o.appointments.noContinue})}),(0,a.jsx)(u.$,{variant:"destructive",className:"w-full order-0 sm:order-none",onClick:()=>F(e.id),children:(0,a.jsx)(N.T,{keyName:v.o.appointments.yesCancel})})]})]})]})})]},e.id))}),"tous"!==e&&(0,a.jsx)(y,{type:"en-cours"===e?"requests":"completes"===e?"completed":"annules"===e?"cancelled":"requests",pagination:"en-cours"===e?P:"completes"===e?A:"annules"===e?q:P})]})})]})]})})}},95303:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GET:()=>l});var a=t(90987),r=t(6721),n=t(31186);let i=new r.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-03-31.basil"});async function l(e){try{console.log("=== PORTAL SESSION DEBUG START ===");let{searchParams:s}=new URL(e.url),t=s.get("locale");console.log("Requested locale:",t);let r=await (0,n.U)(),l=e.headers.get("authorization");console.log("Request headers:",{hasAuthHeader:!!l,authHeaderType:l?l.substring(0,20)+"...":"none",cookies:e.headers.get("cookie")?"present":"none"});let o=null,c=null;if(l&&l.startsWith("Bearer ")){let e=l.substring(7);console.log("Attempting auth with Bearer token...");let{data:s,error:t}=await r.auth.getUser(e);o=s?.user,c=t,console.log("Bearer auth result:",{hasUser:!!o,error:t?.message})}else{console.log("Attempting auth with cookies...");let{data:e,error:s}=await r.auth.getUser();o=e?.user,c=s,console.log("Cookie auth result:",{hasUser:!!o,error:s?.message})}if(c||!o)return console.log("Authentication failed:",{authError:c?.message,hasAuthHeader:!!l}),a.NextResponse.json({error:"Authentication required",details:"No valid session found"},{status:401});console.log("User authenticated:",{userId:o.id,email:o.email});let d=null,{data:m,error:u}=await r.from("subscriptions").select("customer_id, status, plan_type, billing_period").eq("user_id",o.id);if(console.log("Subscription query result:",{hasSubscriptions:!!m,count:m?.length||0,error:u?.message}),m&&m.length>0&&(d=(m.find(e=>"active"===e.status)||m[0]).customer_id,console.log("Found customer_id from subscription:",d)),!d&&o.email){console.log("No subscription found in database, searching Stripe customers by email:",o.email);try{let e=await i.customers.list({email:o.email,limit:1});e.data.length>0?(d=e.data[0].id,console.log("Found Stripe customer by email:",d)):console.log("No Stripe customer found for email:",o.email)}catch(e){console.error("Error searching Stripe customers:",e)}}if(!d&&o.email){console.log("Creating new Stripe customer for:",o.email);try{d=(await i.customers.create({email:o.email,metadata:{supabase_user_id:o.id}})).id,console.log("Created new Stripe customer:",d)}catch(e){return console.error("Error creating Stripe customer:",e),a.NextResponse.json({error:"Failed to create customer",details:"Could not create or find Stripe customer"},{status:500})}}if(!d)return a.NextResponse.json({error:"No customer found",details:"Unable to find or create Stripe customer"},{status:404});console.log("Creating portal session for customer:",d);try{let e={customer:d,return_url:"http://localhost:3000/compte/abonnement"};t&&("fr"===t||"en"===t)&&(e.locale=t,console.log("Setting Stripe portal locale to:",t));let s=await i.billingPortal.sessions.create(e);return console.log("Portal session created successfully"),a.NextResponse.json({url:s.url})}catch(e){if(console.error("Stripe portal session error:",e),"StripeInvalidRequestError"===e.type&&e.message?.includes("No configuration provided"))return a.NextResponse.json({error:"Stripe Customer Portal not configured",details:"The Stripe Customer Portal needs to be configured in your Stripe dashboard. Please contact support.",helpUrl:"https://dashboard.stripe.com/test/settings/billing/portal"},{status:503});return a.NextResponse.json({error:"Stripe error",details:e.message||"Unknown Stripe error"},{status:500})}}catch(e){return console.error("Error creating portal session:",e),console.log("=== PORTAL SESSION DEBUG END ==="),a.NextResponse.json({error:"Failed to create portal session",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}}};