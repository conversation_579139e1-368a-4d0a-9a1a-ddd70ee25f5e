{"/_not-found/page": "app/_not-found/page.js", "/api/debug/auth-test/route": "app/api/debug/auth-test/route.js", "/api/appointment-requests/route": "app/api/appointment-requests/route.js", "/api/debug/auth/route": "app/api/debug/auth/route.js", "/api/debug/session-check/route": "app/api/debug/session-check/route.js", "/api/debug/stripe-customer/route": "app/api/debug/stripe-customer/route.js", "/api/debug/cookies/route": "app/api/debug/cookies/route.js", "/api/webhooks/stripe/route": "app/api/webhooks/stripe/route.js", "/api/subscription/portal/route": "app/api/subscription/portal/route.js", "/auth/callback/route": "app/auth/callback/route.js", "/account/subscription/success/page": "app/account/subscription/success/page.js", "/aide/page": "app/aide/page.js", "/compte/page": "app/compte/page.js", "/compte/abonnement/page": "app/compte/abonnement/page.js", "/compte/preferences/page": "app/compte/preferences/page.js", "/mes-rendez-vous/page": "app/mes-rendez-vous/page.js", "/pricing/page": "app/pricing/page.js", "/page": "app/page.js", "/trouver-rendez-vous/page": "app/trouver-rendez-vous/page.js", "/compte/utilisateurs/page": "app/compte/utilisateurs/page.js", "/dashboard/page": "app/dashboard/page.js", "/compte/profile/page": "app/compte/profile/page.js", "/auth/forgot-password/page": "app/auth/forgot-password/page.js", "/auth/sign-up/page": "app/auth/sign-up/page.js", "/foire-aux-questions/page": "app/foire-aux-questions/page.js", "/foire-aux-questions-EN/page": "app/foire-aux-questions-EN/page.js", "/politique-de-confidentialite-EN/page": "app/politique-de-confidentialite-EN/page.js", "/zaply-landing/page": "app/zaply-landing/page.js", "/auth/sign-in/page": "app/auth/sign-in/page.js", "/conditions-generales-de-vente/page": "app/conditions-generales-de-vente/page.js", "/conditions-utilisation/page": "app/conditions-utilisation/page.js", "/conditions-utilisation-EN/page": "app/conditions-utilisation-EN/page.js", "/politique-de-confidentialite/page": "app/politique-de-confidentialite/page.js", "/conditions-generales-de-vente-EN/page": "app/conditions-generales-de-vente-EN/page.js"}