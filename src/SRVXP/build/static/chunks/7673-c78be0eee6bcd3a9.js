"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7673],{5230:(e,t,a)=>{a.d(t,{yk:()=>P});var n=a(54503),r=a(32310),o=a(23384),i=a(39665),s=a(98846),l=a(5232);let c={profile:null,isLoading:!1,isSaving:!1,error:null,saveError:null,saveSuccess:!1,lastFetched:null},p=(0,r.L)((0,n.v)()((0,i.Zr)((e,t)=>({...c,fetchProfile:async a=>{try{e({isLoading:!0,error:null});let{profile:n,lastFetched:r}=t(),o=Date.now();if(n&&r&&o-r<3e5)return e({isLoading:!1}),n;let{data:i,error:l}=await s.N.auth.getUser();if(l)throw l;let c=null;try{let{data:e,error:t}=await s.N.from("users").select("*").eq("id",a).single();t||(c=e)}catch(e){console.warn("Error getting public user data:",e)}let p=i.user.user_metadata||{},u=p.first_name||"",g=p.last_name||"";u||(u=(null==c?void 0:c.first_name)||""),g||(g=(null==c?void 0:c.last_name)||"");let d="".concat(u," ").concat(g).trim()||"User",m="".concat(u.charAt(0)).concat(g.charAt(0)).toUpperCase()||"??",f={firstName:u,lastName:g,email:i.user.email||"",phone:p.phone||"",avatar:null,initials:m,fullName:d};return e({profile:f,isLoading:!1,lastFetched:Date.now()}),f}catch(t){return e({isLoading:!1,error:t instanceof Error?t:Error("Unknown error fetching profile")}),null}},updateProfile:async(a,n)=>{try{var r;e({isSaving:!0,saveError:null,saveSuccess:!1});let i=o.kH.getState().session,c={},p=!1;if(void 0!==n.firstName&&(c.first_name=n.firstName,p=!0),void 0!==n.lastName&&(c.last_name=n.lastName,p=!0),void 0!==n.phone&&(c.phone=n.phone,p=!0),p){let{error:e}=await s.N.auth.updateUser({data:c});if(e)throw Error("Error updating user metadata: ".concat(e.message))}if(void 0!==n.email&&(null==i?void 0:null===(r=i.user)||void 0===r?void 0:r.email)!==n.email){let{error:e}=await s.N.auth.updateUser({email:n.email});if(e)throw Error("Error updating email: ".concat(e.message))}try{let{data:e,error:t}=await s.N.from("users").select("id").eq("id",a).maybeSingle();if(t&&!t.message.includes("does not exist")&&console.warn("Error checking users table: ".concat(t.message)),e){let e={};if(void 0!==n.firstName&&(e.first_name=n.firstName),void 0!==n.lastName&&(e.last_name=n.lastName),void 0!==n.phone&&(e.phone=n.phone),Object.keys(e).length>0){e.updated_at=new Date().toISOString();let{error:t}=await s.N.from("users").update(e).eq("id",a);t&&console.warn("Error updating users table: ".concat(t.message))}}}catch(e){console.warn("Error updating users table:",e)}let u=t().profile;if(u){let t={...u,firstName:void 0!==n.firstName?n.firstName:u.firstName,lastName:void 0!==n.lastName?n.lastName:u.lastName,email:void 0!==n.email?n.email:u.email,phone:void 0!==n.phone?n.phone:u.phone,fullName:"".concat(n.firstName||u.firstName," ").concat(n.lastName||u.lastName).trim(),initials:"".concat((n.firstName||u.firstName).charAt(0)).concat((n.lastName||u.lastName).charAt(0)).toUpperCase()};e({profile:t})}return(0,l.gm)("user-profile-store"),e({isSaving:!1,saveSuccess:!0}),setTimeout(()=>{e({saveSuccess:!1})},3e3),!0}catch(t){return e({isSaving:!1,saveError:t instanceof Error?t:Error("Unknown error updating profile"),saveSuccess:!1}),!1}},setProfile:t=>e({profile:t}),setIsLoading:t=>e({isLoading:t}),setIsSaving:t=>e({isSaving:t}),setError:t=>e({error:t}),setSaveError:t=>e({saveError:t}),setSaveSuccess:t=>e({saveSuccess:t}),clearErrors:()=>e({error:null,saveError:null}),reset:()=>e(c)}),(0,l.LX)("user-profile-store",{storage:(0,i.KU)(()=>(0,l.oV)()),partialize:e=>({profile:e.profile,lastFetched:e.lastFetched}),version:1,onRehydrateStorage:()=>e=>{if((null==e?void 0:e.profile)&&e.lastFetched&&Date.now()-e.lastFetched>3e5){var t;let e=null===(t=o.kH.getState().user)||void 0===t?void 0:t.id;e&&setTimeout(()=>{p.getState().fetchProfile(e)},0)}}})))),u={theme:"light",language:"fr"},g={preferences:u,isLoading:!1,isSaving:!1,error:null,saveError:null,saveSuccess:!1,lastFetched:null},d=(0,r.L)((0,n.v)()((0,i.Zr)((e,t)=>({...g,fetchPreferences:async a=>{try{e({isLoading:!0,error:null});let{preferences:n,lastFetched:r}=t(),o=Date.now();if(r&&o-r<18e5)return e({isLoading:!1}),n;let{data:i,error:l}=await s.N.from("users").select("language, theme").eq("id",a).maybeSingle();if(l)throw l;let c={language:(null==i?void 0:i.language)||u.language,theme:(null==i?void 0:i.theme)||u.theme};return e({preferences:c,isLoading:!1,lastFetched:Date.now()}),c}catch(t){return e({isLoading:!1,error:t instanceof Error?t:Error("Unknown error fetching preferences")}),u}},updatePreferences:async(a,n)=>{try{e({isSaving:!0,saveError:null,saveSuccess:!1});let{error:r}=await s.N.from("users").update({...n.language&&{language:n.language},...n.theme&&{theme:n.theme},updated_at:new Date().toISOString()}).eq("id",a);if(r)throw r;let o=t().preferences;return e({preferences:{...o,...n},isSaving:!1,saveSuccess:!0}),n.language&&(localStorage.setItem("language",n.language),"undefined"!=typeof document&&(document.documentElement.lang=n.language)),setTimeout(()=>{e({saveSuccess:!1})},3e3),!0}catch(t){return e({isSaving:!1,saveError:t instanceof Error?t:Error("Unknown error updating preferences"),saveSuccess:!1}),!1}},setTheme:a=>{var n;let r=null===(n=o.kH.getState().user)||void 0===n?void 0:n.id;r?t().updatePreferences(r,{theme:a}):e(e=>({preferences:{...e.preferences,theme:a}}))},setLanguage:a=>{var n;let r=null===(n=o.kH.getState().user)||void 0===n?void 0:n.id;r?t().updatePreferences(r,{language:a}):(e(e=>({preferences:{...e.preferences,language:a}})),localStorage.setItem("language",a),"undefined"!=typeof document&&(document.documentElement.lang=a))},setPreferences:t=>e({preferences:t}),setIsLoading:t=>e({isLoading:t}),setIsSaving:t=>e({isSaving:t}),setError:t=>e({error:t}),setSaveError:t=>e({saveError:t}),setSaveSuccess:t=>e({saveSuccess:t}),clearErrors:()=>e({error:null,saveError:null}),reset:()=>e(g)}),(0,l.LX)("preferences-store",{storage:(0,i.KU)(()=>(0,l.oV)()),partialize:e=>({preferences:e.preferences,lastFetched:e.lastFetched}),version:1}))));var m=a(93525);let f={language:"fr",translations:{},isLoading:!0,error:null,lastFetched:null},h=(0,r.L)((0,n.v)()((0,i.Zr)((e,t)=>({...f,setLanguage:async a=>{try{var n;e({isLoading:!0,error:null});let r=null===(n=o.kH.getState().user)||void 0===n?void 0:n.id;r?await d.getState().updatePreferences(r,{language:a}):d.getState().setLanguage(a),e({language:a}),localStorage.setItem("language",a),"undefined"!=typeof document&&(document.documentElement.lang=a),await t().loadTranslations()}catch(t){e({isLoading:!1,error:t instanceof Error?t:Error("Failed to set language to ".concat(a))}),console.error("Error setting language to ".concat(a,":"),t)}},translate:e=>t().translations[e]||e,loadTranslations:async()=>{try{let a=t().language;e({isLoading:!0,error:null});let n=await (0,m.Zi)(a);e({translations:n,isLoading:!1,lastFetched:Date.now()})}catch(t){e({isLoading:!1,error:t instanceof Error?t:Error("Failed to load translations")}),console.error("Error loading translations:",t)}},setTranslations:t=>e({translations:t}),setIsLoading:t=>e({isLoading:t}),setError:t=>e({error:t}),clearError:()=>e({error:null}),reset:()=>e(f)}),(0,l.LX)("language-store",{storage:(0,i.KU)(()=>(0,l.oV)()),partialize:e=>({language:e.language,lastFetched:e.lastFetched}),version:1,onRehydrateStorage:()=>e=>{if(e){let t=d.getState().preferences.language;t!==e.language&&h.setState({language:t}),setTimeout(()=>{h.getState().loadTranslations()},0)}}})))),S={currentPage:1,totalPages:0,totalCount:0,hasMore:!1,pageSize:10},v={appointmentRequests:[],completedAppointments:[],cancelledAppointments:[],requestsPagination:{...S},completedPagination:{...S},cancelledPagination:{...S},isLoading:!1,error:null,lastFetched:null},A=(0,r.L)((0,n.v)()((0,i.Zr)((e,t)=>({...v,fetchAppointmentRequests:async a=>{try{var n;e(e=>({isLoading:!0,error:null}));let r=t(),i=(null==a?void 0:a.page)||r.requestsPagination.currentPage,l=(null==a?void 0:a.pageSize)||r.requestsPagination.pageSize,c=null==a?void 0:a.status,p=(i-1)*l,u=null===(n=o.kH.getState().user)||void 0===n?void 0:n.id;if(!u)throw Error("User not authenticated");let g=s.N.from("appointment_requests").select("\n                *,\n                family_members(first_name, last_name, health_card, birth_date)\n              ",{count:"exact"}).eq("user_id",u).order("created_at",{ascending:!1}).range(p,p+l-1);c&&(g=g.eq("status",c)),(null==a?void 0:a.startDate)&&(g=g.gte("created_at",a.startDate)),(null==a?void 0:a.endDate)&&(g=g.lte("created_at",a.endDate));let{data:d,error:m,count:f}=await g;if(m)throw m;let h=f||0,S=Math.ceil(h/l),v=i<S;return e(e=>({appointmentRequests:d,requestsPagination:{currentPage:i,totalPages:S,totalCount:h,hasMore:v,pageSize:l},isLoading:!1,lastFetched:Date.now()})),d}catch(t){return e({isLoading:!1,error:t instanceof Error?t:Error("Unknown error fetching appointment requests")}),console.error("Error fetching appointment requests:",t),[]}},fetchCompletedAppointments:async a=>{try{var n;e(e=>({isLoading:!0,error:null}));let r=t(),i=(null==a?void 0:a.page)||r.completedPagination.currentPage,l=(null==a?void 0:a.pageSize)||r.completedPagination.pageSize,c=(i-1)*l,p=null===(n=o.kH.getState().user)||void 0===n?void 0:n.id;if(!p)throw Error("User not authenticated");let u=s.N.from("completed_appointments").select("\n                *,\n                appointment_request:appointment_request_id(*)\n              ",{count:"exact"}).order("completed_at",{ascending:!1}).range(c,c+l-1);u=u.filter("appointment_request.user_id","eq",p),(null==a?void 0:a.startDate)&&(u=u.gte("completed_at",a.startDate)),(null==a?void 0:a.endDate)&&(u=u.lte("completed_at",a.endDate));let{data:g,error:d,count:m}=await u;if(d)throw d;let f=m||0,h=Math.ceil(f/l),S=i<h;return e(e=>({completedAppointments:g,completedPagination:{currentPage:i,totalPages:h,totalCount:f,hasMore:S,pageSize:l},isLoading:!1,lastFetched:Date.now()})),g}catch(t){return e({isLoading:!1,error:t instanceof Error?t:Error("Unknown error fetching completed appointments")}),console.error("Error fetching completed appointments:",t),[]}},fetchCancelledAppointments:async a=>{try{var n;e(e=>({isLoading:!0,error:null}));let r=t(),i=(null==a?void 0:a.page)||r.cancelledPagination.currentPage,l=(null==a?void 0:a.pageSize)||r.cancelledPagination.pageSize,c=(i-1)*l,p=null===(n=o.kH.getState().user)||void 0===n?void 0:n.id;if(!p)throw Error("User not authenticated");let u=s.N.from("cancelled_appointments").select("\n                *,\n                appointment_request:appointment_request_id(*)\n              ",{count:"exact"}).order("cancelled_at",{ascending:!1}).range(c,c+l-1);u=u.filter("appointment_request.user_id","eq",p),(null==a?void 0:a.startDate)&&(u=u.gte("cancelled_at",a.startDate)),(null==a?void 0:a.endDate)&&(u=u.lte("cancelled_at",a.endDate));let{data:g,error:d,count:m}=await u;if(d)throw d;let f=m||0,h=Math.ceil(f/l),S=i<h;return e(e=>({cancelledAppointments:g,cancelledPagination:{currentPage:i,totalPages:h,totalCount:f,hasMore:S,pageSize:l},isLoading:!1,lastFetched:Date.now()})),g}catch(t){return e({isLoading:!1,error:t instanceof Error?t:Error("Unknown error fetching cancelled appointments")}),console.error("Error fetching cancelled appointments:",t),[]}},fetchAllAppointmentHistory:async()=>{try{e({isLoading:!0,error:null});let{lastFetched:a}=t(),n=Date.now();if(a&&n-a<3e5){e({isLoading:!1});return}let r={page:1,pageSize:10};await Promise.all([t().fetchAppointmentRequests(r),t().fetchCompletedAppointments(r),t().fetchCancelledAppointments(r)]),e({lastFetched:Date.now()})}catch(t){e({isLoading:!1,error:t instanceof Error?t:Error("Unknown error fetching appointment history")}),console.error("Error fetching appointment history:",t)}},createAppointmentRequest:async t=>{try{e({isLoading:!0,error:null});let a=await fetch("/api/appointment-requests",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),n=await a.json();if(!a.ok)throw Error(n.error||"Failed to create appointment request");let r=n.data;return e(e=>({appointmentRequests:[r,...e.appointmentRequests],isLoading:!1})),{success:!0,data:r}}catch(a){let t=a instanceof Error?a:Error("Unknown error creating appointment request");return e({isLoading:!1,error:t}),console.error("Error creating appointment request:",a),{success:!1,error:t.message}}},cancelAppointmentRequest:async(a,n)=>{try{e({isLoading:!0,error:null});let{error:r}=await s.N.from("appointment_requests").update({status:"cancelled"}).eq("id",a);if(r)throw r;let{error:o}=await s.N.from("cancelled_appointments").insert({appointment_request_id:a,cancellation_reason:n||"Cancelled by user",cancelled_by:"user"});return o&&console.error("Error creating cancelled appointment record:",o),e(e=>({appointmentRequests:e.appointmentRequests.map(e=>e.id===a?{...e,status:"cancelled"}:e),isLoading:!1})),t().fetchCancelledAppointments(),{success:!0}}catch(a){let t=a instanceof Error?a:Error("Unknown error cancelling appointment request");return e({isLoading:!1,error:t}),console.error("Error cancelling appointment request:",a),{success:!1,error:t.message}}},fetchNextPage:async e=>{let a,n;let r=t();switch(e){case"requests":a=r.requestsPagination.currentPage,n=r.requestsPagination.hasMore;break;case"completed":a=r.completedPagination.currentPage,n=r.completedPagination.hasMore;break;case"cancelled":a=r.cancelledPagination.currentPage,n=r.cancelledPagination.hasMore}if(n){let n=a+1;await t().goToPage(e,n)}},fetchPreviousPage:async e=>{let a;let n=t();switch(e){case"requests":a=n.requestsPagination.currentPage;break;case"completed":a=n.completedPagination.currentPage;break;case"cancelled":a=n.cancelledPagination.currentPage}if(a>1){let n=a-1;await t().goToPage(e,n)}},goToPage:async(e,a)=>{let n={page:a};switch(e){case"requests":await t().fetchAppointmentRequests(n);break;case"completed":await t().fetchCompletedAppointments(n);break;case"cancelled":await t().fetchCancelledAppointments(n)}},setPageSize:(a,n)=>{switch(a){case"requests":e(e=>({requestsPagination:{...e.requestsPagination,pageSize:n,currentPage:1}})),t().fetchAppointmentRequests({pageSize:n,page:1});break;case"completed":e(e=>({completedPagination:{...e.completedPagination,pageSize:n,currentPage:1}})),t().fetchCompletedAppointments({pageSize:n,page:1});break;case"cancelled":e(e=>({cancelledPagination:{...e.cancelledPagination,pageSize:n,currentPage:1}})),t().fetchCancelledAppointments({pageSize:n,page:1})}},setAppointmentRequests:(t,a)=>e(e=>({appointmentRequests:t,requestsPagination:a?{...e.requestsPagination,...a}:e.requestsPagination})),setCompletedAppointments:(t,a)=>e(e=>({completedAppointments:t,completedPagination:a?{...e.completedPagination,...a}:e.completedPagination})),setCancelledAppointments:(t,a)=>e(e=>({cancelledAppointments:t,cancelledPagination:a?{...e.cancelledPagination,...a}:e.cancelledPagination})),setIsLoading:t=>e({isLoading:t}),setError:t=>e({error:t}),clearError:()=>e({error:null}),invalidateCache:()=>{e({lastFetched:null}),t().fetchAllAppointmentHistory()},reset:()=>e(v)}),(0,l.LX)("appointment-history-store",{storage:(0,i.KU)(()=>(0,l.oV)()),partialize:e=>({appointmentRequests:e.appointmentRequests,completedAppointments:e.completedAppointments,cancelledAppointments:e.cancelledAppointments,requestsPagination:e.requestsPagination,completedPagination:e.completedPagination,cancelledPagination:e.cancelledPagination,lastFetched:e.lastFetched}),version:3,onRehydrateStorage:()=>e=>{if(e){let{lastFetched:t}=e,a=Date.now();(!t||a-t>3e5)&&setTimeout(()=>{var e;(null===(e=o.kH.getState().user)||void 0===e?void 0:e.id)&&A.getState().fetchAllAppointmentHistory()},0)}}}))));var q=a(28278);let y=(0,r.L)((0,n.v)()((e,t)=>({signIn:async(e,t)=>o.kH.getState().signIn(e,t),signInWithGoogle:async()=>o.kH.getState().signInWithGoogle(),signOut:async e=>o.kH.getState().signOut(e),fetchProfile:async e=>p.getState().fetchProfile(e),updateProfile:async(e,t)=>p.getState().updateProfile(e,t),fetchPreferences:async e=>d.getState().fetchPreferences(e),updatePreferences:async(e,t)=>d.getState().updatePreferences(e,t),setTheme:e=>{d.getState().setTheme(e)},setLanguage:e=>{d.getState().setLanguage(e),h.getState().setLanguage(e)},translate:e=>h.getState().translate(e),loadTranslations:async()=>h.getState().loadTranslations(),fetchAppointmentRequests:async e=>A.getState().fetchAppointmentRequests(e),fetchCompletedAppointments:async e=>A.getState().fetchCompletedAppointments(e),fetchCancelledAppointments:async e=>A.getState().fetchCancelledAppointments(e),fetchAllAppointmentHistory:async()=>A.getState().fetchAllAppointmentHistory(),createAppointmentRequest:async e=>A.getState().createAppointmentRequest(e),cancelAppointmentRequest:async(e,t)=>A.getState().cancelAppointmentRequest(e,t),fetchNextPage:async e=>A.getState().fetchNextPage(e),fetchPreviousPage:async e=>A.getState().fetchPreviousPage(e),goToPage:async(e,t)=>A.getState().goToPage(e,t),setPageSize:(e,t)=>{A.getState().setPageSize(e,t)},filterByDateRange:(e,t)=>A.getState().fetchAllAppointmentHistory({startDate:e,endDate:t}),filterByStatus:e=>{let t=A.getState();return"completed"===e?t.completedAppointments.map(e=>e.appointment_request).filter(Boolean):"cancelled"===e?t.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean):t.appointmentRequests.filter(t=>t.status===e)},filterByType:e=>{let t=A.getState();return[...t.appointmentRequests,...t.completedAppointments.map(e=>e.appointment_request).filter(Boolean),...t.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)].filter(t=>t.request_type===e)},getAppointmentCountsByStatus:()=>{let e=A.getState();return{pending:e.appointmentRequests.filter(e=>"pending"===e.status).length,in_progress:e.appointmentRequests.filter(e=>"in_progress"===e.status).length,completed:e.completedAppointments.length,cancelled:e.cancelledAppointments.length,total:e.appointmentRequests.length+e.completedAppointments.length+e.cancelledAppointments.length}},getAppointmentCountsByMonth:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date().getFullYear(),t=A.getState(),a=Array(12).fill(0).map(()=>({pending:0,in_progress:0,completed:0,cancelled:0,total:0}));return t.appointmentRequests.forEach(t=>{let n=new Date(t.created_at);if(n.getFullYear()===e){let e=n.getMonth();"pending"===t.status?a[e].pending++:"in_progress"===t.status&&a[e].in_progress++,a[e].total++}}),t.completedAppointments.forEach(t=>{if(t.completed_at){let n=new Date(t.completed_at);if(n.getFullYear()===e){let e=n.getMonth();a[e].completed++,a[e].total++}}}),t.cancelledAppointments.forEach(t=>{if(t.cancelled_at){let n=new Date(t.cancelled_at);if(n.getFullYear()===e){let e=n.getMonth();a[e].cancelled++,a[e].total++}}}),a},getAppointmentSummary:()=>{let e=A.getState(),t=e.appointmentRequests.filter(e=>"pending"===e.status).length,a=e.appointmentRequests.filter(e=>"in_progress"===e.status).length,n=e.completedAppointments.length,r=e.cancelledAppointments.length,o=t+a+n+r,i=[...e.appointmentRequests,...e.completedAppointments.map(e=>e.appointment_request).filter(Boolean),...e.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)].sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime());return{counts:{pending:t,in_progress:a,completed:n,cancelled:r,total:o},completionRate:o>0?n/o*100:0,cancellationRate:o>0?r/o*100:0,mostRecentAppointment:i.length>0?i[0]:null,totalAppointments:o}},groupByStatus:()=>{let e=A.getState();return{pending:e.appointmentRequests.filter(e=>"pending"===e.status),in_progress:e.appointmentRequests.filter(e=>"in_progress"===e.status),completed:e.completedAppointments.map(e=>e.appointment_request).filter(Boolean),cancelled:e.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)}},groupByMonth:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date().getFullYear(),t=A.getState(),a=[...t.appointmentRequests,...t.completedAppointments.map(e=>e.appointment_request).filter(Boolean),...t.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)],n={};return a.forEach(t=>{let a=new Date(t.created_at);if(a.getFullYear()===e){let e=a.getMonth();n[e]||(n[e]=[]),n[e].push(t)}}),n},groupByType:()=>{let e=A.getState(),t=[...e.appointmentRequests,...e.completedAppointments.map(e=>e.appointment_request).filter(Boolean),...e.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)],a={};return t.forEach(e=>{let t=e.request_type||"unknown";a[t]||(a[t]=[]),a[t].push(e)}),a},sortByDate:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return[...e].sort((e,a)=>{let n=new Date(e.created_at).getTime(),r=new Date(a.created_at).getTime();return t?n-r:r-n})},getAppointmentsInDateRange:(e,t)=>{let a=A.getState();return[...a.appointmentRequests,...a.completedAppointments.map(e=>e.appointment_request).filter(Boolean),...a.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)].filter(a=>{let n=new Date(a.created_at);return n>=e&&n<=t})},getAppointmentsForDay:e=>{let t=A.getState(),a=[...t.appointmentRequests,...t.completedAppointments.map(e=>e.appointment_request).filter(Boolean),...t.cancelledAppointments.map(e=>e.appointment_request).filter(Boolean)],n=e.getFullYear(),r=e.getMonth(),o=e.getDate();return a.filter(e=>{let t=new Date(e.created_at);return t.getFullYear()===n&&t.getMonth()===r&&t.getDate()===o})},getUpcomingAppointments:()=>{let e=A.getState(),t=new Date;return e.appointmentRequests.filter(e=>{var a,n;return("pending"===e.status||"in_progress"===e.status)&&null!==(a=e.request_details)&&void 0!==a&&!!a.date&&null!==(n=e.request_details)&&void 0!==n&&!!n.time&&new Date("".concat(e.request_details.date,"T").concat(e.request_details.time))>t}).sort((e,t)=>new Date("".concat(e.request_details.date,"T").concat(e.request_details.time)).getTime()-new Date("".concat(t.request_details.date,"T").concat(t.request_details.time)).getTime())},fetchSubscription:async e=>q.S.getState().fetchSubscription(e),updateSubscription:async e=>q.S.getState().updateSubscription(e),cancelSubscription:async e=>q.S.getState().cancelSubscription(e),initializeApp:async()=>{try{await o.kH.getState().refresh();let e=o.kH.getState().user;if(e){let t=e.id;await Promise.all([p.getState().fetchProfile(t),d.getState().fetchPreferences(t),q.S.getState().fetchSubscription(t),A.getState().fetchAllAppointmentHistory()])}await h.getState().loadTranslations();return}catch(e){throw console.error("Error initializing app:",e),e}}}))),P=()=>{let e=(0,o.kH)(e=>e.user),t=(0,o.kH)(e=>e.status),a=y(e=>e.signIn),n=y(e=>e.signInWithGoogle),r=y(e=>e.signOut),i=p(e=>e.profile),s=y(e=>e.updateProfile),l=y(e=>e.fetchProfile),c=d(e=>e.preferences),u=d(e=>e.theme),g=y(e=>e.updatePreferences),m=y(e=>e.fetchPreferences),f=y(e=>e.setTheme),S=h(e=>e.language),v=h(e=>e.translations),P=y(e=>e.setLanguage),_=y(e=>e.translate),w=(0,q.S)(e=>e.subscription),E=y(e=>e.fetchSubscription),L=y(e=>e.updateSubscription),N=y(e=>e.cancelSubscription),D=A(e=>e.appointmentRequests),R=A(e=>e.completedAppointments),k=A(e=>e.cancelledAppointments),C=D.filter(e=>"pending"===e.status),F=D.filter(e=>"in_progress"===e.status),T=A(e=>e.requestsPagination),U=A(e=>e.completedPagination),B=A(e=>e.cancelledPagination),b=y(e=>e.fetchAppointmentRequests),z=y(e=>e.fetchCompletedAppointments),I=y(e=>e.fetchCancelledAppointments),H=y(e=>e.fetchAllAppointmentHistory),M=y(e=>e.createAppointmentRequest),O=y(e=>e.cancelAppointmentRequest),x=y(e=>e.fetchNextPage),Y=y(e=>e.fetchPreviousPage),V=y(e=>e.goToPage),Z=y(e=>e.setPageSize),J=y(e=>e.filterByDateRange),K=y(e=>e.filterByStatus),X=y(e=>e.filterByType),j=y(e=>e.getAppointmentCountsByStatus),G=y(e=>e.getAppointmentCountsByMonth),W=y(e=>e.getAppointmentSummary),Q=y(e=>e.groupByStatus),$=y(e=>e.groupByMonth),ee=y(e=>e.groupByType),et=y(e=>e.sortByDate),ea=y(e=>e.getAppointmentsInDateRange),en=y(e=>e.getAppointmentsForDay),er=y(e=>e.getUpcomingAppointments),eo=(0,o.kH)(e=>"loading"===e.status),ei=p(e=>e.isLoading),es=d(e=>e.isLoading),el=h(e=>e.isLoading),ec=A(e=>e.isLoading),ep=(0,q.S)(e=>e.isLoading);return{user:e,status:t,isAuthenticated:"authenticated"===t,signIn:a,signInWithGoogle:n,signOut:r,profile:i,updateProfile:s,fetchProfile:l,preferences:c,theme:u,updatePreferences:g,fetchPreferences:m,setTheme:f,language:S,translations:v,setLanguage:P,translate:_,subscription:w,fetchSubscription:E,updateSubscription:L,cancelSubscription:N,appointmentRequests:D,completedAppointments:R,cancelledAppointments:k,pendingAppointments:C,inProgressAppointments:F,requestsPagination:T,completedPagination:U,cancelledPagination:B,fetchAppointmentRequests:b,fetchCompletedAppointments:z,fetchCancelledAppointments:I,fetchAllAppointmentHistory:H,createAppointmentRequest:M,cancelAppointmentRequest:O,fetchNextPage:x,fetchPreviousPage:Y,goToPage:V,setPageSize:Z,filterByDateRange:J,filterByStatus:K,filterByType:X,getAppointmentCountsByStatus:j,getAppointmentCountsByMonth:G,getAppointmentSummary:W,groupByStatus:Q,groupByMonth:$,groupByType:ee,sortByDate:et,getAppointmentsInDateRange:ea,getAppointmentsForDay:en,getUpcomingAppointments:er,isLoading:eo||ei||es||el||ec||ep,isAuthLoading:eo,isProfileLoading:ei,isPreferencesLoading:es,isLanguageLoading:el,isAppointmentHistoryLoading:ec,isSubscriptionLoading:ep,initializeApp:y(e=>e.initializeApp)}}},95818:(e,t,a)=>{a.d(t,{s:()=>l});var n=a(6159),r=a(35071),o=a(36965),i=a(88473);let s={};function l(){let e=async e=>{try{let t=await (0,o.V)(e),a=t.firstName||"",n=t.lastName||"",r={firstName:a,lastName:n,avatar:null,initials:"".concat(a.charAt(0)).concat(n.charAt(0)).toUpperCase()||"??",role:"user",fullName:a||n?"".concat(a," ").concat(n).trim():"User",phone:t.phone||""};return g(r),S&&(localStorage.setItem(S,JSON.stringify(r)),s[S]=r),r}catch(e){throw console.error("Error loading user profile:",e),e}},{user:t,status:a}=(0,r.A)(),{notifyProfileUpdate:l,lastUpdatedUserId:c,lastUpdateTimestamp:p}=(0,i.F)(),[u,g]=(0,n.useState)(null),[d,m]=(0,n.useState)(!0),[f,h]=(0,n.useState)(null),S=(0,n.useMemo)(()=>(null==t?void 0:t.email)?"user_profile_".concat(t.email):(null==t?void 0:t.id)?"user_profile_id_".concat(t.id):null,[null==t?void 0:t.email,null==t?void 0:t.id]);(0,n.useEffect)(()=>{(async function(){if("loading"!==a){if("unauthenticated"===a||!t){g(null),m(!1);return}m(!0),h(null);try{if(S&&S in s){g(s[S]),m(!1);return}let a=S?localStorage.getItem(S):null;if(a){let e=JSON.parse(a);S&&(s[S]=e),g(e),m(!1);return}if(null==t?void 0:t.id)await e(t.id);else{let e=((null==t?void 0:t.email)||"").split("@")[0].split("."),a=e[0]?e[0].charAt(0).toUpperCase()+e[0].slice(1):"User",n=e[1]?e[1].charAt(0).toUpperCase()+e[1].slice(1):"",r={firstName:a,lastName:n,avatar:null,initials:"".concat(a.charAt(0)).concat(n.charAt(0)).toUpperCase(),role:"user",fullName:"".concat(a," ").concat(n),phone:""};g(r),S&&(localStorage.setItem(S,JSON.stringify(r)),s[S]=r)}}catch(e){console.error("Error loading user profile:",e),h(e instanceof Error?e:Error("Failed to load user profile"))}finally{m(!1)}}})()},[t,a,S]),(0,n.useEffect)(()=>{c&&(null==t?void 0:t.id)===c&&t.id&&(S&&(localStorage.removeItem(S),S in s&&delete s[S]),e(t.id))},[p,c,null==t?void 0:t.id,S]);let v=(null==u?void 0:u.firstName)||"",A=(null==u?void 0:u.lastName)||"",q=(null==t?void 0:t.email)||"",y=v,P=A;if(!v&&!A&&q){let e=q.split("@")[0].split(".");y=e[0]?e[0].charAt(0).toUpperCase()+e[0].slice(1):"",P=e[1]?e[1].charAt(0).toUpperCase()+e[1].slice(1):""}let _=(null==u?void 0:u.fullName)||(v||A?"".concat(v," ").concat(A).trim():y||P?"".concat(y," ").concat(P).trim():"User"),w=(null==u?void 0:u.initials)||(v||A?"".concat(v.charAt(0)).concat(A.charAt(0)).toUpperCase():y||P?"".concat(y.charAt(0)).concat(P.charAt(0)).toUpperCase():"??"),E=(null==u?void 0:u.phone)||"";return{profile:u,isLoading:d,error:f,initials:w,fullName:_,firstName:v||y,lastName:A||P,phone:E,invalidateCache:function(t,n){let r=!(arguments.length>2)||void 0===arguments[2]||arguments[2],o="user_profile_".concat(n),i="user_profile_id_".concat(t);localStorage.removeItem(o),localStorage.removeItem(i),o in s&&delete s[o],i in s&&delete s[i],r&&t&&"authenticated"===a&&(e(t),l&&l(t))},reloadProfile:t=>e(t),isFromCache:!!S&&(!!s[S]||!!localStorage.getItem(S))}}}}]);