(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6160],{7186:(e,t,r)=>{Promise.resolve().then(r.bind(r,92427))},30150:(e,t,r)=>{"use strict";r.d(t,{w:()=>c});var n=r(6159),i=r(56309),o=r(35071),s=r(49550);function c(){let{theme:e,setTheme:t}=(0,i.D)(),{user:r,status:c}=(0,o.A)();return(0,n.useEffect)(()=>{(async()=>{if("authenticated"===c&&r)try{let e=await (0,s.o)(r.id);if(null==e?void 0:e.theme){console.log("Setting theme from user preferences: ".concat(e.theme)),t(e.theme);return}console.log("No theme preferences found, setting default theme: light"),t("light")}catch(e){console.error("Error initializing theme:",e)}})()},[r,c,t]),{theme:e}}},35476:()=>{},62605:(e,t,r)=>{"use strict";r.d(t,{hv:()=>n.hv});var n=r(29998)}},e=>{var t=t=>e(e.s=t);e.O(0,[5749,8096,4522,8613,5179,5738,7957,8278,7673,7358],()=>t(7186)),_N_E=e.O()}]);