(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9218],{30150:(e,t,r)=>{"use strict";r.d(t,{w:()=>a});var n=r(6159),o=r(56309),s=r(35071),l=r(49550);function a(){let{theme:e,setTheme:t}=(0,o.D)(),{user:r,status:a}=(0,s.A)();return(0,n.useEffect)(()=>{(async()=>{if("authenticated"===a&&r)try{let e=await (0,l.o)(r.id);if(null==e?void 0:e.theme){console.log("Setting theme from user preferences: ".concat(e.theme)),t(e.theme);return}console.log("No theme preferences found, setting default theme: light"),t("light")}catch(e){console.error("Error initializing theme:",e)}})()},[r,a,t]),{theme:e}}},35476:()=>{},55320:(e,t,r)=>{Promise.resolve().then(r.bind(r,21103))},71049:(e,t,r)=>{"use strict";r.d(t,{H:()=>a});var n=r(6159),o=r(56309),s=r(35071),l=r(49550);function a(){let{user:e,status:t}=(0,s.A)(),{setTheme:r}=(0,o.D)(),[a,c]=(0,n.useState)(null),[u,i]=(0,n.useState)(!0),[f,h]=(0,n.useState)(!1),[g,d]=(0,n.useState)(null),[p,m]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{(async function(){if("loading"!==t){if("unauthenticated"===t||!e){c(null),i(!1);return}i(!0);try{let t=await (0,l.o)(e.id);t&&(c(t),t.theme&&(r(t.theme),console.log("Applied theme from user preferences: ".concat(t.theme))))}catch(e){console.error("Error loading preferences:",e)}finally{i(!1)}}})()},[e,t]),{preferences:a,isLoading:u,isSaving:f,saveError:g,saveSuccess:p,updatePreferences:async t=>{if(e){console.log("Updating user preferences:",t),h(!0),d(null),m(!1);try{c(t),localStorage.setItem("language",t.language),console.log("Local language preferences updated");let r=await (0,l.b)(e.id,t);if(!r.success)throw r.error||Error("Failed to save preferences");console.log("Preferences saved successfully to Supabase"),m(!0),setTimeout(()=>{m(!1)},3e3)}catch(e){console.error("Error saving preferences:",e),d(e),m(!1)}finally{h(!1)}}}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[5749,8096,5179,5738,7957,8278,7673,7358],()=>t(55320)),_N_E=e.O()}]);