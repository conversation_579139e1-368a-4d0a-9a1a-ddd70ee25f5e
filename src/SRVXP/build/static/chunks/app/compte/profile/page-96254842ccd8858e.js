(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7097],{9737:(e,t,n)=>{Promise.resolve().then(n.bind(n,48448))},30150:(e,t,n)=>{"use strict";n.d(t,{w:()=>c});var r=n(6159),i=n(56309),o=n(35071),s=n(49550);function c(){let{theme:e,setTheme:t}=(0,i.D)(),{user:n,status:c}=(0,o.A)();return(0,r.useEffect)(()=>{(async()=>{if("authenticated"===c&&n)try{let e=await (0,s.o)(n.id);if(null==e?void 0:e.theme){console.log("Setting theme from user preferences: ".concat(e.theme)),t(e.theme);return}console.log("No theme preferences found, setting default theme: light"),t("light")}catch(e){console.error("Error initializing theme:",e)}})()},[n,c,t]),{theme:e}}},35476:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[5749,8096,5179,5738,7957,8278,7673,7358],()=>t(9737)),_N_E=e.O()}]);