(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1550,6939],{2887:(e,t,l)=>{Promise.resolve().then(l.bind(l,93940))},30150:(e,t,l)=>{"use strict";l.d(t,{w:()=>n});var a=l(6159),r=l(56309),o=l(35071),i=l(49550);function n(){let{theme:e,setTheme:t}=(0,r.D)(),{user:l,status:n}=(0,o.A)();return(0,a.useEffect)(()=>{(async()=>{if("authenticated"===n&&l)try{let e=await (0,i.o)(l.id);if(null==e?void 0:e.theme){console.log("Setting theme from user preferences: ".concat(e.theme)),t(e.theme);return}console.log("No theme preferences found, setting default theme: light"),t("light")}catch(e){console.error("Error initializing theme:",e)}})()},[l,n,t]),{theme:e}}},35476:()=>{},95818:(e,t,l)=>{"use strict";l.d(t,{s:()=>c});var a=l(6159),r=l(35071),o=l(36965),i=l(88473);let n={};function c(){let e=async e=>{try{let t=await (0,o.V)(e),l=t.firstName||"",a=t.lastName||"",r={firstName:l,lastName:a,avatar:null,initials:"".concat(l.charAt(0)).concat(a.charAt(0)).toUpperCase()||"??",role:"user",fullName:l||a?"".concat(l," ").concat(a).trim():"User",phone:t.phone||""};return f(r),g&&(localStorage.setItem(g,JSON.stringify(r)),n[g]=r),r}catch(e){throw console.error("Error loading user profile:",e),e}},{user:t,status:l}=(0,r.A)(),{notifyProfileUpdate:c,lastUpdatedUserId:s,lastUpdateTimestamp:u}=(0,i.F)(),[d,f]=(0,a.useState)(null),[h,m]=(0,a.useState)(!0),[p,v]=(0,a.useState)(null),g=(0,a.useMemo)(()=>(null==t?void 0:t.email)?"user_profile_".concat(t.email):(null==t?void 0:t.id)?"user_profile_id_".concat(t.id):null,[null==t?void 0:t.email,null==t?void 0:t.id]);(0,a.useEffect)(()=>{(async function(){if("loading"!==l){if("unauthenticated"===l||!t){f(null),m(!1);return}m(!0),v(null);try{if(g&&g in n){f(n[g]),m(!1);return}let l=g?localStorage.getItem(g):null;if(l){let e=JSON.parse(l);g&&(n[g]=e),f(e),m(!1);return}if(null==t?void 0:t.id)await e(t.id);else{let e=((null==t?void 0:t.email)||"").split("@")[0].split("."),l=e[0]?e[0].charAt(0).toUpperCase()+e[0].slice(1):"User",a=e[1]?e[1].charAt(0).toUpperCase()+e[1].slice(1):"",r={firstName:l,lastName:a,avatar:null,initials:"".concat(l.charAt(0)).concat(a.charAt(0)).toUpperCase(),role:"user",fullName:"".concat(l," ").concat(a),phone:""};f(r),g&&(localStorage.setItem(g,JSON.stringify(r)),n[g]=r)}}catch(e){console.error("Error loading user profile:",e),v(e instanceof Error?e:Error("Failed to load user profile"))}finally{m(!1)}}})()},[t,l,g]),(0,a.useEffect)(()=>{s&&(null==t?void 0:t.id)===s&&t.id&&(g&&(localStorage.removeItem(g),g in n&&delete n[g]),e(t.id))},[u,s,null==t?void 0:t.id,g]);let N=(null==d?void 0:d.firstName)||"",_=(null==d?void 0:d.lastName)||"",A=(null==t?void 0:t.email)||"",S=N,C=_;if(!N&&!_&&A){let e=A.split("@")[0].split(".");S=e[0]?e[0].charAt(0).toUpperCase()+e[0].slice(1):"",C=e[1]?e[1].charAt(0).toUpperCase()+e[1].slice(1):""}let E=(null==d?void 0:d.fullName)||(N||_?"".concat(N," ").concat(_).trim():S||C?"".concat(S," ").concat(C).trim():"User"),U=(null==d?void 0:d.initials)||(N||_?"".concat(N.charAt(0)).concat(_.charAt(0)).toUpperCase():S||C?"".concat(S.charAt(0)).concat(C.charAt(0)).toUpperCase():"??"),y=(null==d?void 0:d.phone)||"";return{profile:d,isLoading:h,error:p,initials:U,fullName:E,firstName:N||S,lastName:_||C,phone:y,invalidateCache:function(t,a){let r=!(arguments.length>2)||void 0===arguments[2]||arguments[2],o="user_profile_".concat(a),i="user_profile_id_".concat(t);localStorage.removeItem(o),localStorage.removeItem(i),o in n&&delete n[o],i in n&&delete n[i],r&&t&&"authenticated"===l&&(e(t),c&&c(t))},reloadProfile:t=>e(t),isFromCache:!!g&&(!!n[g]||!!localStorage.getItem(g))}}}},e=>{var t=t=>e(e.s=t);e.O(0,[5749,8096,5179,5738,7957,8278,7358],()=>t(2887)),_N_E=e.O()}]);