"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8278],{5232:(e,r,t)=>{t.d(r,{LX:()=>s,gm:()=>n,oV:()=>o});let n=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:localStorage;r.removeItem(e)},s=(e,r)=>({name:e,storage:null==r?void 0:r.storage,partialize:null==r?void 0:r.partialize,merge:null==r?void 0:r.merge,onRehydrateStorage:null==r?void 0:r.onRehydrateStorage,version:(null==r?void 0:r.version)||1,migrate:null==r?void 0:r.migrate}),o=()=>{let e={},r=void 0!==window.localStorage;return{getItem:t=>r?localStorage.getItem(t):e[t]||null,setItem:(t,n)=>{r?localStorage.setItem(t,n):e[t]=n},removeItem:t=>{r?localStorage.removeItem(t):delete e[t]}}}},23384:(e,r,t)=>{t.d(r,{kH:()=>u});var n=t(54503),s=t(39665),o=t(98846),i=t(32310),a=t(5232);let l={user:null,session:null,status:"loading",error:null,lastRefreshed:null},u=(0,i.L)((0,n.v)()((0,s.Zr)((e,r)=>({...l,signIn:async(r,t)=>{try{e({status:"loading",error:null});let{data:n,error:s}=await o.N.auth.signInWithPassword({email:r,password:t});if(s)return e({status:"unauthenticated",error:s}),{success:!1,error:s};if(n.session){e({user:n.session.user,session:n.session,status:"authenticated",lastRefreshed:Date.now()});let r=[];for(let e=0;e<sessionStorage.length;e++){let t=sessionStorage.key(e);t&&(t.includes("auth")||t.includes("logout")||t.includes("supabase"))&&r.push(t)}r.forEach(e=>sessionStorage.removeItem(e))}return{success:!0}}catch(t){let r=t instanceof Error?t:Error("Unknown error during sign in");return e({status:"unauthenticated",error:r}),{success:!1,error:r}}},signInWithGoogle:async()=>{try{e({status:"loading",error:null});let r=window.location.origin,t="".concat(r,"/auth/callback?redirectTo=/dashboard");console.log("Setting up Google auth with callback URL: ".concat(t));let{data:n,error:s}=await o.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:t}});if(s)return e({status:"unauthenticated",error:s}),{success:!1,error:s};return{success:!0}}catch(t){let r=t instanceof Error?t:Error("Unknown error during Google sign in");return e({status:"unauthenticated",error:r}),{success:!1,error:r}}},signOut:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/";try{var n;e({status:"loading",error:null}),await o.N.auth.signOut(),e({user:null,session:null,status:"unauthenticated",error:null,lastRefreshed:null}),localStorage.removeItem("supabase.auth.token"),localStorage.removeItem("theme");let s=null===(n=r().user)||void 0===n?void 0:n.id;s&&(localStorage.removeItem("user_preferences_".concat(s)),(0,a.gm)("user-store"),(0,a.gm)("preferences-store"),(0,a.gm)("subscription-store"),(0,a.gm)("appointment-history-store")),window.location.href=t}catch(r){console.error("Error during sign out:",r),e({user:null,session:null,status:"unauthenticated",error:r instanceof Error?r:Error("Unknown error during sign out"),lastRefreshed:null})}},refresh:async()=>{try{let t=r().status;"authenticated"!==t&&e({status:"loading",error:null});let{data:n,error:s}=await o.N.auth.getSession();if(s)throw s;n.session?e({user:n.session.user,session:n.session,status:"authenticated",lastRefreshed:Date.now()}):e({user:null,session:null,status:"unauthenticated",lastRefreshed:Date.now()})}catch(r){console.error("Error refreshing auth state:",r),e({user:null,session:null,status:"unauthenticated",error:r instanceof Error?r:Error("Unknown error during refresh"),lastRefreshed:Date.now()})}},setUser:r=>e({user:r}),setSession:r=>e({session:r}),setStatus:r=>e({status:r}),setError:r=>e({error:r}),clearError:()=>e({error:null}),reset:()=>e(l)}),(0,a.LX)("user-store",{storage:(0,s.KU)(()=>(0,a.oV)()),partialize:e=>({user:e.user,session:e.session,status:e.status,lastRefreshed:e.lastRefreshed}),version:1,onRehydrateStorage:()=>e=>{{let{data:r}=o.N.auth.onAuthStateChange((e,r)=>{"SIGNED_IN"===e&&r?u.setState({user:r.user,session:r,status:"authenticated",lastRefreshed:Date.now()}):"SIGNED_OUT"===e&&u.setState({user:null,session:null,status:"unauthenticated",lastRefreshed:Date.now()})});if(e){let{lastRefreshed:r}=e,t=Date.now();(!r||t-r>3e5)&&setTimeout(()=>{u.getState().refresh()},0)}}}}))))},28278:(e,r,t)=>{t.d(r,{S:()=>c,f:()=>d});var n=t(54503),s=t(39665),o=t(98846),i=t(32310),a=t(5232),l=t(23384);let u={subscription:null,isLoading:!1,error:null,lastFetched:null},c=(0,i.L)((0,n.v)()((0,s.Zr)((e,r)=>({...u,fetchSubscription:async t=>{try{e({isLoading:!0,error:null});let{subscription:n,lastFetched:s}=r(),i=Date.now();if(n&&s&&i-s<3e5)return e({isLoading:!1}),n;let{data:a,error:l}=await o.N.from("subscriptions").select("*").eq("user_id",t).eq("status","active").in("plan_type",["individual","family"]).maybeSingle();if(l)throw l;return a&&console.log("Subscription billing info:",{interval:a.interval,billing_period:a.billing_period,derived_billing_period:a.billing_period||("month"===a.interval?"monthly":"year"===a.interval?"annual":null)}),e({subscription:a,isLoading:!1,lastFetched:Date.now()}),a}catch(r){return e({isLoading:!1,error:r instanceof Error?r:Error("Unknown error fetching subscription")}),console.error("Error fetching subscription:",r),null}},updateSubscription:async t=>{try{e({isLoading:!0,error:null});let n=r().subscription;if(!n)throw Error("No subscription to update");let{error:s}=await o.N.from("subscriptions").update(t).eq("id",n.id);if(s)throw s;return e({subscription:{...n,...t},isLoading:!1}),!0}catch(r){return e({isLoading:!1,error:r instanceof Error?r:Error("Unknown error updating subscription")}),console.error("Error updating subscription:",r),!1}},cancelSubscription:async t=>{try{e({isLoading:!0,error:null});let n=await fetch("/api/subscriptions/cancel",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({subscriptionId:t})}),s=await n.json();if(!n.ok)throw Error(s.error||"Failed to cancel subscription");let o=r().subscription;return o?e({subscription:{...o,cancel_at_period_end:!0,canceled_at:Math.floor(Date.now()/1e3)},isLoading:!1}):e({isLoading:!1}),!0}catch(r){return e({isLoading:!1,error:r instanceof Error?r:Error("Unknown error cancelling subscription")}),console.error("Error cancelling subscription:",r),!1}},setSubscription:r=>e({subscription:r}),setIsLoading:r=>e({isLoading:r}),setError:r=>e({error:r}),clearError:()=>e({error:null}),reset:()=>e(u)}),(0,a.LX)("subscription-store",{storage:(0,s.KU)(()=>(0,a.oV)()),partialize:e=>({subscription:e.subscription,lastFetched:e.lastFetched}),version:1,onRehydrateStorage:()=>e=>{if(e){let{lastFetched:r}=e,t=Date.now();(!r||t-r>3e5)&&setTimeout(()=>{var e;let r=null===(e=l.kH.getState().user)||void 0===e?void 0:e.id;r&&c.getState().fetchSubscription(r)},0)}}})))),d=()=>{let e=c(e=>e.subscription);c(e=>e.isLoading),c(e=>e.error);let r=!!e&&"active"===e.status,t=(null==e?void 0:e.plan_type)||null,n=(null==e?void 0:e.billing_period)||((null==e?void 0:e.interval)==="month"?"monthly":(null==e?void 0:e.interval)==="year"?"annual":null),s=(null==e?void 0:e.status)||null,o=(null==e?void 0:e.current_period_start)?new Date(1e3*e.current_period_start):null,i=(null==e?void 0:e.current_period_end)?new Date(1e3*e.current_period_end):null,a=(null==e?void 0:e.cancel_at_period_end)||!1,l=(null==e?void 0:e.amount)?e.amount/100:null,u=(null==e?void 0:e.currency)||null,d=null!==l&&u?new Intl.NumberFormat("en-US",{style:"currency",currency:u.toUpperCase()}).format(l):null,g=i?Math.ceil((i.getTime()-Date.now())/864e5):null;return{hasSubscription:r,planType:t,billingPeriod:n,status:s,currentPeriodStart:o,currentPeriodEnd:i,cancelAtPeriodEnd:a,amount:l,currency:u,formattedAmount:d,daysUntilRenewal:g,isTrialing:"trialing"===s,isPastDue:"past_due"===s,isCancelled:"cancelled"===s||a}}},32310:(e,r,t)=>{t.d(r,{L:()=>n});function n(e){for(let r of(e.use={},Object.keys(e.getState())))Object.defineProperty(e.use,r,{get:()=>()=>e(e=>e[r])});return e}}}]);