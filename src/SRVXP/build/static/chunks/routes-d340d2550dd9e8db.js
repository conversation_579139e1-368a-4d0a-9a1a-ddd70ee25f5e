(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7957],{2980:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>p});var t=a(65127),r=a(6159),l=a(29081),n=a.n(l),i=a(72842),c=a(48544),o=a(71532),d=a(96970),m=a(86453),x=a(4946),u=a(35071),h=a(39957);function p(){let e=(0,i.useRouter)(),s=(0,i.useSearchParams)().get("redirectedFrom")||"/dashboard",{t:a}=(0,m.B)(),{signIn:l,signInWithGoogle:p,status:j}=(0,u.A)(),[f,N]=(0,r.useState)(""),[g,v]=(0,r.useState)(""),[y,b]=(0,r.useState)(null),[w,k]=(0,r.useState)(!1),[T,A]=(0,r.useState)(!1);(0,r.useEffect)(()=>{"authenticated"===j&&e.push(s)},[j,s,e]);let C=async e=>{e.preventDefault(),k(!0),b(null);try{let e=await l(f,g);if(e.success)window.location.href="/dashboard";else{var s;throw Error((null===(s=e.error)||void 0===s?void 0:s.message)||"Authentication failed")}}catch(e){console.error("Error signing in:",e),b(a("auth.errors.invalidCredentials"))}finally{k(!1)}},S=async()=>{A(!0),b(null);try{await p()}catch(e){console.error("Error signing in with Google:",e),b(a("auth.errors.socialAuthFailed")),A(!1)}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"space-y-2 text-center",children:(0,t.jsx)("h1",{className:"text-2xl font-bold",children:a("auth.signIn")})}),y&&(0,t.jsx)(x.Fc,{variant:"destructive",children:(0,t.jsx)(x.TN,{children:y})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(c.$,{type:"button",variant:"outline",className:"w-full flex items-center justify-center gap-2",onClick:S,disabled:T,children:[T?(0,t.jsx)("span",{className:"w-5 h-5 border-2 border-gray-300 border-t-primary rounded-full animate-spin"}):(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",height:"24",viewBox:"0 0 24 24",width:"24",children:[(0,t.jsx)("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),(0,t.jsx)("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),(0,t.jsx)("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),(0,t.jsx)("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"}),(0,t.jsx)("path",{d:"M1 1h22v22H1z",fill:"none"})]}),(0,t.jsx)("span",{children:T?a("common.loading"):a("auth.continueWithGoogle")})]}),(0,t.jsxs)("div",{className:"relative flex items-center justify-center mt-6",children:[(0,t.jsx)(h.Separator,{className:"absolute w-full bg-gray-200"}),(0,t.jsx)("span",{className:"relative bg-white px-2 text-xs text-gray-500",children:a("auth.orContinueWith")})]})]}),(0,t.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.J,{htmlFor:"email",children:a("auth.email")}),(0,t.jsx)(o.p,{id:"email",type:"email",placeholder:"<EMAIL>",autoComplete:"email",required:!0,value:f,onChange:e=>N(e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(d.J,{htmlFor:"password",children:a("auth.password")}),(0,t.jsx)(n(),{href:"/auth/forgot-password",className:"text-sm text-primary hover:underline",children:a("auth.forgotPassword")})]}),(0,t.jsx)(o.p,{id:"password",type:"password",autoComplete:"current-password",required:!0,value:g,onChange:e=>v(e.target.value)})]}),(0,t.jsx)(c.$,{type:"submit",className:"w-full py-6",disabled:w,children:w?a("common.loading"):a("auth.signIn")})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[a("auth.noAccount")," ",(0,t.jsx)(n(),{href:"/auth/sign-up",className:"text-primary hover:underline",children:a("auth.createAccount")})]})})]})}},11967:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>h});var t=a(65127),r=a(6159),l=a(72842),n=a(19318),i=a(86453),c=a(49290),o=a(35071),d=a(91303),m=a(28278);let x={individual:{monthly:7.95,annual:71.4},family:{monthly:14.95,annual:134.4}};function u(){let{user:e}=(0,o.A)(),{translate:s}=(0,d.o)(),a=(0,l.useSearchParams)(),n=a.get("session_id"),u=a.get("canceled"),[h,p]=(0,r.useState)(!0),[j,f]=(0,r.useState)(!1),[N,g]=(0,r.useState)(null),v=(0,m.S)(e=>e.fetchSubscription),y=(0,m.f)();if((0,r.useEffect)(()=>{(async function(){if("true"===u){g(s(c.o.subscription.canceledCheckout)),p(!1);return}if(!n){g(s(c.o.subscription.noSessionId)),p(!1);return}if(!e){g(s(c.o.subscription.notLoggedIn)),p(!1);return}try{let a=await v(e.id);a&&"active"===a.status?f(!0):g(s(c.o.subscription.processingSubscription))}catch(e){console.error("Error verifying subscription:",e),g(e instanceof Error?e.message:"An unknown error occurred")}finally{p(!1)}})()},[n,e,v,s]),h)return(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[(0,t.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"}),(0,t.jsx)("p",{className:"mt-4 text-center text-lg",children:(0,t.jsx)(i.T,{keyName:c.o.common.loading})})]});if(j&&y.hasSubscription&&y.planType&&y.billingPeriod){var b,w;let e=(b=y.planType,w=y.billingPeriod,b in x&&w in x[b]?x[b][w]:y.amount||0),s=y.currentPeriodStart?((e,s)=>{let a=new Date(e),t=new Date(e);return"annual"===s?t.setFullYear(t.getFullYear()+1):t.setMonth(t.getMonth()+1),{start:a,end:t}})(y.currentPeriodStart,y.billingPeriod):{start:new Date,end:new Date},a="monthly"===y.billingPeriod?"Monthly":"Annual";return(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[(0,t.jsx)("div",{className:"rounded-full bg-green-100 p-3",children:(0,t.jsx)("svg",{className:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,t.jsx)("h2",{className:"mt-4 text-center text-2xl font-bold",children:(0,t.jsx)(i.T,{keyName:c.o.subscription.success})}),(0,t.jsx)("p",{className:"mt-2 text-center text-gray-600",children:(0,t.jsx)(i.T,{keyName:c.o.subscription.successMessage})}),(0,t.jsxs)("div",{className:"mt-8 w-full max-w-md rounded-lg border p-6",children:[(0,t.jsx)("h3",{className:"mb-4 text-lg font-medium",children:(0,t.jsx)(i.T,{keyName:c.o.subscription.details})}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-gray-500",children:[(0,t.jsx)(i.T,{keyName:c.o.subscription.plan}),":"]}),(0,t.jsx)("span",{className:"font-medium",children:y.planType.charAt(0).toUpperCase()+y.planType.slice(1)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-gray-500",children:[(0,t.jsx)(i.T,{keyName:c.o.subscription.billing}),":"]}),(0,t.jsx)("span",{className:"font-medium",children:a})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-gray-500",children:[(0,t.jsx)(i.T,{keyName:c.o.subscription.amount}),":"]}),(0,t.jsx)("span",{className:"font-medium",children:function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"CAD";return new Intl.NumberFormat("en-CA",{style:"currency",currency:s.toUpperCase()}).format(e)}(e,y.currency||"CAD")})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-gray-500",children:[(0,t.jsx)(i.T,{keyName:c.o.subscription.currentPeriod}),":"]}),(0,t.jsxs)("span",{className:"font-medium",children:[s.start.toLocaleDateString()," - ",s.end.toLocaleDateString()]})]})]})]}),(0,t.jsxs)("div",{className:"mt-8 text-center",children:[(0,t.jsx)("p",{className:"text-gray-600",children:(0,t.jsx)(i.T,{keyName:c.o.subscription.nextSteps})}),(0,t.jsxs)("div",{className:"mt-4 flex flex-wrap justify-center gap-4",children:[(0,t.jsx)("a",{href:"/dashboard",className:"rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700",children:(0,t.jsx)(i.T,{keyName:c.o.subscription.goToDashboard})}),(0,t.jsx)("a",{href:"/compte",className:"rounded-md border border-gray-300 px-4 py-2 hover:bg-gray-50",children:(0,t.jsx)(i.T,{keyName:c.o.subscription.manageAccount})})]})]})]})}return(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[(0,t.jsx)("div",{className:"rounded-full bg-red-100 p-3",children:(0,t.jsx)("svg",{className:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,t.jsx)("h2",{className:"mt-4 text-center text-2xl font-bold",children:(0,t.jsx)(i.T,{keyName:c.o.subscription.error})}),(0,t.jsx)("p",{className:"mt-2 text-center text-gray-600",children:N||s(c.o.subscription.errorMessage)}),(0,t.jsxs)("div",{className:"mt-8 text-center",children:[(0,t.jsx)("p",{className:"text-gray-600",children:(0,t.jsx)(i.T,{keyName:c.o.subscription.needHelp})}),(0,t.jsxs)("div",{className:"mt-4 flex flex-wrap justify-center gap-4",children:[(0,t.jsx)("a",{href:"/pricing",className:"rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700",children:(0,t.jsx)(i.T,{keyName:c.o.subscription.returnToPlans})}),(0,t.jsx)("a",{href:"/aide",className:"rounded-md border border-gray-300 px-4 py-2 hover:bg-gray-50",children:(0,t.jsx)(i.T,{keyName:c.o.subscription.contactSupport})})]})]})]})}function h(){return(0,t.jsx)(n.S,{children:(0,t.jsx)(r.Suspense,{fallback:(0,t.jsx)("div",{className:"flex h-full w-full items-center justify-center",children:(0,t.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"})}),children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:(0,t.jsx)(i.T,{keyName:"subscription.status"})}),(0,t.jsx)("p",{className:"text-muted-foreground mt-1.5",children:(0,t.jsx)(i.T,{keyName:"subscription.verifyingPayment"})})]}),(0,t.jsx)(u,{})]})})})}},15945:(e,s,a)=>{"use strict";a.d(s,{default:()=>x});var t=a(65127),r=a(6159),l=a(72842),n=a(4111),i=a(91303),c=a(35071),o=a(64928),d=a(88473),m=a(99640);function x(e){let{children:s}=e,[a,x]=(0,r.useState)(!1),u=(0,l.usePathname)(),h="/"===u||"/landing"===u||(null==u?void 0:u.startsWith("/auth"));return((0,r.useEffect)(()=>{x(!0)},[]),a)?(0,t.jsx)(c.O,{children:(0,t.jsx)(d.o,{children:(0,t.jsx)(m.N,{attribute:"class",defaultTheme:h?"light":"system",enableSystem:!h,forcedTheme:h?"light":void 0,children:(0,t.jsx)(i.LanguageProvider,{children:(0,t.jsx)(o.$,{children:(0,t.jsx)(n.r,{children:s})})})})})}):null}},21103:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var t=a(65127),r=a(6159),l=a(29081),n=a.n(l),i=a(10210),c=a(98826),o=a(10228),d=a(11),m=a(98282),x=a(48544),u=a(82642),h=a(96970),p=a(48600),j=a(4946),f=a(86453),N=a(49290),g=a(91303),v=a(71049),y=a(5230);function b(){var e;let{user:s,preferences:a,language:l,setLanguage:b,updatePreferences:w,isPreferencesLoading:k,fetchPreferences:T}=(0,y.yk)(),{language:A,setLanguage:C}=(0,g.o)(),{t:S}=(0,f.B)(),{preferences:P,isLoading:E,isSaving:_,saveError:D,saveSuccess:L,updatePreferences:F}=(0,v.H)(),B=l||A,[M,q]=(0,r.useState)(!1),[$,z]=(0,r.useState)(null),[R,Z]=(0,r.useState)(!1);(0,r.useEffect)(()=>{(null==s?void 0:s.id)&&T(s.id)},[null==s?void 0:s.id,T]);let[W,I]=(0,r.useState)(B),[U,J]=(0,r.useState)(!1);(0,r.useEffect)(()=>{I(B)},[B]),(0,r.useEffect)(()=>{J(W!==B)},[W,B]);let H=async()=>{if(null==s?void 0:s.id){console.log("Saving preferences:",{pendingLanguage:W}),q(!0),z(null),Z(!1);try{if(W!==B&&(b(W),C(W)),w)try{await w(s.id,{language:W}),J(!1),Z(!0),setTimeout(()=>Z(!1),3e3),console.log("Preferences saved successfully using store"),q(!1);return}catch(e){console.error("Error saving preferences with store:",e)}await F({language:W}),J(!1),Z(!0),setTimeout(()=>Z(!1),3e3),console.log("Preferences saved successfully using hook")}catch(e){console.error("Error saving preferences:",e),z(e instanceof Error?e:Error("Failed to save preferences")),I(B)}finally{q(!1)}}};return(0,t.jsx)(m.DashboardLayout,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.$,{asChild:!0,variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsxs)(n(),{href:"/compte",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:(0,t.jsx)(f.T,{keyName:N.o.common.back})})]})}),(0,t.jsx)("h1",{className:"text-3xl font-bold",children:(0,t.jsx)(f.T,{keyName:N.o.preferences.managePreferences})})]}),(0,t.jsxs)("div",{className:"grid gap-6",children:[R&&(0,t.jsxs)(j.Fc,{className:"bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),(0,t.jsx)(j.TN,{className:"text-green-600 dark:text-green-400",children:(0,t.jsx)(f.T,{keyName:N.o.preferences.changesSaved})})]}),$&&(0,t.jsx)(j.Fc,{variant:"destructive",children:(0,t.jsx)(j.TN,{children:(null===(e=$.message)||void 0===e?void 0:e.includes("user_preferences table doesn't exist"))?"Une erreur de configuration du syst\xe8me s'est produite. Veuillez contacter l'administrateur.":$.message||(0,t.jsx)(f.T,{keyName:N.o.preferences.errorSaving})})}),(0,t.jsxs)(u.Zp,{children:[(0,t.jsx)(u.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-start gap-4",children:[(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,t.jsx)(o.A,{className:"h-5 w-5 text-blue-700"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(u.ZB,{children:(0,t.jsx)(f.T,{keyName:N.o.preferences.preferredLanguage})}),(0,t.jsx)(u.BT,{className:"mt-1.5",children:(0,t.jsx)(f.T,{keyName:N.o.preferences.languageDescription})})]})]})}),(0,t.jsx)(u.Wu,{children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"language",children:(0,t.jsx)(f.T,{keyName:N.o.preferences.preferredLanguage})}),(0,t.jsxs)(p.l6,{value:W,onValueChange:e=>{("fr"===e||"en"===e)&&(console.log("Language selection changed to:",e),I(e))},children:[(0,t.jsx)(p.bq,{id:"language",children:(0,t.jsx)(p.yv,{placeholder:S(N.o.preferences.preferredLanguage)})}),(0,t.jsxs)(p.gC,{children:[(0,t.jsx)(p.eb,{value:"fr",children:(0,t.jsx)(f.T,{keyName:N.o.preferences.french})}),(0,t.jsx)(p.eb,{value:"en",children:(0,t.jsx)(f.T,{keyName:N.o.preferences.english})})]})]})]})})}),(0,t.jsxs)(u.wL,{className:"flex gap-2 justify-end",children:[U&&(0,t.jsx)(x.$,{variant:"outline",onClick:()=>{I(B),J(!1),console.log("Changes canceled, reverting to:",{language:B})},disabled:M,children:(0,t.jsx)(f.T,{keyName:N.o.common.cancel})}),(0,t.jsx)(x.$,{className:"min-w-32",onClick:H,disabled:M||!U,children:M?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,t.jsx)(f.T,{keyName:N.o.preferences.saving})]}):(0,t.jsx)(f.T,{keyName:N.o.preferences.saveChanges})})]})]})]})]})})}},28890:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>y});var t=a(65127),r=a(6159),l=a(29081),n=a.n(l),i=a(11),c=a(39556),o=a(59019),d=a(9999),m=a(37574),x=a(86809),u=a(98282),h=a(82642),p=a(48544),j=a(86453),f=a(49290),N=a(95818),g=a(5230),v=a(52033);function y(){var e,s;let{profile:a,subscription:l,user:y,fetchProfile:b,fetchSubscription:w,isProfileLoading:k,isSubscriptionLoading:T}=(0,g.yk)(),{fullName:A,initials:C}=(0,N.s)(),S=a?"".concat(a.firstName," ").concat(a.lastName).trim():A,P=a?"".concat((null===(e=a.firstName)||void 0===e?void 0:e.charAt(0))||"").concat((null===(s=a.lastName)||void 0===s?void 0:s.charAt(0))||"").toUpperCase():C;(0,r.useEffect)(()=>{(null==y?void 0:y.id)&&(b(y.id),w(y.id))},[null==y?void 0:y.id,b,w]);let E=(null==l?void 0:l.plan_type)||"individual",_=(null==l?void 0:l.status)==="active";return(0,r.useEffect)(()=>{l&&console.log("Subscription data:",{id:l.id,stripe_id:l.stripe_id,plan_type:l.plan_type,interval:l.interval,billing_period:l.billing_period,amount:l.amount,period_start:l.current_period_start?new Date(1e3*l.current_period_start).toISOString():null,period_end:l.current_period_end?new Date(1e3*l.current_period_end).toISOString():null,period_diff_days:l.current_period_start&&l.current_period_end?Math.round((l.current_period_end-l.current_period_start)/86400):null,display_plan:"family"===l.plan_type?"annual"!==l.billing_period&&(l.billing_period||"year"!==l.interval)?"familyPlanMonthly":"familyPlanAnnual":"annual"!==l.billing_period&&(l.billing_period||"year"!==l.interval)?"individualPlanMonthly":"individualPlanAnnual"})},[l]),(0,t.jsx)(u.DashboardLayout,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsx)("h1",{className:"text-3xl font-bold",children:(0,t.jsx)(j.T,{keyName:f.o.account.yourAccount})})}),(0,t.jsx)(h.Zp,{className:"overflow-hidden",children:(0,t.jsx)(h.aR,{className:"bg-gray-50/50 dark:bg-transparent dark:border-b pb-4 pt-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"h-12 w-12 min-h-[3rem] min-w-[3rem] rounded-full bg-blue-100 dark:bg-blue-950/70 flex items-center justify-center shrink-0",children:k?(0,t.jsx)(i.A,{className:"h-6 w-6 text-blue-700 dark:text-blue-400 animate-spin"}):P?(0,t.jsx)("span",{className:"text-lg font-semibold text-blue-700 dark:text-blue-400",children:P}):(0,t.jsx)(c.A,{className:"h-6 w-6 text-blue-700 dark:text-blue-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(h.ZB,{children:k?"Loading...":S}),l&&(0,t.jsx)(h.BT,{className:"mt-1",children:(0,t.jsx)(j.T,{keyName:"family"===l.plan_type?"annual"!==l.billing_period&&(l.billing_period||"year"!==l.interval)?f.o.account.familyPlanMonthly:f.o.account.familyPlanAnnual:"annual"!==l.billing_period&&(l.billing_period||"year"!==l.interval)?f.o.account.individualPlanMonthly:f.o.account.individualPlanAnnual})})]})]}),(0,t.jsx)(v.E,{className:_?"bg-green-100 text-green-800 hover:bg-green-100":"bg-yellow-100 text-yellow-800 hover:bg-yellow-100",children:T?(0,t.jsx)(i.A,{className:"h-3.5 w-3.5 animate-spin"}):(0,t.jsx)(j.T,{keyName:_?f.o.common.active:f.o.common.inactive})})]})})}),(0,t.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2",children:[(0,t.jsxs)(h.Zp,{className:"flex flex-col min-h-[180px]",children:[(0,t.jsxs)(h.aR,{className:"pb-2 flex flex-row items-start gap-4 flex-1",children:[(0,t.jsx)("div",{className:"h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0",children:(0,t.jsx)(o.A,{className:"h-5 w-5 text-blue-700"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(h.ZB,{children:(0,t.jsx)(j.T,{keyName:f.o.account.manageInformation})}),(0,t.jsx)(h.BT,{className:"mt-1",children:(0,t.jsx)(j.T,{keyName:f.o.account.personalInfoDescription})})]})]}),(0,t.jsx)(h.Wu,{className:"pt-2",children:(0,t.jsx)(p.$,{asChild:!0,variant:"default",className:"w-full",children:(0,t.jsx)(n(),{href:"/compte/profile",children:(0,t.jsx)(j.T,{keyName:f.o.account.modifyProfile})})})})]}),(0,t.jsxs)(h.Zp,{className:"flex flex-col min-h-[180px]",children:[(0,t.jsxs)(h.aR,{className:"pb-2 flex flex-row items-start gap-4 flex-1",children:[(0,t.jsx)("div",{className:"h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0",children:(0,t.jsx)(d.A,{className:"h-5 w-5 text-blue-700"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(h.ZB,{children:(0,t.jsx)(j.T,{keyName:f.o.account.modifySubscription})}),(0,t.jsx)(h.BT,{className:"mt-1",children:(0,t.jsx)(j.T,{keyName:_?f.o.account.subscriptionDescription:((null==l?void 0:l.status)==="cancelled"||null==l||l.cancel_at_period_end,f.o.subscription.returnToPlans)})})]})]}),(0,t.jsx)(h.Wu,{className:"pt-2",children:(0,t.jsx)(p.$,{asChild:!0,variant:"default",className:"w-full",children:(0,t.jsx)(n(),{href:_?"/compte/abonnement":"/pricing",children:(0,t.jsx)(j.T,{keyName:_?f.o.account.modifySubscription:(null==l?void 0:l.status)==="cancelled"||(null==l?void 0:l.cancel_at_period_end)?f.o.account.choosePlan:f.o.account.subscribePlan})})})})]}),(0,t.jsxs)(h.Zp,{className:"flex flex-col min-h-[180px]",children:[(0,t.jsxs)(h.aR,{className:"pb-2 flex flex-row items-start gap-4 flex-1",children:[(0,t.jsx)("div",{className:"h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0",children:(0,t.jsx)(m.A,{className:"h-5 w-5 text-blue-700"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(h.ZB,{children:(0,t.jsx)(j.T,{keyName:f.o.account.appearanceLanguage})}),(0,t.jsx)(h.BT,{className:"mt-1",children:(0,t.jsx)(j.T,{keyName:f.o.account.appearanceDescription})})]})]}),(0,t.jsx)(h.Wu,{className:"pt-2",children:(0,t.jsx)(p.$,{asChild:!0,variant:"default",className:"w-full",children:(0,t.jsx)(n(),{href:"/compte/preferences",children:(0,t.jsx)(j.T,{keyName:f.o.account.modifyPreferences})})})})]}),(0,t.jsxs)(h.Zp,{className:"flex flex-col min-h-[180px]",children:[(0,t.jsxs)(h.aR,{className:"pb-2 flex flex-row items-start gap-4 flex-1",children:[(0,t.jsx)("div",{className:"h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0",children:"family"===E?(0,t.jsx)(x.A,{className:"h-5 w-5 text-blue-700"}):(0,t.jsx)(c.A,{className:"h-5 w-5 text-blue-700"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(h.ZB,{children:(0,t.jsx)(j.T,{keyName:"family"===E?f.o.account.manageAccountUsers:f.o.account.manageProfile})}),(0,t.jsx)(h.BT,{className:"mt-1",children:(0,t.jsx)(j.T,{keyName:"family"===E?f.o.account.manageUsersDescription:f.o.account.manageProfileDescription})})]})]}),(0,t.jsx)(h.Wu,{className:"pt-2",children:(0,t.jsx)(p.$,{asChild:!0,variant:"default",className:"w-full",children:(0,t.jsx)(n(),{href:"/compte/utilisateurs",children:(0,t.jsx)(j.T,{keyName:"family"===E?f.o.account.manageUsers:f.o.account.manageProfileButton})})})})]})]})]})})}},34240:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var t=a(65127),r=a(6159),l=a(29081),n=a.n(l),i=a(98846),c=a(48544),o=a(71532),d=a(96970),m=a(86453),x=a(4946);function u(){let{t:e}=(0,m.B)(),[s,a]=(0,r.useState)(""),[l,u]=(0,r.useState)(null),[h,p]=(0,r.useState)(!1),[j,f]=(0,r.useState)(!1),N=async a=>{a.preventDefault(),f(!0),u(null),p(!1);try{let{error:e}=await i.N.auth.resetPasswordForEmail(s,{redirectTo:"".concat(window.location.origin,"/auth/reset-password")});if(e)throw e;p(!0)}catch(s){console.error("Error resetting password:",s),u(s.message||e("auth.errors.generic"))}finally{f(!1)}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2 text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:e("auth.resetPassword")}),(0,t.jsx)("p",{className:"text-gray-500",children:e("auth.enterEmailForReset")})]}),l&&(0,t.jsx)(x.Fc,{variant:"destructive",children:(0,t.jsx)(x.TN,{children:l})}),h?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(x.Fc,{children:(0,t.jsx)(x.TN,{children:e("auth.resetEmailSent")})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(n(),{href:"/auth/sign-in",children:(0,t.jsx)(c.$,{variant:"outline",children:e("auth.backToSignIn")})})})]}):(0,t.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.J,{htmlFor:"email",children:e("auth.email")}),(0,t.jsx)(o.p,{id:"email",type:"email",placeholder:"<EMAIL>",required:!0,value:s,onChange:e=>a(e.target.value)})]}),(0,t.jsx)(c.$,{type:"submit",className:"w-full py-6",disabled:j,children:j?e("common.loading"):e("auth.sendResetEmail")}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(n(),{href:"/auth/sign-in",className:"text-sm text-primary hover:underline",children:e("auth.backToSignIn")})})]})]})}},45715:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var t=a(65127),r=a(29081),l=a.n(r),n=a(62153),i=a(38766),c=a(86453);function o(e){let{children:s}=e,{t:a,language:r}=(0,c.B)();return(0,t.jsxs)("div",{className:"min-h-screen flex flex-col justify-center items-center bg-gray-50 light relative",children:[(0,t.jsx)("div",{className:"absolute top-4 right-4",children:(0,t.jsx)(i.J,{})}),(0,t.jsxs)("div",{className:"w-full max-w-md bg-white p-8 rounded-lg shadow-sm text-gray-900",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-8",children:(0,t.jsxs)(l(),{href:"/",className:"flex items-center group",children:[(0,t.jsx)("div",{className:"w-8 h-8 flex items-center justify-center transition-transform duration-300 group-hover:scale-110",children:(0,t.jsx)(n.default,{src:"/zaply-images/srvxp_logorevised.svg",alt:"Logo",width:32,height:32,priority:!0,suppressHydrationWarning:!0,className:"text-brandBlue fill-current"})}),(0,t.jsx)("span",{className:"ml-4 text-lg sm:text-xl font-bold text-[#212242]",children:"Sans rendez-vous express"})]})}),s]}),(0,t.jsxs)("div",{className:"absolute bottom-4 left-4 flex items-center space-x-4 text-xs text-gray-400",children:[(0,t.jsx)("span",{children:"\xa9 Sans rendez-vous express"}),(0,t.jsx)(l(),{href:"en"===r?"/politique-de-confidentialite-EN":"/politique-de-confidentialite",className:"hover:text-gray-600 transition-colors",children:a("landing.footer.privacyPolicy")})]})]})}},48448:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>C});var t=a(65127),r=a(6159),l=a(29081),n=a.n(l),i=a(10210),c=a(39556),o=a(30096),d=a(17224),m=a(23389),x=a(45301),u=a(98282),h=a(48544),p=a(82642),j=a(71532),f=a(96970),N=a(47567),g=a(86453),v=a(49290),y=a(95818),b=a(36965),w=a(88473),k=a(5230),T=a(4946);function A(e){return!!e&&""!==e.trim()&&/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/.test(e)}function C(){var e,s;let{profile:a,user:l,session:C,isProfileLoading:S,updateProfile:P,fetchProfile:E}=(0,k.yk)(),{profile:_,isLoading:D,initials:L,phone:F,invalidateCache:B,isFromCache:M}=(0,y.s)(),{notifyProfileUpdate:q}=(0,w.F)(),$=a||_,z=a?"".concat((null===(e=a.firstName)||void 0===e?void 0:e.charAt(0))||"").concat((null===(s=a.lastName)||void 0===s?void 0:s.charAt(0))||"").toUpperCase():L,R=(null==a?void 0:a.phone)||F,[Z,W]=(0,r.useState)({firstName:"",lastName:"",email:"",phone:""}),[I,U]=(0,r.useState)({firstName:"",lastName:""}),[J,H]=(0,r.useState)(!1),[V,Y]=(0,r.useState)(!1),[O,X]=(0,r.useState)(null),[G,K]=(0,r.useState)(null),[Q,ee]=(0,r.useState)(null),[es,ea]=(0,r.useState)(null),{t:et}=(0,g.B)();(0,r.useEffect)(()=>{(null==l?void 0:l.id)&&E(l.id)},[null==l?void 0:l.id,E]),(0,r.useEffect)(()=>{if(l){let e={firstName:(null==$?void 0:$.firstName)||"",lastName:(null==$?void 0:$.lastName)||"",email:l.email||"",phone:R||(null==$?void 0:$.phone)||""};W(e),U({firstName:e.firstName,lastName:e.lastName}),K(null),ee(null),ea(null)}},[$,l,R]);let er=(e,s)=>{"phone"===e&&(s=function(e){let s=e.replace(/\D/g,"");return s.length<=3?s:s.length<=6?"(".concat(s.substring(0,3),") ").concat(s.substring(3)):"(".concat(s.substring(0,3),") ").concat(s.substring(3,6),"-").concat(s.substring(6,10))}(s)),"firstName"===e&&(K(null),""===s.trim()&&K(et("account.firstNameRequired"))),"lastName"===e&&(ee(null),""===s.trim()&&ee(et("account.lastNameRequired"))),"email"!==e||(ea(null),""===s.trim()?ea(et("account.emailCannotBeEmpty")):A(s)||ea(et("account.invalidEmail"))),W(a=>({...a,[e]:s}))},el=async()=>{if(null==l?void 0:l.id){X(null),Y(!0);try{if(!Z.firstName.trim()){K(et("account.firstNameRequired")),Y(!1);return}if(!Z.lastName.trim()){ee(et("account.lastNameRequired")),Y(!1);return}if(!Z.email.trim()){ea(et("account.emailCannotBeEmpty")),Y(!1);return}if(Z.email&&!A(Z.email)){ea(et("account.invalidEmail")),Y(!1);return}let{valid:e,formattedPhone:s}=function(e){if(!e||""===e.trim())return{valid:!0,formattedPhone:""};let s=e.replace(/\D/g,"");if(s.length<10||s.length>15)return{valid:!1,formattedPhone:e};let a=e;return 10===s.length?a="(".concat(s.substring(0,3),") ").concat(s.substring(3,6),"-").concat(s.substring(6)):11===s.length&&"1"===s.charAt(0)&&(a="+1 (".concat(s.substring(1,4),") ").concat(s.substring(4,7),"-").concat(s.substring(7))),{valid:!0,formattedPhone:a}}(Z.phone);if(!e){X(et("account.invalidPhone")||"Please enter a valid phone number"),Y(!1);return}s!==Z.phone&&W(e=>({...e,phone:s}));let a={firstName:Z.firstName,lastName:Z.lastName,email:Z.email,phone:s};if(P)try{if(await P(l.id,a)){U({firstName:Z.firstName,lastName:Z.lastName}),H(!0),setTimeout(()=>H(!1),3e3),Z.email!==l.email&&X(et("account.emailVerificationSent")||"A verification email has been sent to your new email address. Please verify it to complete the update."),q(l.id);return}}catch(e){console.error("Error updating profile with store:",e)}let t=await (0,b.e)(l.id,a,C);t.success?(U({firstName:Z.firstName,lastName:Z.lastName}),l.email?(B(l.id,l.email,!0),q(l.id),t.updates.includes("email")?(H(!0),setTimeout(()=>H(!1),3e3),Z.email!==l.email&&X(et("account.emailVerificationSent")||"A verification email has been sent to your new email address. Please verify it to complete the update.")):(H(!0),setTimeout(()=>H(!1),3e3))):(H(!0),setTimeout(()=>H(!1),3e3))):X(et("common.errorOccurred")||"An error occurred while saving your profile.")}catch(e){console.error("Error saving profile:",e),X(e instanceof Error?e.message:et("common.errorOccurred")||"An error occurred while saving your profile.")}finally{Y(!1)}}};return S||D?(0,t.jsx)(u.DashboardLayout,{children:(0,t.jsxs)("div",{className:"space-y-6 mx-auto max-w-6xl",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsx)("h1",{className:"text-3xl font-bold",children:(0,t.jsx)(g.T,{keyName:v.o.account.modifyProfile})})}),(0,t.jsx)(p.Zp,{children:(0,t.jsx)(p.Wu,{className:"py-10",children:(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsxs)("div",{className:"animate-pulse text-center",children:[(0,t.jsx)("div",{className:"h-20 w-20 bg-gray-200 rounded-full mx-auto mb-4"}),(0,t.jsx)("div",{className:"h-6 w-40 bg-gray-200 rounded mx-auto mb-2"}),(0,t.jsx)("div",{className:"h-4 w-60 bg-gray-200 rounded mx-auto"})]})})})})]})}):(0,t.jsx)(u.DashboardLayout,{children:(0,t.jsxs)("div",{className:"space-y-6 mx-auto max-w-6xl",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(h.$,{asChild:!0,variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsxs)(n(),{href:"/compte",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:(0,t.jsx)(g.T,{keyName:v.o.common.back})})]})}),(0,t.jsx)("h1",{className:"text-3xl font-bold",children:(0,t.jsx)(g.T,{keyName:v.o.account.modifyProfile})})]}),(0,t.jsxs)(p.Zp,{children:[(0,t.jsx)(p.aR,{children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)(N.eu,{className:"h-16 w-16 flex-shrink-0",children:[(0,t.jsx)(N.BK,{src:"",alt:"Avatar"}),(0,t.jsx)(N.q5,{className:"bg-blue-100 text-blue-700",children:z})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)(p.ZB,{children:[I.firstName," ",I.lastName]}),(0,t.jsx)(p.BT,{className:"mt-2",children:(0,t.jsx)(g.T,{keyName:v.o.account.editPersonalInfo})})]})]})}),(0,t.jsx)(p.Wu,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"firstName",children:(0,t.jsx)(g.T,{keyName:v.o.account.firstName})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)(j.p,{id:"firstName",value:Z.firstName,onChange:e=>er("firstName",e.target.value),className:G?"border-orange-500":""})]}),G&&(0,t.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 text-orange-500"}),(0,t.jsx)("p",{className:"text-sm text-orange-500",children:G})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"lastName",children:(0,t.jsx)(g.T,{keyName:v.o.account.lastName})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)(j.p,{id:"lastName",value:Z.lastName,onChange:e=>er("lastName",e.target.value),className:Q?"border-orange-500":""})]}),Q&&(0,t.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 text-orange-500"}),(0,t.jsx)("p",{className:"text-sm text-orange-500",children:Q})]})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"email",children:(0,t.jsx)(g.T,{keyName:v.o.account.email})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)(j.p,{id:"email",type:"email",value:Z.email,onChange:e=>er("email",e.target.value),className:es?"border-orange-500":""})]}),es&&(0,t.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 text-orange-500"}),(0,t.jsx)("p",{className:"text-sm text-orange-500",children:es})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"phone",children:(0,t.jsx)(g.T,{keyName:v.o.account.phone})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)(j.p,{id:"phone",type:"tel",value:Z.phone,onChange:e=>er("phone",e.target.value)})]})]})]})]})}),(0,t.jsxs)(p.wL,{className:"flex flex-col gap-4 w-full",children:[O&&(0,t.jsxs)(T.Fc,{className:"w-full border-orange-500 bg-orange-50/50 text-orange-800",children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),(0,t.jsx)(T.TN,{children:O})]}),(0,t.jsx)("div",{className:"flex justify-end w-full",children:(0,t.jsxs)(h.$,{onClick:el,className:"flex items-center gap-1",disabled:V||!!es||!!G||!!Q||!Z.email.trim()||!Z.firstName.trim()||!Z.lastName.trim(),children:[J?(0,t.jsx)(x.A,{className:"h-4 w-4"}):null,V?(0,t.jsx)(g.T,{keyName:v.o.common.saving||"Saving..."}):J?(0,t.jsx)(g.T,{keyName:v.o.common.saved}):(0,t.jsx)(g.T,{keyName:v.o.common.saveChanges})]})})]})]})]})})}},50511:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});var t=a(65127);function r(e){let{children:s}=e;return(0,t.jsx)("div",{className:"zaply-page",children:s})}},56904:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>f});var t=a(65127),r=a(6159),l=a(45301),n=a(72842),i=a(93773),c=a(2902),o=a(19318),d=a(86453),m=a(49290),x=a(91303),u=a(98846);let h={INDIVIDUAL:{MONTHLY:"price_1R0CkxElTneJ74CJARjKlS9U",ANNUAL:"price_1REN04ElTneJ74CJVejJzVDH"},FAMILY:{MONTHLY:"price_1R0ClKElTneJ74CJO45trpKh",ANNUAL:"price_1REN0TElTneJ74CJDRnv2MXk"}},p=e=>{let{title:s,price:a,description:r,features:n,annualSavings:c,planId:o,userSubscription:u,isLoadingSubscription:h,isLoading:p,onSubscribe:j,onManageSubscription:f}=e,{language:N}=(0,x.o)();return(0,t.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex-grow",children:[(0,t.jsx)("h3",{className:"text-lg font-bold mb-2",children:s}),(0,t.jsxs)("div",{className:"text-4xl font-bold mb-5 animate-price-fade text-[#212244]",children:[a.split("/")[0],(0,t.jsx)("span",{className:"text-xl font-normal",children:"fr"===N?"/mois":"/month"})]},"price-".concat(a)),c&&(0,t.jsx)("div",{className:"text-green-600 text-xs font-medium mb-2",children:c}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mb-6",children:r})]}),h?(0,t.jsx)(i.$,{disabled:!0,size:"lg",className:"w-full rounded-lg font-medium text-sm py-5 mb-6 group hover:shadow-md transition-all duration-300 bg-brandBlue text-white hover:bg-brandBlue/90",children:(0,t.jsx)(d.T,{keyName:m.o.common.loading})}):u?(0,t.jsx)(i.$,{onClick:f,size:"lg",className:"w-full rounded-lg font-medium text-sm py-5 mb-6 group hover:shadow-md transition-all duration-300 bg-brandBlue text-white hover:bg-brandBlue/90",children:(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.manageSubscription})}):(0,t.jsx)(i.$,{onClick:()=>j(o),disabled:p[o],size:"lg",className:"w-full rounded-lg font-medium text-sm py-5 mb-6 group hover:shadow-md transition-all duration-300 bg-brandBlue text-white hover:bg-brandBlue/90",children:p[o]?(0,t.jsx)(d.T,{keyName:m.o.common.processing}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.choosePlan}),(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-2 h-5 w-5 arrow-icon",children:[(0,t.jsx)("path",{d:"M5 12h14"}),(0,t.jsx)("path",{d:"m12 5 7 7-7 7"})]})]})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h4",{className:"font-bold text-sm text-[#212244]",children:(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.included})}),n.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-4 h-4 mr-2",children:(0,t.jsx)(l.A,{className:"w-full h-full text-brandBlue",strokeWidth:2})}),(0,t.jsx)("span",{className:"text-[#212244] text-sm",children:e})]},s))]})]})};function j(){let[e,s]=(0,r.useState)("monthly"),[a,l]=(0,r.useState)(!1),[i,o]=(0,r.useState)({}),[j,f]=(0,r.useState)(null),[N,g]=(0,r.useState)(!0),{language:v}=(0,x.o)(),y=(0,n.useRouter)(),b=(0,n.useSearchParams)(),w="".concat(window.location.origin,"/account/subscription/success");(0,r.useEffect)(()=>{l(!0)},[]),(0,r.useEffect)(()=>{(async function(){try{let{data:{user:e}}=await u.N.auth.getUser();if(!e){g(!1);return}let{data:s,error:a}=await u.N.from("subscriptions").select("*").eq("user_id",e.id).eq("status","active").single();a&&"PGRST116"!==a.code&&console.error("Error fetching subscription:",a),f(s||null),g(!1)}catch(e){console.error("Error checking subscription status:",e),g(!1)}})()},[]),(0,r.useEffect)(()=>{let e=b.get("selected_plan");e&&!N&&!j&&(k(e),window.history.replaceState({},"","/pricing"))},[b,N,j]);let k=async e=>{console.log("Starting checkout process for plan ID: ".concat(e)),o(s=>({...s,[e]:!0}));try{let{data:{user:s}}=await u.N.auth.getUser();if(!s){y.push("/login?redirect=/pricing&selected_plan=".concat(e));return}let{data:a,error:t}=await u.N.functions.invoke("supabase-functions-create-checkout",{body:{price_id:e,user_id:s.id,return_url:w},headers:{"X-Customer-Email":s.email}});if(t)throw t;if(null==a?void 0:a.url)console.log("Redirecting to Stripe checkout: ".concat(a.url)),window.location.href=a.url;else throw console.error("No checkout URL returned in the response:",a),Error("No checkout URL returned")}catch(e){console.error("Error creating checkout session:",e),alert("Error creating checkout session: ".concat(e instanceof Error?e.message:"Unknown error",". Please try again."))}finally{o(s=>({...s,[e]:!1}))}},T=()=>{y.push("/compte/abonnement")},A=e=>"fr"===v?"".concat(e,"$"):"$".concat(e),C=A("fr"===v?"7,95":"7.95"),S=A("fr"===v?"14,95":"14.95"),P=A("fr"===v?"5,95":"5.95"),E=A("fr"===v?"11,20":"11.20"),_="annual"===e?(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.individual.annualSavings}):void 0,D="annual"===e?(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.family.annualSavings}):void 0;return(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("div",{className:"flex justify-center mb-8",children:(0,t.jsx)(c.S,{period:e,onChange:e=>{s(e)}})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,t.jsx)(p,{title:(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.individual.title}),price:"".concat("monthly"===e?C:P,"/suffix"),description:(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.individual.description}),features:[(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.feature1},"1"),(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.feature2},"2"),(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.feature3},"3"),(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.feature4},"4")],annualSavings:_,planId:"monthly"===e?h.INDIVIDUAL.MONTHLY:h.INDIVIDUAL.ANNUAL,userSubscription:j,isLoadingSubscription:N,isLoading:i,onSubscribe:k,onManageSubscription:T},"individual-".concat(e)),(0,t.jsx)(p,{title:(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.family.title}),price:"".concat("monthly"===e?S:E,"/suffix"),description:(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.family.description}),features:[(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.family.features},"1"),(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.feature2},"2"),(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.feature3},"3"),(0,t.jsx)(d.T,{keyName:m.o.landing.pricing.feature4},"4")],annualSavings:D,planId:"monthly"===e?h.FAMILY.MONTHLY:h.FAMILY.ANNUAL,userSubscription:j,isLoadingSubscription:N,isLoading:i,onSubscribe:k,onManageSubscription:T},"family-".concat(e))]})]})})}function f(){return(0,t.jsx)(o.S,{children:(0,t.jsx)(r.Suspense,{fallback:(0,t.jsx)("div",{className:"flex h-full w-full items-center justify-center",children:(0,t.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"})}),children:(0,t.jsx)(j,{})})})}},63748:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>P});var t=a(65127),r=a(6159),l=a(29081),n=a.n(l),i=a(39556),c=a(60023),o=a(81261),d=a(52975),m=a(98282),x=a(82642),u=a(48544),h=a(71532),p=a(96970),j=a(69934),f=a(86375),N=a(48600),g=a(79071),v=a(46833),y=a(2292),b=a(61867),w=a(90264),k=a(4111),T=a(12391),A=a(62153),C=a(86453),S=a(49290);function P(){let{familyMembers:e}=(0,k.h)(),[s,a]=(0,r.useState)(),[l,P]=(0,r.useState)(""),[E,_]=(0,r.useState)(""),[D,L]=(0,r.useState)(""),[F,B]=(0,r.useState)(""),[M,q]=(0,r.useState)((0,v.o)(new Date)),[$,z]=(0,r.useState)(""),[R,Z]=(0,r.useState)(""),[W,I]=(0,r.useState)(""),[U,J]=(0,r.useState)(null),[H,V]=(0,r.useState)({}),[Y,O]=(0,r.useState)(!1),[X,G]=(0,r.useState)(!1),[K,Q]=(0,r.useState)(!1),[ee,es]=(0,r.useState)(""),{t:ea,language:et}=(0,C.B)(),er=e=>{let s=e.replace(/[^a-zA-Z0-9]/g,"").toUpperCase();(s=s.slice(0,6)).length>3&&(s=s.slice(0,3)+" "+s.slice(3));let a=/^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/.test(s);return s.length>0&&!a?B(ea(S.o.findAppointment.postalFormatWarning)):B(""),s.length>0&&!a&&s.length>=7?L(ea(S.o.findAppointment.invalidPostalFormat)):L(""),s};(0,r.useEffect)(()=>{if($){J(e.find(e=>e.id.toString()===$)||null),Z(""),I("");let s={...H};delete s.healthCardLastDigits,V(s)}else J(null)},[$,e]);let el=()=>{let e={};return s||(e.date=ea(S.o.findAppointment.selectDateError)),l||(e.time=ea(S.o.findAppointment.selectTimeError)),E?D?e.postalCode=D:/^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/.test(E)||(e.postalCode=ea(S.o.findAppointment.invalidPostalError)):e.postalCode=ea(S.o.findAppointment.enterPostalError),$||(e.selectedMember=ea(S.o.findAppointment.selectPersonError)),$&&!(0,g.E)(R)&&(e.healthCardLastDigits=ea(S.o.findAppointment.healthCardDigitsError)),$&&2!==W.length&&(e.cardSequenceNumber=ea(S.o.findAppointment.sequenceNumberError)),V(e),0===Object.keys(e).length};async function en(e){if(e.preventDefault(),el()){Q(!0),es("");try{let e={request_type:"general_appointment",patient_id:(null==U?void 0:U.supabaseId)||null,request_details:{postalCode:E,date:s?(0,y.GP)(s,"yyyy-MM-dd"):"",time:l,healthCardLastDigits:U?R:"",cardSequenceNumber:U?W:"",patientName:U?"".concat(U.firstName," ").concat(U.lastName):""}},a=await (0,T.hb)(e);a.success?G(!0):"no_subscription"===a.errorType||"authentication_required"===a.errorType?es(ea(S.o.findAppointment.noSubscription)):es(a.error||"Failed to submit appointment request")}catch(e){console.error("Error submitting appointment request:",e),es("An unexpected error occurred. Please try again.")}finally{Q(!1)}}}let ei=e.filter(e=>e.firstName||e.lastName);return(0,t.jsx)(m.DashboardLayout,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.title})}),(0,t.jsx)("p",{className:"text-muted-foreground",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.description})})]}),(0,t.jsx)(x.Zp,{children:X?(0,t.jsx)(x.Wu,{children:(0,t.jsxs)("div",{className:"p-8 flex flex-col items-center justify-center space-y-4",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-green-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,t.jsx)("h3",{className:"text-xl font-medium text-center",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.thankYou})}),(0,t.jsx)("p",{className:"text-muted-foreground text-center max-w-md",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.confirmationMessage})}),(0,t.jsx)("div",{className:"flex gap-3 mt-2",children:(0,t.jsx)(u.$,{variant:"secondary",asChild:!0,children:(0,t.jsx)(n(),{href:"/mes-rendez-vous",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.viewRequests})})})}),(0,t.jsx)(u.$,{variant:"outline",onClick:()=>{G(!1),a(void 0),P(""),_(""),z(""),Z(""),I(""),V({})},className:"mt-4",children:(0,t.jsx)(C.T,{keyName:S.o.common.newRequest})})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(x.aR,{children:[(0,t.jsx)(x.ZB,{children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.searchCriteria})}),(0,t.jsx)(x.BT,{children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.requiredFields})})]}),(0,t.jsx)(x.Wu,{children:(0,t.jsxs)("form",{onSubmit:en,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"family-member",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.appointmentFor})}),(0,t.jsxs)(N.l6,{value:$,onValueChange:e=>{if(z(e),H.selectedMember){let e={...H};delete e.selectedMember,V(e)}},children:[(0,t.jsx)(N.bq,{className:(0,g.cn)("w-full",H.selectedMember&&"border-red-500"),children:(0,t.jsx)(N.yv,{placeholder:ea(S.o.findAppointment.selectPerson)})}),(0,t.jsx)(N.gC,{children:ei.map(e=>(0,t.jsx)(N.eb,{value:e.id.toString(),children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center",children:(0,t.jsx)(i.A,{className:"h-3 w-3 text-blue-700"})}),(0,t.jsxs)("span",{children:[e.firstName," ",e.lastName]})]})},e.id))})]}),H.selectedMember&&(0,t.jsx)("p",{className:"text-xs text-red-500",children:H.selectedMember}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground flex items-center gap-1 mt-1",children:[(0,t.jsx)(c.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:"fr"===et?(0,t.jsxs)(t.Fragment,{children:["Les personnes dans cette liste sont g\xe9r\xe9es dans la section ",(0,t.jsx)(n(),{href:"/compte/utilisateurs",className:"text-primary hover:underline",children:'"G\xe9rer les utilisateurs du compte"'})]}):(0,t.jsxs)(t.Fragment,{children:["The people listed here are managed in the ",(0,t.jsx)(n(),{href:"/compte/utilisateurs",className:"text-primary hover:underline",children:'"Manage account users"'})," section"]})})]})]}),U&&(0,t.jsxs)("div",{className:"space-y-4 p-4 border rounded-md bg-muted/40",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)("div",{className:"h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center",children:(0,t.jsx)(i.A,{className:"h-3 w-3 text-blue-700"})}),(0,t.jsxs)("h3",{className:"font-medium",children:[(0,t.jsx)(C.T,{keyName:S.o.findAppointment.healthCardOf})," ",U.firstName," ",U.lastName]})]}),(0,t.jsxs)("div",{className:"grid gap-6 sm:grid-cols-2",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"health-card-last-digits",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.lastDigits})}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)("div",{className:"bg-muted rounded px-2 py-1.5 font-mono text-sm border",children:U.healthCard}),(0,t.jsx)(h.p,{id:"health-card-last-digits",placeholder:"xxxx-xxxx",value:R,onChange:e=>{let s=(0,g.d)(e.target.value,8);Z(s),H.healthCardLastDigits&&(0,g.E)(s)&&V({...H,healthCardLastDigits:""})},required:!!$,className:"flex-grow font-mono ".concat(H.healthCardLastDigits?"border-red-500":R.length>0&&R.length<8?"border-orange-500":"")})]}),H.healthCardLastDigits?(0,t.jsx)("p",{className:"text-xs text-red-500",children:H.healthCardLastDigits}):R.length>0&&R.length<8&&(0,t.jsx)("p",{className:"text-xs text-orange-500",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.enterEightDigits})}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.format})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p.J,{htmlFor:"sequence-number",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.sequenceNumber})}),(0,t.jsxs)(f.AM,{children:[(0,t.jsx)(f.Wv,{asChild:!0,children:(0,t.jsxs)(u.$,{variant:"ghost",size:"icon",className:"h-6 w-6 p-0 rounded-md",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"}),(0,t.jsx)("span",{className:"sr-only",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.sequenceInfo})})]})}),(0,t.jsx)(f.hl,{className:"p-0 max-w-[450px] w-auto border-0 bg-transparent shadow-none",children:(0,t.jsx)("div",{className:"overflow-hidden rounded-lg border bg-background shadow-lg",children:(0,t.jsx)("div",{className:"p-2",children:(0,t.jsx)(A.default,{src:"/ramq.png",alt:"Carte RAMQ",width:400,height:250,className:"rounded-md"})})})})]})]}),(0,t.jsx)(h.p,{id:"sequence-number",placeholder:"01-99",value:W,onChange:e=>{I(e.target.value.replace(/\D/g,"").substring(0,2))},required:!0,className:"font-mono ".concat(H.cardSequenceNumber?"border-red-500":"")}),H.cardSequenceNumber?(0,t.jsx)("p",{className:"text-xs text-red-500",children:H.cardSequenceNumber}):(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.enterTwoDigits})})]})]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(p.J,{htmlFor:"postal-code",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.postalCode})}),(0,t.jsx)(h.p,{id:"postal-code",placeholder:ea(S.o.findAppointment.postalExample),value:E,onChange:e=>{_(er(e.target.value))},required:!0,maxLength:7,className:H.postalCode?"border-red-500":F?"border-orange-500":""}),H.postalCode?(0,t.jsx)("p",{className:"text-sm text-red-500 mt-1",children:H.postalCode}):F?(0,t.jsx)("p",{className:"text-xs text-orange-500",children:F}):null,(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.postalCodeDescription})})]}),(0,t.jsxs)("div",{className:"grid gap-6 sm:grid-cols-2",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"date",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.fromDate})}),(0,t.jsxs)(f.AM,{children:[(0,t.jsx)(f.Wv,{asChild:!0,children:(0,t.jsxs)(u.$,{id:"date",variant:"outline",className:(0,g.cn)("w-full justify-start text-left font-normal",!s&&"text-muted-foreground",H.date&&"border-red-500"),children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4"}),s?(0,y.GP)(s,"PPP",{locale:w.fr}):ea(S.o.findAppointment.selectDate)]})}),(0,t.jsx)(f.hl,{className:"w-auto p-0",children:(0,t.jsx)(j.V,{mode:"single",selected:s,onSelect:e=>{if(a(e),H.date){let e={...H};delete e.date,V(e)}},initialFocus:!0,locale:w.fr,disabled:e=>(0,b.Y)(e,M),fromDate:M})})]}),H.date&&(0,t.jsx)("p",{className:"text-xs text-red-500",children:H.date})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"time",children:(0,t.jsx)(C.T,{keyName:S.o.findAppointment.appointmentTime})}),(0,t.jsxs)(N.l6,{value:l,onValueChange:e=>{if(P(e),H.time){let e={...H};delete e.time,V(e)}},children:[(0,t.jsx)(N.bq,{className:(0,g.cn)("w-full",H.time&&"border-red-500"),children:(0,t.jsx)(N.yv,{placeholder:ea(S.o.findAppointment.chooseTime),children:l?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4"}),l]}):ea(S.o.findAppointment.chooseTime)})}),(0,t.jsxs)(N.gC,{children:[(0,t.jsx)(N.eb,{value:ea(S.o.findAppointment.asap),children:ea(S.o.findAppointment.asap)}),(0,t.jsx)(N.eb,{value:ea(S.o.findAppointment.morning),children:ea(S.o.findAppointment.morning)}),(0,t.jsx)(N.eb,{value:ea(S.o.findAppointment.afternoon),children:ea(S.o.findAppointment.afternoon)}),(0,t.jsx)(N.eb,{value:ea(S.o.findAppointment.evening),children:ea(S.o.findAppointment.evening)})]})]}),H.time&&(0,t.jsx)("p",{className:"text-xs text-red-500",children:H.time})]})]}),ee&&(0,t.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm mb-4",children:(0,t.jsx)("div",{dangerouslySetInnerHTML:{__html:ee}})}),(0,t.jsx)(u.$,{type:"submit",className:"w-full sm:w-auto",size:"lg",disabled:!(()=>{let e=/^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/.test(E);return!!(s&&l&&e&&$&&(!$||(0,g.E)(R)&&2===W.length))})()||K,children:K?(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,t.jsx)(C.T,{keyName:S.o.common.submitting})]}):(0,t.jsx)(C.T,{keyName:S.o.findAppointment.submitRequest})})]})})]})})]})})}},75824:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>w});var t=a(65127),r=a(6159),l=a(30096),n=a(60023),i=a(1657),c=a(52981),o=a(52975),d=a(98282),m=a(82642),x=a(48544),u=a(52033),h=a(86832),p=a(42404),j=a(2292),f=a(90264),N=a(27176),g=a(86453),v=a(49290),y=a(5230);function b(e){let{type:s,pagination:a}=e,{goToPage:r,fetchNextPage:l,fetchPreviousPage:n}=(0,y.yk)(),{currentPage:i,totalPages:c,hasMore:o}=a;return c<=1?null:(0,t.jsx)(N.dK,{className:"mt-4",children:(0,t.jsxs)(N.Iu,{children:[(0,t.jsx)(N.cU,{children:(0,t.jsx)(N.Eb,{onClick:()=>n(s),className:i<=1?"pointer-events-none opacity-50":"cursor-pointer"})}),(()=>{let e=[];if(c<=5)for(let s=1;s<=c;s++)e.push(s);else{e.push(1);let s=Math.max(2,i-1),a=Math.min(c-1,i+1);i<=2?a=Math.min(c-1,4):i>=c-1&&(s=Math.max(2,c-3)),s>2&&e.push("ellipsis");for(let t=s;t<=a;t++)e.push(t);a<c-1&&e.push("ellipsis"),c>1&&e.push(c)}return e})().map((e,a)=>"ellipsis"===e?(0,t.jsx)(N.cU,{children:(0,t.jsx)(N.M_,{})},"ellipsis-".concat(a)):(0,t.jsx)(N.cU,{children:(0,t.jsx)(N.n$,{isActive:e===i,onClick:()=>r(s,e),children:e})},e)),(0,t.jsx)(N.cU,{children:(0,t.jsx)(N.WA,{onClick:()=>l(s),className:o?"cursor-pointer":"pointer-events-none opacity-50"})})]})})}function w(){let[e,s]=(0,r.useState)("tous"),[a,N]=(0,r.useState)(null),{t:w,language:k}=(0,g.B)(),{appointmentRequests:T,completedAppointments:A,cancelledAppointments:C,requestsPagination:S,completedPagination:P,cancelledPagination:E,fetchAppointmentRequests:_,fetchCompletedAppointments:D,fetchCancelledAppointments:L,fetchAllAppointmentHistory:F,cancelAppointmentRequest:B,isLoading:M,error:q}=(0,y.yk)();(0,r.useEffect)(()=>{F()},[F]);let $=()=>"tous"===e?[...T,...A.map(e=>e.appointment_request),...C.map(e=>e.appointment_request)]:"en-cours"===e?T:"completes"===e?A.map(e=>e.appointment_request):"annules"===e?C.map(e=>e.appointment_request):[],z=async e=>{try{let s=await B(e);s.success?(F(),N(null)):console.error("Failed to cancel appointment:",s.error)}catch(e){console.error("Error cancelling appointment:",e)}};return(0,t.jsx)(d.DashboardLayout,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:(0,t.jsx)(g.T,{keyName:v.o.appointments.title})}),(0,t.jsx)("p",{className:"text-muted-foreground mt-1.5",children:(0,t.jsx)(g.T,{keyName:v.o.appointments.description})})]}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsxs)(m.aR,{className:"pb-3 flex flex-col gap-4",children:[(0,t.jsx)(m.ZB,{children:(0,t.jsx)(g.T,{keyName:v.o.appointments.requestsTitle})}),(0,t.jsx)(p.tU,{defaultValue:"tous",value:e,onValueChange:e=>{s(e),"en-cours"===e?_({page:1}):"completes"===e?D({page:1}):"annules"===e?L({page:1}):F()},className:"w-full",children:(0,t.jsxs)(p.j7,{className:"w-full sm:w-auto",children:[(0,t.jsx)(p.Xi,{value:"tous",className:"flex-1 sm:flex-initial",children:(0,t.jsx)(g.T,{keyName:v.o.appointments.all})}),(0,t.jsx)(p.Xi,{value:"en-cours",className:"flex-1 sm:flex-initial",children:(0,t.jsx)(g.T,{keyName:v.o.appointments.inProgress})}),(0,t.jsx)(p.Xi,{value:"completes",className:"flex-1 sm:flex-initial",children:(0,t.jsx)(g.T,{keyName:v.o.appointments.completed})}),(0,t.jsx)(p.Xi,{value:"annules",className:"flex-1 sm:flex-initial",children:"fr"===k?"Annul\xe9s":"Cancelled"})]})})]}),(0,t.jsx)(m.Wu,{children:M?(0,t.jsx)("div",{className:"flex justify-center py-8",children:(0,t.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"})}):q?(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,t.jsx)(l.A,{className:"h-12 w-12 text-destructive opacity-80 mb-3"}),(0,t.jsx)("h3",{className:"font-medium text-lg",children:(0,t.jsx)(g.T,{keyName:v.o.common.error})}),(0,t.jsx)("p",{className:"text-muted-foreground mt-1",children:q.message||(0,t.jsx)(g.T,{keyName:v.o.common.errorMessage})}),(0,t.jsx)(x.$,{onClick:()=>F(),className:"mt-4",children:(0,t.jsx)(g.T,{keyName:v.o.common.retry})})]}):0===$().length?(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,t.jsx)(n.A,{className:"h-12 w-12 text-muted-foreground opacity-20 mb-3"}),(0,t.jsx)("h3",{className:"font-medium text-lg",children:(0,t.jsx)(g.T,{keyName:v.o.appointments.noRequests})}),(0,t.jsx)("p",{className:"text-muted-foreground mt-1",children:"en-cours"===e?(0,t.jsx)(g.T,{keyName:v.o.appointments.noRequestsInProgress}):"completes"===e?(0,t.jsx)(g.T,{keyName:v.o.appointments.noRequestsCompleted}):"annules"===e?w(v.o.appointments.noRequestsCancelled)||("fr"===k?"Vous n'avez aucune demande de rendez-vous annul\xe9e":"You have no cancelled appointment requests"):(0,t.jsx)(g.T,{keyName:v.o.appointments.noRequests})})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"space-y-4",children:$().map(e=>{var s;return(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between p-4 rounded-lg border bg-card text-card-foreground",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h3",{className:"font-medium",children:e.family_member?e.family_member.first_name+" "+e.family_member.last_name:"Patient"}),(0,t.jsx)(u.E,{variant:"completed"===e.status?"outline":"cancelled"===e.status?"destructive":"secondary",className:"ml-2",children:"completed"===e.status?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i.A,{className:"h-3.5 w-3.5 mr-1 text-green-600"}),(0,t.jsx)("span",{children:(0,t.jsx)(g.T,{keyName:v.o.appointments.done})})]}):"cancelled"===e.status?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"h-3.5 w-3.5 mr-1"}),(0,t.jsx)("span",{children:(0,t.jsx)(g.T,{keyName:v.o.appointments.cancelled})})]}):(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"h-3.5 w-3.5 mr-1"}),(0,t.jsx)("span",{children:(0,t.jsx)(g.T,{keyName:v.o.appointments.pending})})]})})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6 text-xs text-muted-foreground",children:[(0,t.jsxs)("span",{children:[(0,t.jsx)(g.T,{keyName:v.o.appointments.requestType}),": ",e.request_type]}),(null===(s=e.request_details)||void 0===s?void 0:s.postalCode)&&(0,t.jsxs)("span",{children:[(0,t.jsx)(g.T,{keyName:v.o.appointments.postalCode}),": ",e.request_details.postalCode]}),(0,t.jsxs)("span",{children:[(0,t.jsx)(g.T,{keyName:v.o.appointments.sentOn}),": ",(0,j.GP)(new Date(e.created_at),"d MMMM yyyy '\xe0' HH:mm",{locale:f.fr})]})]})]}),"pending"===e.status&&(0,t.jsx)("div",{className:"mt-3 sm:mt-0",children:(0,t.jsxs)(h.lG,{open:a===e.id,onOpenChange:s=>N(s?e.id:null),children:[(0,t.jsx)(h.zM,{asChild:!0,children:(0,t.jsxs)(x.$,{variant:"destructive",size:"sm",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-1"}),(0,t.jsx)(g.T,{keyName:v.o.appointments.cancelAppointment})]})}),(0,t.jsxs)(h.Cf,{className:"w-[90%] mx-auto sm:max-w-md p-6 pt-10 rounded-md",children:[(0,t.jsxs)(h.c7,{className:"space-y-3",children:[(0,t.jsx)(h.L3,{className:"text-xl font-bold text-center",children:(0,t.jsx)(g.T,{keyName:v.o.appointments.cancelConfirmation})}),(0,t.jsx)(h.rr,{className:"text-base text-left",children:(0,t.jsx)(g.T,{keyName:v.o.appointments.cancelConfirmationText})})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 w-full pt-6",children:[(0,t.jsx)(x.$,{variant:"outline",className:"w-full order-1 sm:order-none",onClick:()=>N(null),children:(0,t.jsx)(g.T,{keyName:v.o.appointments.noContinue})}),(0,t.jsx)(x.$,{variant:"destructive",className:"w-full order-0 sm:order-none",onClick:()=>z(e.id),children:(0,t.jsx)(g.T,{keyName:v.o.appointments.yesCancel})})]})]})]})})]},e.id)})}),"tous"!==e&&(0,t.jsx)(b,{type:"en-cours"===e?"requests":"completes"===e?"completed":"annules"===e?"cancelled":"requests",pagination:"en-cours"===e?S:"completes"===e?P:"annules"===e?E:S})]})})]})]})})}},76934:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>v});var t=a(65127),r=a(29081),l=a.n(r),n=a(39556),i=a(54867),c=a(3938),o=a(86809),d=a(19318),m=a(82642),x=a(48544),u=a(52033),h=a(86453),p=a(49290),j=a(6159),f=a(95818),N=a(5230);function g(){let{profile:e,subscription:s,pendingAppointments:a,completedAppointments:r,cancelledAppointments:d,fetchAllAppointmentHistory:g,getAppointmentSummary:v,isLoading:y}=(0,N.yk)(),{firstName:b,fullName:w}=(0,f.s)(),{}=(0,h.B)();(0,j.useEffect)(()=>{g()},[g]);let k=v(),{t:T,language:A}=(0,h.B)(),C=(null==s?void 0:s.plan_type)||"individual",S=(null==s?void 0:s.status)==="active";return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:(0,t.jsx)(h.T,{keyName:p.o.home.greeting,params:{firstName:b}})}),(0,t.jsx)("p",{className:"text-muted-foreground",children:(0,t.jsx)(h.T,{keyName:p.o.home.welcome})})]}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsxs)(m.aR,{className:"py-4 flex flex-row items-center justify-between",children:[(0,t.jsx)("div",{children:(0,t.jsx)(m.ZB,{children:(0,t.jsx)(h.T,{keyName:p.o.account.yourAccount})})}),(0,t.jsx)(u.E,{className:S?"bg-green-100 text-green-800 hover:bg-green-100":"bg-yellow-100 text-yellow-800 hover:bg-yellow-100",children:(0,t.jsx)(h.T,{keyName:S?p.o.common.active:p.o.common.inactive})})]}),(0,t.jsx)(m.Wu,{className:"pb-6",children:(0,t.jsx)("div",{className:"flex flex-col md:flex-row items-start md:items-center gap-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,t.jsx)(n.A,{className:"h-5 w-5 text-blue-700"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:(null==e?void 0:e.firstName)?"".concat(e.firstName," ").concat(e.lastName):w}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:(0,t.jsx)(h.T,{keyName:"family"===C?p.o.account.familyPlan:p.o.account.individualPlan})})]})]})})})]}),(0,t.jsxs)("div",{className:"grid gap-4 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3",children:[(0,t.jsxs)(m.Zp,{className:"hover:shadow-md transition-shadow flex flex-col",children:[(0,t.jsx)(m.aR,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-5",children:(0,t.jsx)(i.A,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsx)(m.ZB,{className:"text-base break-words hyphens-auto",children:(0,t.jsx)(h.T,{keyName:p.o.home.findAppointmentTitle})})]})}),(0,t.jsxs)(m.Wu,{className:"p-4 pt-0 flex-1 flex flex-col",children:[(0,t.jsx)(m.BT,{className:"mb-3 flex-1",children:(0,t.jsx)(h.T,{keyName:p.o.home.findAppointmentDesc})}),(0,t.jsx)(x.$,{asChild:!0,className:"w-full mt-auto",children:(0,t.jsx)(l(),{href:"/trouver-rendez-vous",children:(0,t.jsx)(h.T,{keyName:p.o.common.search})})})]})]}),(0,t.jsxs)(m.Zp,{className:"hover:shadow-md transition-shadow flex flex-col",children:[(0,t.jsx)(m.aR,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-5",children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsx)(m.ZB,{className:"text-base break-words hyphens-auto",children:(0,t.jsx)(h.T,{keyName:p.o.nav.appointments})})]})}),(0,t.jsxs)(m.Wu,{className:"p-4 pt-0 flex-1 flex flex-col",children:[(0,t.jsx)(m.BT,{className:"mb-3 flex-1",children:(0,t.jsx)(h.T,{keyName:p.o.home.manageAppointmentsDesc})}),(0,t.jsx)(x.$,{asChild:!0,className:"w-full mt-auto",variant:"default",children:(0,t.jsx)(l(),{href:"/mes-rendez-vous",children:(0,t.jsx)(h.T,{keyName:p.o.home.viewRequests})})})]})]}),(0,t.jsxs)(m.Zp,{className:"hover:shadow-md transition-shadow flex flex-col",children:[(0,t.jsx)(m.aR,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-5",children:(0,t.jsx)(o.A,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsx)(m.ZB,{className:"text-base break-words hyphens-auto",children:"family"===C?(0,t.jsx)(h.T,{keyName:p.o.account.manageUsers}):(0,t.jsx)(h.T,{keyName:p.o.home.manageProfileTitle})})]})}),(0,t.jsxs)(m.Wu,{className:"p-4 pt-0 flex-1 flex flex-col",children:[(0,t.jsx)(m.BT,{className:"mb-3 flex-1",children:"family"===C?(0,t.jsx)(h.T,{keyName:p.o.home.manageUsersDesc}):(0,t.jsx)(h.T,{keyName:p.o.home.manageProfileDesc})}),(0,t.jsx)(x.$,{asChild:!0,className:"w-full mt-auto",variant:"default",children:(0,t.jsx)(l(),{href:"/compte/utilisateurs",children:"family"===C?(0,t.jsx)(h.T,{keyName:p.o.account.manageUsers}):(0,t.jsx)(h.T,{keyName:p.o.home.manageProfileButton})})})]})]})]}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)(m.aR,{className:"py-4",children:(0,t.jsx)(m.ZB,{children:(0,t.jsx)(h.T,{keyName:p.o.appointments.summary})})}),(0,t.jsxs)(m.Wu,{className:"pb-6",children:[y?(0,t.jsx)("div",{className:"flex justify-center py-4",children:(0,t.jsx)("div",{className:"h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-primary"})}):(0,t.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-3",children:[(0,t.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:(0,t.jsx)(h.T,{keyName:p.o.appointments.inProgress})}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:k.counts.in_progress})]}),(0,t.jsxs)("div",{className:"bg-green-50 p-3 rounded-lg",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:(0,t.jsx)(h.T,{keyName:p.o.appointments.completed})}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:k.counts.completed})]}),(0,t.jsxs)("div",{className:"bg-red-50 p-3 rounded-lg",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"fr"===A?"Annul\xe9s":"Cancelled"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:k.counts.cancelled})]})]}),(0,t.jsx)("div",{className:"mt-4 flex justify-end",children:(0,t.jsx)(x.$,{asChild:!0,variant:"outline",size:"sm",children:(0,t.jsx)(l(),{href:"/mes-rendez-vous",children:T(p.o.appointments.viewAll)||("fr"===A?"Voir tous les rendez-vous":"View all appointments")})})})]})]})]})}function v(){let{initializeApp:e,isLoading:s}=(0,N.yk)();return(0,j.useEffect)(()=>{e()},[e]),(0,t.jsxs)(d.S,{children:[s&&(0,t.jsx)("div",{className:"fixed inset-0 bg-background/80 z-50 flex items-center justify-center",children:(0,t.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"})}),(0,t.jsx)(j.Suspense,{fallback:(0,t.jsx)("div",{className:"flex h-full w-full items-center justify-center",children:(0,t.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"})}),children:(0,t.jsx)(g,{})})]})}},87778:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>f});var t=a(65127),r=a(6159),l=a(29081),n=a.n(l),i=a(72842),c=a(98846),o=a(48544),d=a(71532),m=a(96970),x=a(86453),u=a(4946),h=a(31635),p=a(39957),j=a(35071);function f(){(0,i.useRouter)();let{t:e,language:s}=(0,x.B)(),{signInWithGoogle:a}=(0,j.A)(),[l,f]=(0,r.useState)(""),[N,g]=(0,r.useState)(""),[v,y]=(0,r.useState)(""),[b,w]=(0,r.useState)(""),[k,T]=(0,r.useState)(!1),[A,C]=(0,r.useState)(null),[S,P]=(0,r.useState)(!1),[E,_]=(0,r.useState)(!1),[D,L]=(0,r.useState)(null),F=async a=>{if(a.preventDefault(),P(!0),C(null),L(null),!k){C(e("auth.errors.agreeTerms")),P(!1);return}if(!v.trim()){C(e("account.firstNameRequired")),P(!1);return}if(!b.trim()){C(e("account.lastNameRequired")),P(!1);return}try{var t,r;let{data:a,error:n}=await c.N.auth.signUp({email:l,password:N,options:{data:{first_name:v,last_name:b,language:s}}});if(n)throw n;(null==a?void 0:null===(r=a.user)||void 0===r?void 0:null===(t=r.identities)||void 0===t?void 0:t.length)===0?L(e("auth.emailInUse")):L(e("auth.confirmEmail"))}catch(s){console.error("Error signing up:",s),C(s.message||e("auth.errors.generic"))}finally{P(!1)}},B=async()=>{_(!0),C(null);try{await a()}catch(s){console.error("Error signing up with Google:",s),C(e("auth.errors.socialAuthFailed")),_(!1)}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"space-y-2 text-center",children:(0,t.jsx)("h1",{className:"text-2xl font-bold",children:e("auth.createAccount")})}),A&&(0,t.jsx)(u.Fc,{variant:"destructive",children:(0,t.jsx)(u.TN,{children:A})}),D&&(0,t.jsx)(u.Fc,{children:(0,t.jsx)(u.TN,{children:D})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(o.$,{type:"button",variant:"outline",className:"w-full flex items-center justify-center gap-2",onClick:B,disabled:E,children:[E?(0,t.jsx)("span",{className:"w-5 h-5 border-2 border-gray-300 border-t-primary rounded-full animate-spin"}):(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",height:"24",viewBox:"0 0 24 24",width:"24",children:[(0,t.jsx)("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),(0,t.jsx)("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),(0,t.jsx)("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),(0,t.jsx)("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"}),(0,t.jsx)("path",{d:"M1 1h22v22H1z",fill:"none"})]}),(0,t.jsx)("span",{children:E?e("common.loading"):e("auth.continueWithGoogle")})]}),(0,t.jsxs)("div",{className:"relative flex items-center justify-center mt-6",children:[(0,t.jsx)(p.Separator,{className:"absolute w-full bg-gray-200"}),(0,t.jsx)("span",{className:"relative bg-white px-2 text-xs text-gray-500",children:e("auth.orContinueWith")})]})]}),(0,t.jsxs)("form",{onSubmit:F,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"firstName",children:e("auth.firstName")}),(0,t.jsx)(d.p,{id:"firstName",type:"text",placeholder:"fr"===s?"Pr\xe9nom":"First name",required:!0,value:v,onChange:e=>y(e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"lastName",children:e("auth.lastName")}),(0,t.jsx)(d.p,{id:"lastName",type:"text",placeholder:"fr"===s?"Nom":"Last name",required:!0,value:b,onChange:e=>w(e.target.value)})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"email",children:e("auth.email")}),(0,t.jsx)(d.p,{id:"email",type:"email",placeholder:"<EMAIL>",autoComplete:"email",required:!0,value:l,onChange:e=>f(e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"password",children:e("auth.createPassword")}),(0,t.jsx)(d.p,{id:"password",type:"password",autoComplete:"new-password",required:!0,value:N,onChange:e=>g(e.target.value)}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:e("auth.passwordHint")})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,t.jsx)(h.S,{id:"terms",checked:k,onCheckedChange:e=>T(!0===e)}),(0,t.jsxs)(m.J,{htmlFor:"terms",className:"text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[e("auth.agreeTerms")," ",(0,t.jsx)(n(),{href:"en"===s?"/conditions-utilisation-EN":"/conditions-utilisation",className:"text-primary hover:underline",children:e("auth.termsAndConditions")})]})]}),(0,t.jsx)(o.$,{type:"submit",className:"w-full py-6",disabled:S,children:S?e("common.loading"):e("auth.signUp")})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[e("auth.alreadyHaveAccount")," ",(0,t.jsx)(n(),{href:"/auth/sign-in",className:"text-primary hover:underline",children:e("auth.signIn")})]})})]})}},90319:()=>{},92427:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>S});var t=a(65127),r=a(6159),l=a(29081),n=a.n(l),i=a(11),c=a(10210),o=a(39556),d=a(52981),m=a(4906),x=a(27896),u=a(59974),h=a(98282),p=a(48544),j=a(82642),f=a(71532),N=a(96970),g=a(79071),v=a(2292),y=a(90264),b=a(4111),w=a(86453),k=a(49290),T=a(91303),A=a(99302),C=a(5230);function S(){let{familyMembers:e,updateTempFamilyMember:s,toggleEditing:a,saveMemberChanges:l,deleteMember:S,isLoading:P,error:E}=(0,b.h)(),{language:_}=(0,T.o)(),D="fr"===_?y.fr:void 0,[L,F]=(0,r.useState)({}),[B,M]=(0,r.useState)({}),{subscription:q,fetchSubscription:$,user:z,isSubscriptionLoading:R}=(0,C.yk)();(0,r.useEffect)(()=>{(null==z?void 0:z.id)&&$(z.id)},[null==z?void 0:z.id,$]);let Z=(null==q?void 0:q.plan_type)==="family",W=(e,a)=>{s(e,{tempHealthCard:a.toUpperCase().replace(/[^A-Z]/g,"").substring(0,4)})},I=e=>{let s=l(e);M(a=>({...a,[e]:!s})),s||setTimeout(()=>{M(s=>({...s,[e]:!1}))},3e3)},U=e=>{s(e,{tempBirthDate:void 0}),F(s=>({...s,[e]:(s[e]||0)+1})),M(s=>({...s,[e]:!1})),S(e)};return(0,t.jsx)(h.DashboardLayout,{children:(0,t.jsx)("div",{className:"space-y-6 mx-auto max-w-6xl",children:P||R?(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)(i.A,{className:"h-8 w-8 animate-spin text-blue-500"}),(0,t.jsx)("span",{className:"ml-2 text-lg",children:(0,t.jsx)(w.T,{keyName:k.o.common.loading})})]}):E?(0,t.jsxs)("div",{className:"p-4 border border-red-200 rounded-md bg-red-50 text-red-700",children:[(0,t.jsxs)("p",{children:["Error: ",E.message]}),(0,t.jsx)("p",{children:(0,t.jsx)(w.T,{keyName:k.o.errors.tryAgainLater})})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p.$,{asChild:!0,variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsxs)(n(),{href:"/compte",children:[(0,t.jsx)(c.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:(0,t.jsx)(w.T,{keyName:k.o.common.back})})]})}),(0,t.jsx)("h1",{className:"text-3xl font-bold",children:(0,t.jsx)(w.T,{keyName:Z?k.o.users.manageAccountUsers:k.o.users.manageProfile})})]}),(0,t.jsxs)(j.Zp,{children:[(0,t.jsxs)(j.aR,{children:[(0,t.jsx)(j.ZB,{children:(0,t.jsx)(w.T,{keyName:Z?k.o.users.familyMembers:k.o.users.userProfile})}),(0,t.jsx)(j.BT,{children:(0,t.jsx)(w.T,{keyName:Z?k.o.users.familyMembersDescription:k.o.users.userProfileDescription})})]}),(0,t.jsx)(j.Wu,{children:(0,t.jsx)("div",{className:"space-y-5",children:(Z?5===e.length?e:[void 0,void 0,void 0,void 0,void 0].map((s,a)=>e.find(e=>e.id===a+1)||{id:a+1,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1}):[e[0]||{id:1,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1}]).map(e=>(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0",children:(0,t.jsx)(o.A,{className:"h-5 w-5 text-blue-700"})}),(0,t.jsx)("div",{children:e.firstName||e.lastName?(0,t.jsx)("h3",{className:"font-medium",children:"".concat(e.firstName," ").concat(e.lastName)}):(0,t.jsx)("h3",{className:"font-medium text-muted-foreground",children:(0,t.jsx)(w.T,{keyName:Z?k.o.users.addMember:k.o.users.addYourInfo})})})]}),(0,t.jsx)(p.$,{variant:"ghost",size:"sm",onClick:()=>a(e.id),className:"flex items-center gap-1.5",children:e.editing?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:(0,t.jsx)(w.T,{keyName:k.o.users.cancel})})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:(0,t.jsx)(w.T,{keyName:k.o.users.edit})})]})})]}),e.editing?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(N.J,{htmlFor:"firstName-".concat(e.id),children:(0,t.jsx)(w.T,{keyName:k.o.users.firstName})}),(0,t.jsx)(f.p,{id:"firstName-".concat(e.id),value:e.tempFirstName,onChange:a=>s(e.id,{tempFirstName:a.target.value}),className:B[e.id]&&!e.tempFirstName?"border-red-500 focus-visible:ring-red-500":""})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(N.J,{htmlFor:"lastName-".concat(e.id),children:(0,t.jsx)(w.T,{keyName:k.o.users.lastName})}),(0,t.jsx)(f.p,{id:"lastName-".concat(e.id),value:e.tempLastName,onChange:a=>s(e.id,{tempLastName:a.target.value}),className:B[e.id]&&!e.tempLastName?"border-red-500 focus-visible:ring-red-500":""})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(N.J,{htmlFor:"healthCard-".concat(e.id),children:(0,t.jsx)(w.T,{keyName:k.o.users.healthCardPrefix})}),(0,t.jsx)(f.p,{id:"healthCard-".concat(e.id),value:e.tempHealthCard,onChange:s=>W(e.id,s.target.value),maxLength:4,className:(0,g.cn)("uppercase",B[e.id]&&(!e.tempHealthCard||e.tempHealthCard.length<4)?"border-red-500 focus-visible:ring-red-500":"")}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:(0,t.jsx)(w.T,{keyName:k.o.users.healthCardDescription})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(N.J,{htmlFor:"birthDate-".concat(e.id),children:(0,t.jsx)(w.T,{keyName:k.o.users.birthDate})}),(0,t.jsx)("div",{className:B[e.id]&&!e.tempBirthDate?"border rounded border-red-500":"",children:(0,t.jsx)(A.u,{date:e.tempBirthDate,onSelect:a=>s(e.id,{tempBirthDate:a}),locale:D,fromYear:1900,toYear:2025},"date-picker-".concat(e.id,"-").concat(L[e.id]||0))})]})]}),B[e.id]&&(0,t.jsx)("div",{className:"text-sm text-red-500 bg-red-50 dark:bg-red-950 p-2 rounded border border-red-200 dark:border-red-800 mb-2",children:(0,t.jsx)(w.T,{keyName:k.o.users.validationError,fallback:"Please enter first name, last name, health card (4 characters), and birth date to save."})}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)(p.$,{variant:"outline",onClick:()=>U(e.id),className:"flex items-center gap-1 border-red-500 text-red-500 hover:bg-red-50 hover:text-red-500 dark:hover:bg-red-950 dark:border-red-400 dark:text-red-400 dark:hover:text-red-400",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(w.T,{keyName:k.o.common.delete})]}),(0,t.jsxs)(p.$,{onClick:()=>I(e.id),className:"flex items-center gap-1",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsx)(w.T,{keyName:k.o.users.save})]})]})]}):e.firstName||e.lastName?(0,t.jsxs)("div",{className:"grid gap-4 grid-cols-1 md:grid-cols-2 text-sm",children:[e.healthCard&&(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:(0,t.jsx)(w.T,{keyName:k.o.users.healthCard})}),(0,t.jsx)("span",{className:"mt-1",children:"TRAF-XXXX-XXXX"})]}),e.birthDate&&(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:(0,t.jsx)(w.T,{keyName:k.o.users.birthDate})}),(0,t.jsx)("span",{className:"mt-1",children:(0,v.GP)(e.birthDate,"PPP",{locale:D})})]})]}):(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:(0,t.jsx)(w.T,{keyName:Z?k.o.users.editMemberPrompt:k.o.users.addYourInfoPrompt})})]},e.id))})})]})]})})})}},93940:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var t=a(65127),r=a(6159),l=a(29081),n=a.n(l),i=a(10210),c=a(9999),o=a(35635),d=a(98282),m=a(48544),x=a(82642),u=a(86453),h=a(49290),p=a(35071),j=a(91303),f=a(28278),N=a(98846),g=a(37595),v=a(7857);let y={"family-annual":{fr:{title:"Plan familiale",price:"134,40$ / an",description:"Ce que vous obtenez:",features:["R\xe9servations illimit\xe9es pour 5 membres de votre famille","Notifications par courriel pour vos rendez-vous"]},en:{title:"Family plan",price:"$134.40 / year",description:"What you get:",features:["Unlimited bookings for 5 family members","Email notifications for your appointments"]}},"individual-annual":{fr:{title:"Plan individuel",price:"71,40$ / an",description:"Ce que vous obtenez:",features:["R\xe9servations illimit\xe9es pour une personne","Notifications par courriel pour vos rendez-vous"]},en:{title:"Individual plan",price:"$71.40 / year",description:"What you get:",features:["Unlimited bookings for one person","Email notifications for your appointments"]}},"family-monthly":{fr:{title:"Plan familiale",price:"14,95$ / mois",description:"Ce que vous obtenez:",features:["R\xe9servations illimit\xe9es pour 5 personnes","Notifications par courriel pour vos rendez-vous"]},en:{title:"Family plan",price:"$14.95 / month",description:"What you get:",features:["Unlimited bookings for 5 family members","Email notifications for your appointments"]}},"individual-monthly":{fr:{title:"Plan individuel",price:"7,95$ / mois",description:"Ce que vous obtenez:",features:["R\xe9servations illimit\xe9es pour une personne","Notifications par courriel pour vos rendez-vous"]},en:{title:"Individual plan",price:"$7.95 / month",description:"What you get:",features:["Unlimited bookings for one person","Email notifications for your appointments"]}}};function b(){var e,s,a;let{user:l}=(0,p.A)(),{language:b}=(0,j.o)(),[w,k]=(0,r.useState)(!1),[T,A]=(0,r.useState)(null),C=(0,f.S)(e=>e.fetchSubscription),S=(0,f.f)(),{markStripeNavigation:P}=(0,g.X)();(0,r.useEffect)(()=>{(null==l?void 0:l.id)&&C(l.id)},[null==l?void 0:l.id,C]);let E=async()=>{k(!0),A(null);try{P();let{data:{session:e}}=await N.N.auth.getSession();console.log("Session status:",{hasSession:!!e,hasToken:!!(null==e?void 0:e.access_token)});let s={"Content-Type":"application/json"};(null==e?void 0:e.access_token)?(s.Authorization="Bearer ".concat(e.access_token),console.log("Added Authorization header")):console.log("No session token available");let a=await fetch("/api/subscription/portal?locale=".concat(b),{method:"GET",credentials:"include",headers:s}),t=await a.json();if(console.log("Portal API response:",{status:a.status,data:t}),!a.ok){if(503===a.status&&"Stripe Customer Portal not configured"===t.error)throw Error("Customer portal is not yet configured. Please contact support for assistance.");if(401===a.status)throw Error("Session expired. Please refresh the page and try again.");throw Error(t.error||t.details||"Failed to create portal session")}window.location.href=t.url}catch(e){console.error("Error creating portal session:",e),A(e instanceof Error?e.message:"Failed to open customer portal")}finally{k(!1)}},_=(e,s)=>"".concat(e,"-").concat(s),D=(()=>{if(!S.hasSubscription||!S.planType||!S.billingPeriod)return null;let e=y[_(S.planType,S.billingPeriod)];return e?e[b]:null})();return S.hasSubscription||S.planType?S.hasSubscription?(0,t.jsx)(v.o,{children:(0,t.jsx)(d.DashboardLayout,{children:(0,t.jsxs)("div",{className:"space-y-6 mx-auto max-w-6xl",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(m.$,{asChild:!0,variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsxs)(n(),{href:"/compte",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:(0,t.jsx)(u.T,{keyName:h.o.common.back})})]})}),(0,t.jsx)("h1",{className:"text-3xl font-bold",children:(0,t.jsx)(u.T,{keyName:h.o.subscription.modifySubscription})})]}),(0,t.jsxs)(x.Zp,{children:[(0,t.jsx)(x.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x.ZB,{children:(null==D?void 0:D.title)||"Subscription Plan"}),(0,t.jsx)(x.BT,{children:(null==D?void 0:D.price)||"Subscription Cost"})]}),(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-blue-700"})})]})}),(0,t.jsx)(x.Wu,{className:"pb-2",children:(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:(null==D?void 0:D.description)||"Benefits"}),(0,t.jsx)("ul",{className:"mt-2 grid gap-2 text-sm",children:(null==D?void 0:D.features.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"h-1.5 w-1.5 rounded-full bg-blue-500"}),(0,t.jsx)("span",{children:e})]},s)))||(0,t.jsxs)("li",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"h-1.5 w-1.5 rounded-full bg-blue-500"}),(0,t.jsx)("span",{children:(0,t.jsx)(u.T,{keyName:h.o.subscription.unlimitedAccess})})]})})]})})}),(0,t.jsxs)(x.wL,{className:"flex flex-col space-y-4 pt-4",children:[S.isCancelled&&(0,t.jsxs)("div",{className:"w-full p-4 bg-orange-50 border border-orange-200 rounded-lg dark:bg-orange-900/20 dark:border-orange-800",children:[(0,t.jsx)("p",{className:"text-orange-800 dark:text-orange-200 text-sm font-medium",children:"fr"===b?"Votre abonnement sera annul\xe9 le ".concat(null===(e=S.currentPeriodEnd)||void 0===e?void 0:e.toLocaleDateString("fr-CA")):"Your subscription will be cancelled on ".concat(null===(s=S.currentPeriodEnd)||void 0===s?void 0:s.toLocaleDateString("en-CA"))}),(0,t.jsx)("p",{className:"text-orange-600 dark:text-orange-300 text-xs mt-1",children:"fr"===b?"Vous conserverez l'acc\xe8s jusqu'\xe0 cette date.":"You will retain access until this date."})]}),T&&(0,t.jsx)("div",{className:"w-full p-4 bg-red-50 border border-red-200 rounded-lg dark:bg-red-900/20 dark:border-red-800",children:(0,t.jsx)("p",{className:"text-red-800 dark:text-red-200 text-sm",children:T})}),(0,t.jsx)("div",{className:"w-full max-w-md mx-auto",children:(0,t.jsx)(m.$,{className:"w-full",onClick:E,disabled:w,children:w?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-b-2 border-white mr-2"}),"fr"===b?"Ouverture...":"Opening..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"fr"===b?"G\xe9rer l'abonnement":"Manage Subscription"]})})}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground text-center max-w-md mx-auto",children:"fr"===b?"Vous serez redirig\xe9 vers le portail client Stripe pour g\xe9rer votre abonnement, modifier votre m\xe9thode de paiement ou annuler votre abonnement.":"You will be redirected to the Stripe customer portal to manage your subscription, update payment methods, or cancel your subscription."})]})]}),(0,t.jsxs)(x.Zp,{children:[(0,t.jsx)(x.aR,{children:(0,t.jsx)(x.ZB,{children:(0,t.jsx)(u.T,{keyName:h.o.subscription.paymentHistory})})}),(0,t.jsx)(x.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"flex justify-between border-b pb-4",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("p",{className:"font-medium",children:[null==D?void 0:D.title," - ","monthly"===S.billingPeriod?"fr"===b?"Mensuel":"Monthly":"fr"===b?"Annuel":"Annual"]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:null===(a=S.currentPeriodStart)||void 0===a?void 0:a.toLocaleDateString("fr"===b?"fr-CA":"en-CA")})]}),(0,t.jsx)("p",{className:"font-medium",children:null==D?void 0:D.price})]})})})]})]})})}):(0,t.jsx)(v.o,{children:(0,t.jsx)(d.DashboardLayout,{children:(0,t.jsxs)("div",{className:"space-y-6 mx-auto max-w-6xl",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(m.$,{asChild:!0,variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsxs)(n(),{href:"/compte",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:(0,t.jsx)(u.T,{keyName:h.o.common.back})})]})}),(0,t.jsx)("h1",{className:"text-3xl font-bold",children:(0,t.jsx)(u.T,{keyName:h.o.subscription.modifySubscription})})]}),(0,t.jsx)(x.Zp,{children:(0,t.jsx)(x.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsxs)("div",{className:"text-center space-y-4",children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"fr"===b?"Aucun abonnement actif trouv\xe9.":"No active subscription found."}),(0,t.jsx)(m.$,{asChild:!0,children:(0,t.jsx)(n(),{href:"/pricing",children:"fr"===b?"Voir les plans":"View Plans"})})]})})})})]})})}):(0,t.jsx)(v.o,{children:(0,t.jsx)(d.DashboardLayout,{children:(0,t.jsxs)("div",{className:"space-y-6 mx-auto max-w-6xl",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(m.$,{asChild:!0,variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsxs)(n(),{href:"/compte",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:(0,t.jsx)(u.T,{keyName:h.o.common.back})})]})}),(0,t.jsx)("h1",{className:"text-3xl font-bold",children:(0,t.jsx)(u.T,{keyName:h.o.subscription.modifySubscription})})]}),(0,t.jsx)(x.Zp,{children:(0,t.jsx)(x.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("p",{className:"text-muted-foreground",children:(0,t.jsx)(u.T,{keyName:h.o.common.loading})})})})})})]})})})}}}]);