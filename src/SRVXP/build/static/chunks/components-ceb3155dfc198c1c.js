"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5179],{2067:(e,t,s)=>{s.d(t,{Navbar:()=>y});var a=s(65127),n=s(29081),r=s.n(n),i=s(6159),o=s(93773),l=s(42761),d=s(39556),c=s(52981),u=s(48268),m=s(19089),x=s(62153),f=s(72842),p=s(86453),h=s(49290),g=s(91303),v=s(35071);function y(){let[e,t]=(0,i.useState)(!1),[s,n]=(0,i.useState)(!1),[y,b]=(0,i.useState)(!1),j=(0,f.usePathname)(),N=(0,f.useRouter)(),w=(0,i.useRef)(null),{language:k,setLanguage:q}=(0,g.o)(),{status:z}=(0,v.A)(),C="authenticated"===z,A={fr:{"/politique-de-confidentialite":"/politique-de-confidentialite","/politique-de-confidentialite-EN":"/politique-de-confidentialite","/conditions-utilisation":"/conditions-utilisation","/conditions-utilisation-EN":"/conditions-utilisation","/conditions-generales-de-vente":"/conditions-generales-de-vente","/conditions-generales-de-vente-EN":"/conditions-generales-de-vente","/foire-aux-questions":"/foire-aux-questions","/foire-aux-questions-EN":"/foire-aux-questions"},en:{"/politique-de-confidentialite":"/politique-de-confidentialite-EN","/politique-de-confidentialite-EN":"/politique-de-confidentialite-EN","/conditions-utilisation":"/conditions-utilisation-EN","/conditions-utilisation-EN":"/conditions-utilisation-EN","/conditions-generales-de-vente":"/conditions-generales-de-vente-EN","/conditions-generales-de-vente-EN":"/conditions-generales-de-vente-EN","/foire-aux-questions":"/foire-aux-questions-EN","/foire-aux-questions-EN":"/foire-aux-questions-EN"}},T=["/foire-aux-questions","/foire-aux-questions-EN","/politique-de-confidentialite","/politique-de-confidentialite-EN","/conditions-utilisation","/conditions-utilisation-EN","/conditions-generales-de-vente","/conditions-generales-de-vente-EN"].includes(j),L=e=>{if(q(e),n(!1),j&&A[e][j]){let t=A[e][j];t!==j&&N.push(t)}};return(0,i.useEffect)(()=>{let e=e=>{w.current&&!w.current.contains(e.target)&&s&&n(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[s]),(0,i.useEffect)(()=>{let a=()=>{window.scrollY>10?b(!0):b(!1),s&&n(!1),e&&t(!1)};return window.addEventListener("scroll",a),()=>{window.removeEventListener("scroll",a)}},[s,e]),(0,i.useEffect)(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="",()=>{document.body.style.overflow=""}),[e]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-full ".concat(T?"bg-white":"bg-[#f8f9fb]"," lg:static lg:relative lg:top-auto lg:z-auto sticky top-0 z-50 ").concat(y?"shadow-md":"shadow-sm lg:shadow-none"," transition-shadow duration-300"),children:(0,a.jsxs)("nav",{className:"container mx-auto flex items-center justify-between py-4 px-4 md:px-6 relative",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(r(),{href:"/",className:"flex items-center group",children:[(0,a.jsx)("div",{className:"w-8 h-8 flex items-center justify-center transition-transform duration-300 group-hover:scale-110",children:(0,a.jsx)(x.default,{src:"/zaply-images/srvxp_logorevised.svg",alt:"Logo",width:32,height:32,priority:!0,suppressHydrationWarning:!0,className:"text-brandBlue fill-current"})}),(0,a.jsx)("span",{className:"ml-4 text-lg sm:text-xl font-bold text-[#212242]",children:(0,a.jsx)(p.T,{keyName:h.o.landing.navbar.title})})]})}),(0,a.jsxs)("div",{className:"hidden lg:flex items-center gap-4",children:[(0,a.jsxs)("div",{ref:w,className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>n(!s),className:"flex items-center gap-1 font-normal text-base text-[#4d5562] hover:text-primary transition-all duration-300 hover:scale-105","aria-label":"Toggle language",children:[(0,a.jsx)("span",{children:"fr"===k?"FR":"EN"}),(0,a.jsx)(l.A,{className:"w-4 h-4 transition-transform duration-300 ".concat(s?"rotate-180":""),strokeWidth:1.5})]}),s&&(0,a.jsxs)("div",{className:"absolute top-full left-0 mt-1 bg-white shadow-md rounded-md py-2 min-w-[150px]",children:[(0,a.jsx)("button",{onClick:()=>L("fr"),className:"w-full text-left px-4 py-2 text-base ".concat("fr"===k?"text-primary":"text-[#4d5562]"," hover:bg-gray-50 transition-colors duration-200"),children:"Fran\xe7ais"}),(0,a.jsx)("button",{onClick:()=>L("en"),className:"w-full text-left px-4 py-2 text-base ".concat("en"===k?"text-primary":"text-[#4d5562]"," hover:bg-gray-50 transition-colors duration-200"),children:"English"})]})]}),!C&&(0,a.jsx)(r(),{href:"/auth/sign-in",className:"font-normal text-base text-[#4d5562] hover:text-primary transition-all duration-300 px-4 py-2 hover:scale-105",children:(0,a.jsx)(p.T,{keyName:h.o.landing.navbar.signIn})}),(0,a.jsx)(o.$,{asChild:!0,size:"lg",className:"rounded-lg font-medium text-base h-auto py-2 px-4 transition-transform duration-300 hover:scale-105 shadow-sm hover:shadow",children:C?(0,a.jsx)(r(),{href:"/dashboard",children:(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"fr"===k?"Mon compte":"My account"})]})}):(0,a.jsx)(r(),{href:"/auth/sign-up",children:(0,a.jsx)(p.T,{keyName:h.o.landing.navbar.signUp})})})]}),(0,a.jsx)("div",{className:"lg:hidden flex items-center",children:(0,a.jsx)("button",{className:"flex items-center justify-center w-10 h-10 text-[#212244] hover:bg-gray-100 rounded-full transition-all duration-300",onClick:()=>t(!e),"aria-label":"Toggle menu",children:e?(0,a.jsx)(c.A,{size:24,className:"text-[#212244] transition-transform duration-300"}):(0,a.jsx)(u.A,{className:"h-5 w-5 text-[#212244] transition-transform duration-300"})})})]})}),e&&(0,a.jsxs)("div",{className:"fixed inset-0 z-50 lg:hidden overflow-hidden",children:[(0,a.jsx)("div",{className:"".concat(T?"bg-white":"bg-[#f8f9fb]"," h-[75vh] overflow-auto"),children:(0,a.jsxs)("div",{className:"container mx-auto px-4 pt-4 pb-8 h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 flex items-center justify-center",children:(0,a.jsx)(x.default,{src:"/zaply-images/srvxp_logorevised.svg",alt:"Logo",width:32,height:32,className:"text-brandBlue fill-current"})}),(0,a.jsx)("span",{className:"ml-4 text-lg sm:text-xl font-bold text-[#212242]",children:(0,a.jsx)(p.T,{keyName:h.o.landing.navbar.title})})]}),(0,a.jsx)("button",{onClick:()=>t(!1),className:"flex items-center justify-center w-10 h-10","aria-label":"Close menu",children:(0,a.jsx)(c.A,{size:24,className:"text-[#212244]"})})]}),(0,a.jsx)("div",{className:"mt-10 flex-grow",children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)(r(),{href:"#FeaturesSection",className:"block text-xl font-normal text-[#212242] hover:text-brandBlue transition-colors duration-200 font-sans",onClick:()=>t(!1),children:(0,a.jsx)(p.T,{keyName:h.o.landing.navbar.service})}),(0,a.jsx)(r(),{href:"#tarifs",className:"block text-xl font-normal text-[#212242] hover:text-brandBlue transition-colors duration-200 font-sans",onClick:()=>t(!1),children:(0,a.jsx)(p.T,{keyName:h.o.landing.navbar.pricing})}),(0,a.jsx)(r(),{href:"/foire-aux-questions",className:"block text-xl font-normal text-[#212242] hover:text-brandBlue transition-colors duration-200 font-sans",onClick:()=>t(!1),children:(0,a.jsx)(p.T,{keyName:h.o.landing.navbar.faq})}),(0,a.jsx)("button",{className:"block text-xl font-normal text-[#212242] hover:text-brandBlue transition-colors duration-200 text-left font-sans",onClick:()=>{L("fr"===k?"en":"fr"),t(!1)},children:"fr"===k?"English":"Fran\xe7ais"})]})}),(0,a.jsx)("div",{className:"flex-grow"}),(0,a.jsx)("hr",{className:"border-gray-300 border-t-[1px] w-full my-6"}),(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[!C&&(0,a.jsxs)(r(),{href:"/auth/sign-in",className:"w-full flex items-center justify-center gap-2 py-3 px-4 border border-gray-200 rounded-lg bg-white text-[#212244] hover:bg-gray-50 transition-all duration-200 font-sans",onClick:()=>t(!1),children:[(0,a.jsx)(m.A,{className:"w-5 h-5",strokeWidth:1.5}),(0,a.jsx)("span",{className:"text-lg",children:(0,a.jsx)(p.T,{keyName:h.o.landing.navbar.signIn})})]}),(0,a.jsxs)(r(),{href:C?"/dashboard":"/auth/sign-up",className:"w-full flex items-center justify-center gap-2 py-3 px-4 rounded-lg bg-brandBlue text-white transition-all duration-200 hover:bg-brandBlue/90 font-sans",onClick:()=>t(!1),children:[(0,a.jsx)(d.A,{className:"w-5 h-5",strokeWidth:1.5}),(0,a.jsx)("span",{className:"text-lg",children:C?"fr"===k?"Mon compte":"My account":(0,a.jsx)(p.T,{keyName:h.o.landing.navbar.signUp})})]})]})]})}),(0,a.jsx)("div",{className:"h-[25vh] bg-black/50 backdrop-blur-sm cursor-pointer",onClick:()=>t(!1),"aria-label":"Close menu"})]})]})}},2902:(e,t,s)=>{s.d(t,{S:()=>c});var a=s(65127),n=s(70992),r=s(74522),i=s(60410),o=s(6159);function l(e){let{children:t,defaultValue:s,onValueChange:l,className:d,transition:c,enableHover:u=!1,textColorActive:m="white",textColorInactive:x="#9CA3AF"}=e,[f,p]=(0,o.useState)(s||null),h=(0,o.useId)(),g=e=>{p(e),l&&l(e)};return(0,o.useEffect)(()=>{void 0!==s&&p(s)},[s]),o.Children.map(t,(e,t)=>{let l=e.props["data-id"],p=f===l,v=u?{onMouseEnter:()=>g(l),onMouseLeave:()=>g(null)}:{onClick:()=>g(l)};return(0,o.cloneElement)(e,{key:t,className:(0,n.cn)("relative inline-flex",e.props.className),"data-checked":p?"true":"false",...v},(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.N,{initial:!1,children:p&&(0,a.jsx)(i.P.div,{layoutId:"background-".concat(h),className:(0,n.cn)("absolute inset-0",d),transition:c,initial:{opacity:+!!s},animate:{opacity:1},exit:{opacity:0}})}),(0,a.jsx)("div",{className:"z-10",style:{color:p?m:x},children:e.props.children})]}))})}var d=s(91303);function c(e){let{period:t,onChange:s}=e,{language:n}=(0,d.o)(),r="fr"===n?"Mensuel":"Monthly",i="fr"===n?"Annuel -30%":"Yearly -30%",o={[r]:"monthly",[i]:"annual"};return(0,a.jsx)("div",{className:"relative rounded-lg bg-gray-100 p-1 flex",style:{width:"".concat(261,"px")},children:(0,a.jsx)(l,{defaultValue:"monthly"===t?r:i,className:"rounded-lg bg-white border border-gray-100 shadow-sm",transition:{ease:"easeInOut",duration:.2},onValueChange:e=>{e&&(e===r||e===i)&&s(o[e])},textColorActive:"#4B5563",textColorInactive:"#9CA3AF",children:[r,i].map((e,s)=>{let r=t===o[e];return(0,a.jsx)("button",{"data-id":e,type:"button","aria-label":"".concat(e," pricing"),"aria-pressed":r,className:"inline-flex items-center justify-center text-center px-2 py-2 text-sm font-medium transition-transform active:scale-[0.98] rounded-lg",style:{width:"".concat(126.5,"px")},children:e===i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{children:"fr"===n?"Annuel":"Yearly"}),(0,a.jsx)("span",{style:{color:r?"#16a349":"#9CA3AF"},children:" -30%"})]}):e},s)})})})}},4946:(e,t,s)=>{s.d(t,{Fc:()=>l,TN:()=>d});var a=s(65127),n=s(6159),r=s(57767),i=s(79071);let o=(0,r.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=n.forwardRef((e,t)=>{let{className:s,variant:n,...r}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,i.cn)(o({variant:n}),s),...r})});l.displayName="Alert",n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",s),...n})}).displayName="AlertTitle";let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",s),...n})});d.displayName="AlertDescription"},5326:(e,t,s)=>{s.d(t,{CtaSection2:()=>m});var a=s(65127),n=s(93773),r=s(29081),i=s.n(r),o=s(94417),l=s(6159),d=s(86453),c=s(91303),u=s(49290);function m(){let{language:e}=(0,c.o)(),{ref:t,isIntersecting:s}=(0,o.w)({threshold:.2,rootMargin:"-50px"}),[r,m]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{m(!0)},[]),(0,a.jsx)("section",{className:"bg-white text-[#212244] w-full pt-0 pb-16 overflow-hidden",ref:t,children:(0,a.jsx)("div",{className:"max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"bg-[#f8f9fb] rounded-xl py-16 px-8 sm:px-8 md:px-16 w-full max-w-6xl",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row items-center gap-8 lg:gap-12",children:[(0,a.jsxs)("div",{className:"w-full lg:w-1/2 text-left",children:[(0,a.jsx)("h2",{className:"text-[42px] sm:text-[52px] m:text-[52px] lg:text-[56px] xl:text-[60px] leading-none font-bold text-[#212244] mb-8 ".concat(r&&s?"animate-fade-in-up-1":"opacity-0"),children:"fr"===e?(0,a.jsxs)(a.Fragment,{children:["Ne tardez pas \xe0 consulter ",(0,a.jsx)("span",{className:"text-brandBlue",children:"un m\xe9decin."})]}):(0,a.jsxs)(a.Fragment,{children:["Don't wait to see ",(0,a.jsx)("span",{className:"text-brandBlue",children:"a doctor."})]})}),(0,a.jsx)("p",{className:"text-xl font-normal text-gray-600 mb-10 ".concat(r&&s?"animate-fade-in-up-2":"opacity-0"),children:(0,a.jsx)(d.T,{keyName:u.o.landing.cta.subtitle})}),(0,a.jsx)("div",{className:"".concat(r&&s?"animate-fade-in-up-3":"opacity-0"),children:(0,a.jsx)(n.$,{asChild:!0,size:"lg",className:"rounded-md font-medium text-base px-8 py-4 bg-brandBlue text-white hover:bg-brandBlue/90 transition-all duration-300 h-auto hover:shadow-lg group hover:scale-105",children:(0,a.jsxs)(i(),{href:"/auth/sign-up",children:[(0,a.jsx)(d.T,{keyName:u.o.landing.cta.buttonText}),(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-2 h-5 w-5 arrow-icon",children:[(0,a.jsx)("path",{d:"M5 12h14"}),(0,a.jsx)("path",{d:"m12 5 7 7-7 7"})]})]})})})]}),(0,a.jsx)("div",{className:"w-full lg:w-1/2 flex justify-center items-center ".concat(r&&s?"animate-fade-in-up-4":"opacity-0"),children:(0,a.jsx)("img",{src:"/zaply-images/CTAimage.png",alt:u.o.landing.cta.imageAlt,className:"rounded-xl w-full max-w-[450px] h-auto object-contain"})})]})})})})}},7857:(e,t,s)=>{s.d(t,{o:()=>o});var a=s(65127),n=s(6159),r=s(35071),i=s(37595);function o(e){let{children:t}=e,{status:s,refresh:o}=(0,r.A)(),{detectStripeReturn:l}=(0,i.X)(),[d,c]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{(async()=>{if(l()&&"authenticated"!==s){c(!0),console.log("Handling Stripe return, attempting auth restoration...");try{await new Promise(e=>setTimeout(e,1e3)),await o(),await new Promise(e=>setTimeout(e,500))}catch(e){console.error("Error handling Stripe return:",e)}finally{c(!1)}}})()},[l,s,o]),d)?(0,a.jsx)("div",{className:"flex h-screen w-screen items-center justify-center bg-white",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-lg text-gray-600",children:"Restoring your session..."}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Please wait while we verify your authentication."})]})}):(0,a.jsx)(a.Fragment,{children:t})}},10244:(e,t,s)=>{s.d(t,{Footer:()=>u});var a=s(65127),n=s(29081),r=s.n(n),i=s(62153),o=s(17224),l=s(86453),d=s(49290),c=s(91303);function u(){let{language:e}=(0,c.o)();return(0,a.jsx)("footer",{className:"border-t border-gray-200 py-16 bg-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 sm:gap-12",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)(r(),{href:"/",className:"flex items-center mb-6",children:[(0,a.jsx)("div",{className:"w-8 h-8 flex items-center justify-center",children:(0,a.jsx)(i.default,{src:"/zaply-images/srvxp_logorevised.svg",alt:"Logo",width:32,height:32,priority:!0,suppressHydrationWarning:!0,className:"text-brandBlue fill-current"})}),(0,a.jsx)("span",{className:"ml-4 text-xl font-bold text-[#212242]",children:(0,a.jsx)(l.T,{keyName:d.o.landing.navbar.title})})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-6 w-full sm:max-w-[66.7%]",children:(0,a.jsx)(l.T,{keyName:d.o.landing.footer.description})})]}),(0,a.jsx)("div",{className:"lg:justify-self-end",children:(0,a.jsxs)("ul",{className:"space-y-3",children:[(0,a.jsx)("li",{children:(0,a.jsxs)(r(),{href:"mailto:<EMAIL>",className:"text-gray-600 hover:text-primary transition-colors flex items-center",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),(0,a.jsx)(l.T,{keyName:d.o.landing.footer.contactUs})]})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"en"===e?"/politique-de-confidentialite-EN":"/politique-de-confidentialite",className:"text-gray-600 hover:text-primary transition-colors",children:(0,a.jsx)(l.T,{keyName:d.o.landing.footer.privacyPolicy})})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"en"===e?"/conditions-utilisation-EN":"/conditions-utilisation",className:"text-gray-600 hover:text-primary transition-colors",children:(0,a.jsx)(l.T,{keyName:d.o.landing.footer.termsOfUse})})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"en"===e?"/conditions-generales-de-vente-EN":"/conditions-generales-de-vente",className:"text-gray-600 hover:text-primary transition-colors",children:(0,a.jsx)(l.T,{keyName:d.o.landing.footer.termsOfSale})})})]})})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 mt-12 pt-8 flex flex-col sm:flex-row justify-between items-center",children:(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4 sm:mb-0",children:(0,a.jsx)(l.T,{keyName:d.o.landing.footer.copyright})})})]})})}},19318:(e,t,s)=>{s.d(t,{S:()=>a});let a=(0,s(6002).T$)(()=>Promise.all([s.e(5749),s.e(8096),s.e(5179),s.e(5738),s.e(1550)]).then(s.bind(s,98282)).then(e=>({default:e.DashboardLayout})),{ssr:!1,displayName:"DashboardLayout"})},22955:(e,t,s)=>{s.d(t,{FaqFullPage:()=>o});var a=s(65127),n=s(96981),r=s(94417),i=s(6159);function o(){let{ref:e,isIntersecting:t}=(0,r.w)({threshold:.2,rootMargin:"-50px"}),[s,o]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{o(!0)},[]),(0,a.jsx)("section",{className:"py-16 lg:py-24 overflow-hidden",ref:e,children:(0,a.jsxs)("div",{className:"".concat(s&&t?"animate-fade-in-together":"opacity-0"),children:[(0,a.jsx)("div",{className:"text-center mb-16",children:(0,a.jsx)("h1",{className:"text-4xl md:text-[52px] lg:text-[56px] xl:text-[60px] font-bold text-[#212244]",children:"Foire aux questions"})}),(0,a.jsx)("div",{className:"max-w-5xl mx-auto px-2",children:(0,a.jsx)(n.nD,{type:"single",collapsible:!0,children:[{question:"Qu'est-ce que Sans rendez-vous express propose exactement?",answer:"Sans rendez-vous express est un service qui vous aide \xe0 trouver rapidement des rendez-vous m\xe9dicaux dans des cliniques sans rendez-vous pr\xe8s de chez vous. Notre plateforme connecte les patients avec des m\xe9decins disponibles pour des consultations d'ici 24 \xe0 48 heures."},{question:"Quel est le d\xe9lai d'attente pour obtenir un rendez-vous?",answer:"Chez Sans rendez-vous express, nous nous effor\xe7ons de trouver un rendez-vous avec un m\xe9decin le plus rapidement possible pour tous nos utilisateurs. En g\xe9n\xe9ral, la plupart des rendez-vous sont trouv\xe9s en moins de 48 heures. Pour les grandes municipalit\xe9s, la majorit\xe9 des rendez-vous sont trouv\xe9s en moins de 24 heures."},{question:"Quelles sont les informations n\xe9cessaires pour prendre un rendez-vous?",answer:"Pour prendre un rendez-vous, vous devrez fournir vos informations de base (nom, pr\xe9nom, date de naissance), vos coordonn\xe9es (t\xe9l\xe9phone, email) et votre num\xe9ro d'assurance maladie. Ces informations nous permettent de vous trouver le rendez-vous le plus appropri\xe9."},{question:"Comment est-ce que les rendez-vous sont r\xe9serv\xe9s?",answer:"Apr\xe8s avoir soumis votre demande, notre syst\xe8me recherche automatiquement les disponibilit\xe9s dans les cliniques participantes selon vos crit\xe8res. Une fois qu'un rendez-vous correspondant est trouv\xe9, vous recevez une confirmation par email et/ou SMS avec tous les d\xe9tails du rendez-vous."},{question:"Qui peut utiliser ce service?",answer:"Notre service est accessible \xe0 tous les r\xe9sidents du Qu\xe9bec poss\xe9dant une carte d'assurance maladie valide. Nous servons les adultes et les enfants, que ce soit pour des probl\xe8mes de sant\xe9 ponctuels ou des suivis m\xe9dicaux r\xe9guliers."},{question:"Comment choisissez-vous les cliniques?",answer:"Les cliniques sont choisies en fonction du code postal que l'utilisateur a s\xe9lectionn\xe9 pour effectuer sa recherche, ainsi que du p\xe9rim\xe8tre de recherche qu'il est pr\xeat \xe0 parcourir. Notre plateforme utilise ces informations pour identifier les cliniques les plus proches de l'emplacement sp\xe9cifi\xe9 par l'utilisateur. Cela garantit que les options de cliniques pr\xe9sent\xe9es correspondent aux besoins de localisation et de d\xe9placement de chaque utilisateur, offrant ainsi une exp\xe9rience de recherche personnalis\xe9e et pratique."},{question:"Est-ce que je peux choisir une clinique en particulier?",answer:"Malheureusement, l'option de choisir une clinique en particulier n'est pas disponible sur notre plateforme. Les cliniques sont s\xe9lectionn\xe9es en fonction du code postal que vous avez choisi pour effectuer votre recherche, ainsi que du p\xe9rim\xe8tre de recherche que vous \xeates pr\xeat \xe0 parcourir. Cela garantit que les cliniques propos\xe9es correspondent aux crit\xe8res de localisation que vous avez sp\xe9cifi\xe9s"},{question:"Est-ce que je peux changer ma demande de rendez-vous en cours de route?",answer:"Oui, vous pouvez modifier votre demande de rendez-vous en cours de route. Les utilisateurs ont la possibilit\xe9 de modifier ou d'annuler leur demande de rendez-vous sans frais, tant qu'aucun rendez-vous n'a \xe9t\xe9 d\xe9j\xe0 r\xe9serv\xe9. Pour ce faire, les utilisateurs doivent simplement cliquer sur le lien pour modifier ou annuler un rendez-vous qui leur est envoy\xe9 par courriel apr\xe8s avoir soumis leur demande initiale. Cette fonctionnalit\xe9 vous permet de g\xe9rer facilement votre rendez-vous en fonction de vos besoins et de votre emploi du temps."},{question:"Comment faire pour annuler un rendez-vous?",answer:"Pour annuler un rendez-vous, recherchez le courriel de confirmation que vous avez re\xe7u apr\xe8s avoir effectu\xe9 votre demande de rendez-vous et cliquez le lien pour modifier ou annuler votre rendez-vous. Vous pouvez \xe9galement annuler un rendez-vous \xe0 travers le portail client. Si vous rencontrez des difficult\xe9s, n'h\xe9sitez pas \xe0 contacter notre service \xe0 la client\xe8le. Nous serons heureux de vous aider!"},{question:"Est-ce que Sans rendez-vous express propose des rendez-vous d'urgence?",answer:"Bien que nous ne proposons pas de services pour des situations d'urgence m\xe9dicale, notre plateforme permet aux utilisateurs de trouver rapidement des rendez-vous avec des m\xe9decins pour des consultations r\xe9guli\xe8res et des besoins de suivi m\xe9dical. En cas d'urgence m\xe9dicale, nous vous encourageons \xe0 contacter imm\xe9diatement les services d'urgence appropri\xe9s ou \xe0 se rendre \xe0 l'h\xf4pital le plus proche."},{question:"Est-ce que Rendez-vous m\xe9decins Qu\xe9bec propose des consultations en ligne?",answer:"Actuellement, nous ne proposons pas de consultations en ligne sur notre plateforme. Cependant, nous travaillons constamment \xe0 am\xe9liorer nos services et \xe0 explorer de nouvelles options pour r\xe9pondre aux besoins de nos utilisateurs. Restez \xe0 l'\xe9coute pour toute mise \xe0 jour concernant les consultations en ligne sur Sans rendez-vous express."},{question:"Le service ne r\xe9pond pas \xe0 mes attentes.",answer:"Nous sommes d\xe9sol\xe9s d'apprendre que le service ne r\xe9pond pas \xe0 vos attentes. Votre satisfaction est notre priorit\xe9 absolue, et nous souhaitons r\xe9soudre tout probl\xe8me que vous pourriez rencontrer. N'h\xe9sitez pas \xe0 nous contacter directement pour nous faire part de vos pr\xe9occupations et de vos commentaires. Notre \xe9quipe est l\xe0 pour vous aider et pour trouver des solutions qui vous conviennent."},{question:"Comment est-ce que vous prot\xe9gez mes renseignements personnels?",answer:"Chez Rendez-vous m\xe9decins Qu\xe9bec, nous accordons une grande importance \xe0 la protection de vos renseignements personnels. Nous nous engageons \xe0 respecter la confidentialit\xe9 des informations que vous nous confiez. Conform\xe9ment aux lois applicables concernant le r\xe9gime d’assurance maladie du Qu\xe9bec (Loi sur l’assurance maladie, RLRQ c A-29) et la protection des renseignements personnels dans le secteur priv\xe9 (Loi sur la protection des renseignements personnels dans le secteur priv\xe9, RLRQ, c P-39.1), nous avons mis en place des mesures pour garantir la s\xe9curit\xe9 et la confidentialit\xe9 de vos donn\xe9es. Vos renseignements personnels seront supprim\xe9s de notre syst\xe8me sept (7) jours apr\xe8s la prise de rendez-vous, conform\xe9ment \xe0 notre Politique de confidentialit\xe9."}].map((e,t)=>(0,a.jsxs)(n.As,{value:"item-".concat(t),className:"mb-6",children:[(0,a.jsx)(n.$m,{className:"text-left text-lg font-medium",children:e.question}),(0,a.jsx)(n.ub,{className:"text-gray-600 text-lg leading-relaxed",children:e.answer})]},t))})})]})})}},27176:(e,t,s)=>{s.d(t,{Eb:()=>h,Iu:()=>x,M_:()=>v,WA:()=>g,cU:()=>f,dK:()=>m,n$:()=>p});var a=s(65127),n=s(6159),r=s(29081),i=s.n(r),o=s(9432),l=s(14169),d=s(73716),c=s(79071),u=s(48544);let m=e=>{let{className:t,...s}=e;return(0,a.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,c.cn)("mx-auto flex w-full justify-center",t),...s})};m.displayName="Pagination";let x=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("ul",{ref:t,className:(0,c.cn)("flex flex-row items-center gap-1",s),...n})});x.displayName="PaginationContent";let f=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("li",{ref:t,className:(0,c.cn)("",s),...n})});f.displayName="PaginationItem";let p=e=>{let{className:t,isActive:s,size:n="icon",...r}=e;return(0,a.jsx)(i(),{"aria-current":s?"page":void 0,className:(0,c.cn)((0,u.r)({variant:s?"outline":"ghost",size:n}),s&&"pointer-events-none",t),...r})};p.displayName="PaginationLink";let h=e=>{let{className:t,...s}=e;return(0,a.jsxs)(p,{"aria-label":"Go to previous page",size:"default",className:(0,c.cn)("gap-1 pl-2.5",t),...s,children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Previous"})]})};h.displayName="PaginationPrevious";let g=e=>{let{className:t,...s}=e;return(0,a.jsxs)(p,{"aria-label":"Go to next page",size:"default",className:(0,c.cn)("gap-1 pr-2.5",t),...s,children:[(0,a.jsx)("span",{children:"Next"}),(0,a.jsx)(l.A,{className:"h-4 w-4"})]})};g.displayName="PaginationNext";let v=e=>{let{className:t,...s}=e;return(0,a.jsxs)("span",{"aria-hidden":!0,className:(0,c.cn)("flex h-9 w-9 items-center justify-center",t),...s,children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"More pages"})]})};v.displayName="PaginationEllipsis"},31635:(e,t,s)=>{s.d(t,{S:()=>l});var a=s(65127),n=s(6159),r=s(80332),i=s(45301),o=s(79071);let l=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.bL,{ref:t,className:(0,o.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",s),...n,children:(0,a.jsx)(r.C1,{className:(0,o.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})})});l.displayName=r.bL.displayName},38766:(e,t,s)=>{s.d(t,{J:()=>o});var a=s(65127),n=s(48544),r=s(10228),i=s(91303);let o=(0,s(6159).memo)(function(e){let{className:t,variant:s="default"}=e,{language:o,setLanguage:l,isLoading:d}=(0,i.o)();return d?null:"mobile"===s?(0,a.jsxs)("div",{className:"mt-4 border-t pt-4",children:[(0,a.jsx)("p",{className:"mb-2 px-2 text-sm font-medium text-muted-foreground",children:"Langue / Language"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(n.$,{variant:"fr"===o?"secondary":"ghost",size:"sm",className:"flex-1 justify-start gap-2",onClick:()=>l("fr"),children:[(0,a.jsx)(r.A,{className:"h-4 w-4"}),"Fran\xe7ais"]}),(0,a.jsxs)(n.$,{variant:"en"===o?"secondary":"ghost",size:"sm",className:"flex-1 justify-start gap-2",onClick:()=>l("en"),children:[(0,a.jsx)(r.A,{className:"h-4 w-4"}),"English"]})]})]}):(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>{l("fr"===o?"en":"fr")},className:"".concat(t," flex items-center justify-center gap-2 border border-border bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3"),"aria-label":"fr"===o?"Switch to English":"Passer au fran\xe7ais",children:[(0,a.jsx)(r.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-normal",children:"fr"===o?"English":"Fran\xe7ais"})]})})},39957:(e,t,s)=>{s.d(t,{Separator:()=>o});var a=s(65127),n=s(6159),r=s(39246),i=s(79071);let o=n.forwardRef((e,t)=>{let{className:s,orientation:n="horizontal",decorative:o=!0,...l}=e;return(0,a.jsx)(r.b,{ref:t,decorative:o,orientation:n,className:(0,i.cn)("shrink-0 bg-border","horizontal"===n?"h-[1px] w-full":"h-full w-[1px]",s),...l})});o.displayName=r.b.displayName},42404:(e,t,s)=>{s.d(t,{Xi:()=>d,j7:()=>l,tU:()=>o});var a=s(65127),n=s(6159),r=s(13693),i=s(79071);let o=r.bL,l=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.B8,{ref:t,className:(0,i.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",s),...n})});l.displayName=r.B8.displayName;let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.l9,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",s),...n})});d.displayName=r.l9.displayName,n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.UC,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...n})}).displayName=r.UC.displayName},44868:(e,t,s)=>{s.d(t,{HeroSection:()=>m});var a=s(65127),n=s(93773),r=s(29081),i=s.n(r),o=s(62153),l=s(6159),d=s(86453),c=s(91303),u=s(49290);function m(){let{language:e}=(0,c.o)(),[t,s]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{s(!0)},[]),(0,a.jsx)("section",{className:"py-8 sm:py-12 lg:py-16 xl:py-20 overflow-hidden",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row items-center gap-8 sm:gap-12 lg:gap-16",children:[(0,a.jsxs)("div",{className:"w-full lg:w-1/2 space-y-4 sm:space-y-6 xl:space-y-8",children:[(0,a.jsx)("h1",{className:"text-5xl xs:text-4xl sm:text-5xl md:text-[52px] lg:text-[60px] xl:text-[66px] font-bold tracking-tight text-[#212244] leading-none ".concat(t?"animate-fade-in-up-1":"opacity-0"),children:"fr"===e?(0,a.jsxs)(a.Fragment,{children:["Obtenez un rendez-vous avec un m\xe9decin ",(0,a.jsx)("span",{className:"text-brandBlue",children:"d\xe8s aujourd'hui."})]}):(0,a.jsxs)(a.Fragment,{children:["Find an appointment with a doctor ",(0,a.jsx)("span",{className:"text-brandBlue",children:"today."})]})}),(0,a.jsx)("p",{className:"text-gray-600 max-w-lg md:max-w-none lg:max-w-xl xl:max-w-2xl text-lg xs:text-sm sm:text-lg md:text-lg lg:text-xl xl:text-[20px] ".concat(t?"animate-fade-in-up-2":"opacity-0"),children:(0,a.jsx)(d.T,{keyName:u.o.landing.hero.subtitle})}),(0,a.jsxs)("div",{className:"pt-4 flex flex-col gap-4 md:flex-row ".concat(t?"animate-fade-in-up-3":"opacity-0"),children:[(0,a.jsx)(n.$,{asChild:!0,size:"lg",className:"rounded-lg font-medium text-base sm:text-lg px-8 py-6 group hover:shadow-md transition-all duration-300",children:(0,a.jsxs)(i(),{href:"/auth/sign-up",children:[(0,a.jsx)(d.T,{keyName:u.o.landing.hero.findAppointment}),(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-2 h-5 w-5 arrow-icon",children:[(0,a.jsx)("path",{d:"M5 12h14"}),(0,a.jsx)("path",{d:"m12 5 7 7-7 7"})]})]})}),(0,a.jsx)(n.$,{asChild:!0,variant:"outline",size:"lg",className:"rounded-lg font-medium text-base sm:text-lg px-8 py-6 border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-md transition-all duration-300 hover:border-gray-300",children:(0,a.jsx)(i(),{href:"#features-section",onClick:e=>{e.preventDefault();let t=document.getElementById("features-section");if(t){let e=t.getBoundingClientRect().top+window.pageYOffset;window.scrollTo({top:e-1,behavior:"smooth"})}},children:(0,a.jsx)(d.T,{keyName:u.o.landing.hero.learnMore})})})]})]}),(0,a.jsx)("div",{className:"w-full lg:w-1/2 flex justify-center items-center ".concat(t?"animate-fade-in-up-4":"opacity-0"),children:(0,a.jsx)(o.default,{src:"/zaply-images/hero-image-version2.png",alt:u.o.landing.hero.imageAlt,className:"max-w-full h-auto object-contain",width:600,height:480,priority:!0,suppressHydrationWarning:!0})})]})})}},47567:(e,t,s)=>{s.d(t,{BK:()=>l,eu:()=>o,q5:()=>d});var a=s(65127),n=s(6159),r=s(65924),i=s(79071);let o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.bL,{ref:t,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...n})});o.displayName=r.bL.displayName;let l=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r._V,{ref:t,className:(0,i.cn)("aspect-square h-full w-full",s),...n})});l.displayName=r._V.displayName;let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.H4,{ref:t,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...n})});d.displayName=r.H4.displayName},48544:(e,t,s)=>{s.d(t,{$:()=>d,r:()=>l});var a=s(65127),n=s(6159),r=s(83489),i=s(57767),o=s(79071);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:s,variant:n,size:i,asChild:d=!1,...c}=e,u=d?r.DX:"button";return(0,a.jsx)(u,{className:(0,o.cn)(l({variant:n,size:i,className:s})).replace("rounded-full","rounded-md"),ref:t,...c})});d.displayName="Button"},48600:(e,t,s)=>{s.d(t,{bq:()=>m,eb:()=>h,gC:()=>p,l6:()=>c,yv:()=>u});var a=s(65127),n=s(6159),r=s(12558),i=s(42761),o=s(13588),l=s(45301),d=s(79071);let c=r.bL;r.YJ;let u=r.WT,m=n.forwardRef((e,t)=>{let{className:s,children:n,...o}=e;return(0,a.jsxs)(r.l9,{ref:t,className:(0,d.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...o,children:[n,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=r.l9.displayName;let x=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...n,children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})});x.displayName=r.PP.displayName;let f=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...n,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});f.displayName=r.wn.displayName;let p=n.forwardRef((e,t)=>{let{className:s,children:n,position:i="popper",...o}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{ref:t,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:i,...o,children:[(0,a.jsx)(x,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),(0,a.jsx)(f,{})]})})});p.displayName=r.UC.displayName,n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.JU,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",s),...n})}).displayName=r.JU.displayName;let h=n.forwardRef((e,t)=>{let{className:s,children:n,...i}=e;return(0,a.jsxs)(r.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...i,children:[(0,a.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})}),(0,a.jsx)(r.p4,{children:n})]})});h.displayName=r.q7.displayName,n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",s),...n})}).displayName=r.wv.displayName},52033:(e,t,s)=>{s.d(t,{E:()=>o});var a=s(65127);s(6159);var n=s(57767),r=s(79071);let i=(0,n.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:s,...n}=e;return(0,a.jsx)("div",{className:(0,r.cn)(i({variant:s}),t),...n})}},59955:(e,t,s)=>{s.d(t,{FeaturesSection:()=>d});var a=s(65127),n=s(94417),r=s(6159),i=s(86453),o=s(49290);let l=e=>{let{icon:t,title:s,description:n}=e;return(0,a.jsxs)("div",{className:"flex flex-col items-start",children:[(0,a.jsx)("div",{className:"mb-5",children:t}),(0,a.jsx)("h3",{className:"text-2xl font-bold mb-4",children:s}),(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:n})]})};function d(){let{ref:e,isIntersecting:t}=(0,n.w)({threshold:.2,rootMargin:"-50px"}),[s,d]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{d(!0)},[]),(0,a.jsx)("section",{id:"features-section",className:"py-16 lg:py-24 overflow-hidden",ref:e,children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 sm:gap-12",children:[(0,a.jsx)("div",{className:"".concat(s&&t?"animate-fade-in-up-1":"opacity-0"),children:(0,a.jsx)(l,{icon:(0,a.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M8 2V5",stroke:"#144ee0",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M16 2V5",stroke:"#144ee0",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M3 8H21",stroke:"#144ee0",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M19 4H5C3.89543 4 3 4.89543 3 6V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V6C21 4.89543 20.1046 4 19 4Z",stroke:"#144ee0",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M12 12H12.01",stroke:"#144ee0",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M16 12H16.01",stroke:"#144ee0",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M8 12H8.01",stroke:"#144ee0",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M12 16H12.01",stroke:"#144ee0",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M8 16H8.01",stroke:"#144ee0",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),title:(0,a.jsx)(i.T,{keyName:o.o.landing.features.sameDay.title}),description:(0,a.jsx)(i.T,{keyName:o.o.landing.features.sameDay.description})})}),(0,a.jsx)("div",{className:"".concat(s&&t?"animate-fade-in-up-2":"opacity-0"),children:(0,a.jsx)(l,{icon:(0,a.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M21 10C21 17 12 23 12 23C12 23 3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.364 3.63604C20.0518 5.32387 21 7.61305 21 10Z",stroke:"#144ee0",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z",stroke:"#144ee0",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),title:(0,a.jsx)(i.T,{keyName:o.o.landing.features.nearbyClinic.title}),description:(0,a.jsx)(i.T,{keyName:o.o.landing.features.nearbyClinic.description})})}),(0,a.jsx)("div",{className:"".concat(s&&t?"animate-fade-in-up-3":"opacity-0"),children:(0,a.jsx)(l,{icon:(0,a.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z",stroke:"#144ee0",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M9 22V12H15V22",stroke:"#144ee0",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),title:(0,a.jsx)(i.T,{keyName:o.o.landing.features.anywhereInQuebec.title}),description:(0,a.jsx)(i.T,{keyName:o.o.landing.features.anywhereInQuebec.description})})})]})})}},64928:(e,t,s)=>{s.d(t,{$:()=>r});var a=s(65127),n=s(91303);function r(e){let{children:t}=e,{isLoading:s}=(0,n.o)();return s?(0,a.jsx)("div",{className:"flex h-screen w-screen items-center justify-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Chargement / Loading..."})]})}):(0,a.jsx)(a.Fragment,{children:t})}},67332:(e,t,s)=>{s.d(t,{PricingSection:()=>p});var a=s(65127),n=s(93773),r=s(45301),i=s(29081),o=s.n(i),l=s(6159),d=s(94417),c=s(2902),u=s(86453),m=s(49290),x=s(91303);let f=e=>{let{title:t,price:s,description:i,features:l,annualSavings:d}=e,{language:c}=(0,x.o)();return(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm flex flex-col h-full sm:max-w-[75%] sm:mx-auto md:max-w-none",children:[(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"text-xl font-bold mb-2",children:t}),(0,a.jsxs)("div",{className:"text-5xl font-bold mb-5 animate-price-fade text-[#212244]",children:[s.split("/")[0],(0,a.jsx)("span",{className:"text-2xl font-normal",children:"fr"===c?"/mois":"/month"})]},"price-".concat(s)),d&&(0,a.jsx)("div",{className:"text-green-600 text-sm font-medium mb-2",children:d}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:i})]}),(0,a.jsx)(n.$,{asChild:!0,size:"lg",className:"w-full rounded-lg font-medium text-base py-6 mb-8 group hover:shadow-md transition-all duration-300",children:(0,a.jsxs)(o(),{href:"/auth/sign-up",children:[(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.choosePlan}),(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-2 h-5 w-5 arrow-icon",children:[(0,a.jsx)("path",{d:"M5 12h14"}),(0,a.jsx)("path",{d:"m12 5 7 7-7 7"})]})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-bold text-[#212244]",children:(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.included})}),l.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-5 h-5 mr-3",children:(0,a.jsx)(r.A,{className:"w-full h-full text-brandBlue",strokeWidth:2})}),(0,a.jsx)("span",{className:"text-[#212244]",children:e})]},t))]})]})};function p(){let[e,t]=(0,l.useState)("monthly"),[s,n]=(0,l.useState)(!1),{ref:r,isIntersecting:i}=(0,d.w)({threshold:.2,rootMargin:"-50px"}),{language:o}=(0,x.o)();(0,l.useEffect)(()=>{n(!0)},[]);let p=e=>{console.log("Pricing period changed to:",e),t(e)},h=e=>"fr"===o?"".concat(e,"$"):"$".concat(e),g=h("fr"===o?"7,95":"7.95"),v=h("fr"===o?"14,95":"14.95"),y=h("fr"===o?"5,95":"5.95"),b=h("fr"===o?"11,20":"11.20"),j="annual"===e?(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.individual.annualSavings}):void 0,N="annual"===e?(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.family.annualSavings}):void 0,w="monthly"===e?g:y,k="monthly"===e?v:b,q="/suffix",z="".concat(w).concat(q),C="".concat(k).concat(q);return(0,a.jsx)("section",{className:"py-16 lg:py-24 overflow-hidden",ref:r,children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row items-start gap-12 lg:gap-16",children:[(0,a.jsxs)("div",{className:"w-full lg:w-1/3 ".concat(s&&i?"animate-fade-in-up-1":"opacity-0"),children:[(0,a.jsx)("h2",{className:"text-[42px] sm:text-[52px] m:text-[52px] lg:text-[56px] xl:text-[60px] leading-none font-bold text-[#212244] mb-6",children:(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.title})}),(0,a.jsx)("p",{className:"text-gray-600 text-lg mb-6",children:(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.description})}),(0,a.jsx)("div",{className:"hidden lg:flex lg:items-center lg:justify-start",children:(0,a.jsx)(c.S,{period:e,onChange:p})})]}),(0,a.jsxs)("div",{className:"w-full lg:w-2/3",children:[(0,a.jsx)("div",{className:"flex justify-center mb-8 -mt-12 lg:hidden ".concat(s&&i?"animate-fade-in-up-1":"opacity-0"),children:(0,a.jsx)(c.S,{period:e,onChange:p})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsx)("div",{className:"".concat(s&&i?"animate-fade-in-up-2":"opacity-0"),children:(0,a.jsx)(f,{title:(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.individual.title}),price:z,description:(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.individual.description}),features:[(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.feature1},"1"),(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.feature2},"2"),(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.feature3},"3"),(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.feature4},"4")],annualSavings:"annual"===e?j:void 0},"individual-".concat(e))}),(0,a.jsx)("div",{className:"".concat(s&&i?"animate-fade-in-up-3":"opacity-0"),children:(0,a.jsx)(f,{title:(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.family.title}),price:C,description:(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.family.description}),features:[(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.family.features},"1"),(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.feature2},"2"),(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.feature3},"3"),(0,a.jsx)(u.T,{keyName:m.o.landing.pricing.feature4},"4")],annualSavings:"annual"===e?N:void 0},"family-".concat(e))})]})]})]})})}},67547:(e,t,s)=>{s.d(t,{HowItWorksSection:()=>d});var a=s(65127),n=s(45301),r=s(94417),i=s(6159),o=s(86453),l=s(49290);function d(){let{ref:e,isIntersecting:t}=(0,r.w)({threshold:.2,rootMargin:"-50px"}),[s,d]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{d(!0)},[]),(0,a.jsx)("section",{id:"how-it-works-section",className:"pt-0 pb-16 lg:pb-24 overflow-hidden",ref:e,children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row items-center gap-12 lg:gap-16",children:[(0,a.jsx)("div",{className:"w-full lg:w-1/2 flex justify-center lg:justify-start ".concat(s&&t?"animate-fade-in-up-1":"opacity-0"),children:(0,a.jsx)("img",{src:"/zaply-images/howitworks_image.png",alt:l.o.landing.howItWorks.imageAlt,className:"rounded-xl w-full max-w-[500px] h-auto object-contain"})}),(0,a.jsxs)("div",{className:"w-full lg:w-1/2 space-y-8",children:[(0,a.jsx)("h2",{className:"text-[42px] sm:text-[52px] m:text-[52px] lg:text-[56px] xl:text-[60px] leading-none font-bold text-[#212244] mb-6 md:mb-10 lg:mb-20 ".concat(s&&t?"animate-fade-in-up-2":"opacity-0"),children:(0,a.jsx)(o.T,{keyName:l.o.landing.howItWorks.title})}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-start gap-4 ".concat(s&&t?"animate-fade-in-up-3":"opacity-0"),children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-[#16a349] rounded-full flex items-center justify-center mt-1",children:(0,a.jsx)(n.A,{className:"w-4 h-4 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-[#212244] mb-2",children:(0,a.jsx)(o.T,{keyName:l.o.landing.howItWorks.customAppointments.title})}),(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:(0,a.jsx)(o.T,{keyName:l.o.landing.howItWorks.customAppointments.description})})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-4 ".concat(s&&t?"animate-fade-in-up-4":"opacity-0"),children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-[#16a349] rounded-full flex items-center justify-center mt-1",children:(0,a.jsx)(n.A,{className:"w-4 h-4 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-[#212244] mb-2",children:(0,a.jsx)(o.T,{keyName:l.o.landing.howItWorks.easyManagement.title})}),(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:(0,a.jsx)(o.T,{keyName:l.o.landing.howItWorks.easyManagement.description})})]})]})]})]})]})})}},69934:(e,t,s)=>{s.d(t,{V:()=>d});var a=s(65127);s(6159);var n=s(9432),r=s(14169),i=s(62605),o=s(79071),l=s(48544);function d(e){let{className:t,classNames:s,showOutsideDays:d=!0,...c}=e;return(0,a.jsx)(i.hv,{showOutsideDays:d,className:(0,o.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",caption_dropdowns:"flex justify-center gap-1",nav:"space-x-1 flex items-center",nav_button:(0,o.cn)((0,l.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-medium text-[0.8rem] flex items-center justify-center h-9",row:"flex w-full mt-0",cell:(0,o.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===c.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,o.cn)((0,l.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start",day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground rounded-md",day_today:"bg-accent text-accent-foreground rounded-md",day_outside:"day-outside text-muted-foreground opacity-50",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...s},components:{IconLeft:e=>{let{...t}=e;return(0,a.jsx)(n.A,{className:"h-4 w-4",...t})},IconRight:e=>{let{...t}=e;return(0,a.jsx)(r.A,{className:"h-4 w-4",...t})}},...c})}d.displayName="Calendar"},71532:(e,t,s)=>{s.d(t,{p:()=>i});var a=s(65127),n=s(6159),r=s(79071);let i=n.forwardRef((e,t)=>{let{className:s,type:n,...i}=e;return(0,a.jsx)("input",{type:n,className:(0,r.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...i})});i.displayName="Input"},82642:(e,t,s)=>{s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>o,wL:()=>u});var a=s(65127),n=s(6159),r=s(79071);let i=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("rounded-xl border bg-card text-card-foreground shadow",s),...n})});i.displayName="Card";let o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",s),...n})});o.displayName="CardHeader";let l=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("font-semibold leading-none tracking-tight",s),...n})});l.displayName="CardTitle";let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("text-sm text-muted-foreground",s),...n})});d.displayName="CardDescription";let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("p-6 pt-0",s),...n})});c.displayName="CardContent";let u=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("flex items-center p-6 pt-0",s),...n})});u.displayName="CardFooter"},85146:(e,t,s)=>{s.d(t,{FaqFullPageEN:()=>o});var a=s(65127),n=s(96981),r=s(94417),i=s(6159);function o(){let{ref:e,isIntersecting:t}=(0,r.w)({threshold:.2,rootMargin:"-50px"}),[s,o]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{o(!0)},[]),(0,a.jsx)("section",{className:"py-16 lg:py-24 overflow-hidden",ref:e,children:(0,a.jsxs)("div",{className:"".concat(s&&t?"animate-fade-in-together":"opacity-0"),children:[(0,a.jsx)("div",{className:"text-center mb-16",children:(0,a.jsx)("h1",{className:"text-4xl md:text-[52px] lg:text-[56px] xl:text-[60px] font-bold text-[#212244]",children:"Frequently Asked Questions"})}),(0,a.jsx)("div",{className:"max-w-5xl mx-auto px-2",children:(0,a.jsx)(n.nD,{type:"single",collapsible:!0,children:[{question:"What exactly does Sans rendez-vous express offer?",answer:"Sans rendez-vous express is an online platform that facilitates the search and booking of appointments with doctors in Quebec. Our goal is to simplify the process for patients by allowing them to quickly find available appointments and providing them with easy access to a variety of healthcare professionals in their area."},{question:"What is the waiting time to get an appointment?",answer:"At Sans rendez-vous express, we strive to find an appointment with a doctor as quickly as possible for all our users. In general, most appointments are found within 48 hours. For larger municipalities, the majority of appointments are found within 24 hours. Our team works diligently to meet the needs of patients and to ensure quick access to necessary healthcare services."},{question:"What information is needed to schedule an appointment with a doctor via Sans rendez-vous express?",answer:"To schedule an appointment with a doctor through our platform, users will need to provide basic information such as their name, phone number, email address, and health insurance number. Once this information is provided, users can then specify the time, date, and location of the appointment according to their preferences. These details help us find an appointment that best matches the needs and availability of each user."},{question:"How are appointments booked?",answer:"After submitting your request, our system automatically searches for available appointments in participating clinics according to your preferences. Once an appropriate appointment is found, you will receive a confirmation by email and/or SMS with all the appointment details."},{question:"Who can use this service?",answer:"Our service is accessible to anyone holding a Quebec Health Insurance Card."},{question:"How do you choose the clinics?",answer:"Clinics are selected based on the postal code that the user has chosen for their search, as well as the search radius they are willing to travel. Our platform uses this information to identify the clinics closest to the location specified by the user. This ensures that the clinic options presented match each user's location and travel needs, providing a personalized and convenient search experience."},{question:"Can I choose a specific clinic?",answer:"Unfortunately, the option to choose a specific clinic is not available on our platform. Clinics are selected based on the postal code you have chosen for your search, as well as the search radius you are willing to travel. This ensures that the clinics offered match the location criteria you have specified."},{question:"Can I change my appointment request along the way?",answer:"Yes, you can modify your appointment request along the way. Users have the option to change or cancel their appointment request at no cost, as long as no appointment has already been booked. To do this, users simply need to click on the link to modify or cancel an appointment that is sent to them by email after submitting their initial request. This feature allows you to easily manage your appointment according to your needs and schedule."},{question:"How do I cancel an appointment?",answer:"To cancel an appointment, look for the confirmation email you received after making your appointment request and click the link to modify or cancel your appointment. Follow the instructions to cancel your appointment. If you encounter any difficulties, do not hesitate to contact our customer service. We will be happy to assist you!"},{question:"Does Sans rendez-vous express offer emergency appointments?",answer:"While we do not offer services for medical emergency situations, our platform allows users to quickly find appointments with doctors for regular consultations and medical follow-up needs. In case of a medical emergency, we encourage you to immediately contact the appropriate emergency services or go to the nearest hospital."},{question:"Does Sans rendez-vous express offer online consultations?",answer:"Currently, we do not offer online consultations on our platform. However, we are constantly working to improve our services and explore new options to meet the needs of our users. Stay tuned for any updates regarding online consultations at Sans rendez-vous express."},{question:"The service does not meet my expectations.",answer:"We are sorry to hear that the service does not meet your expectations. Your satisfaction is our top priority, and we want to resolve any issues you may encounter. Please do not hesitate to contact us directly to share your concerns and feedback. Our team is here to help and to find solutions that suit you."},{question:"How do you protect my personal information?",answer:"At Sans rendez-vous express, we place great importance on the protection of your personal information. We are committed to maintaining the confidentiality of the information you entrust to us. In accordance with applicable laws regarding the Quebec health insurance plan (Health Insurance Act, RLRQ c A-29) and the protection of personal information in the private sector (Act respecting the protection of personal information in the private sector, RLRQ, c P-39.1), we have implemented measures to ensure the security and confidentiality of your data. Your personal information will be deleted from our system seven (7) days after the appointment is made, in accordance with our Privacy Policy."}].map((e,t)=>(0,a.jsxs)(n.As,{value:"item-".concat(t),className:"mb-6",children:[(0,a.jsx)(n.$m,{className:"text-left text-lg font-medium",children:e.question}),(0,a.jsx)(n.ub,{className:"text-gray-600 text-lg leading-relaxed",children:e.answer})]},t))})})]})})}},86375:(e,t,s)=>{s.d(t,{AM:()=>o,Wv:()=>l,hl:()=>d});var a=s(65127),n=s(6159),r=s(34360),i=s(79071);let o=r.bL,l=r.l9;r.Mz;let d=n.forwardRef((e,t)=>{let{className:s,align:n="center",sideOffset:o=4,...l}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{ref:t,align:n,sideOffset:o,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...l})})});d.displayName=r.UC.displayName},86453:(e,t,s)=>{s.d(t,{B:()=>i,T:()=>r});var a=s(65127),n=s(91303);let r=(0,s(6159).memo)(function(e){let{keyName:t,params:s}=e,{translate:r,isLoading:i}=(0,n.o)();if(i)return(0,a.jsx)(a.Fragment,{children:t});let o=r(t);return s&&Object.entries(s).forEach(e=>{let[t,s]=e;o=o.replace("{{".concat(t,"}}"),String(s))}),(0,a.jsx)(a.Fragment,{children:o})}),i=()=>{let{translate:e,language:t,isLoading:s}=(0,n.o)();return{t:(t,a)=>{if(s)return t;let n=e(t);return a&&Object.entries(a).forEach(e=>{let[t,s]=e;n=n.replace("{{".concat(t,"}}"),String(s))}),n},language:t,isLoading:s}}},86832:(e,t,s)=>{s.d(t,{Cf:()=>m,L3:()=>f,c7:()=>x,lG:()=>l,rr:()=>p,zM:()=>d});var a=s(65127),n=s(6159),r=s(64149),i=s(52981),o=s(79071);let l=r.bL,d=r.l9,c=r.ZL;r.bm;let u=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.hJ,{ref:t,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...n})});u.displayName=r.hJ.displayName;let m=n.forwardRef((e,t)=>{let{className:s,children:n,...l}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(u,{}),(0,a.jsxs)(r.UC,{ref:t,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...l,children:[n,(0,a.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=r.UC.displayName;let x=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};x.displayName="DialogHeader";let f=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.hE,{ref:t,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",s),...n})});f.displayName=r.hE.displayName;let p=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.VY,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",s),...n})});p.displayName=r.VY.displayName},92878:(e,t,s)=>{s.d(t,{FaqSection:()=>m});var a=s(65127),n=s(96981),r=s(94417),i=s(6159),o=s(29081),l=s.n(o),d=s(86453),c=s(49290),u=s(91303);function m(){let{ref:e,isIntersecting:t}=(0,r.w)({threshold:.2,rootMargin:"-50px"}),[s,o]=(0,i.useState)(!1);(0,i.useEffect)(()=>{o(!0)},[]);let{language:m}=(0,u.o)(),x=[{question:(0,a.jsx)(d.T,{keyName:c.o.landing.faq.questions[0].question}),answer:(0,a.jsx)(d.T,{keyName:c.o.landing.faq.questions[0].answer})},{question:(0,a.jsx)(d.T,{keyName:c.o.landing.faq.questions[1].question}),answer:(0,a.jsx)(d.T,{keyName:c.o.landing.faq.questions[1].answer})},{question:(0,a.jsx)(d.T,{keyName:c.o.landing.faq.questions[2].question}),answer:(0,a.jsx)(d.T,{keyName:c.o.landing.faq.questions[2].answer})},{question:(0,a.jsx)(d.T,{keyName:c.o.landing.faq.questions[3].question}),answer:(0,a.jsx)(d.T,{keyName:c.o.landing.faq.questions[3].answer})},{question:(0,a.jsx)(d.T,{keyName:c.o.landing.faq.questions[4].question}),answer:(0,a.jsx)(d.T,{keyName:c.o.landing.faq.questions[4].answer})}];return(0,a.jsx)("section",{id:"faq",className:"py-16 lg:py-24 overflow-hidden",ref:e,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-center mb-16 ".concat(s&&t?"animate-fade-in-up-1":"opacity-0"),children:(0,a.jsx)("h2",{className:"text-[42px] sm:text-[52px] m:text-[52px] lg:text-[56px] xl:text-[60px] leading-none font-bold text-[#212244]",children:(0,a.jsx)(d.T,{keyName:c.o.landing.faq.title})})}),(0,a.jsxs)("div",{className:"max-w-3xl mx-auto px-2",children:[(0,a.jsx)(n.nD,{type:"single",collapsible:!0,children:x.map((e,r)=>(0,a.jsxs)(n.As,{value:"item-".concat(r),className:"mb-7 ".concat(s&&t?"animate-fade-in-up-".concat(r+2," -mt-6"):"opacity-0"),children:[(0,a.jsx)(n.$m,{className:"text-left text-lg font-medium",children:e.question}),(0,a.jsx)(n.ub,{className:"text-gray-600 text-lg leading-relaxed",children:e.answer})]},r))}),(0,a.jsx)("div",{className:"mt-10 text-center ".concat(s&&t?"animate-fade-in-up-3":"opacity-0"),children:(0,a.jsxs)(l(),{href:"en"===m?"/foire-aux-questions-EN":"/foire-aux-questions",className:"text-lg font-medium text-brandBlue hover:underline inline-flex items-center",children:[(0,a.jsx)(d.T,{keyName:c.o.landing.faq.viewFullFaq}),(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-2 h-5 w-5",children:[(0,a.jsx)("path",{d:"M5 12h14"}),(0,a.jsx)("path",{d:"m12 5 7 7-7 7"})]})]})})]})]})})}},93773:(e,t,s)=>{s.d(t,{$:()=>d});var a=s(65127),n=s(6159),r=s(83489),i=s(57767),o=s(70992);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-brandBlue text-white shadow hover:bg-brandBlue/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-brandBlue underline-offset-4 hover:underline"},effect:{expandIcon:"group gap-0 relative",ringHover:"transition-all duration-300 hover:ring-2 hover:ring-primary/90 hover:ring-offset-2",shine:"before:animate-shine relative overflow-hidden before:absolute before:inset-0 before:rounded-[inherit] before:bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.5)_50%,transparent_75%,transparent_100%)] before:bg-[length:250%_250%,100%_100%] before:bg-no-repeat background-position_0s_ease",shineHover:"relative overflow-hidden before:absolute before:inset-0 before:rounded-[inherit] before:bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.5)_50%,transparent_75%,transparent_100%)] before:bg-[length:250%_250%,100%_100%] before:bg-[position:200%_0,0_0] before:bg-no-repeat before:transition-[background-position_0s_ease] hover:before:bg-[position:-100%_0,0_0] before:duration-1000",gooeyRight:"relative z-0 overflow-hidden transition-all duration-500 before:absolute before:inset-0 before:-z-10 before:translate-x-[150%] before:translate-y-[150%] before:scale-[2.5] before:rounded-[100%] before:bg-gradient-to-r from-white/40 before:transition-transform before:duration-1000  hover:before:translate-x-[0%] hover:before:translate-y-[0%]",gooeyLeft:"relative z-0 overflow-hidden transition-all duration-500 after:absolute after:inset-0 after:-z-10 after:translate-x-[-150%] after:translate-y-[150%] after:scale-[2.5] after:rounded-[100%] after:bg-gradient-to-l from-white/40 after:transition-transform after:duration-1000  hover:after:translate-x-[0%] hover:after:translate-y-[0%]",underline:"relative !no-underline after:absolute after:bg-primary after:bottom-2 after:h-[1px] after:w-2/3 after:origin-bottom-left after:scale-x-100 hover:after:origin-bottom-right hover:after:scale-x-0 after:transition-transform after:ease-in-out after:duration-300",hoverUnderline:"relative !no-underline after:absolute after:bg-primary after:bottom-2 after:h-[1px] after:w-2/3 after:origin-bottom-right after:scale-x-0 hover:after:origin-bottom-left hover:after:scale-x-100 after:transition-transform after:ease-in-out after:duration-300",gradientSlideShow:"bg-[size:400%] bg-[linear-gradient(-45deg,var(--gradient-lime),var(--gradient-ocean),var(--gradient-wine),var(--gradient-rust))] animate-gradient-flow"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:s,variant:n,effect:i,size:d,icon:c,iconPlacement:u,asChild:m=!1,...x}=e,f=m?r.DX:"button";return(0,a.jsxs)(f,{className:(0,o.cn)(l({variant:n,effect:i,size:d,className:s})),ref:t,...x,children:[c&&"left"===u&&("expandIcon"===i?(0,a.jsx)("div",{className:"w-0 translate-x-[0%] pr-0 opacity-0 transition-all duration-200 group-hover:w-5 group-hover:translate-x-100 group-hover:pr-2 group-hover:opacity-100",children:(0,a.jsx)(c,{})}):(0,a.jsx)(c,{})),(0,a.jsx)(r.xV,{children:x.children}),c&&"right"===u&&("expandIcon"===i?(0,a.jsx)("div",{className:"w-0 translate-x-[100%] pl-0 opacity-0 transition-all duration-200 group-hover:w-5 group-hover:translate-x-0 group-hover:pl-2 group-hover:opacity-100",children:(0,a.jsx)(c,{})}):(0,a.jsx)(c,{}))]})});d.displayName="Button"},96970:(e,t,s)=>{s.d(t,{J:()=>d});var a=s(65127),n=s(6159),r=s(87119),i=s(57767),o=s(79071);let l=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.b,{ref:t,className:(0,o.cn)(l(),s),...n})});d.displayName=r.b.displayName},96981:(e,t,s)=>{s.d(t,{$m:()=>d,As:()=>l,nD:()=>o,ub:()=>c});var a=s(65127),n=s(6159),r=s(54733),i=s(70992);let o=r.bL,l=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.q7,{ref:t,className:(0,i.cn)("border-b",s),...n})});l.displayName="AccordionItem";let d=n.forwardRef((e,t)=>{let{className:s,children:n,...o}=e;return(0,a.jsx)(r.Y9,{className:"flex",children:(0,a.jsxs)(r.l9,{ref:t,className:(0,i.cn)("group flex flex-1 items-center justify-between py-5 text-lg font-medium transition-all hover:text-brandBlue data-[state=open]:text-brandBlue",s),...o,children:[(0,a.jsx)("div",{className:"pr-8",children:n}),(0,a.jsxs)("div",{className:"relative h-5 w-5 flex items-center justify-center shrink-0",children:[(0,a.jsx)("span",{className:"absolute h-0.5 w-4 bg-brandBlue rounded-full transition-transform group-data-[state=open]:rotate-45"}),(0,a.jsx)("span",{className:"absolute h-4 w-0.5 bg-brandBlue rounded-full transition-transform group-data-[state=open]:rotate-45"})]})]})})});d.displayName="AccordionTrigger";let c=n.forwardRef((e,t)=>{let{className:s,children:n,...o}=e;return(0,a.jsx)(r.UC,{ref:t,className:(0,i.cn)("overflow-hidden text-base transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",s),...o,children:(0,a.jsx)("div",{className:"pb-4 pt-2",children:n})})});c.displayName="AccordionContent"},98282:(e,t,s)=>{s.r(t),s.d(t,{DashboardLayout:()=>V});var a=s(65127),n=s(6159),r=s(29081),i=s.n(r),o=s(62153);s(35476);var l=s(72842),d=s(37574),c=s(54867),u=s(3938),m=s(39556),x=s(92797),f=s(35734),p=s(79071),h=s(48544),g=s(86453),v=s(49290),y=s(35071);let b=(0,n.memo)(function(){let e=(0,l.usePathname)(),{t}=(0,g.B)(),[s,r]=(0,n.useState)(!1),{signOut:o}=(0,y.A)(),b=[{title:t(v.o.nav.dashboard),href:"/dashboard",icon:d.A},{title:t(v.o.nav.findAppointment),href:"/trouver-rendez-vous",icon:c.A},{title:t(v.o.nav.appointments),href:"/mes-rendez-vous",icon:u.A},{title:t(v.o.account.yourAccount),href:"/compte",icon:m.A},{title:t(v.o.nav.needHelp),href:"/aide",icon:x.A}],j=async()=>{try{r(!0),await o("/")}catch(e){console.error("Error during logout:",e),r(!1)}};return(0,a.jsx)("div",{className:"hidden border-r bg-gray-50 dark:bg-gray-900 md:block md:w-56 lg:w-72 sidebar-no-top-border",children:(0,a.jsx)("div",{className:"flex h-full flex-col",children:(0,a.jsx)("div",{className:"pt-4 px-1.5",children:(0,a.jsxs)("nav",{className:"grid gap-1",children:[b.map(t=>(0,a.jsx)(h.$,{variant:e===t.href?"secondary":"ghost",className:(0,p.cn)("flex w-full items-center justify-start gap-2 pl-5",e===t.href?"bg-secondary font-medium":"font-normal"),asChild:!0,children:(0,a.jsxs)(i(),{href:t.href,children:[(0,a.jsx)(t.icon,{className:"h-4 w-4"}),t.title]})},t.href)),(0,a.jsx)("div",{className:"pt-3 mt-2 border-t border-border/40",children:(0,a.jsxs)(h.$,{variant:"ghost",size:"sm",className:"flex w-full items-center justify-start gap-2 pl-5 text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-950/10",onClick:j,disabled:s,children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:t(s?v.o.common.loggingOut:v.o.common.logout)})]})})]})})})})});var j=s(48268),N=s(52981),w=s(64149),k=s(57767);let q=w.bL,z=w.l9,C=w.bm,A=w.ZL,T=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(w.hJ,{className:(0,p.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...n,ref:t})});T.displayName=w.hJ.displayName;let L=(0,k.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),E=n.forwardRef((e,t)=>{let{side:s="right",className:n,children:r,...i}=e;return(0,a.jsxs)(A,{children:[(0,a.jsx)(T,{}),(0,a.jsxs)(w.UC,{ref:t,className:(0,p.cn)(L({side:s}),n),...i,children:[(0,a.jsxs)(w.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,a.jsx)(N.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]}),r]})]})});E.displayName=w.UC.displayName;let S=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,p.cn)("flex flex-col space-y-2 text-center sm:text-left",t),...s})};S.displayName="SheetHeader";let _=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(w.hE,{ref:t,className:(0,p.cn)("text-lg font-semibold text-foreground",s),...n})});_.displayName=w.hE.displayName,n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(w.VY,{ref:t,className:(0,p.cn)("text-sm text-muted-foreground",s),...n})}).displayName=w.VY.displayName;var R=s(47567),M=s(75616),F=s(95818);let B=(0,n.memo)(function(){let e=(0,l.usePathname)();(0,l.useRouter)();let{t}=(0,g.B)(),[s,r]=(0,n.useState)(!1),{fullName:o,initials:y}=(0,F.s)(),b=[{title:t(v.o.nav.dashboard),href:"/dashboard",icon:d.A},{title:t(v.o.nav.findAppointment),href:"/trouver-rendez-vous",icon:c.A},{title:t(v.o.nav.appointments),href:"/mes-rendez-vous",icon:u.A},{title:t(v.o.account.yourAccount),href:"/compte",icon:m.A},{title:t(v.o.nav.needHelp),href:"/aide",icon:x.A}],w=async()=>{try{console.log("Starting logout process..."),r(!0),await (0,M.Y9)("/")}catch(e){console.error("Error during logout:",e),r(!1)}};return(0,a.jsxs)(q,{children:[(0,a.jsx)(z,{asChild:!0,children:(0,a.jsxs)(h.$,{variant:"ghost",size:"sm",className:"md:hidden",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle menu"})]})}),(0,a.jsxs)(E,{side:"right",className:"px-0 [&>button]:hidden",children:[(0,a.jsx)(S,{className:"sr-only",children:(0,a.jsx)(_,{children:t(v.o.nav.mobileNavigation)})}),(0,a.jsxs)("div",{className:"flex h-full flex-col",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center py-6",children:[(0,a.jsx)(R.eu,{className:"h-16 w-16 mb-2",children:(0,a.jsx)(R.q5,{className:"bg-blue-100 text-blue-700 text-xl font-normal",children:y})}),(0,a.jsx)("h2",{className:"text-lg font-medium",children:o}),(0,a.jsx)(C,{asChild:!0,className:"absolute right-4 top-4",children:(0,a.jsxs)(h.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(N.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:t(v.o.common.close)})]})})]}),(0,a.jsx)("div",{className:"px-3 py-4",children:(0,a.jsxs)("nav",{className:"grid gap-1 px-2",children:[b.map(t=>(0,a.jsx)(h.$,{variant:e===t.href?"secondary":"ghost",className:(0,p.cn)("flex w-full items-center justify-start gap-2 text-base py-2.5",e===t.href?"bg-secondary font-medium":"font-normal"),asChild:!0,children:(0,a.jsxs)(i(),{href:t.href,children:[(0,a.jsx)(t.icon,{className:"h-5 w-5 mr-1"}),t.title]})},t.href)),(0,a.jsx)("div",{className:"pt-3 mt-2 border-t border-border/40",children:(0,a.jsxs)(h.$,{variant:"ghost",size:"sm",className:"flex w-full items-center justify-start gap-2 text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-950/10 text-base py-2.5",onClick:w,disabled:s,children:[(0,a.jsx)(f.A,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{children:t(s?v.o.common.loggingOut:v.o.common.logout)})]})})]})})]})]})]})});var P=s(38766);function W(e){let{children:t,redirectTo:s="/auth/sign-in"}=e,{status:r,refresh:i}=(0,y.A)(),o=(0,l.useRouter)();return((0,n.useEffect)(()=>{if("loading"!==r&&"unauthenticated"===r){let e=encodeURIComponent(window.location.pathname);o.push("".concat(s,"?redirectedFrom=").concat(e))}},[r,o,s]),(0,n.useEffect)(()=>{let e=()=>{"visible"===document.visibilityState&&i()};return document.addEventListener("visibilitychange",e),()=>{document.removeEventListener("visibilitychange",e)}},[i]),"loading"===r)?(0,a.jsx)("div",{className:"flex h-screen w-screen items-center justify-center bg-white",children:(0,a.jsx)("div",{className:"h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"})}):"authenticated"===r?(0,a.jsx)(a.Fragment,{children:t}):null}var I=s(30150);function H(){let{theme:e}=(0,I.w)();return null}function V(e){let{children:t}=e,{initials:s}=(0,F.s)();return(0,a.jsxs)(W,{children:[(0,a.jsx)(H,{}),(0,a.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,a.jsx)("header",{className:"sticky top-0 z-30 flex h-14 items-center border-b bg-gray-50 dark:bg-gray-900 px-4 sm:px-6 md:px-3 md:z-10 md:border-l-0",children:(0,a.jsxs)("div",{className:"flex w-full justify-between",children:[(0,a.jsx)("div",{className:"flex items-center gap-2 md:w-56 lg:w-72",children:(0,a.jsxs)(i(),{href:"/dashboard",className:"flex items-center group pl-1 md:pl-1.5",children:[(0,a.jsx)("div",{className:"w-8 h-8 flex items-center justify-center transition-transform duration-300 group-hover:scale-110",children:(0,a.jsx)(o.default,{src:"/zaply-images/srvxp_logorevised.svg",alt:"Logo",width:32,height:32,priority:!0,suppressHydrationWarning:!0,className:"text-brandBlue fill-current"})}),(0,a.jsx)("span",{className:"ml-3 text-base font-bold text-[#212242] dark:text-white whitespace-nowrap hidden md:inline-block",children:(0,a.jsx)(g.T,{keyName:v.o.landing.navbar.title})})]})}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(P.J,{}),(0,a.jsx)(R.eu,{className:"h-8 w-8",children:(0,a.jsx)(R.q5,{className:"bg-blue-100 text-blue-700 text-sm font-normal",children:s})}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)(B,{})})]})]})}),(0,a.jsx)("div",{className:"flex flex-1 relative",children:(0,a.jsxs)("div",{className:"flex w-full",children:[(0,a.jsx)(b,{}),(0,a.jsx)("main",{className:"flex-1 p-4 md:p-6",children:(0,a.jsx)("div",{className:"max-w-[1000px]",children:t})})]})})]})]})}},99302:(e,t,s)=>{s.d(t,{u:()=>p});var a=s(65127),n=s(6159),r=s(81261),i=s(2292),o=s(62605),l=s(79071),d=s(48544),c=s(86375),u=s(48600),m=s(26568);let x=n.forwardRef((e,t)=>{let{className:s,children:n,...r}=e;return(0,a.jsxs)(m.bL,{ref:t,className:(0,l.cn)("relative overflow-hidden",s),...r,children:[(0,a.jsx)(m.LM,{className:"h-full w-full rounded-[inherit]",children:n}),(0,a.jsx)(f,{}),(0,a.jsx)(m.OK,{})]})});x.displayName=m.bL.displayName;let f=n.forwardRef((e,t)=>{let{className:s,orientation:n="vertical",...r}=e;return(0,a.jsx)(m.VM,{ref:t,orientation:n,className:(0,l.cn)("flex touch-none select-none transition-colors","vertical"===n&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===n&&"h-2.5 border-t border-t-transparent p-[1px]",s),...r,children:(0,a.jsx)(m.lr,{className:"relative flex-1 rounded-full bg-border"})})});function p(e){let{date:t,onSelect:s,locale:m,fromYear:f=1900,toYear:p=new Date().getFullYear()}=e,[h,g]=(0,n.useState)(!1),[v,y]=(0,n.useState)(t),[b,j]=(0,n.useState)(v||new Date);return(0,n.useEffect)(()=>{v&&j(v)},[v]),(0,a.jsxs)(c.AM,{open:h,onOpenChange:g,children:[(0,a.jsx)(c.Wv,{asChild:!0,children:(0,a.jsxs)(d.$,{variant:"outline",className:(0,l.cn)("w-full justify-start text-left font-normal",!v&&"text-muted-foreground"),children:[(0,a.jsx)(r.A,{className:"mr-2 h-4 w-4"}),v?(0,i.GP)(v,"PPP",{locale:m}):(0,a.jsx)("span",{children:"S\xe9lectionner une date"})]})}),(0,a.jsx)(c.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4 pb-3",children:[(0,a.jsxs)("div",{className:"flex gap-2 justify-between mb-4",children:[(0,a.jsx)("div",{className:"relative flex-1",children:(0,a.jsxs)(u.l6,{value:(0,i.GP)(b,"MMMM"),onValueChange:e=>{let t=new Date(b),s=new Date(Date.parse("".concat(e," 1, 2000"))).getMonth();t.setMonth(s),j(t)},children:[(0,a.jsx)(u.bq,{className:"w-full h-10 pl-3 rounded-md text-left font-normal",children:(0,a.jsx)(u.yv,{})}),(0,a.jsx)(u.gC,{children:(0,a.jsx)(x,{className:"h-60",children:Array.from({length:12}).map((e,t)=>{let s=new Date;return s.setMonth(t),(0,a.jsx)(u.eb,{value:(0,i.GP)(s,"MMMM"),children:(0,i.GP)(s,"MMMM",{locale:m})},t)})})})]})}),(0,a.jsx)("div",{className:"relative flex-1",children:(0,a.jsxs)(u.l6,{value:b.getFullYear().toString(),onValueChange:e=>{let t=new Date(b);t.setFullYear(parseInt(e)),j(t)},children:[(0,a.jsx)(u.bq,{className:"w-full h-10 pl-3 rounded-md text-left font-normal",children:(0,a.jsx)(u.yv,{})}),(0,a.jsx)(u.gC,{children:(0,a.jsx)(x,{className:"h-60",children:Array.from({length:p-f+1}).map((e,t)=>{let s=p-t;return(0,a.jsx)(u.eb,{value:s.toString(),children:s},t)})})})]})})]}),(0,a.jsx)(()=>(0,a.jsx)(o.hv,{mode:"single",selected:v,onSelect:e=>{y(e||void 0),s(e||void 0),e&&g(!1)},month:b,fromYear:f,toYear:p,locale:m,showOutsideDays:!0,className:"custom-calendar",classNames:{months:"flex flex-col",month:"space-y-4",caption:"hidden",caption_label:"hidden",caption_dropdowns:"hidden",nav:"hidden",table:"w-full border-collapse",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-medium text-[0.8rem] flex items-center justify-center h-9",row:"flex w-full mt-0",cell:"relative p-0 text-center text-sm focus-within:relative focus-within:z-20",day:"h-9 w-9 p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground rounded-md",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground rounded-md",day_today:"bg-accent text-accent-foreground rounded-md",day_outside:"text-muted-foreground opacity-50",day_disabled:"text-muted-foreground opacity-50",day_hidden:"invisible"}}),{})]})})]})}f.displayName=m.VM.displayName},99640:(e,t,s)=>{s.d(t,{N:()=>r});var a=s(65127);s(6159);var n=s(56309);function r(e){let{children:t,...s}=e;return(0,a.jsx)(n.N,{...s,children:t})}}}]);