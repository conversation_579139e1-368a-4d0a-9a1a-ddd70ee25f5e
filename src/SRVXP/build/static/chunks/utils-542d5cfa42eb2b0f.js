"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5738],{4111:(e,t,r)=>{r.d(t,{r:()=>d,h:()=>p});var n=r(65127),a=r(6159),i=r(35071),o=r(98846);async function s(e){try{let{data:t,error:r}=await o.N.from("family_members").select("*").eq("user_id",e).order("position",{ascending:!0});if(r)throw r;let n=t.map(e=>({id:e.position,firstName:e.first_name||"",lastName:e.last_name||"",healthCard:e.health_card||"",birthDate:e.birth_date?new Date(e.birth_date):void 0,editing:!1,supabaseId:e.id})),a=[];for(let e=1;e<=5;e++){let t=n.find(t=>t.id===e);t?a.push(t):a.push({id:e,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1})}return a}catch(e){throw console.error("Error fetching family members:",e),e}}async function l(e,t,r){try{var n;let a={user_id:e,first_name:t.firstName,last_name:t.lastName,health_card:t.healthCard,birth_date:(null===(n=t.birthDate)||void 0===n?void 0:n.toISOString())||null,position:r};if(t.supabaseId){let{error:e}=await o.N.from("family_members").update(a).eq("id",t.supabaseId);if(e)throw e;return{success:!0,id:t.supabaseId}}{let{data:e,error:t}=await o.N.from("family_members").insert(a).select("id").single();if(t)throw t;return{success:!0,id:e.id}}}catch(e){return console.error("Error saving family member:",e),{success:!1}}}async function c(e){try{let{error:t}=await o.N.from("family_members").delete().eq("id",e);if(t)throw t;return{success:!0}}catch(e){return console.error("Error deleting family member:",e),{success:!1}}}let u=[{id:1,firstName:"F\xe9lix",lastName:"Tremblay",healthCard:"TREF",birthDate:new Date(1985,3,12),editing:!1},{id:2,firstName:"Marie",lastName:"Tremblay",healthCard:"TREM",birthDate:new Date(1987,8,23),editing:!1},{id:3,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1},{id:4,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1},{id:5,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1}],m=(0,a.createContext)({familyMembers:u,setFamilyMembers:()=>{},updateFamilyMember:()=>{},updateTempFamilyMember:()=>{},toggleEditing:()=>{},saveMemberChanges:()=>!1,deleteMember:()=>{},isLoading:!1,error:null});function d(e){let{children:t}=e,{user:r,status:o}=(0,i.A)(),[d,p]=(0,a.useState)(u),[g,h]=(0,a.useState)(!0),[f,v]=(0,a.useState)(null);return(0,a.useEffect)(()=>{"loading"!==o&&("authenticated"===o&&(null==r?void 0:r.id)?(h(!0),v(null),s(r.id).then(e=>{p(e)}).catch(e=>{console.error("Error loading family members:",e),v(Error("Failed to load family members")),p([,,,,,].fill(null).map((e,t)=>({id:t+1,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1})))}).finally(()=>{h(!1)})):"unauthenticated"===o&&(p(u),h(!1)))},[null==r?void 0:r.id,o]),(0,n.jsx)(m.Provider,{value:{familyMembers:d,setFamilyMembers:p,updateFamilyMember:(e,t)=>{p(r=>r.map(r=>r.id===e?{...r,...t}:r))},updateTempFamilyMember:(e,t)=>{p(r=>r.map(r=>r.id===e?{...r,tempFirstName:"tempFirstName"in t?t.tempFirstName:r.tempFirstName,tempLastName:"tempLastName"in t?t.tempLastName:r.tempLastName,tempHealthCard:"tempHealthCard"in t?t.tempHealthCard:r.tempHealthCard,tempBirthDate:"tempBirthDate"in t?t.tempBirthDate:r.tempBirthDate}:r))},toggleEditing:e=>{p(t=>t.map(t=>t.id===e?t.editing?{...t,editing:!1,tempFirstName:void 0,tempLastName:void 0,tempHealthCard:void 0,tempBirthDate:void 0}:{...t,editing:!0,tempFirstName:t.firstName,tempLastName:t.lastName,tempHealthCard:t.healthCard,tempBirthDate:t.birthDate}:t))},saveMemberChanges:e=>{let t=d.find(t=>t.id===e);if(!t)return!1;let n=void 0!==t.tempFirstName?t.tempFirstName:t.firstName,a=void 0!==t.tempLastName?t.tempLastName:t.lastName,i=void 0!==t.tempHealthCard?t.tempHealthCard:t.healthCard,o=void 0!==t.tempBirthDate?t.tempBirthDate:t.birthDate;return!!n&&!!a&&!!i&&4===i.length&&!!o&&(p(t=>{let n=t.map(t=>{if(t.id===e&&t.editing){let e={...t,editing:!1,firstName:void 0!==t.tempFirstName?t.tempFirstName:t.firstName,lastName:void 0!==t.tempLastName?t.tempLastName:t.lastName,healthCard:void 0!==t.tempHealthCard?t.tempHealthCard:t.healthCard,birthDate:void 0!==t.tempBirthDate?t.tempBirthDate:t.birthDate,tempFirstName:void 0,tempLastName:void 0,tempHealthCard:void 0,tempBirthDate:void 0};return(null==r?void 0:r.id)&&(e.firstName||e.lastName||e.healthCard||e.birthDate)&&l(r.id,e,t.id).then(e=>{e.success&&e.id&&p(r=>r.map(r=>r.id===t.id?{...r,supabaseId:e.id}:r))}).catch(e=>console.error("Error saving member:",e)),e}return t});if(n.length<5){let e=n.map(e=>e.id);for(let t=1;t<=5;t++)e.includes(t)||n.push({id:t,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1});n.sort((e,t)=>e.id-t.id)}return n}),!0)},deleteMember:e=>{let t=d.find(t=>t.id===e);if(!t||!t.supabaseId){p(t=>t.map(t=>t.id===e?{...t,firstName:"",lastName:"",healthCard:"",birthDate:void 0,editing:!1,tempFirstName:void 0,tempLastName:void 0,tempHealthCard:void 0,tempBirthDate:void 0}:t));return}(null==r?void 0:r.id)&&c(t.supabaseId).then(t=>{t.success&&p(t=>t.map(t=>t.id===e?{...t,firstName:"",lastName:"",healthCard:"",birthDate:void 0,supabaseId:void 0,editing:!1,tempFirstName:void 0,tempLastName:void 0,tempHealthCard:void 0,tempBirthDate:void 0}:t))}).catch(e=>console.error("Error deleting member:",e))},isLoading:g,error:f},children:t})}function p(){return(0,a.useContext)(m)}},6002:(e,t,r)=>{r.d(t,{T$:()=>s});var n=r(65127),a=r(57107),i=r(6159);function o(){return(0,n.jsx)("div",{className:"flex h-24 w-full items-center justify-center",children:(0,n.jsx)("div",{className:"h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-primary"})})}function s(e){let{ssr:t=!1,loading:r=o,displayName:s}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},l=(0,a.default)(e,{loading:r,ssr:t}),c=e=>(0,n.jsx)(i.Suspense,{fallback:(0,n.jsx)("loading",{}),children:(0,n.jsx)(l,{...e})});return s&&(c.displayName="Dynamic(".concat(s,")")),c}},12391:(e,t,r)=>{async function n(e){try{let t=await fetch("/api/appointment-requests",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await t.json();if(!t.ok)return{success:!1,error:r.error||"Failed to create appointment request",errorType:r.errorType};return{success:!0,data:r.data}}catch(e){return console.error("Error creating appointment request:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}r.d(t,{hb:()=>n}),r(98846)},35071:(e,t,r)=>{r.d(t,{A:()=>s,O:()=>d});var n=r(65127),a=r(6159),i=r(98846);let o=(0,a.createContext)({user:null,session:null,status:"loading",signIn:async()=>({success:!1}),signInWithGoogle:async()=>({success:!1}),signOut:async()=>{},refresh:async()=>{}}),s=()=>(0,a.useContext)(o),l="auth_cache",c="auth_cache_expiry",u="session_backup",m="external_return_detected";function d(e){let{children:t}=e,[r,s]=(0,a.useState)(null),[d,p]=(0,a.useState)(null),[g,h]=(0,a.useState)("loading"),f=(0,a.useCallback)(()=>{let e=localStorage.getItem(u),t=localStorage.getItem(l),r=new URLSearchParams(window.location.search).has("session_id")||window.location.pathname.includes("/compte/abonnement")||document.referrer.includes("stripe.com"),n=sessionStorage.getItem(m);return!!(e&&(!t||r||n))},[]),v=(0,a.useCallback)(e=>{localStorage.setItem(u,JSON.stringify({session:e,timestamp:Date.now(),user:e.user}))},[]),b=(0,a.useCallback)(async()=>{let e=localStorage.getItem(u);if(!e)return null;try{let{session:t,timestamp:r}=JSON.parse(e);if(Date.now()-r>72e5)return localStorage.removeItem(u),null;if(null==t?void 0:t.access_token){console.log("Attempting to restore session from backup...");let{data:e,error:r}=await i.N.auth.setSession({access_token:t.access_token,refresh_token:t.refresh_token});if(!r&&e.session)return console.log("Successfully restored session from backup"),localStorage.removeItem(u),e.session}}catch(e){console.error("Error restoring session from backup:",e),localStorage.removeItem(u)}return null},[]),y=(0,a.useCallback)(async()=>{try{h("loading");let t=f();if(t){console.log("External return detected, attempting session restoration..."),sessionStorage.removeItem(m);let e=await b();if(e){p(e),s(e.user),h("authenticated"),localStorage.setItem(l,JSON.stringify({session:e,user:e.user})),localStorage.setItem(c,(Date.now()+18e5).toString());return}}{let r=localStorage.getItem(l),n=localStorage.getItem(c);if(r&&n){let a=parseInt(n,10);if(Date.now()<a&&!t)try{var e;let{session:t,user:n}=JSON.parse(r),{data:a,error:o}=await i.N.auth.getSession();if(o||(null==a?void 0:null===(e=a.session)||void 0===e?void 0:e.access_token)!==(null==t?void 0:t.access_token))localStorage.removeItem(l),localStorage.removeItem(c);else{p(t),s(n),h(t?"authenticated":"unauthenticated");return}}catch(e){console.error("Error parsing cached auth data:",e),localStorage.removeItem(l),localStorage.removeItem(c)}}}let r=null,n=null,a=await i.N.auth.getSession();if(r=a.data,((n=a.error)||!(null==r?void 0:r.session))&&t){console.log("First session attempt failed, trying refresh...");let e=await i.N.auth.refreshSession();!e.error&&e.data.session&&(r={session:e.data.session},n=null)}if(n)throw n;(null==r?void 0:r.session)?(p(r.session),s(r.session.user),h("authenticated"),v(r.session),localStorage.setItem(l,JSON.stringify({session:r.session,user:r.session.user})),localStorage.setItem(c,(Date.now()+18e5).toString())):(p(null),s(null),h("unauthenticated"),localStorage.removeItem(l),localStorage.removeItem(c),localStorage.removeItem(u))}catch(e){h("unauthenticated"),p(null),s(null),localStorage.removeItem(l),localStorage.removeItem(c),console.error("Error refreshing auth state:",e)}},[f,b,v]),w=(0,a.useCallback)(async(e,t)=>{try{let{data:r,error:n}=await i.N.auth.signInWithPassword({email:e,password:t});if(n)throw n;if(r.session){p(r.session),s(r.session.user),h("authenticated"),v(r.session),localStorage.setItem(l,JSON.stringify({session:r.session,user:r.session.user})),localStorage.setItem(c,(Date.now()+18e5).toString());let e=[];for(let t=0;t<sessionStorage.length;t++){let r=sessionStorage.key(t);r&&(r.includes("auth")||r.includes("logout")||r.includes("supabase"))&&e.push(r)}e.forEach(e=>sessionStorage.removeItem(e))}return{success:!0}}catch(e){return console.error("Sign in error:",e),{success:!1,error:e}}},[v]),S=(0,a.useCallback)(async()=>{try{let e=window.location.origin,t="".concat(e,"/auth/callback?redirectTo=/dashboard");console.log("Setting up Google auth with callback URL: ".concat(t));let{data:r,error:n}=await i.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:t}});if(n)throw n;return{success:!0}}catch(e){return console.error("Google sign in error:",e),{success:!1,error:e}}},[]),N=(0,a.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/";try{await i.N.auth.signOut(),p(null),s(null),h("unauthenticated"),localStorage.removeItem(l),localStorage.removeItem(c),localStorage.removeItem(u),localStorage.removeItem("supabase.auth.token"),localStorage.removeItem("theme"),(null==r?void 0:r.id)&&localStorage.removeItem("user_preferences_".concat(r.id)),sessionStorage.setItem("isLoggedOut","true"),window.location.href=e}catch(t){console.error("Sign out error:",t),p(null),s(null),h("unauthenticated"),window.location.href=e}},[r]);return(0,a.useEffect)(()=>{y();let{data:e}=i.N.auth.onAuthStateChange((e,t)=>{"SIGNED_IN"===e&&t?(p(t),s(t.user),h("authenticated"),v(t),localStorage.setItem(l,JSON.stringify({session:t,user:t.user})),localStorage.setItem(c,(Date.now()+18e5).toString())):"SIGNED_OUT"===e&&(p(null),s(null),h("unauthenticated"),localStorage.removeItem(l),localStorage.removeItem(c),localStorage.removeItem(u))}),t=()=>{"visible"===document.visibilityState&&(sessionStorage.setItem(m,"true"),setTimeout(()=>{y()},100))},r=()=>{sessionStorage.setItem(m,"true"),setTimeout(()=>{y()},100)};return document.addEventListener("visibilitychange",t),window.addEventListener("focus",r),()=>{e.subscription.unsubscribe(),document.removeEventListener("visibilitychange",t),window.removeEventListener("focus",r)}},[y,v]),(0,n.jsx)(o.Provider,{value:{user:r,session:d,status:g,signIn:w,signInWithGoogle:S,signOut:N,refresh:y},children:t})}},36965:(e,t,r)=>{r.d(t,{V:()=>i,e:()=>a});var n=r(98846);async function a(e,t,r){var a,i;if(t.email&&t.email!==(null==r?void 0:null===(a=r.user)||void 0===a?void 0:a.email)&&!r)throw Error("Auth session is required to update email");try{let e=[],a={},o=!1;if(void 0!==t.firstName&&(a.first_name=t.firstName,o=!0),void 0!==t.lastName&&(a.last_name=t.lastName,o=!0),void 0!==t.phone&&(a.phone=t.phone,o=!0),o){let{data:t,error:r}=await n.N.auth.updateUser({data:a});if(r)throw Error("Error updating user metadata: ".concat(r.message));e.push("metadata")}if(void 0!==t.email&&(null==r?void 0:null===(i=r.user)||void 0===i?void 0:i.email)!==t.email){let{data:r,error:a}=await n.N.auth.updateUser({email:t.email});if(a)throw Error("Error updating email: ".concat(a.message));e.push("email")}return{success:e.length>0||0===Object.keys(t).length,updates:e}}catch(e){throw console.error("Error updating user profile:",e),e}}async function i(e){try{let{data:t,error:r}=await n.N.auth.getUser();if(r)throw r;let a=null;try{let{data:t,error:r}=await n.N.from("users").select("*").eq("id",e).single();r||(a=t)}catch(e){console.warn("Error getting public user data:",e)}let i=t.user.user_metadata||{},o=i.first_name||"",s=i.last_name||"";return o||(o=(null==a?void 0:a.first_name)||""),s||(s=(null==a?void 0:a.last_name)||""),{id:e,email:t.user.email,firstName:o,lastName:s,phone:i.phone||"",updatedAt:(null==a?void 0:a.updated_at)||new Date().toISOString(),...a||{}}}catch(e){throw console.error("Error getting user profile:",e),e}}},37595:(e,t,r)=>{r.d(t,{X:()=>i});var n=r(6159),a=r(35071);let i=()=>{let{refresh:e}=(0,a.A)(),t=(0,n.useCallback)(()=>{let e=new URLSearchParams(window.location.search),t=window.location.pathname,r=document.referrer;return e.has("session_id")||t.includes("/compte/abonnement")||r.includes("stripe.com")||r.includes("checkout.stripe.com")||"true"===sessionStorage.getItem("stripe_redirect")},[]),r=(0,n.useCallback)(()=>{sessionStorage.setItem("stripe_redirect","true"),sessionStorage.setItem("stripe_redirect_time",Date.now().toString())},[]),i=(0,n.useCallback)(()=>{sessionStorage.removeItem("stripe_redirect"),sessionStorage.removeItem("stripe_redirect_time")},[]);return(0,n.useEffect)(()=>{let n=()=>{"visible"===document.visibilityState&&t()&&(console.log("Stripe return detected, refreshing auth state..."),i(),setTimeout(()=>{e()},500))},a=()=>{t()&&(console.log("Window focus with Stripe return detected, refreshing auth state..."),i(),setTimeout(()=>{e()},500))},o=()=>{window.location.pathname.includes("/compte/abonnement")&&r()};return document.addEventListener("visibilitychange",n),window.addEventListener("focus",a),window.addEventListener("beforeunload",o),t()&&(console.log("Stripe return detected on mount, refreshing auth state..."),i(),setTimeout(()=>{e()},100)),()=>{document.removeEventListener("visibilitychange",n),window.removeEventListener("focus",a),window.removeEventListener("beforeunload",o)}},[t,r,i,e]),{detectStripeReturn:t,markStripeNavigation:r,clearStripeNavigation:i}}},49290:(e,t,r)=>{r.d(t,{o:()=>a});var n=r(93525);let a={errors:{tryAgainLater:(0,n.re)("errors","tryAgainLater"),saveFailed:(0,n.re)("errors","saveFailed"),notFound:(0,n.re)("errors","notFound"),unauthorized:(0,n.re)("errors","unauthorized"),generalError:(0,n.re)("errors","generalError")},common:{save:(0,n.re)("common","save"),cancel:(0,n.re)("common","cancel"),confirm:(0,n.re)("common","confirm"),delete:(0,n.re)("common","delete"),loading:(0,n.re)("common","loading"),search:(0,n.re)("common","search"),logout:(0,n.re)("common","logout"),loggingOut:(0,n.re)("common","loggingOut"),close:(0,n.re)("common","close"),active:(0,n.re)("common","active"),inactive:(0,n.re)("common","inactive"),submit:(0,n.re)("common","submit"),submitting:(0,n.re)("common","submitting"),processing:(0,n.re)("common","processing"),newRequest:(0,n.re)("common","newRequest"),required:(0,n.re)("common","required"),yes:(0,n.re)("common","yes"),no:(0,n.re)("common","no"),continue:(0,n.re)("common","continue"),manage:(0,n.re)("common","manage"),modify:(0,n.re)("common","modify"),back:(0,n.re)("common","back"),saved:(0,n.re)("common","saved"),saving:(0,n.re)("common","saving"),saveChanges:(0,n.re)("common","saveChanges"),errorOccurred:(0,n.re)("common","errorOccurred")},auth:{signIn:(0,n.re)("auth","signIn"),signUp:(0,n.re)("auth","signUp"),email:(0,n.re)("auth","email"),password:(0,n.re)("auth","password"),confirmPassword:(0,n.re)("auth","confirmPassword"),forgotPassword:(0,n.re)("auth","forgotPassword"),resetPassword:(0,n.re)("auth","resetPassword"),enterCredentials:(0,n.re)("auth","enterCredentials"),createAccount:(0,n.re)("auth","createAccount"),alreadyHaveAccount:(0,n.re)("auth","alreadyHaveAccount"),noAccount:(0,n.re)("auth","noAccount"),passwordReset:(0,n.re)("auth","passwordReset"),passwordResetInstructions:(0,n.re)("auth","passwordResetInstructions"),passwordResetSent:(0,n.re)("auth","passwordResetSent"),successfulReset:(0,n.re)("auth","successfulReset"),firstName:(0,n.re)("auth","firstName"),lastName:(0,n.re)("auth","lastName"),errors:{invalidCredentials:(0,n.re)("auth.errors","invalidCredentials"),passwordsDontMatch:(0,n.re)("auth.errors","passwordsDontMatch"),emailInUse:(0,n.re)("auth.errors","emailInUse"),invalidEmail:(0,n.re)("auth.errors","invalidEmail"),passwordTooShort:(0,n.re)("auth.errors","passwordTooShort"),generalError:(0,n.re)("auth.errors","generalError")}},nav:{home:(0,n.re)("nav","home"),dashboard:(0,n.re)("nav","dashboard"),appointments:(0,n.re)("nav","appointments"),findAppointment:(0,n.re)("nav","findAppointment"),calendar:(0,n.re)("nav","calendar"),help:(0,n.re)("nav","help"),account:(0,n.re)("nav","account"),mobileNavigation:(0,n.re)("nav","mobileNavigation"),needHelp:(0,n.re)("nav","needHelp")},account:{profile:(0,n.re)("account","profile"),preferences:(0,n.re)("account","preferences"),subscription:(0,n.re)("account","subscription"),users:(0,n.re)("account","users"),manageUsers:(0,n.re)("account","manageUsers"),yourAccount:(0,n.re)("account","yourAccount"),individualPlan:(0,n.re)("account","individualPlan"),individualPlanMonthly:(0,n.re)("account","individualPlanMonthly"),individualPlanAnnual:(0,n.re)("account","individualPlanAnnual"),familyPlan:(0,n.re)("account","familyPlan"),familyPlanMonthly:(0,n.re)("account","familyPlanMonthly"),familyPlanAnnual:(0,n.re)("account","familyPlanAnnual"),manageInformation:(0,n.re)("account","manageInformation"),personalInfoDescription:(0,n.re)("account","personalInfoDescription"),modifyProfile:(0,n.re)("account","modifyProfile"),modifySubscription:(0,n.re)("account","modifySubscription"),subscriptionDescription:(0,n.re)("account","subscriptionDescription"),manageSubscription:(0,n.re)("account","manageSubscription"),appearanceLanguage:(0,n.re)("account","appearanceLanguage"),appearanceDescription:(0,n.re)("account","appearanceDescription"),modifyPreferences:(0,n.re)("account","modifyPreferences"),manageAccountUsers:(0,n.re)("account","manageAccountUsers"),manageUsersDescription:(0,n.re)("account","manageUsersDescription"),manageProfile:(0,n.re)("account","manageProfile"),manageProfileDescription:(0,n.re)("account","manageProfileDescription"),manageProfileButton:(0,n.re)("account","manageProfileButton"),subscribePlan:(0,n.re)("account","subscribePlan"),choosePlan:(0,n.re)("account","choosePlan"),editPersonalInfo:(0,n.re)("account","editPersonalInfo"),firstName:(0,n.re)("account","firstName"),lastName:(0,n.re)("account","lastName"),email:(0,n.re)("account","email"),phone:(0,n.re)("account","phone"),invalidEmail:(0,n.re)("account","invalidEmail"),invalidPhone:(0,n.re)("account","invalidPhone"),emailCannotBeEmpty:(0,n.re)("account","emailCannotBeEmpty"),firstNameRequired:(0,n.re)("account","firstNameRequired"),lastNameRequired:(0,n.re)("account","lastNameRequired"),emailVerificationSent:(0,n.re)("account","emailVerificationSent")},home:{greeting:(0,n.re)("home","greeting"),welcome:(0,n.re)("home","welcome"),findAppointmentTitle:(0,n.re)("home","findAppointmentTitle"),findAppointmentDesc:(0,n.re)("home","findAppointmentDesc"),viewRequests:(0,n.re)("home","viewRequests"),manageAppointmentsDesc:(0,n.re)("home","manageAppointmentsDesc"),manageUsersDesc:(0,n.re)("home","manageUsersDesc"),manageProfileTitle:(0,n.re)("home","manageProfileTitle"),manageProfileDesc:(0,n.re)("home","manageProfileDesc"),manageProfileButton:(0,n.re)("home","manageProfileButton")},appointments:{title:(0,n.re)("appointments","title"),description:(0,n.re)("appointments","description"),requestsTitle:(0,n.re)("appointments","requestsTitle"),all:(0,n.re)("appointments","all"),inProgress:(0,n.re)("appointments","inProgress"),completed:(0,n.re)("appointments","completed"),noRequests:(0,n.re)("appointments","noRequests"),noRequestsInProgress:(0,n.re)("appointments","noRequestsInProgress"),noRequestsCompleted:(0,n.re)("appointments","noRequestsCompleted"),noRequestsCancelled:(0,n.re)("appointments","noRequestsCancelled"),postalCode:(0,n.re)("appointments","postalCode"),sentOn:(0,n.re)("appointments","sentOn"),pending:(0,n.re)("appointments","pending"),done:(0,n.re)("appointments","done"),cancelAppointment:(0,n.re)("appointments","cancelAppointment"),cancelConfirmation:(0,n.re)("appointments","cancelConfirmation"),cancelConfirmationText:(0,n.re)("appointments","cancelConfirmationText"),noContinue:(0,n.re)("appointments","noContinue"),yesCancel:(0,n.re)("appointments","yesCancel"),viewAll:(0,n.re)("appointments","viewAll")},meta:{title:(0,n.re)("meta","title"),description:(0,n.re)("meta","description")},subscription:{modifySubscription:(0,n.re)("subscription","modifySubscription"),individualPlan:(0,n.re)("subscription","individualPlan"),monthlyCost:(0,n.re)("subscription","monthlyCost"),benefits:(0,n.re)("subscription","benefits"),unlimitedAccess:(0,n.re)("subscription","unlimitedAccess"),emailNotifications:(0,n.re)("subscription","emailNotifications"),familyProfiles:(0,n.re)("subscription","familyProfiles"),modifyPlan:(0,n.re)("subscription","modifyPlan"),cancelPlan:(0,n.re)("subscription","cancelPlan"),paymentHistory:(0,n.re)("subscription","paymentHistory"),monthlySubscription:(0,n.re)("subscription","monthlySubscription"),march:(0,n.re)("subscription","march"),february:(0,n.re)("subscription","february"),january:(0,n.re)("subscription","january"),cost:(0,n.re)("subscription","cost"),changePlan:(0,n.re)("subscription","changePlan"),changePlanDescription:(0,n.re)("subscription","changePlanDescription"),confirmChange:(0,n.re)("subscription","confirmChange"),cancelConfirmation:(0,n.re)("subscription","cancelConfirmation"),cancelWarning:(0,n.re)("subscription","cancelWarning"),yesCancel:(0,n.re)("subscription","yesCancel"),noCancel:(0,n.re)("subscription","noCancel"),status:(0,n.re)("subscription","status"),verifyingPayment:(0,n.re)("subscription","verifyingPayment"),success:(0,n.re)("subscription","success"),successMessage:(0,n.re)("subscription","successMessage"),details:(0,n.re)("subscription","details"),plan:(0,n.re)("subscription","plan"),billing:(0,n.re)("subscription","billing"),amount:(0,n.re)("subscription","amount"),currentPeriod:(0,n.re)("subscription","currentPeriod"),nextSteps:(0,n.re)("subscription","nextSteps"),goToDashboard:(0,n.re)("subscription","goToDashboard"),manageAccount:(0,n.re)("subscription","manageAccount"),error:(0,n.re)("subscription","error"),errorMessage:(0,n.re)("subscription","errorMessage"),needHelp:(0,n.re)("subscription","needHelp"),returnToPlans:(0,n.re)("subscription","returnToPlans"),contactSupport:(0,n.re)("subscription","contactSupport"),canceledCheckout:(0,n.re)("subscription","canceledCheckout"),processingSubscription:(0,n.re)("subscription","processingSubscription"),noSessionId:(0,n.re)("subscription","noSessionId"),notLoggedIn:(0,n.re)("subscription","notLoggedIn")},preferences:{managePreferences:(0,n.re)("preferences","managePreferences"),languageAppearance:(0,n.re)("preferences","languageAppearance"),customizeInterface:(0,n.re)("preferences","customizeInterface"),preferredLanguage:(0,n.re)("preferences","preferredLanguage"),french:(0,n.re)("preferences","french"),english:(0,n.re)("preferences","english"),languageDescription:(0,n.re)("preferences","languageDescription"),appTheme:(0,n.re)("preferences","appTheme"),light:(0,n.re)("preferences","light"),dark:(0,n.re)("preferences","dark"),themeDescription:(0,n.re)("preferences","themeDescription"),saveChanges:(0,n.re)("preferences","saveChanges"),saving:(0,n.re)("preferences","saving"),changesSaved:(0,n.re)("preferences","changesSaved"),errorSaving:(0,n.re)("preferences","errorSaving")},users:{manageAccountUsers:(0,n.re)("users","manageAccountUsers"),manageProfile:(0,n.re)("users","manageProfile"),familyMembers:(0,n.re)("users","familyMembers"),userProfile:(0,n.re)("users","userProfile"),familyMembersDescription:(0,n.re)("users","familyMembersDescription"),userProfileDescription:(0,n.re)("users","userProfileDescription"),addYourInfo:(0,n.re)("users","addYourInfo"),addYourInfoPrompt:(0,n.re)("users","addYourInfoPrompt"),cancel:(0,n.re)("users","cancel"),firstName:(0,n.re)("users","firstName"),lastName:(0,n.re)("users","lastName"),healthCardPrefix:(0,n.re)("users","healthCardPrefix"),healthCardDescription:(0,n.re)("users","healthCardDescription"),birthDate:(0,n.re)("users","birthDate"),save:(0,n.re)("users","save"),edit:(0,n.re)("users","edit"),healthCard:(0,n.re)("users","healthCard"),addMember:(0,n.re)("users","addMember"),editMemberPrompt:(0,n.re)("users","editMemberPrompt"),selectDate:(0,n.re)("users","selectDate"),validationError:(0,n.re)("users","validationError")},help:{needHelp:(0,n.re)("help","needHelp"),helpDescription:(0,n.re)("help","helpDescription"),faq:(0,n.re)("help","faq"),faqDescription:(0,n.re)("help","faqDescription"),howToBookAppointment:(0,n.re)("help","howToBookAppointment"),howToBookDescription:(0,n.re)("help","howToBookDescription"),howToCancelAppointment:(0,n.re)("help","howToCancelAppointment"),howToCancelDescription:(0,n.re)("help","howToCancelDescription"),howToChangePlan:(0,n.re)("help","howToChangePlan"),howToChangePlanDescription:(0,n.re)("help","howToChangePlanDescription"),customerSupport:(0,n.re)("help","customerSupport"),supportDescription:(0,n.re)("help","supportDescription"),email:(0,n.re)("help","email"),supportEmail:(0,n.re)("help","supportEmail"),responseTime:(0,n.re)("help","responseTime"),contactSupport:(0,n.re)("help","contactSupport")},landing:{hero:{title:(0,n.re)("landing.hero","title"),subtitle:(0,n.re)("landing.hero","subtitle"),findAppointment:(0,n.re)("landing.hero","findAppointment"),learnMore:(0,n.re)("landing.hero","learnMore"),imageAlt:(0,n.re)("landing.hero","imageAlt")},features:{sameDay:{title:(0,n.re)("landing.features.sameDay","title"),description:(0,n.re)("landing.features.sameDay","description")},nearbyClinic:{title:(0,n.re)("landing.features.nearbyClinic","title"),description:(0,n.re)("landing.features.nearbyClinic","description")},anywhereInQuebec:{title:(0,n.re)("landing.features.anywhereInQuebec","title"),description:(0,n.re)("landing.features.anywhereInQuebec","description")}},howItWorks:{title:(0,n.re)("landing.howItWorks","title"),customAppointments:{title:(0,n.re)("landing.howItWorks.customAppointments","title"),description:(0,n.re)("landing.howItWorks.customAppointments","description")},easyManagement:{title:(0,n.re)("landing.howItWorks.easyManagement","title"),description:(0,n.re)("landing.howItWorks.easyManagement","description")},imageAlt:(0,n.re)("landing.howItWorks","imageAlt")},pricing:{title:(0,n.re)("landing.pricing","title"),description:(0,n.re)("landing.pricing","description"),period:{monthly:(0,n.re)("landing.pricing.period","monthly"),annually:(0,n.re)("landing.pricing.period","annually")},individual:{title:(0,n.re)("landing.pricing.individual","title"),description:(0,n.re)("landing.pricing.individual","description"),features:(0,n.re)("landing.pricing.individual","features"),annualSavings:(0,n.re)("landing.pricing.individual","annualSavings")},family:{title:(0,n.re)("landing.pricing.family","title"),description:(0,n.re)("landing.pricing.family","description"),features:(0,n.re)("landing.pricing.family","features"),annualSavings:(0,n.re)("landing.pricing.family","annualSavings")},choosePlan:(0,n.re)("landing.pricing","choosePlan"),included:(0,n.re)("landing.pricing","included"),manageSubscription:(0,n.re)("landing.pricing","manageSubscription"),feature1:(0,n.re)("landing.pricing","feature1"),feature2:(0,n.re)("landing.pricing","feature2"),feature3:(0,n.re)("landing.pricing","feature3"),feature4:(0,n.re)("landing.pricing","feature4")},faq:{title:(0,n.re)("landing.faq","title"),viewFullFaq:(0,n.re)("landing.faq","viewFullFaq"),questions:[{question:(0,n.re)("landing.faq.questions.0","question"),answer:(0,n.re)("landing.faq.questions.0","answer")},{question:(0,n.re)("landing.faq.questions.1","question"),answer:(0,n.re)("landing.faq.questions.1","answer")},{question:(0,n.re)("landing.faq.questions.2","question"),answer:(0,n.re)("landing.faq.questions.2","answer")},{question:(0,n.re)("landing.faq.questions.3","question"),answer:(0,n.re)("landing.faq.questions.3","answer")},{question:(0,n.re)("landing.faq.questions.4","question"),answer:(0,n.re)("landing.faq.questions.4","answer")}]},cta:{title:(0,n.re)("landing.cta","title"),subtitle:(0,n.re)("landing.cta","subtitle"),buttonText:(0,n.re)("landing.cta","buttonText"),imageAlt:(0,n.re)("landing.cta","imageAlt")},navbar:{title:(0,n.re)("landing.navbar","title"),signIn:(0,n.re)("landing.navbar","signIn"),signUp:(0,n.re)("landing.navbar","signUp"),service:(0,n.re)("landing.navbar","service"),pricing:(0,n.re)("landing.navbar","pricing"),faq:(0,n.re)("landing.navbar","faq")},footer:{description:(0,n.re)("landing.footer","description"),contactUs:(0,n.re)("landing.footer","contactUs"),privacyPolicy:(0,n.re)("landing.footer","privacyPolicy"),termsOfUse:(0,n.re)("landing.footer","termsOfUse"),termsOfSale:(0,n.re)("landing.footer","termsOfSale"),copyright:(0,n.re)("landing.footer","copyright")}},findAppointment:{title:(0,n.re)("findAppointment","title"),description:(0,n.re)("findAppointment","description"),searchCriteria:(0,n.re)("findAppointment","searchCriteria"),requiredFields:(0,n.re)("findAppointment","requiredFields"),appointmentFor:(0,n.re)("findAppointment","appointmentFor"),selectPerson:(0,n.re)("findAppointment","selectPerson"),managedInUsersSection:(0,n.re)("findAppointment","managedInUsersSection"),healthCard:(0,n.re)("findAppointment","healthCard"),healthCardOf:(0,n.re)("findAppointment","healthCardOf"),lastDigits:(0,n.re)("findAppointment","lastDigits"),enterEightDigits:(0,n.re)("findAppointment","enterEightDigits"),format:(0,n.re)("findAppointment","format"),sequenceNumber:(0,n.re)("findAppointment","sequenceNumber"),sequenceInfo:(0,n.re)("findAppointment","sequenceInfo"),enterTwoDigits:(0,n.re)("findAppointment","enterTwoDigits"),postalCode:(0,n.re)("findAppointment","postalCode"),postalExample:(0,n.re)("findAppointment","postalExample"),invalidPostalFormat:(0,n.re)("findAppointment","invalidPostalFormat"),postalFormatWarning:(0,n.re)("findAppointment","postalFormatWarning"),postalCodeDescription:(0,n.re)("findAppointment","postalCodeDescription"),fromDate:(0,n.re)("findAppointment","fromDate"),selectDate:(0,n.re)("findAppointment","selectDate"),appointmentTime:(0,n.re)("findAppointment","appointmentTime"),chooseTime:(0,n.re)("findAppointment","chooseTime"),morning:(0,n.re)("findAppointment","morning"),afternoon:(0,n.re)("findAppointment","afternoon"),evening:(0,n.re)("findAppointment","evening"),asap:(0,n.re)("findAppointment","asap"),submitRequest:(0,n.re)("findAppointment","submitRequest"),thankYou:(0,n.re)("findAppointment","thankYou"),confirmationMessage:(0,n.re)("findAppointment","confirmationMessage"),viewRequests:(0,n.re)("findAppointment","viewRequests"),selectDateError:(0,n.re)("findAppointment","selectDateError"),selectTimeError:(0,n.re)("findAppointment","selectTimeError"),enterPostalError:(0,n.re)("findAppointment","enterPostalError"),invalidPostalError:(0,n.re)("findAppointment","invalidPostalError"),selectPersonError:(0,n.re)("findAppointment","selectPersonError"),healthCardDigitsError:(0,n.re)("findAppointment","healthCardDigitsError"),sequenceNumberError:(0,n.re)("findAppointment","sequenceNumberError"),noSubscription:(0,n.re)("findAppointment","noSubscription")}}},49550:(e,t,r)=>{r.d(t,{b:()=>a,o:()=>i});var n=r(98846);async function a(e,t){try{let{error:r}=await n.N.from("users").update({language:t.language,theme:t.theme,updated_at:new Date().toISOString()}).eq("id",e);if(r)throw r;return{success:!0}}catch(e){return console.error("Error saving user preferences:",e),{success:!1,error:e}}}async function i(e){try{let{data:t,error:r}=await n.N.from("users").select("language, theme").eq("id",e).maybeSingle();if(r)throw r;if(!t||!t.language&&!t.theme)return{language:"fr",theme:"light"};return{language:t.language||"fr",theme:t.theme||"light"}}catch(e){return console.error("Error getting user preferences:",e),null}}},70992:(e,t,r)=>{r.d(t,{cn:()=>i});var n=r(8795),a=r(30367);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}},75616:(e,t,r)=>{r.d(t,{Y9:()=>a});var n=r(98846);let a=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/";try{await n.N.auth.signOut(),sessionStorage.setItem("isLoggedOut","true"),localStorage.removeItem("supabase.auth.token"),window.location.href=e}catch(t){console.error("Error during complete sign out:",t),window.location.href=e}}},79071:(e,t,r)=>{r.d(t,{E:()=>s,cn:()=>i,d:()=>o});var n=r(8795),a=r(30367);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8,r=e.replace(/\D/g,"").substring(0,t);return r.length<=4?r:"".concat(r.slice(0,4),"-").concat(r.slice(4))}function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8;return e.replace(/\D/g,"").length===t}},88473:(e,t,r)=>{r.d(t,{F:()=>s,o:()=>o});var n=r(65127),a=r(6159);let i=(0,a.createContext)({notifyProfileUpdate:()=>{},lastUpdatedUserId:null,lastUpdateTimestamp:0});function o(e){let{children:t}=e,[r,o]=(0,a.useState)(null),[s,l]=(0,a.useState)(0);return(0,n.jsx)(i.Provider,{value:{notifyProfileUpdate:e=>{o(e),l(Date.now())},lastUpdatedUserId:r,lastUpdateTimestamp:s},children:t})}let s=()=>(0,a.useContext)(i)},91303:(e,t,r)=>{r.d(t,{LanguageProvider:()=>m,o:()=>u});var n=r(65127),a=r(6159),i=r(93525),o=r(49550),s=r(98846),l=r(35071);let c=(0,a.createContext)({language:"fr",translations:{},setLanguage:()=>{},translate:e=>e,isLoading:!0}),u=()=>(0,a.useContext)(c);function m(e){let{children:t}=e,[r,u]=(0,a.useState)("fr"),[m,d]=(0,a.useState)({}),[p,g]=(0,a.useState)(!0),{user:h,status:f}=(0,l.A)(),v=async e=>{console.log("Language context: changing to",e),g(!0);try{u(e),localStorage.setItem("language",e),document.documentElement.lang=e;let t=await (0,i.Zi)(e);if(d(t),"authenticated"===f&&h)try{await (0,o.b)(h.id,{language:e});let{error:t}=await s.N.auth.updateUser({data:{language:e}});t?console.error("Error updating user metadata with language preference:",t):console.log("User metadata updated with language preference:",e)}catch(e){console.error("Error saving language preference to Supabase:",e)}console.log("Language context updated successfully to",e)}catch(t){console.error("Failed to load ".concat(e," translations:"),t)}finally{g(!1)}};return(0,a.useEffect)(()=>{(async function(){let e="fr",t=localStorage.getItem("language");if(t&&("fr"===t||"en"===t)&&(e=t),"authenticated"===f&&h)try{let t=await (0,o.o)(h.id);t&&t.language&&(e=t.language,localStorage.setItem("language",e))}catch(e){console.error("Error loading language preference from Supabase:",e)}v(e)})()},[f,h]),(0,n.jsx)(c.Provider,{value:{language:r,translations:m,setLanguage:v,translate:e=>m[e]||e,isLoading:p},children:t})}},93525:(e,t,r)=>{r.d(t,{Zi:()=>a,re:()=>i});let n={en:null,fr:null};async function a(e){if(n[e])return n[e];try{let t=await fetch("/translations/".concat(e,".json"));if(!t.ok)throw Error("Failed to load ".concat(e," translations: ").concat(t.statusText));let r=await t.json();return n[e]=r,r}catch(t){return console.error("Error loading ".concat(e," translations:"),t),{}}}function i(e,t){return"".concat(e,".").concat(t)}},94417:(e,t,r)=>{r.d(t,{w:()=>a});var n=r(6159);let a=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,r]=(0,n.useState)(!1),a=(0,n.useRef)(null),{threshold:i=.1,rootMargin:o="0px"}=e;return(0,n.useEffect)(()=>{let e=new IntersectionObserver(t=>{let[n]=t;n.isIntersecting&&(r(!0),a.current&&e.unobserve(a.current))},{threshold:i,rootMargin:o}),t=a.current;return t&&e.observe(t),()=>{t&&e.unobserve(t)}},[i,o]),{ref:a,isIntersecting:t}}},98846:(e,t,r)=>{r.d(t,{N:()=>n});let n=(0,r(57386).UU)("https://tfvswgreslsbctjrvdbd.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRmdnN3Z3Jlc2xzYmN0anJ2ZGJkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIzMjEyODUsImV4cCI6MjA1Nzg5NzI4NX0.5bNos8JS_77EUjWOxIGHpgNaYE28qQvO2g4qj0wRWiU")}}]);