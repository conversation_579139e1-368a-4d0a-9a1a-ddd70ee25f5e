-- Create family_members table for storing user's family information
CREATE TABLE IF NOT EXISTS family_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  health_card TEXT NOT NULL CHECK (length(health_card) <= 4),
  birth_date TIMESTAMP WITH TIME ZONE,
  position INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS (Row Level Security) policies
ALTER TABLE family_members ENABLE ROW LEVEL SECURITY;

-- Allow users to only see and modify their own family members
CREATE POLICY "Users can view their own family members" 
  ON family_members
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own family members" 
  ON family_members
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own family members" 
  ON family_members
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own family members" 
  ON family_members
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX family_members_user_id_idx ON family_members(user_id);
CREATE INDEX family_members_position_idx ON family_members(position);
