{"name": "medical-dashboard", "version": "0.1.0", "description": "A medical appointment dashboard application with a French UI", "private": true, "scripts": {"dev": "bun --bun next dev -H 0.0.0.0", "build": "bun --bun next build", "start": "bun --bun next start", "lint": "bun --bun eslint src/ --max-warnings=0 --ignore-pattern '.next/' --ignore-pattern 'node_modules/' --ignore-pattern build/", "postinstall": "echo '✅ SRVXP dependencies installed with Bun!'", "clean": "rm -rf .next build node_modules bun.lockb", "update-deps": "bun update --latest"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-tooltip": "^1.1.8", "@stripe/stripe-js": "^7.1.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.25", "date-fns": "3.6.0", "framer-motion": "^12.5.0", "lucide-react": "^0.475.0", "motion": "12.6.2", "next": "^15.2.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "stripe": "^18.0.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}