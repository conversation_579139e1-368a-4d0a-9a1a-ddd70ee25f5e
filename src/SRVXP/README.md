# Sans Rendez-Vous Express

Une application de prise de rendez-vous médicaux développée avec Next.js et Supabase.

## Fonctionnalités principales

- Système d'authentification avec Supabase (inscription, connexion, récupération de mot de passe)
- Interface en français et en anglais
- Recherche de rendez-vous médicaux par code postal
- Système de paiement avec Stripe pour les abonnements
- Interface d'administration pour les médecins et cliniques
- Gestion de profils familiaux pour les utilisateurs

## Configuration technique

### Prérequis

- [Bun](https://bun.sh/) (recommandé) ou Node.js 18+
- Compte Supabase (gratuit pour commencer)
- Compte Stripe (optionnel, pour les paiements)

### Configuration de Supabase

1. Créez un compte sur [Supabase](https://supabase.com)
2. Créez un nouveau projet
3. Dans les paramètres du projet, copiez l'URL du projet et la clé anon
4. Créez un fichier `.env.local` à la racine du projet et ajoutez-y les variables suivantes:

```
NEXT_PUBLIC_SUPABASE_URL=votre-url-supabase
NEXT_PUBLIC_SUPABASE_ANON_KEY=votre-clé-anon-supabase
NEXT_PUBLIC_URL=http://localhost:3000
```

5. Dans Supabase, allez dans Authentication > Settings et configurez les options suivantes:
   - Activez "Email auth" dans les providers
   - Configurez les URL de redirection pour qu'elles pointent vers `http://localhost:3000/auth/callback`
   - Désactivez "Email confirmations" pour les tests (optionnel)

### Démarrage du projet

```bash
# Installation des dépendances
bun install
# ou avec npm (non recommandé)
npm install

# Démarrage du serveur de développement
bun dev
# ou avec npm (non recommandé)
npm run dev
```

> **Note**: Ce projet est configuré pour utiliser Bun pour des performances optimales. Voir [BUN_README.md](./BUN_README.md) pour plus d'informations.

Ouvrez [http://localhost:3000](http://localhost:3000) dans votre navigateur pour voir le résultat.

## Structure du projet

- `/src/app` - Les pages de l'application
  - `/auth` - Pages d'authentification (connexion, inscription, etc.)
  - `/landing` - Page d'accueil publique
  - `/dashboard` - Interface utilisateur après connexion
  - `/compte` - Gestion du compte utilisateur
    - `/compte/utilisateurs` - Gestion des membres de la famille
    - `/compte/preferences` - Préférences de langue et de thème (clair/sombre)
  - `/mes-rendez-vous` - Suivi des rendez-vous de l'utilisateur
  - `/trouver-rendez-vous` - Recherche de rendez-vous

- `/src/components` - Composants réutilisables
- `/src/lib` - Utilitaires et contextes
  - `/supabase` - Configuration et clients Supabase
  - `/family-members` - API pour la gestion des membres de la famille
  - `/user-preferences-utils.ts` - API pour la gestion des préférences utilisateur

- `/docs` - Documentation du projet
- `/migrations` - Scripts SQL pour la configuration de la base de données

## Intégration avec Stripe

Pour configurer les paiements:

1. Créez un compte sur [Stripe](https://stripe.com)
2. Ajoutez votre clé API dans le fichier `.env.local`:

```
STRIPE_SECRET_KEY=votre-clé-secrète-stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=votre-clé-publique-stripe
```

3. Suivez la documentation Supabase pour configurer l'intégration Stripe: [Supabase + Stripe](https://supabase.com/docs/guides/functions/examples/stripe-webhooks)

## Gestion des membres de la famille

La fonctionnalité de gestion des membres de la famille permet aux utilisateurs d'enregistrer jusqu'à 5 profils familiaux dans leur compte. Pour chaque membre, les informations suivantes sont stockées:

- Prénom
- Nom de famille
- Les 4 premiers caractères de la carte d'assurance maladie
- Date de naissance

Ces informations sont automatiquement synchronisées avec Supabase et peuvent être utilisées pour faciliter la prise de rendez-vous pour les membres de la famille.

Pour plus d'informations sur cette fonctionnalité, consultez la [documentation des membres de la famille](/docs/family-members.md).

## Gestion des préférences utilisateur

L'application permet aux utilisateurs de personnaliser leur expérience en configurant leurs préférences:

- Langue de l'interface (français ou anglais)
- Thème de l'application (clair, sombre, ou système)

Ces préférences sont automatiquement synchronisées avec Supabase et sont également mises en cache localement pour améliorer les performances.

**Important:** Pour que cette fonctionnalité fonctionne correctement, vous devez appliquer la migration `00002_create_user_preferences_table.sql` à votre base de données Supabase. Consultez la section sur les migrations pour plus d'informations.

## Déploiement

L'application peut être déployée sur [Vercel](https://vercel.com) ou [Netlify](https://netlify.com).

N'oubliez pas de configurer les variables d'environnement sur votre plateforme d'hébergement.
