#!/usr/bin/env node

/**
 * Test script to reproduce authentication issues
 * This script will test the authentication flow and identify problems
 */

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://tfvswgreslsbctjrvdbd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRmdnN3Z3Jlc2xzYmN0anJ2ZGJkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIzMjEyODUsImV4cCI6MjA1Nzg5NzI4NX0.5bNos8JS_77EUjWOxIGHpgNaYE28qQvO2g4qj0wRWiU';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testAuthFlow() {
  console.log('🔍 Testing Authentication Flow...\n');

  try {
    // Test 1: Check initial session state
    console.log('1. Checking initial session state...');
    const { data: initialSession, error: initialError } = await supabase.auth.getSession();
    console.log('Initial session:', initialSession?.session ? 'EXISTS' : 'NONE');
    if (initialError) console.log('Initial session error:', initialError.message);

    // Test 2: Attempt login
    console.log('\n2. Attempting login with test credentials...');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword123'
    });

    if (loginError) {
      console.log('❌ Login failed:', loginError.message);
      return;
    }

    console.log('✅ Login successful!');
    console.log('User ID:', loginData.user?.id);
    console.log('Email:', loginData.user?.email);
    console.log('Session exists:', !!loginData.session);

    // Test 3: Check session persistence
    console.log('\n3. Checking session persistence...');
    const { data: sessionAfterLogin, error: sessionError } = await supabase.auth.getSession();
    console.log('Session after login:', sessionAfterLogin?.session ? 'EXISTS' : 'NONE');
    if (sessionError) console.log('Session error:', sessionError.message);

    // Test 4: Test session refresh
    console.log('\n4. Testing session refresh...');
    const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
    console.log('Session refresh:', refreshData?.session ? 'SUCCESS' : 'FAILED');
    if (refreshError) console.log('Refresh error:', refreshError.message);

    // Test 5: Check user data
    console.log('\n5. Checking user data...');
    const { data: userData, error: userError } = await supabase.auth.getUser();
    console.log('User data:', userData?.user ? 'EXISTS' : 'NONE');
    if (userError) console.log('User error:', userError.message);

    // Test 6: Test logout
    console.log('\n6. Testing logout...');
    const { error: logoutError } = await supabase.auth.signOut();
    if (logoutError) {
      console.log('❌ Logout failed:', logoutError.message);
    } else {
      console.log('✅ Logout successful');
    }

    // Test 7: Check session after logout
    console.log('\n7. Checking session after logout...');
    const { data: finalSession, error: finalError } = await supabase.auth.getSession();
    console.log('Final session:', finalSession?.session ? 'EXISTS' : 'NONE');
    if (finalError) console.log('Final session error:', finalError.message);

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testAuthFlow().then(() => {
  console.log('\n🏁 Authentication flow test completed');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Test script failed:', error);
  process.exit(1);
});
