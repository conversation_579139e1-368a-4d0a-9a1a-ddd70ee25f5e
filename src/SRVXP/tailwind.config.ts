import type { Config } from "tailwindcss";

export default {
    darkMode: ["class"],
    content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/zaply/**/*.{js,ts,jsx,tsx}",
    ],
    theme: {
    screens: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px'
    },
    extend: {
    colors: {
    background: 'hsl(var(--background))',
    foreground: 'hsl(var(--foreground))',
    brandBlue: '#144ee0', // Added new custom color
    card: {
    DEFAULT: 'hsl(var(--card))',
    foreground: 'hsl(var(--card-foreground))'
    },
    popover: {
    DEFAULT: 'hsl(var(--popover))',
    foreground: 'hsl(var(--popover-foreground))'
    },
    primary: {
    DEFAULT: 'hsl(var(--primary))',
    foreground: 'hsl(var(--primary-foreground))'
    },
    secondary: {
    DEFAULT: 'hsl(var(--secondary))',
    foreground: 'hsl(var(--secondary-foreground))'
    },
    muted: {
    DEFAULT: 'hsl(var(--muted))',
    foreground: 'hsl(var(--muted-foreground))'
    },
    accent: {
    DEFAULT: 'hsl(var(--accent))',
    foreground: 'hsl(var(--accent-foreground))'
    },
    destructive: {
    DEFAULT: 'hsl(var(--destructive))',
    foreground: 'hsl(var(--destructive-foreground))'
    },
    border: 'hsl(var(--border))',
    input: 'hsl(var(--input))',
    ring: 'hsl(var(--ring))',
    chart: {
    '1': 'hsl(var(--chart-1))',
    '2': 'hsl(var(--chart-2))',
    '3': 'hsl(var(--chart-3))',
    '4': 'hsl(var(--chart-4))',
    '5': 'hsl(var(--chart-5))'
    }
    },
    borderRadius: {
    lg: 'var(--radius)',
    md: 'calc(var(--radius) - 2px)',
    sm: 'calc(var(--radius) - 4px)'
    },
    container: {
    center: true,
    padding: {
    DEFAULT: '1rem',
    md: '1.25rem',
    lg: '1.5rem',
    xl: '1.5rem',
    '2xl': '1.5rem',
    },
    screens: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1200px',
    '2xl': '1200px'
    }
    },
    keyframes: {
    'accordion-down': {
    from: {
    height: '0'
    },
    to: {
    height: 'var(--radix-accordion-content-height)'
    }
    },
    'accordion-up': {
    from: {
    height: 'var(--radix-accordion-content-height)'
    },
    to: {
    height: '0'
    }
    },
    'fade-in-up': {
    '0%': {
    opacity: '0',
    transform: 'translateY(20px)'
    },
    '100%': {
    opacity: '1',
    transform: 'translateY(0)'
    }
    },
    'price-fade': {
    '0%': {
    opacity: '0',
    },
    '100%': {
    opacity: '1',
    }
    },
    'button-pulse': {
    '0%, 100%': {
    transform: 'scale(1)'
    },
    '50%': {
    transform: 'scale(1.05)'
    }
    },
    'button-slide-right': {
    '0%': {
    transform: 'translateX(0)'
    },
    '100%': {
    transform: 'translateX(5px)'
    }
    },
    'arrow-bounce': {
    '0%, 100%': {
    transform: 'translateX(0)'
    },
    '50%': {
    transform: 'translateX(3px)'
    }
    },
    // Added new keyframes
    'shine': {
    '0%': { backgroundPosition: '200% 0' },
    '25%': { backgroundPosition: '-200% 0' },
    '100%': { backgroundPosition: '-200% 0' },
    },
    'gradientFlow': {
    '0%': { 'background-position': '0% 50%' },
    '50%': { 'background-position': '100% 50%' },
    '100%': { 'background-position': '0% 50%' },
    }
    },
    animation: {
    'accordion-down': 'accordion-down 0.2s ease-out',
    'accordion-up': 'accordion-up 0.2s ease-out',
    'fade-in-up-1': 'fade-in-up 0.5s ease-out forwards',
    'fade-in-up-2': 'fade-in-up 0.5s ease-out 0.2s forwards',
    'fade-in-up-3': 'fade-in-up 0.5s ease-out 0.4s forwards',
    'fade-in-up-4': 'fade-in-up 0.5s ease-out 0.6s forwards',
    'fade-in-up-5': 'fade-in-up 0.5s ease-out 0.8s forwards',
    'fade-in-up-6': 'fade-in-up 0.5s ease-out 1s forwards',
    'fade-in-up-7': 'fade-in-up 0.5s ease-out 1.2s forwards',
    'fade-in-together': 'fade-in-up 0.7s ease-out forwards',
    'price-fade': 'price-fade 0.4s ease-out forwards',
    'btn-pulse': 'button-pulse 1s ease-in-out infinite',
    'btn-slide-right': 'button-slide-right 0.3s ease-out forwards',
    'arrow-bounce': 'arrow-bounce 0.6s ease-in-out infinite',
    // Added new animations
    'shine': 'shine 3s ease-out infinite',
    'gradient-flow': 'gradientFlow 10s ease 0s infinite normal none running',
    }
    }
    },
    plugins: [require("tailwindcss-animate")],
} satisfies Config;