"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["utils"],{

/***/ "(app-pages-browser)/./src/lib/AuthContext.tsx":
/*!*********************************!*\
  !*** ./src/lib/AuthContext.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Create context with default values\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    session: null,\n    status: \"loading\",\n    signIn: async ()=>({\n            success: false\n        }),\n    signInWithGoogle: async ()=>({\n            success: false\n        }),\n    signOut: async ()=>{},\n    refresh: async ()=>{}\n});\n// Custom hook to use auth context\nconst useAuth = ()=>{\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n};\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n// Cache keys\nconst AUTH_CACHE_KEY = \"auth_cache\";\nconst AUTH_CACHE_EXPIRY_KEY = \"auth_cache_expiry\";\nconst SESSION_BACKUP_KEY = \"session_backup\";\nconst EXTERNAL_RETURN_KEY = \"external_return_detected\";\nconst CACHE_DURATION = 30 * 60 * 1000; // Increased to 30 minutes\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"loading\");\n    // Rate limiting for refresh function\n    const lastRefreshTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const REFRESH_DEBOUNCE_MS = 3000; // 3 seconds between refreshes\n    // Function to detect if user returned from external site\n    const detectExternalReturn = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[detectExternalReturn]\": ()=>{\n            if (false) {}\n            // Check if we have a session backup but no current session\n            const hasSessionBackup = localStorage.getItem(SESSION_BACKUP_KEY);\n            const hasCurrentSession = localStorage.getItem(AUTH_CACHE_KEY);\n            // Check if URL contains return parameters from Stripe or other external services\n            const urlParams = new URLSearchParams(window.location.search);\n            const isStripeReturn = urlParams.has('session_id') || window.location.pathname.includes('/compte/abonnement') || document.referrer.includes('stripe.com');\n            // Check if user was marked as returning from external site\n            const wasExternal = sessionStorage.getItem(EXTERNAL_RETURN_KEY);\n            // Check for OAuth success flag (indicates recent OAuth login)\n            const hasOAuthSuccess = document.cookie.includes('oauth_success=true');\n            const hasOAuthNewUser = document.cookie.includes('oauth_new_user=true');\n            return !!(hasSessionBackup && (!hasCurrentSession || isStripeReturn || wasExternal || hasOAuthSuccess || hasOAuthNewUser));\n        }\n    }[\"AuthProvider.useCallback[detectExternalReturn]\"], []);\n    // Function to backup session before external navigation\n    const backupSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[backupSession]\": (session)=>{\n            if (false) {}\n            localStorage.setItem(SESSION_BACKUP_KEY, JSON.stringify({\n                session,\n                timestamp: Date.now(),\n                user: session.user\n            }));\n        }\n    }[\"AuthProvider.useCallback[backupSession]\"], []);\n    // Function to restore session from backup\n    const restoreSessionFromBackup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[restoreSessionFromBackup]\": async ()=>{\n            if (false) {}\n            const backup = localStorage.getItem(SESSION_BACKUP_KEY);\n            if (!backup) return null;\n            try {\n                const { session: backedUpSession, timestamp } = JSON.parse(backup);\n                // Check if backup is not too old (within 2 hours)\n                if (Date.now() - timestamp > 2 * 60 * 60 * 1000) {\n                    localStorage.removeItem(SESSION_BACKUP_KEY);\n                    return null;\n                }\n                // Try to refresh the session using the backed up access token\n                if (backedUpSession === null || backedUpSession === void 0 ? void 0 : backedUpSession.access_token) {\n                    console.log('Attempting to restore session from backup...');\n                    // Set the session manually to trigger Supabase to use it\n                    const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.setSession({\n                        access_token: backedUpSession.access_token,\n                        refresh_token: backedUpSession.refresh_token\n                    });\n                    if (!error && data.session) {\n                        console.log('Successfully restored session from backup');\n                        localStorage.removeItem(SESSION_BACKUP_KEY);\n                        return data.session;\n                    }\n                }\n            } catch (error) {\n                console.error('Error restoring session from backup:', error);\n                localStorage.removeItem(SESSION_BACKUP_KEY);\n            }\n            return null;\n        }\n    }[\"AuthProvider.useCallback[restoreSessionFromBackup]\"], []);\n    // Enhanced refresh function with rate limiting and external return detection\n    const refresh = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refresh]\": async ()=>{\n            try {\n                // Check if this is a new OAuth user first\n                const isNewOAuthUser =  true && document.cookie.includes('oauth_new_user=true');\n                // Rate limiting - prevent too frequent refreshes (but allow for new OAuth users)\n                const now = Date.now();\n                if (!isNewOAuthUser && now - lastRefreshTime.current < REFRESH_DEBOUNCE_MS) {\n                    console.log('Auth refresh rate limited, skipping');\n                    return;\n                }\n                lastRefreshTime.current = now;\n                console.log(\"AuthContext: Starting auth refresh... \".concat(isNewOAuthUser ? '(new OAuth user)' : ''));\n                setStatus(\"loading\");\n                // For new OAuth users, try multiple attempts with delays\n                let sessionAttempts = isNewOAuthUser ? 3 : 1;\n                let sessionData = null;\n                let sessionError = null;\n                for(let attempt = 1; attempt <= sessionAttempts; attempt++){\n                    console.log(\"AuthContext: Session check attempt \".concat(attempt, \"/\").concat(sessionAttempts));\n                    const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                    if (error) {\n                        sessionError = error;\n                        console.error(\"AuthContext: Session error (attempt \".concat(attempt, \"):\"), error);\n                    } else if (data === null || data === void 0 ? void 0 : data.session) {\n                        sessionData = data;\n                        sessionError = null;\n                        break;\n                    }\n                    if (attempt < sessionAttempts) {\n                        console.log('Waiting before next session check attempt...');\n                        await new Promise({\n                            \"AuthProvider.useCallback[refresh]\": (resolve)=>setTimeout(resolve, 1000)\n                        }[\"AuthProvider.useCallback[refresh]\"]);\n                    }\n                }\n                if (sessionError && !sessionData) {\n                    console.error('AuthContext: Final session error:', sessionError);\n                    throw sessionError;\n                }\n                if (sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) {\n                    console.log(\"AuthContext: Session found for user: \".concat(sessionData.session.user.email, \" \").concat(isNewOAuthUser ? '(new OAuth user)' : ''));\n                    setSession(sessionData.session);\n                    setUser(sessionData.session.user);\n                    setStatus(\"authenticated\");\n                    // Backup session for potential external navigation recovery\n                    backupSession(sessionData.session);\n                    // Cache the auth state for faster subsequent loads\n                    if (true) {\n                        try {\n                            localStorage.setItem(AUTH_CACHE_KEY, JSON.stringify({\n                                session: sessionData.session,\n                                user: sessionData.session.user\n                            }));\n                            localStorage.setItem(AUTH_CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());\n                            // Clear new OAuth user flag if it exists\n                            if (isNewOAuthUser) {\n                                document.cookie = \"oauth_new_user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;\";\n                            }\n                        } catch (storageError) {\n                            console.warn('Failed to update localStorage cache:', storageError);\n                        // Don't fail auth if localStorage is not available\n                        }\n                    }\n                } else {\n                    console.log('AuthContext: No session found');\n                    // Check if user is returning from external site and try backup restoration\n                    const isExternalReturn = detectExternalReturn();\n                    if (isExternalReturn) {\n                        console.log('External return detected, attempting session restoration...');\n                        sessionStorage.removeItem(EXTERNAL_RETURN_KEY);\n                        // Try to restore from backup\n                        const restoredSession = await restoreSessionFromBackup();\n                        if (restoredSession) {\n                            console.log('Session restored from backup');\n                            setSession(restoredSession);\n                            setUser(restoredSession.user);\n                            setStatus(\"authenticated\");\n                            // Update cache\n                            if (true) {\n                                try {\n                                    localStorage.setItem(AUTH_CACHE_KEY, JSON.stringify({\n                                        session: restoredSession,\n                                        user: restoredSession.user\n                                    }));\n                                    localStorage.setItem(AUTH_CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());\n                                } catch (storageError) {\n                                    console.warn('Failed to update localStorage after restore:', storageError);\n                                }\n                            }\n                            return;\n                        }\n                    }\n                    // No session found, set unauthenticated state\n                    setSession(null);\n                    setUser(null);\n                    setStatus(\"unauthenticated\");\n                    // Clear cache and backup\n                    if (true) {\n                        try {\n                            localStorage.removeItem(AUTH_CACHE_KEY);\n                            localStorage.removeItem(AUTH_CACHE_EXPIRY_KEY);\n                            localStorage.removeItem(SESSION_BACKUP_KEY);\n                        } catch (storageError) {\n                            console.warn('Failed to clear localStorage:', storageError);\n                        }\n                    }\n                }\n            } catch (error) {\n                console.error(\"AuthContext: Error refreshing auth state:\", error);\n                // On error, assume not authenticated\n                setStatus(\"unauthenticated\");\n                setSession(null);\n                setUser(null);\n                // Clear cache on error\n                if (true) {\n                    try {\n                        localStorage.removeItem(AUTH_CACHE_KEY);\n                        localStorage.removeItem(AUTH_CACHE_EXPIRY_KEY);\n                    } catch (storageError) {\n                        console.warn('Failed to clear localStorage on error:', storageError);\n                    }\n                }\n            }\n        }\n    }[\"AuthProvider.useCallback[refresh]\"], [\n        detectExternalReturn,\n        restoreSessionFromBackup,\n        backupSession\n    ]);\n    // Sign in with email and password\n    const signIn = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[signIn]\": async (email, password)=>{\n            try {\n                const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n                    email,\n                    password\n                });\n                if (error) {\n                    throw error;\n                }\n                if (data.session) {\n                    // Update state with new session\n                    setSession(data.session);\n                    setUser(data.session.user);\n                    setStatus(\"authenticated\");\n                    // Backup session for external navigation\n                    backupSession(data.session);\n                    // Cache the auth state\n                    localStorage.setItem(AUTH_CACHE_KEY, JSON.stringify({\n                        session: data.session,\n                        user: data.session.user\n                    }));\n                    localStorage.setItem(AUTH_CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());\n                    // Clear any old auth-related flags from sessionStorage\n                    const keysToRemove = [];\n                    for(let i = 0; i < sessionStorage.length; i++){\n                        const key = sessionStorage.key(i);\n                        if (key && (key.includes(\"auth\") || key.includes(\"logout\") || key.includes(\"supabase\"))) {\n                            keysToRemove.push(key);\n                        }\n                    }\n                    keysToRemove.forEach({\n                        \"AuthProvider.useCallback[signIn]\": (key)=>sessionStorage.removeItem(key)\n                    }[\"AuthProvider.useCallback[signIn]\"]);\n                }\n                return {\n                    success: true\n                };\n            } catch (error) {\n                console.error(\"Sign in error:\", error);\n                return {\n                    success: false,\n                    error: error\n                };\n            }\n        }\n    }[\"AuthProvider.useCallback[signIn]\"], [\n        backupSession\n    ]);\n    // Sign in with Google\n    const signInWithGoogle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[signInWithGoogle]\": async ()=>{\n            try {\n                // Get the current origin to ensure correct redirect URL\n                const origin = window.location.origin;\n                // Create the callback URL with explicit dashboard redirect\n                const callbackUrl = \"\".concat(origin, \"/auth/callback?redirectTo=/dashboard\");\n                console.log(\"Setting up Google auth with callback URL: \".concat(callbackUrl));\n                const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOAuth({\n                    provider: 'google',\n                    options: {\n                        redirectTo: callbackUrl\n                    }\n                });\n                if (error) {\n                    throw error;\n                }\n                return {\n                    success: true\n                };\n            } catch (error) {\n                console.error(\"Google sign in error:\", error);\n                return {\n                    success: false,\n                    error: error\n                };\n            }\n        }\n    }[\"AuthProvider.useCallback[signInWithGoogle]\"], []);\n    // Enhanced sign out function\n    const signOut = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[signOut]\": async function() {\n            let redirectUrl = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"/\";\n            try {\n                // Sign out with Supabase\n                await _supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n                // Clear auth state\n                setSession(null);\n                setUser(null);\n                setStatus(\"unauthenticated\");\n                // Clear all auth-related storage\n                localStorage.removeItem(AUTH_CACHE_KEY);\n                localStorage.removeItem(AUTH_CACHE_EXPIRY_KEY);\n                localStorage.removeItem(SESSION_BACKUP_KEY);\n                localStorage.removeItem(\"supabase.auth.token\");\n                // Reset theme to system default when logging out\n                localStorage.removeItem(\"theme\");\n                // Clear user-specific preferences cache\n                if (user === null || user === void 0 ? void 0 : user.id) {\n                    localStorage.removeItem(\"user_preferences_\".concat(user.id));\n                }\n                // Set a flag in session storage to indicate logout\n                sessionStorage.setItem(\"isLoggedOut\", \"true\");\n                // Force page refresh for clean state\n                window.location.href = redirectUrl;\n            } catch (error) {\n                console.error(\"Sign out error:\", error);\n                // Even if there was an error, try to redirect and clean up state\n                setSession(null);\n                setUser(null);\n                setStatus(\"unauthenticated\");\n                window.location.href = redirectUrl;\n            }\n        }\n    }[\"AuthProvider.useCallback[signOut]\"], [\n        user\n    ]);\n    // Setup auth state with enhanced external navigation detection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Initial auth check\n            refresh();\n            // Setup auth state listener for changes\n            const { data: authListener } = _supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": (event, session)=>{\n                    var _session_user;\n                    console.log(\"AuthContext: Auth state change - \".concat(event), {\n                        hasSession: !!session,\n                        userEmail: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email\n                    });\n                    if (event === \"SIGNED_IN\" && session) {\n                        setSession(session);\n                        setUser(session.user);\n                        setStatus(\"authenticated\");\n                        // Backup session for external navigation\n                        backupSession(session);\n                        // Update cache\n                        localStorage.setItem(AUTH_CACHE_KEY, JSON.stringify({\n                            session,\n                            user: session.user\n                        }));\n                        localStorage.setItem(AUTH_CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());\n                        // Clear OAuth success flag if it exists\n                        if (true) {\n                            document.cookie = \"oauth_success=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;\";\n                        }\n                    } else if (event === \"SIGNED_OUT\") {\n                        setSession(null);\n                        setUser(null);\n                        setStatus(\"unauthenticated\");\n                        // Clear cache and backup\n                        localStorage.removeItem(AUTH_CACHE_KEY);\n                        localStorage.removeItem(AUTH_CACHE_EXPIRY_KEY);\n                        localStorage.removeItem(SESSION_BACKUP_KEY);\n                    } else if (event === \"TOKEN_REFRESHED\" && session) {\n                        console.log(\"AuthContext: Token refreshed successfully\");\n                        setSession(session);\n                        setUser(session.user);\n                        setStatus(\"authenticated\");\n                        // Update backup and cache with refreshed session\n                        backupSession(session);\n                        localStorage.setItem(AUTH_CACHE_KEY, JSON.stringify({\n                            session,\n                            user: session.user\n                        }));\n                        localStorage.setItem(AUTH_CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Enhanced visibility change handler for external returns\n            const handleVisibilityChange = {\n                \"AuthProvider.useEffect.handleVisibilityChange\": ()=>{\n                    if (document.visibilityState === \"visible\") {\n                        // Mark as potential external return\n                        sessionStorage.setItem(EXTERNAL_RETURN_KEY, \"true\");\n                        // Refresh auth state\n                        setTimeout({\n                            \"AuthProvider.useEffect.handleVisibilityChange\": ()=>{\n                                refresh();\n                            }\n                        }[\"AuthProvider.useEffect.handleVisibilityChange\"], 100); // Small delay to ensure page is fully visible\n                    }\n                }\n            }[\"AuthProvider.useEffect.handleVisibilityChange\"];\n            // Enhanced focus handler for external returns\n            const handleWindowFocus = {\n                \"AuthProvider.useEffect.handleWindowFocus\": ()=>{\n                    // Mark as potential external return\n                    sessionStorage.setItem(EXTERNAL_RETURN_KEY, \"true\");\n                    // Refresh auth state\n                    setTimeout({\n                        \"AuthProvider.useEffect.handleWindowFocus\": ()=>{\n                            refresh();\n                        }\n                    }[\"AuthProvider.useEffect.handleWindowFocus\"], 100);\n                }\n            }[\"AuthProvider.useEffect.handleWindowFocus\"];\n            // Add event listeners\n            document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n            window.addEventListener(\"focus\", handleWindowFocus);\n            // Cleanup on unmount\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    authListener.subscription.unsubscribe();\n                    document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n                    window.removeEventListener(\"focus\", handleWindowFocus);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        refresh,\n        backupSession\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            session,\n            status,\n            signIn,\n            signInWithGoogle,\n            signOut,\n            refresh\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/lib/AuthContext.tsx\",\n        lineNumber: 494,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"bSyxo09mLWlJbQotVcZ2EW4Oq6M=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvQXV0aENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRThHO0FBRWpFO0FBZTdDLHFDQUFxQztBQUNyQyxNQUFNUSw0QkFBY1Asb0RBQWFBLENBQW1CO0lBQ2xEUSxNQUFNO0lBQ05DLFNBQVM7SUFDVEMsUUFBUTtJQUNSQyxRQUFRLFVBQWE7WUFBRUMsU0FBUztRQUFNO0lBQ3RDQyxrQkFBa0IsVUFBYTtZQUFFRCxTQUFTO1FBQU07SUFDaERFLFNBQVMsV0FBYTtJQUN0QkMsU0FBUyxXQUFhO0FBQ3hCO0FBRUEsa0NBQWtDO0FBQzNCLE1BQU1DLFVBQVU7O0lBQU1mLE9BQUFBLGlEQUFVQSxDQUFDTTtBQUFXLEVBQUU7R0FBeENTO0FBRWIsYUFBYTtBQUNiLE1BQU1DLGlCQUFpQjtBQUN2QixNQUFNQyx3QkFBd0I7QUFDOUIsTUFBTUMscUJBQXFCO0FBQzNCLE1BQU1DLHNCQUFzQjtBQUM1QixNQUFNQyxpQkFBaUIsS0FBSyxLQUFLLE1BQU0sMEJBQTBCO0FBTTFELFNBQVNDLGFBQWEsS0FBK0I7UUFBL0IsRUFBRUMsUUFBUSxFQUFxQixHQUEvQjs7SUFDM0IsTUFBTSxDQUFDZixNQUFNZ0IsUUFBUSxHQUFHdEIsK0NBQVFBLENBQWM7SUFDOUMsTUFBTSxDQUFDTyxTQUFTZ0IsV0FBVyxHQUFHdkIsK0NBQVFBLENBQWlCO0lBQ3ZELE1BQU0sQ0FBQ1EsUUFBUWdCLFVBQVUsR0FBR3hCLCtDQUFRQSxDQUFhO0lBRWpELHFDQUFxQztJQUNyQyxNQUFNeUIsa0JBQWtCdEIsNkNBQU1BLENBQUM7SUFDL0IsTUFBTXVCLHNCQUFzQixNQUFNLDhCQUE4QjtJQUVoRSx5REFBeUQ7SUFDekQsTUFBTUMsdUJBQXVCekIsa0RBQVdBOzBEQUFDO1lBQ3ZDLElBQUksS0FBNkIsRUFBRSxFQUFhO1lBRWhELDJEQUEyRDtZQUMzRCxNQUFNMEIsbUJBQW1CQyxhQUFhQyxPQUFPLENBQUNiO1lBQzlDLE1BQU1jLG9CQUFvQkYsYUFBYUMsT0FBTyxDQUFDZjtZQUUvQyxpRkFBaUY7WUFDakYsTUFBTWlCLFlBQVksSUFBSUMsZ0JBQWdCQyxPQUFPQyxRQUFRLENBQUNDLE1BQU07WUFDNUQsTUFBTUMsaUJBQWlCTCxVQUFVTSxHQUFHLENBQUMsaUJBQ2ZKLE9BQU9DLFFBQVEsQ0FBQ0ksUUFBUSxDQUFDQyxRQUFRLENBQUMseUJBQ2xDQyxTQUFTQyxRQUFRLENBQUNGLFFBQVEsQ0FBQztZQUVqRCwyREFBMkQ7WUFDM0QsTUFBTUcsY0FBY0MsZUFBZWQsT0FBTyxDQUFDWjtZQUUzQyw4REFBOEQ7WUFDOUQsTUFBTTJCLGtCQUFrQkosU0FBU0ssTUFBTSxDQUFDTixRQUFRLENBQUM7WUFDakQsTUFBTU8sa0JBQWtCTixTQUFTSyxNQUFNLENBQUNOLFFBQVEsQ0FBQztZQUVqRCxPQUFPLENBQUMsQ0FBRVosQ0FBQUEsb0JBQXFCLEVBQUNHLHFCQUFxQk0sa0JBQWtCTSxlQUFlRSxtQkFBbUJFLGVBQWMsQ0FBQztRQUMxSDt5REFBRyxFQUFFO0lBRUwsd0RBQXdEO0lBQ3hELE1BQU1DLGdCQUFnQjlDLGtEQUFXQTttREFBQyxDQUFDSztZQUNqQyxJQUFJLEtBQTZCLEVBQUUsRUFBTztZQUUxQ3NCLGFBQWFvQixPQUFPLENBQUNoQyxvQkFBb0JpQyxLQUFLQyxTQUFTLENBQUM7Z0JBQ3RENUM7Z0JBQ0E2QyxXQUFXQyxLQUFLQyxHQUFHO2dCQUNuQmhELE1BQU1DLFFBQVFELElBQUk7WUFDcEI7UUFDRjtrREFBRyxFQUFFO0lBRUwsMENBQTBDO0lBQzFDLE1BQU1pRCwyQkFBMkJyRCxrREFBV0E7OERBQUM7WUFDM0MsSUFBSSxLQUE2QixFQUFFLEVBQVk7WUFFL0MsTUFBTXNELFNBQVMzQixhQUFhQyxPQUFPLENBQUNiO1lBQ3BDLElBQUksQ0FBQ3VDLFFBQVEsT0FBTztZQUVwQixJQUFJO2dCQUNGLE1BQU0sRUFBRWpELFNBQVNrRCxlQUFlLEVBQUVMLFNBQVMsRUFBRSxHQUFHRixLQUFLUSxLQUFLLENBQUNGO2dCQUUzRCxrREFBa0Q7Z0JBQ2xELElBQUlILEtBQUtDLEdBQUcsS0FBS0YsWUFBWSxJQUFJLEtBQUssS0FBSyxNQUFNO29CQUMvQ3ZCLGFBQWE4QixVQUFVLENBQUMxQztvQkFDeEIsT0FBTztnQkFDVDtnQkFFQSw4REFBOEQ7Z0JBQzlELElBQUl3Qyw0QkFBQUEsc0NBQUFBLGdCQUFpQkcsWUFBWSxFQUFFO29CQUNqQ0MsUUFBUUMsR0FBRyxDQUFDO29CQUVaLHlEQUF5RDtvQkFDekQsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU01RCxzREFBUUEsQ0FBQzZELElBQUksQ0FBQzFDLFVBQVUsQ0FBQzt3QkFDckRxQyxjQUFjSCxnQkFBZ0JHLFlBQVk7d0JBQzFDTSxlQUFlVCxnQkFBZ0JTLGFBQWE7b0JBQzlDO29CQUVBLElBQUksQ0FBQ0YsU0FBU0QsS0FBS3hELE9BQU8sRUFBRTt3QkFDMUJzRCxRQUFRQyxHQUFHLENBQUM7d0JBQ1pqQyxhQUFhOEIsVUFBVSxDQUFDMUM7d0JBQ3hCLE9BQU84QyxLQUFLeEQsT0FBTztvQkFDckI7Z0JBQ0Y7WUFDRixFQUFFLE9BQU95RCxPQUFPO2dCQUNkSCxRQUFRRyxLQUFLLENBQUMsd0NBQXdDQTtnQkFDdERuQyxhQUFhOEIsVUFBVSxDQUFDMUM7WUFDMUI7WUFFQSxPQUFPO1FBQ1Q7NkRBQUcsRUFBRTtJQUVMLDZFQUE2RTtJQUM3RSxNQUFNSixVQUFVWCxrREFBV0E7NkNBQUM7WUFDMUIsSUFBSTtnQkFDRiwwQ0FBMEM7Z0JBQzFDLE1BQU1pRSxpQkFBaUIsS0FBNkIsSUFDOUIxQixTQUFTSyxNQUFNLENBQUNOLFFBQVEsQ0FBQztnQkFFL0MsaUZBQWlGO2dCQUNqRixNQUFNYyxNQUFNRCxLQUFLQyxHQUFHO2dCQUNwQixJQUFJLENBQUNhLGtCQUFrQmIsTUFBTTdCLGdCQUFnQjJDLE9BQU8sR0FBRzFDLHFCQUFxQjtvQkFDMUVtQyxRQUFRQyxHQUFHLENBQUM7b0JBQ1o7Z0JBQ0Y7Z0JBQ0FyQyxnQkFBZ0IyQyxPQUFPLEdBQUdkO2dCQUUxQk8sUUFBUUMsR0FBRyxDQUFDLHlDQUFrRixPQUF6Q0ssaUJBQWlCLHFCQUFxQjtnQkFDM0YzQyxVQUFVO2dCQUVWLHlEQUF5RDtnQkFDekQsSUFBSTZDLGtCQUFrQkYsaUJBQWlCLElBQUk7Z0JBQzNDLElBQUlHLGNBQWM7Z0JBQ2xCLElBQUlDLGVBQWU7Z0JBRW5CLElBQUssSUFBSUMsVUFBVSxHQUFHQSxXQUFXSCxpQkFBaUJHLFVBQVc7b0JBQzNEWCxRQUFRQyxHQUFHLENBQUMsc0NBQWlETyxPQUFYRyxTQUFRLEtBQW1CLE9BQWhCSDtvQkFFN0QsTUFBTSxFQUFFTixJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU01RCxzREFBUUEsQ0FBQzZELElBQUksQ0FBQ1EsVUFBVTtvQkFFdEQsSUFBSVQsT0FBTzt3QkFDVE8sZUFBZVA7d0JBQ2ZILFFBQVFHLEtBQUssQ0FBQyx1Q0FBK0MsT0FBUlEsU0FBUSxPQUFLUjtvQkFDcEUsT0FBTyxJQUFJRCxpQkFBQUEsMkJBQUFBLEtBQU14RCxPQUFPLEVBQUU7d0JBQ3hCK0QsY0FBY1A7d0JBQ2RRLGVBQWU7d0JBQ2Y7b0JBQ0Y7b0JBRUEsSUFBSUMsVUFBVUgsaUJBQWlCO3dCQUM3QlIsUUFBUUMsR0FBRyxDQUFDO3dCQUNaLE1BQU0sSUFBSVk7aUVBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7O29CQUNuRDtnQkFDRjtnQkFFQSxJQUFJSixnQkFBZ0IsQ0FBQ0QsYUFBYTtvQkFDaENULFFBQVFHLEtBQUssQ0FBQyxxQ0FBcUNPO29CQUNuRCxNQUFNQTtnQkFDUjtnQkFFQSxJQUFJRCx3QkFBQUEsa0NBQUFBLFlBQWEvRCxPQUFPLEVBQUU7b0JBQ3hCc0QsUUFBUUMsR0FBRyxDQUFDLHdDQUEwRUssT0FBbENHLFlBQVkvRCxPQUFPLENBQUNELElBQUksQ0FBQ3VFLEtBQUssRUFBQyxLQUE0QyxPQUF6Q1YsaUJBQWlCLHFCQUFxQjtvQkFDNUg1QyxXQUFXK0MsWUFBWS9ELE9BQU87b0JBQzlCZSxRQUFRZ0QsWUFBWS9ELE9BQU8sQ0FBQ0QsSUFBSTtvQkFDaENrQixVQUFVO29CQUVWLDREQUE0RDtvQkFDNUR3QixjQUFjc0IsWUFBWS9ELE9BQU87b0JBRWpDLG1EQUFtRDtvQkFDbkQsSUFBSSxJQUE2QixFQUFFO3dCQUNqQyxJQUFJOzRCQUNGc0IsYUFBYW9CLE9BQU8sQ0FDbEJsQyxnQkFDQW1DLEtBQUtDLFNBQVMsQ0FBQztnQ0FBRTVDLFNBQVMrRCxZQUFZL0QsT0FBTztnQ0FBRUQsTUFBTWdFLFlBQVkvRCxPQUFPLENBQUNELElBQUk7NEJBQUM7NEJBRWhGdUIsYUFBYW9CLE9BQU8sQ0FDbEJqQyx1QkFDQSxDQUFDcUMsS0FBS0MsR0FBRyxLQUFLbkMsY0FBYSxFQUFHMkQsUUFBUTs0QkFHeEMseUNBQXlDOzRCQUN6QyxJQUFJWCxnQkFBZ0I7Z0NBQ2xCMUIsU0FBU0ssTUFBTSxHQUFHOzRCQUNwQjt3QkFDRixFQUFFLE9BQU9pQyxjQUFjOzRCQUNyQmxCLFFBQVFtQixJQUFJLENBQUMsd0NBQXdDRDt3QkFDckQsbURBQW1EO3dCQUNyRDtvQkFDRjtnQkFDRixPQUFPO29CQUNMbEIsUUFBUUMsR0FBRyxDQUFDO29CQUVaLDJFQUEyRTtvQkFDM0UsTUFBTW1CLG1CQUFtQnREO29CQUV6QixJQUFJc0Qsa0JBQWtCO3dCQUNwQnBCLFFBQVFDLEdBQUcsQ0FBQzt3QkFDWmxCLGVBQWVlLFVBQVUsQ0FBQ3pDO3dCQUUxQiw2QkFBNkI7d0JBQzdCLE1BQU1nRSxrQkFBa0IsTUFBTTNCO3dCQUM5QixJQUFJMkIsaUJBQWlCOzRCQUNuQnJCLFFBQVFDLEdBQUcsQ0FBQzs0QkFDWnZDLFdBQVcyRDs0QkFDWDVELFFBQVE0RCxnQkFBZ0I1RSxJQUFJOzRCQUM1QmtCLFVBQVU7NEJBRVYsZUFBZTs0QkFDZixJQUFJLElBQTZCLEVBQUU7Z0NBQ2pDLElBQUk7b0NBQ0ZLLGFBQWFvQixPQUFPLENBQ2xCbEMsZ0JBQ0FtQyxLQUFLQyxTQUFTLENBQUM7d0NBQUU1QyxTQUFTMkU7d0NBQWlCNUUsTUFBTTRFLGdCQUFnQjVFLElBQUk7b0NBQUM7b0NBRXhFdUIsYUFBYW9CLE9BQU8sQ0FDbEJqQyx1QkFDQSxDQUFDcUMsS0FBS0MsR0FBRyxLQUFLbkMsY0FBYSxFQUFHMkQsUUFBUTtnQ0FFMUMsRUFBRSxPQUFPQyxjQUFjO29DQUNyQmxCLFFBQVFtQixJQUFJLENBQUMsZ0RBQWdERDtnQ0FDL0Q7NEJBQ0Y7NEJBQ0E7d0JBQ0Y7b0JBQ0Y7b0JBRUEsOENBQThDO29CQUM5Q3hELFdBQVc7b0JBQ1hELFFBQVE7b0JBQ1JFLFVBQVU7b0JBRVYseUJBQXlCO29CQUN6QixJQUFJLElBQTZCLEVBQUU7d0JBQ2pDLElBQUk7NEJBQ0ZLLGFBQWE4QixVQUFVLENBQUM1Qzs0QkFDeEJjLGFBQWE4QixVQUFVLENBQUMzQzs0QkFDeEJhLGFBQWE4QixVQUFVLENBQUMxQzt3QkFDMUIsRUFBRSxPQUFPOEQsY0FBYzs0QkFDckJsQixRQUFRbUIsSUFBSSxDQUFDLGlDQUFpQ0Q7d0JBQ2hEO29CQUNGO2dCQUNGO1lBQ0YsRUFBRSxPQUFPZixPQUFPO2dCQUNkSCxRQUFRRyxLQUFLLENBQUMsNkNBQTZDQTtnQkFFM0QscUNBQXFDO2dCQUNyQ3hDLFVBQVU7Z0JBQ1ZELFdBQVc7Z0JBQ1hELFFBQVE7Z0JBRVIsdUJBQXVCO2dCQUN2QixJQUFJLElBQTZCLEVBQUU7b0JBQ2pDLElBQUk7d0JBQ0ZPLGFBQWE4QixVQUFVLENBQUM1Qzt3QkFDeEJjLGFBQWE4QixVQUFVLENBQUMzQztvQkFDMUIsRUFBRSxPQUFPK0QsY0FBYzt3QkFDckJsQixRQUFRbUIsSUFBSSxDQUFDLDBDQUEwQ0Q7b0JBQ3pEO2dCQUNGO1lBQ0Y7UUFDRjs0Q0FBRztRQUFDcEQ7UUFBc0I0QjtRQUEwQlA7S0FBYztJQUVsRSxrQ0FBa0M7SUFDbEMsTUFBTXZDLFNBQVNQLGtEQUFXQTs0Q0FBQyxPQUFPMkUsT0FBZU07WUFDL0MsSUFBSTtnQkFDRixNQUFNLEVBQUVwQixJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU01RCxzREFBUUEsQ0FBQzZELElBQUksQ0FBQ21CLGtCQUFrQixDQUFDO29CQUM3RFA7b0JBQ0FNO2dCQUNGO2dCQUVBLElBQUluQixPQUFPO29CQUNULE1BQU1BO2dCQUNSO2dCQUVBLElBQUlELEtBQUt4RCxPQUFPLEVBQUU7b0JBQ2hCLGdDQUFnQztvQkFDaENnQixXQUFXd0MsS0FBS3hELE9BQU87b0JBQ3ZCZSxRQUFReUMsS0FBS3hELE9BQU8sQ0FBQ0QsSUFBSTtvQkFDekJrQixVQUFVO29CQUVWLHlDQUF5QztvQkFDekN3QixjQUFjZSxLQUFLeEQsT0FBTztvQkFFMUIsdUJBQXVCO29CQUN2QnNCLGFBQWFvQixPQUFPLENBQ2xCbEMsZ0JBQ0FtQyxLQUFLQyxTQUFTLENBQUM7d0JBQUU1QyxTQUFTd0QsS0FBS3hELE9BQU87d0JBQUVELE1BQU15RCxLQUFLeEQsT0FBTyxDQUFDRCxJQUFJO29CQUFDO29CQUVsRXVCLGFBQWFvQixPQUFPLENBQ2xCakMsdUJBQ0EsQ0FBQ3FDLEtBQUtDLEdBQUcsS0FBS25DLGNBQWEsRUFBRzJELFFBQVE7b0JBR3hDLHVEQUF1RDtvQkFDdkQsTUFBTU8sZUFBZSxFQUFFO29CQUN2QixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSTFDLGVBQWUyQyxNQUFNLEVBQUVELElBQUs7d0JBQzlDLE1BQU1FLE1BQU01QyxlQUFlNEMsR0FBRyxDQUFDRjt3QkFDL0IsSUFBSUUsT0FBUUEsQ0FBQUEsSUFBSWhELFFBQVEsQ0FBQyxXQUFXZ0QsSUFBSWhELFFBQVEsQ0FBQyxhQUFhZ0QsSUFBSWhELFFBQVEsQ0FBQyxXQUFVLEdBQUk7NEJBQ3ZGNkMsYUFBYUksSUFBSSxDQUFDRDt3QkFDcEI7b0JBQ0Y7b0JBQ0FILGFBQWFLLE9BQU87NERBQUNGLENBQUFBLE1BQU81QyxlQUFlZSxVQUFVLENBQUM2Qjs7Z0JBQ3hEO2dCQUVBLE9BQU87b0JBQUU5RSxTQUFTO2dCQUFLO1lBQ3pCLEVBQUUsT0FBT3NELE9BQU87Z0JBQ2RILFFBQVFHLEtBQUssQ0FBQyxrQkFBa0JBO2dCQUNoQyxPQUFPO29CQUFFdEQsU0FBUztvQkFBT3NELE9BQU9BO2dCQUFlO1lBQ2pEO1FBQ0Y7MkNBQUc7UUFBQ2hCO0tBQWM7SUFFbEIsc0JBQXNCO0lBQ3RCLE1BQU1yQyxtQkFBbUJULGtEQUFXQTtzREFBQztZQUNuQyxJQUFJO2dCQUNGLHdEQUF3RDtnQkFDeEQsTUFBTXlGLFNBQVN6RCxPQUFPQyxRQUFRLENBQUN3RCxNQUFNO2dCQUNyQywyREFBMkQ7Z0JBQzNELE1BQU1DLGNBQWMsR0FBVSxPQUFQRCxRQUFPO2dCQUU5QjlCLFFBQVFDLEdBQUcsQ0FBQyw2Q0FBeUQsT0FBWjhCO2dCQUV6RCxNQUFNLEVBQUU3QixJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU01RCxzREFBUUEsQ0FBQzZELElBQUksQ0FBQzRCLGVBQWUsQ0FBQztvQkFDMURDLFVBQVU7b0JBQ1ZDLFNBQVM7d0JBQ1BDLFlBQVlKO29CQUNkO2dCQUNGO2dCQUVBLElBQUk1QixPQUFPO29CQUNULE1BQU1BO2dCQUNSO2dCQUVBLE9BQU87b0JBQUV0RCxTQUFTO2dCQUFLO1lBQ3pCLEVBQUUsT0FBT3NELE9BQU87Z0JBQ2RILFFBQVFHLEtBQUssQ0FBQyx5QkFBeUJBO2dCQUN2QyxPQUFPO29CQUFFdEQsU0FBUztvQkFBT3NELE9BQU9BO2dCQUFlO1lBQ2pEO1FBQ0Y7cURBQUcsRUFBRTtJQUVMLDZCQUE2QjtJQUM3QixNQUFNcEQsVUFBVVYsa0RBQVdBOzZDQUFDO2dCQUFPK0YsK0VBQXNCO1lBQ3ZELElBQUk7Z0JBQ0YseUJBQXlCO2dCQUN6QixNQUFNN0Ysc0RBQVFBLENBQUM2RCxJQUFJLENBQUNyRCxPQUFPO2dCQUUzQixtQkFBbUI7Z0JBQ25CVyxXQUFXO2dCQUNYRCxRQUFRO2dCQUNSRSxVQUFVO2dCQUVWLGlDQUFpQztnQkFDakNLLGFBQWE4QixVQUFVLENBQUM1QztnQkFDeEJjLGFBQWE4QixVQUFVLENBQUMzQztnQkFDeEJhLGFBQWE4QixVQUFVLENBQUMxQztnQkFDeEJZLGFBQWE4QixVQUFVLENBQUM7Z0JBRXhCLGlEQUFpRDtnQkFDakQ5QixhQUFhOEIsVUFBVSxDQUFDO2dCQUV4Qix3Q0FBd0M7Z0JBQ3hDLElBQUlyRCxpQkFBQUEsMkJBQUFBLEtBQU00RixFQUFFLEVBQUU7b0JBQ1pyRSxhQUFhOEIsVUFBVSxDQUFDLG9CQUE0QixPQUFSckQsS0FBSzRGLEVBQUU7Z0JBQ3JEO2dCQUVBLG1EQUFtRDtnQkFDbkR0RCxlQUFlSyxPQUFPLENBQUMsZUFBZTtnQkFFdEMscUNBQXFDO2dCQUNyQ2YsT0FBT0MsUUFBUSxDQUFDZ0UsSUFBSSxHQUFHRjtZQUN6QixFQUFFLE9BQU9qQyxPQUFPO2dCQUNkSCxRQUFRRyxLQUFLLENBQUMsbUJBQW1CQTtnQkFFakMsaUVBQWlFO2dCQUNqRXpDLFdBQVc7Z0JBQ1hELFFBQVE7Z0JBQ1JFLFVBQVU7Z0JBQ1ZVLE9BQU9DLFFBQVEsQ0FBQ2dFLElBQUksR0FBR0Y7WUFDekI7UUFDRjs0Q0FBRztRQUFDM0Y7S0FBSztJQUVULCtEQUErRDtJQUMvREwsZ0RBQVNBO2tDQUFDO1lBQ1IscUJBQXFCO1lBQ3JCWTtZQUVBLHdDQUF3QztZQUN4QyxNQUFNLEVBQUVrRCxNQUFNcUMsWUFBWSxFQUFFLEdBQUdoRyxzREFBUUEsQ0FBQzZELElBQUksQ0FBQ29DLGlCQUFpQjswQ0FBQyxDQUFDQyxPQUFPL0Y7d0JBQ3dCQTtvQkFBN0ZzRCxRQUFRQyxHQUFHLENBQUMsb0NBQTBDLE9BQU53QyxRQUFTO3dCQUFFQyxZQUFZLENBQUMsQ0FBQ2hHO3dCQUFTaUcsU0FBUyxFQUFFakcsb0JBQUFBLCtCQUFBQSxnQkFBQUEsUUFBU0QsSUFBSSxjQUFiQyxvQ0FBQUEsY0FBZXNFLEtBQUs7b0JBQUM7b0JBRWxILElBQUl5QixVQUFVLGVBQWUvRixTQUFTO3dCQUNwQ2dCLFdBQVdoQjt3QkFDWGUsUUFBUWYsUUFBUUQsSUFBSTt3QkFDcEJrQixVQUFVO3dCQUVWLHlDQUF5Qzt3QkFDekN3QixjQUFjekM7d0JBRWQsZUFBZTt3QkFDZnNCLGFBQWFvQixPQUFPLENBQ2xCbEMsZ0JBQ0FtQyxLQUFLQyxTQUFTLENBQUM7NEJBQUU1Qzs0QkFBU0QsTUFBTUMsUUFBUUQsSUFBSTt3QkFBQzt3QkFFL0N1QixhQUFhb0IsT0FBTyxDQUNsQmpDLHVCQUNBLENBQUNxQyxLQUFLQyxHQUFHLEtBQUtuQyxjQUFhLEVBQUcyRCxRQUFRO3dCQUd4Qyx3Q0FBd0M7d0JBQ3hDLElBQUksSUFBNkIsRUFBRTs0QkFDakNyQyxTQUFTSyxNQUFNLEdBQUc7d0JBQ3BCO29CQUNGLE9BQU8sSUFBSXdELFVBQVUsY0FBYzt3QkFDakMvRSxXQUFXO3dCQUNYRCxRQUFRO3dCQUNSRSxVQUFVO3dCQUVWLHlCQUF5Qjt3QkFDekJLLGFBQWE4QixVQUFVLENBQUM1Qzt3QkFDeEJjLGFBQWE4QixVQUFVLENBQUMzQzt3QkFDeEJhLGFBQWE4QixVQUFVLENBQUMxQztvQkFDMUIsT0FBTyxJQUFJcUYsVUFBVSxxQkFBcUIvRixTQUFTO3dCQUNqRHNELFFBQVFDLEdBQUcsQ0FBQzt3QkFDWnZDLFdBQVdoQjt3QkFDWGUsUUFBUWYsUUFBUUQsSUFBSTt3QkFDcEJrQixVQUFVO3dCQUVWLGlEQUFpRDt3QkFDakR3QixjQUFjekM7d0JBQ2RzQixhQUFhb0IsT0FBTyxDQUNsQmxDLGdCQUNBbUMsS0FBS0MsU0FBUyxDQUFDOzRCQUFFNUM7NEJBQVNELE1BQU1DLFFBQVFELElBQUk7d0JBQUM7d0JBRS9DdUIsYUFBYW9CLE9BQU8sQ0FDbEJqQyx1QkFDQSxDQUFDcUMsS0FBS0MsR0FBRyxLQUFLbkMsY0FBYSxFQUFHMkQsUUFBUTtvQkFFMUM7Z0JBQ0Y7O1lBRUEsMERBQTBEO1lBQzFELE1BQU0yQjtpRUFBeUI7b0JBQzdCLElBQUloRSxTQUFTaUUsZUFBZSxLQUFLLFdBQVc7d0JBQzFDLG9DQUFvQzt3QkFDcEM5RCxlQUFlSyxPQUFPLENBQUMvQixxQkFBcUI7d0JBRTVDLHFCQUFxQjt3QkFDckIwRDs2RUFBVztnQ0FDVC9EOzRCQUNGOzRFQUFHLE1BQU0sOENBQThDO29CQUN6RDtnQkFDRjs7WUFFQSw4Q0FBOEM7WUFDOUMsTUFBTThGOzREQUFvQjtvQkFDeEIsb0NBQW9DO29CQUNwQy9ELGVBQWVLLE9BQU8sQ0FBQy9CLHFCQUFxQjtvQkFFNUMscUJBQXFCO29CQUNyQjBEO29FQUFXOzRCQUNUL0Q7d0JBQ0Y7bUVBQUc7Z0JBQ0w7O1lBRUEsc0JBQXNCO1lBQ3RCNEIsU0FBU21FLGdCQUFnQixDQUFDLG9CQUFvQkg7WUFDOUN2RSxPQUFPMEUsZ0JBQWdCLENBQUMsU0FBU0Q7WUFFakMscUJBQXFCO1lBQ3JCOzBDQUFPO29CQUNMUCxhQUFhUyxZQUFZLENBQUNDLFdBQVc7b0JBQ3JDckUsU0FBU3NFLG1CQUFtQixDQUFDLG9CQUFvQk47b0JBQ2pEdkUsT0FBTzZFLG1CQUFtQixDQUFDLFNBQVNKO2dCQUN0Qzs7UUFDRjtpQ0FBRztRQUFDOUY7UUFBU21DO0tBQWM7SUFFM0IscUJBQ0UsOERBQUMzQyxZQUFZMkcsUUFBUTtRQUNuQkMsT0FBTztZQUNMM0c7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUU7WUFDQUM7WUFDQUM7UUFDRjtrQkFFQ1E7Ozs7OztBQUdQO0lBL2NnQkQ7S0FBQUEiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWlqZXR0ZS9zcmMvU1JWWFAvc3JjL2xpYi9BdXRoQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjaywgUmVhY3ROb2RlLCB1c2VSZWYgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IFNlc3Npb24sIFVzZXIgfSBmcm9tIFwiQHN1cGFiYXNlL3N1cGFiYXNlLWpzXCI7XG5pbXBvcnQgeyBzdXBhYmFzZSB9IGZyb20gXCIuL3N1cGFiYXNlL2NsaWVudFwiO1xuXG4vLyBBdXRoIHN0YXRlIHR5cGVzXG5leHBvcnQgdHlwZSBBdXRoU3RhdHVzID0gXCJsb2FkaW5nXCIgfCBcImF1dGhlbnRpY2F0ZWRcIiB8IFwidW5hdXRoZW50aWNhdGVkXCI7XG5cbmludGVyZmFjZSBBdXRoQ29udGV4dFZhbHVlIHtcbiAgdXNlcjogVXNlciB8IG51bGw7XG4gIHNlc3Npb246IFNlc3Npb24gfCBudWxsO1xuICBzdGF0dXM6IEF1dGhTdGF0dXM7XG4gIHNpZ25JbjogKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpID0+IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBlcnJvcj86IEVycm9yIH0+O1xuICBzaWduSW5XaXRoR29vZ2xlOiAoKSA9PiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgZXJyb3I/OiBFcnJvciB9PjtcbiAgc2lnbk91dDogKHJlZGlyZWN0VXJsPzogc3RyaW5nKSA9PiBQcm9taXNlPHZvaWQ+O1xuICByZWZyZXNoOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xufVxuXG4vLyBDcmVhdGUgY29udGV4dCB3aXRoIGRlZmF1bHQgdmFsdWVzXG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRWYWx1ZT4oe1xuICB1c2VyOiBudWxsLFxuICBzZXNzaW9uOiBudWxsLFxuICBzdGF0dXM6IFwibG9hZGluZ1wiLFxuICBzaWduSW46IGFzeW5jICgpID0+ICh7IHN1Y2Nlc3M6IGZhbHNlIH0pLFxuICBzaWduSW5XaXRoR29vZ2xlOiBhc3luYyAoKSA9PiAoeyBzdWNjZXNzOiBmYWxzZSB9KSxcbiAgc2lnbk91dDogYXN5bmMgKCkgPT4ge30sXG4gIHJlZnJlc2g6IGFzeW5jICgpID0+IHt9LFxufSk7XG5cbi8vIEN1c3RvbSBob29rIHRvIHVzZSBhdXRoIGNvbnRleHRcbmV4cG9ydCBjb25zdCB1c2VBdXRoID0gKCkgPT4gdXNlQ29udGV4dChBdXRoQ29udGV4dCk7XG5cbi8vIENhY2hlIGtleXNcbmNvbnN0IEFVVEhfQ0FDSEVfS0VZID0gXCJhdXRoX2NhY2hlXCI7XG5jb25zdCBBVVRIX0NBQ0hFX0VYUElSWV9LRVkgPSBcImF1dGhfY2FjaGVfZXhwaXJ5XCI7XG5jb25zdCBTRVNTSU9OX0JBQ0tVUF9LRVkgPSBcInNlc3Npb25fYmFja3VwXCI7XG5jb25zdCBFWFRFUk5BTF9SRVRVUk5fS0VZID0gXCJleHRlcm5hbF9yZXR1cm5fZGV0ZWN0ZWRcIjtcbmNvbnN0IENBQ0hFX0RVUkFUSU9OID0gMzAgKiA2MCAqIDEwMDA7IC8vIEluY3JlYXNlZCB0byAzMCBtaW51dGVzXG5cbmludGVyZmFjZSBBdXRoUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBBdXRoUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBBdXRoUHJvdmlkZXJQcm9wcykge1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxVc2VyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzZXNzaW9uLCBzZXRTZXNzaW9uXSA9IHVzZVN0YXRlPFNlc3Npb24gfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3N0YXR1cywgc2V0U3RhdHVzXSA9IHVzZVN0YXRlPEF1dGhTdGF0dXM+KFwibG9hZGluZ1wiKTtcbiAgXG4gIC8vIFJhdGUgbGltaXRpbmcgZm9yIHJlZnJlc2ggZnVuY3Rpb25cbiAgY29uc3QgbGFzdFJlZnJlc2hUaW1lID0gdXNlUmVmKDApO1xuICBjb25zdCBSRUZSRVNIX0RFQk9VTkNFX01TID0gMzAwMDsgLy8gMyBzZWNvbmRzIGJldHdlZW4gcmVmcmVzaGVzXG5cbiAgLy8gRnVuY3Rpb24gdG8gZGV0ZWN0IGlmIHVzZXIgcmV0dXJuZWQgZnJvbSBleHRlcm5hbCBzaXRlXG4gIGNvbnN0IGRldGVjdEV4dGVybmFsUmV0dXJuID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiKSByZXR1cm4gZmFsc2U7XG5cbiAgICAvLyBDaGVjayBpZiB3ZSBoYXZlIGEgc2Vzc2lvbiBiYWNrdXAgYnV0IG5vIGN1cnJlbnQgc2Vzc2lvblxuICAgIGNvbnN0IGhhc1Nlc3Npb25CYWNrdXAgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShTRVNTSU9OX0JBQ0tVUF9LRVkpO1xuICAgIGNvbnN0IGhhc0N1cnJlbnRTZXNzaW9uID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oQVVUSF9DQUNIRV9LRVkpO1xuXG4gICAgLy8gQ2hlY2sgaWYgVVJMIGNvbnRhaW5zIHJldHVybiBwYXJhbWV0ZXJzIGZyb20gU3RyaXBlIG9yIG90aGVyIGV4dGVybmFsIHNlcnZpY2VzXG4gICAgY29uc3QgdXJsUGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh3aW5kb3cubG9jYXRpb24uc2VhcmNoKTtcbiAgICBjb25zdCBpc1N0cmlwZVJldHVybiA9IHVybFBhcmFtcy5oYXMoJ3Nlc3Npb25faWQnKSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICB3aW5kb3cubG9jYXRpb24ucGF0aG5hbWUuaW5jbHVkZXMoJy9jb21wdGUvYWJvbm5lbWVudCcpIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnJlZmVycmVyLmluY2x1ZGVzKCdzdHJpcGUuY29tJyk7XG5cbiAgICAvLyBDaGVjayBpZiB1c2VyIHdhcyBtYXJrZWQgYXMgcmV0dXJuaW5nIGZyb20gZXh0ZXJuYWwgc2l0ZVxuICAgIGNvbnN0IHdhc0V4dGVybmFsID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbShFWFRFUk5BTF9SRVRVUk5fS0VZKTtcblxuICAgIC8vIENoZWNrIGZvciBPQXV0aCBzdWNjZXNzIGZsYWcgKGluZGljYXRlcyByZWNlbnQgT0F1dGggbG9naW4pXG4gICAgY29uc3QgaGFzT0F1dGhTdWNjZXNzID0gZG9jdW1lbnQuY29va2llLmluY2x1ZGVzKCdvYXV0aF9zdWNjZXNzPXRydWUnKTtcbiAgICBjb25zdCBoYXNPQXV0aE5ld1VzZXIgPSBkb2N1bWVudC5jb29raWUuaW5jbHVkZXMoJ29hdXRoX25ld191c2VyPXRydWUnKTtcblxuICAgIHJldHVybiAhIShoYXNTZXNzaW9uQmFja3VwICYmICghaGFzQ3VycmVudFNlc3Npb24gfHwgaXNTdHJpcGVSZXR1cm4gfHwgd2FzRXh0ZXJuYWwgfHwgaGFzT0F1dGhTdWNjZXNzIHx8IGhhc09BdXRoTmV3VXNlcikpO1xuICB9LCBbXSk7XG5cbiAgLy8gRnVuY3Rpb24gdG8gYmFja3VwIHNlc3Npb24gYmVmb3JlIGV4dGVybmFsIG5hdmlnYXRpb25cbiAgY29uc3QgYmFja3VwU2Vzc2lvbiA9IHVzZUNhbGxiYWNrKChzZXNzaW9uOiBTZXNzaW9uKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09IFwidW5kZWZpbmVkXCIpIHJldHVybjtcbiAgICBcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShTRVNTSU9OX0JBQ0tVUF9LRVksIEpTT04uc3RyaW5naWZ5KHtcbiAgICAgIHNlc3Npb24sXG4gICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICB1c2VyOiBzZXNzaW9uLnVzZXJcbiAgICB9KSk7XG4gIH0sIFtdKTtcblxuICAvLyBGdW5jdGlvbiB0byByZXN0b3JlIHNlc3Npb24gZnJvbSBiYWNrdXBcbiAgY29uc3QgcmVzdG9yZVNlc3Npb25Gcm9tQmFja3VwID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiKSByZXR1cm4gbnVsbDtcbiAgICBcbiAgICBjb25zdCBiYWNrdXAgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShTRVNTSU9OX0JBQ0tVUF9LRVkpO1xuICAgIGlmICghYmFja3VwKSByZXR1cm4gbnVsbDtcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBzZXNzaW9uOiBiYWNrZWRVcFNlc3Npb24sIHRpbWVzdGFtcCB9ID0gSlNPTi5wYXJzZShiYWNrdXApO1xuICAgICAgXG4gICAgICAvLyBDaGVjayBpZiBiYWNrdXAgaXMgbm90IHRvbyBvbGQgKHdpdGhpbiAyIGhvdXJzKVxuICAgICAgaWYgKERhdGUubm93KCkgLSB0aW1lc3RhbXAgPiAyICogNjAgKiA2MCAqIDEwMDApIHtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oU0VTU0lPTl9CQUNLVVBfS0VZKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIFRyeSB0byByZWZyZXNoIHRoZSBzZXNzaW9uIHVzaW5nIHRoZSBiYWNrZWQgdXAgYWNjZXNzIHRva2VuXG4gICAgICBpZiAoYmFja2VkVXBTZXNzaW9uPy5hY2Nlc3NfdG9rZW4pIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0F0dGVtcHRpbmcgdG8gcmVzdG9yZSBzZXNzaW9uIGZyb20gYmFja3VwLi4uJyk7XG4gICAgICAgIFxuICAgICAgICAvLyBTZXQgdGhlIHNlc3Npb24gbWFudWFsbHkgdG8gdHJpZ2dlciBTdXBhYmFzZSB0byB1c2UgaXRcbiAgICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zZXRTZXNzaW9uKHtcbiAgICAgICAgICBhY2Nlc3NfdG9rZW46IGJhY2tlZFVwU2Vzc2lvbi5hY2Nlc3NfdG9rZW4sXG4gICAgICAgICAgcmVmcmVzaF90b2tlbjogYmFja2VkVXBTZXNzaW9uLnJlZnJlc2hfdG9rZW5cbiAgICAgICAgfSk7XG4gICAgICAgIFxuICAgICAgICBpZiAoIWVycm9yICYmIGRhdGEuc2Vzc2lvbikge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdTdWNjZXNzZnVsbHkgcmVzdG9yZWQgc2Vzc2lvbiBmcm9tIGJhY2t1cCcpO1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFNFU1NJT05fQkFDS1VQX0tFWSk7XG4gICAgICAgICAgcmV0dXJuIGRhdGEuc2Vzc2lvbjtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZXN0b3Jpbmcgc2Vzc2lvbiBmcm9tIGJhY2t1cDonLCBlcnJvcik7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShTRVNTSU9OX0JBQ0tVUF9LRVkpO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gbnVsbDtcbiAgfSwgW10pO1xuXG4gIC8vIEVuaGFuY2VkIHJlZnJlc2ggZnVuY3Rpb24gd2l0aCByYXRlIGxpbWl0aW5nIGFuZCBleHRlcm5hbCByZXR1cm4gZGV0ZWN0aW9uXG4gIGNvbnN0IHJlZnJlc2ggPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIENoZWNrIGlmIHRoaXMgaXMgYSBuZXcgT0F1dGggdXNlciBmaXJzdFxuICAgICAgY29uc3QgaXNOZXdPQXV0aFVzZXIgPSB0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuY29va2llLmluY2x1ZGVzKCdvYXV0aF9uZXdfdXNlcj10cnVlJyk7XG5cbiAgICAgIC8vIFJhdGUgbGltaXRpbmcgLSBwcmV2ZW50IHRvbyBmcmVxdWVudCByZWZyZXNoZXMgKGJ1dCBhbGxvdyBmb3IgbmV3IE9BdXRoIHVzZXJzKVxuICAgICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcbiAgICAgIGlmICghaXNOZXdPQXV0aFVzZXIgJiYgbm93IC0gbGFzdFJlZnJlc2hUaW1lLmN1cnJlbnQgPCBSRUZSRVNIX0RFQk9VTkNFX01TKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIHJlZnJlc2ggcmF0ZSBsaW1pdGVkLCBza2lwcGluZycpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBsYXN0UmVmcmVzaFRpbWUuY3VycmVudCA9IG5vdztcblxuICAgICAgY29uc29sZS5sb2coYEF1dGhDb250ZXh0OiBTdGFydGluZyBhdXRoIHJlZnJlc2guLi4gJHtpc05ld09BdXRoVXNlciA/ICcobmV3IE9BdXRoIHVzZXIpJyA6ICcnfWApO1xuICAgICAgc2V0U3RhdHVzKFwibG9hZGluZ1wiKTtcblxuICAgICAgLy8gRm9yIG5ldyBPQXV0aCB1c2VycywgdHJ5IG11bHRpcGxlIGF0dGVtcHRzIHdpdGggZGVsYXlzXG4gICAgICBsZXQgc2Vzc2lvbkF0dGVtcHRzID0gaXNOZXdPQXV0aFVzZXIgPyAzIDogMTtcbiAgICAgIGxldCBzZXNzaW9uRGF0YSA9IG51bGw7XG4gICAgICBsZXQgc2Vzc2lvbkVycm9yID0gbnVsbDtcblxuICAgICAgZm9yIChsZXQgYXR0ZW1wdCA9IDE7IGF0dGVtcHQgPD0gc2Vzc2lvbkF0dGVtcHRzOyBhdHRlbXB0KyspIHtcbiAgICAgICAgY29uc29sZS5sb2coYEF1dGhDb250ZXh0OiBTZXNzaW9uIGNoZWNrIGF0dGVtcHQgJHthdHRlbXB0fS8ke3Nlc3Npb25BdHRlbXB0c31gKTtcblxuICAgICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFNlc3Npb24oKTtcblxuICAgICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgICBzZXNzaW9uRXJyb3IgPSBlcnJvcjtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKGBBdXRoQ29udGV4dDogU2Vzc2lvbiBlcnJvciAoYXR0ZW1wdCAke2F0dGVtcHR9KTpgLCBlcnJvcik7XG4gICAgICAgIH0gZWxzZSBpZiAoZGF0YT8uc2Vzc2lvbikge1xuICAgICAgICAgIHNlc3Npb25EYXRhID0gZGF0YTtcbiAgICAgICAgICBzZXNzaW9uRXJyb3IgPSBudWxsO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKGF0dGVtcHQgPCBzZXNzaW9uQXR0ZW1wdHMpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnV2FpdGluZyBiZWZvcmUgbmV4dCBzZXNzaW9uIGNoZWNrIGF0dGVtcHQuLi4nKTtcbiAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCkpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmIChzZXNzaW9uRXJyb3IgJiYgIXNlc3Npb25EYXRhKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGhDb250ZXh0OiBGaW5hbCBzZXNzaW9uIGVycm9yOicsIHNlc3Npb25FcnJvcik7XG4gICAgICAgIHRocm93IHNlc3Npb25FcnJvcjtcbiAgICAgIH1cblxuICAgICAgaWYgKHNlc3Npb25EYXRhPy5zZXNzaW9uKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKGBBdXRoQ29udGV4dDogU2Vzc2lvbiBmb3VuZCBmb3IgdXNlcjogJHtzZXNzaW9uRGF0YS5zZXNzaW9uLnVzZXIuZW1haWx9ICR7aXNOZXdPQXV0aFVzZXIgPyAnKG5ldyBPQXV0aCB1c2VyKScgOiAnJ31gKTtcbiAgICAgICAgc2V0U2Vzc2lvbihzZXNzaW9uRGF0YS5zZXNzaW9uKTtcbiAgICAgICAgc2V0VXNlcihzZXNzaW9uRGF0YS5zZXNzaW9uLnVzZXIpO1xuICAgICAgICBzZXRTdGF0dXMoXCJhdXRoZW50aWNhdGVkXCIpO1xuXG4gICAgICAgIC8vIEJhY2t1cCBzZXNzaW9uIGZvciBwb3RlbnRpYWwgZXh0ZXJuYWwgbmF2aWdhdGlvbiByZWNvdmVyeVxuICAgICAgICBiYWNrdXBTZXNzaW9uKHNlc3Npb25EYXRhLnNlc3Npb24pO1xuXG4gICAgICAgIC8vIENhY2hlIHRoZSBhdXRoIHN0YXRlIGZvciBmYXN0ZXIgc3Vic2VxdWVudCBsb2Fkc1xuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcbiAgICAgICAgICAgICAgQVVUSF9DQUNIRV9LRVksXG4gICAgICAgICAgICAgIEpTT04uc3RyaW5naWZ5KHsgc2Vzc2lvbjogc2Vzc2lvbkRhdGEuc2Vzc2lvbiwgdXNlcjogc2Vzc2lvbkRhdGEuc2Vzc2lvbi51c2VyIH0pXG4gICAgICAgICAgICApO1xuICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXG4gICAgICAgICAgICAgIEFVVEhfQ0FDSEVfRVhQSVJZX0tFWSxcbiAgICAgICAgICAgICAgKERhdGUubm93KCkgKyBDQUNIRV9EVVJBVElPTikudG9TdHJpbmcoKVxuICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgLy8gQ2xlYXIgbmV3IE9BdXRoIHVzZXIgZmxhZyBpZiBpdCBleGlzdHNcbiAgICAgICAgICAgIGlmIChpc05ld09BdXRoVXNlcikge1xuICAgICAgICAgICAgICBkb2N1bWVudC5jb29raWUgPSBcIm9hdXRoX25ld191c2VyPTsgZXhwaXJlcz1UaHUsIDAxIEphbiAxOTcwIDAwOjAwOjAwIFVUQzsgcGF0aD0vO1wiO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0gY2F0Y2ggKHN0b3JhZ2VFcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKCdGYWlsZWQgdG8gdXBkYXRlIGxvY2FsU3RvcmFnZSBjYWNoZTonLCBzdG9yYWdlRXJyb3IpO1xuICAgICAgICAgICAgLy8gRG9uJ3QgZmFpbCBhdXRoIGlmIGxvY2FsU3RvcmFnZSBpcyBub3QgYXZhaWxhYmxlXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmxvZygnQXV0aENvbnRleHQ6IE5vIHNlc3Npb24gZm91bmQnKTtcbiAgICAgICAgXG4gICAgICAgIC8vIENoZWNrIGlmIHVzZXIgaXMgcmV0dXJuaW5nIGZyb20gZXh0ZXJuYWwgc2l0ZSBhbmQgdHJ5IGJhY2t1cCByZXN0b3JhdGlvblxuICAgICAgICBjb25zdCBpc0V4dGVybmFsUmV0dXJuID0gZGV0ZWN0RXh0ZXJuYWxSZXR1cm4oKTtcbiAgICAgICAgXG4gICAgICAgIGlmIChpc0V4dGVybmFsUmV0dXJuKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0V4dGVybmFsIHJldHVybiBkZXRlY3RlZCwgYXR0ZW1wdGluZyBzZXNzaW9uIHJlc3RvcmF0aW9uLi4uJyk7XG4gICAgICAgICAgc2Vzc2lvblN0b3JhZ2UucmVtb3ZlSXRlbShFWFRFUk5BTF9SRVRVUk5fS0VZKTtcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBUcnkgdG8gcmVzdG9yZSBmcm9tIGJhY2t1cFxuICAgICAgICAgIGNvbnN0IHJlc3RvcmVkU2Vzc2lvbiA9IGF3YWl0IHJlc3RvcmVTZXNzaW9uRnJvbUJhY2t1cCgpO1xuICAgICAgICAgIGlmIChyZXN0b3JlZFNlc3Npb24pIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdTZXNzaW9uIHJlc3RvcmVkIGZyb20gYmFja3VwJyk7XG4gICAgICAgICAgICBzZXRTZXNzaW9uKHJlc3RvcmVkU2Vzc2lvbik7XG4gICAgICAgICAgICBzZXRVc2VyKHJlc3RvcmVkU2Vzc2lvbi51c2VyKTtcbiAgICAgICAgICAgIHNldFN0YXR1cyhcImF1dGhlbnRpY2F0ZWRcIik7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIFVwZGF0ZSBjYWNoZVxuICAgICAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcbiAgICAgICAgICAgICAgICAgIEFVVEhfQ0FDSEVfS0VZLFxuICAgICAgICAgICAgICAgICAgSlNPTi5zdHJpbmdpZnkoeyBzZXNzaW9uOiByZXN0b3JlZFNlc3Npb24sIHVzZXI6IHJlc3RvcmVkU2Vzc2lvbi51c2VyIH0pXG4gICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcbiAgICAgICAgICAgICAgICAgIEFVVEhfQ0FDSEVfRVhQSVJZX0tFWSxcbiAgICAgICAgICAgICAgICAgIChEYXRlLm5vdygpICsgQ0FDSEVfRFVSQVRJT04pLnRvU3RyaW5nKClcbiAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICB9IGNhdGNoIChzdG9yYWdlRXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byB1cGRhdGUgbG9jYWxTdG9yYWdlIGFmdGVyIHJlc3RvcmU6Jywgc3RvcmFnZUVycm9yKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIE5vIHNlc3Npb24gZm91bmQsIHNldCB1bmF1dGhlbnRpY2F0ZWQgc3RhdGVcbiAgICAgICAgc2V0U2Vzc2lvbihudWxsKTtcbiAgICAgICAgc2V0VXNlcihudWxsKTtcbiAgICAgICAgc2V0U3RhdHVzKFwidW5hdXRoZW50aWNhdGVkXCIpO1xuXG4gICAgICAgIC8vIENsZWFyIGNhY2hlIGFuZCBiYWNrdXBcbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oQVVUSF9DQUNIRV9LRVkpO1xuICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oQVVUSF9DQUNIRV9FWFBJUllfS0VZKTtcbiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFNFU1NJT05fQkFDS1VQX0tFWSk7XG4gICAgICAgICAgfSBjYXRjaCAoc3RvcmFnZUVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBjbGVhciBsb2NhbFN0b3JhZ2U6Jywgc3RvcmFnZUVycm9yKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkF1dGhDb250ZXh0OiBFcnJvciByZWZyZXNoaW5nIGF1dGggc3RhdGU6XCIsIGVycm9yKTtcbiAgICAgIFxuICAgICAgLy8gT24gZXJyb3IsIGFzc3VtZSBub3QgYXV0aGVudGljYXRlZFxuICAgICAgc2V0U3RhdHVzKFwidW5hdXRoZW50aWNhdGVkXCIpO1xuICAgICAgc2V0U2Vzc2lvbihudWxsKTtcbiAgICAgIHNldFVzZXIobnVsbCk7XG5cbiAgICAgIC8vIENsZWFyIGNhY2hlIG9uIGVycm9yXG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKEFVVEhfQ0FDSEVfS0VZKTtcbiAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShBVVRIX0NBQ0hFX0VYUElSWV9LRVkpO1xuICAgICAgICB9IGNhdGNoIChzdG9yYWdlRXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBjbGVhciBsb2NhbFN0b3JhZ2Ugb24gZXJyb3I6Jywgc3RvcmFnZUVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfSwgW2RldGVjdEV4dGVybmFsUmV0dXJuLCByZXN0b3JlU2Vzc2lvbkZyb21CYWNrdXAsIGJhY2t1cFNlc3Npb25dKTtcblxuICAvLyBTaWduIGluIHdpdGggZW1haWwgYW5kIHBhc3N3b3JkXG4gIGNvbnN0IHNpZ25JbiA9IHVzZUNhbGxiYWNrKGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguc2lnbkluV2l0aFBhc3N3b3JkKHtcbiAgICAgICAgZW1haWwsXG4gICAgICAgIHBhc3N3b3JkLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cblxuICAgICAgaWYgKGRhdGEuc2Vzc2lvbikge1xuICAgICAgICAvLyBVcGRhdGUgc3RhdGUgd2l0aCBuZXcgc2Vzc2lvblxuICAgICAgICBzZXRTZXNzaW9uKGRhdGEuc2Vzc2lvbik7XG4gICAgICAgIHNldFVzZXIoZGF0YS5zZXNzaW9uLnVzZXIpO1xuICAgICAgICBzZXRTdGF0dXMoXCJhdXRoZW50aWNhdGVkXCIpO1xuXG4gICAgICAgIC8vIEJhY2t1cCBzZXNzaW9uIGZvciBleHRlcm5hbCBuYXZpZ2F0aW9uXG4gICAgICAgIGJhY2t1cFNlc3Npb24oZGF0YS5zZXNzaW9uKTtcblxuICAgICAgICAvLyBDYWNoZSB0aGUgYXV0aCBzdGF0ZVxuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcbiAgICAgICAgICBBVVRIX0NBQ0hFX0tFWSxcbiAgICAgICAgICBKU09OLnN0cmluZ2lmeSh7IHNlc3Npb246IGRhdGEuc2Vzc2lvbiwgdXNlcjogZGF0YS5zZXNzaW9uLnVzZXIgfSlcbiAgICAgICAgKTtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXG4gICAgICAgICAgQVVUSF9DQUNIRV9FWFBJUllfS0VZLFxuICAgICAgICAgIChEYXRlLm5vdygpICsgQ0FDSEVfRFVSQVRJT04pLnRvU3RyaW5nKClcbiAgICAgICAgKTtcblxuICAgICAgICAvLyBDbGVhciBhbnkgb2xkIGF1dGgtcmVsYXRlZCBmbGFncyBmcm9tIHNlc3Npb25TdG9yYWdlXG4gICAgICAgIGNvbnN0IGtleXNUb1JlbW92ZSA9IFtdO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHNlc3Npb25TdG9yYWdlLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgY29uc3Qga2V5ID0gc2Vzc2lvblN0b3JhZ2Uua2V5KGkpO1xuICAgICAgICAgIGlmIChrZXkgJiYgKGtleS5pbmNsdWRlcyhcImF1dGhcIikgfHwga2V5LmluY2x1ZGVzKFwibG9nb3V0XCIpIHx8IGtleS5pbmNsdWRlcyhcInN1cGFiYXNlXCIpKSkge1xuICAgICAgICAgICAga2V5c1RvUmVtb3ZlLnB1c2goa2V5KTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAga2V5c1RvUmVtb3ZlLmZvckVhY2goa2V5ID0+IHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oa2V5KSk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIlNpZ24gaW4gZXJyb3I6XCIsIGVycm9yKTtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3IgYXMgRXJyb3IgfTtcbiAgICB9XG4gIH0sIFtiYWNrdXBTZXNzaW9uXSk7XG5cbiAgLy8gU2lnbiBpbiB3aXRoIEdvb2dsZVxuICBjb25zdCBzaWduSW5XaXRoR29vZ2xlID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBHZXQgdGhlIGN1cnJlbnQgb3JpZ2luIHRvIGVuc3VyZSBjb3JyZWN0IHJlZGlyZWN0IFVSTFxuICAgICAgY29uc3Qgb3JpZ2luID0gd2luZG93LmxvY2F0aW9uLm9yaWdpbjtcbiAgICAgIC8vIENyZWF0ZSB0aGUgY2FsbGJhY2sgVVJMIHdpdGggZXhwbGljaXQgZGFzaGJvYXJkIHJlZGlyZWN0XG4gICAgICBjb25zdCBjYWxsYmFja1VybCA9IGAke29yaWdpbn0vYXV0aC9jYWxsYmFjaz9yZWRpcmVjdFRvPS9kYXNoYm9hcmRgO1xuXG4gICAgICBjb25zb2xlLmxvZyhgU2V0dGluZyB1cCBHb29nbGUgYXV0aCB3aXRoIGNhbGxiYWNrIFVSTDogJHtjYWxsYmFja1VybH1gKTtcblxuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduSW5XaXRoT0F1dGgoe1xuICAgICAgICBwcm92aWRlcjogJ2dvb2dsZScsXG4gICAgICAgIG9wdGlvbnM6IHtcbiAgICAgICAgICByZWRpcmVjdFRvOiBjYWxsYmFja1VybFxuICAgICAgICB9XG4gICAgICB9KTtcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIHRocm93IGVycm9yO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJHb29nbGUgc2lnbiBpbiBlcnJvcjpcIiwgZXJyb3IpO1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBlcnJvciBhcyBFcnJvciB9O1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIC8vIEVuaGFuY2VkIHNpZ24gb3V0IGZ1bmN0aW9uXG4gIGNvbnN0IHNpZ25PdXQgPSB1c2VDYWxsYmFjayhhc3luYyAocmVkaXJlY3RVcmw6IHN0cmluZyA9IFwiL1wiKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIFNpZ24gb3V0IHdpdGggU3VwYWJhc2VcbiAgICAgIGF3YWl0IHN1cGFiYXNlLmF1dGguc2lnbk91dCgpO1xuXG4gICAgICAvLyBDbGVhciBhdXRoIHN0YXRlXG4gICAgICBzZXRTZXNzaW9uKG51bGwpO1xuICAgICAgc2V0VXNlcihudWxsKTtcbiAgICAgIHNldFN0YXR1cyhcInVuYXV0aGVudGljYXRlZFwiKTtcblxuICAgICAgLy8gQ2xlYXIgYWxsIGF1dGgtcmVsYXRlZCBzdG9yYWdlXG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShBVVRIX0NBQ0hFX0tFWSk7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShBVVRIX0NBQ0hFX0VYUElSWV9LRVkpO1xuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oU0VTU0lPTl9CQUNLVVBfS0VZKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFwic3VwYWJhc2UuYXV0aC50b2tlblwiKTtcblxuICAgICAgLy8gUmVzZXQgdGhlbWUgdG8gc3lzdGVtIGRlZmF1bHQgd2hlbiBsb2dnaW5nIG91dFxuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oXCJ0aGVtZVwiKTtcblxuICAgICAgLy8gQ2xlYXIgdXNlci1zcGVjaWZpYyBwcmVmZXJlbmNlcyBjYWNoZVxuICAgICAgaWYgKHVzZXI/LmlkKSB7XG4gICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGB1c2VyX3ByZWZlcmVuY2VzXyR7dXNlci5pZH1gKTtcbiAgICAgIH1cblxuICAgICAgLy8gU2V0IGEgZmxhZyBpbiBzZXNzaW9uIHN0b3JhZ2UgdG8gaW5kaWNhdGUgbG9nb3V0XG4gICAgICBzZXNzaW9uU3RvcmFnZS5zZXRJdGVtKFwiaXNMb2dnZWRPdXRcIiwgXCJ0cnVlXCIpO1xuXG4gICAgICAvLyBGb3JjZSBwYWdlIHJlZnJlc2ggZm9yIGNsZWFuIHN0YXRlXG4gICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IHJlZGlyZWN0VXJsO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiU2lnbiBvdXQgZXJyb3I6XCIsIGVycm9yKTtcblxuICAgICAgLy8gRXZlbiBpZiB0aGVyZSB3YXMgYW4gZXJyb3IsIHRyeSB0byByZWRpcmVjdCBhbmQgY2xlYW4gdXAgc3RhdGVcbiAgICAgIHNldFNlc3Npb24obnVsbCk7XG4gICAgICBzZXRVc2VyKG51bGwpO1xuICAgICAgc2V0U3RhdHVzKFwidW5hdXRoZW50aWNhdGVkXCIpO1xuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSByZWRpcmVjdFVybDtcbiAgICB9XG4gIH0sIFt1c2VyXSk7XG5cbiAgLy8gU2V0dXAgYXV0aCBzdGF0ZSB3aXRoIGVuaGFuY2VkIGV4dGVybmFsIG5hdmlnYXRpb24gZGV0ZWN0aW9uXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gSW5pdGlhbCBhdXRoIGNoZWNrXG4gICAgcmVmcmVzaCgpO1xuXG4gICAgLy8gU2V0dXAgYXV0aCBzdGF0ZSBsaXN0ZW5lciBmb3IgY2hhbmdlc1xuICAgIGNvbnN0IHsgZGF0YTogYXV0aExpc3RlbmVyIH0gPSBzdXBhYmFzZS5hdXRoLm9uQXV0aFN0YXRlQ2hhbmdlKChldmVudCwgc2Vzc2lvbikgPT4ge1xuICAgICAgY29uc29sZS5sb2coYEF1dGhDb250ZXh0OiBBdXRoIHN0YXRlIGNoYW5nZSAtICR7ZXZlbnR9YCwgeyBoYXNTZXNzaW9uOiAhIXNlc3Npb24sIHVzZXJFbWFpbDogc2Vzc2lvbj8udXNlcj8uZW1haWwgfSk7XG5cbiAgICAgIGlmIChldmVudCA9PT0gXCJTSUdORURfSU5cIiAmJiBzZXNzaW9uKSB7XG4gICAgICAgIHNldFNlc3Npb24oc2Vzc2lvbik7XG4gICAgICAgIHNldFVzZXIoc2Vzc2lvbi51c2VyKTtcbiAgICAgICAgc2V0U3RhdHVzKFwiYXV0aGVudGljYXRlZFwiKTtcblxuICAgICAgICAvLyBCYWNrdXAgc2Vzc2lvbiBmb3IgZXh0ZXJuYWwgbmF2aWdhdGlvblxuICAgICAgICBiYWNrdXBTZXNzaW9uKHNlc3Npb24pO1xuXG4gICAgICAgIC8vIFVwZGF0ZSBjYWNoZVxuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcbiAgICAgICAgICBBVVRIX0NBQ0hFX0tFWSxcbiAgICAgICAgICBKU09OLnN0cmluZ2lmeSh7IHNlc3Npb24sIHVzZXI6IHNlc3Npb24udXNlciB9KVxuICAgICAgICApO1xuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcbiAgICAgICAgICBBVVRIX0NBQ0hFX0VYUElSWV9LRVksXG4gICAgICAgICAgKERhdGUubm93KCkgKyBDQUNIRV9EVVJBVElPTikudG9TdHJpbmcoKVxuICAgICAgICApO1xuXG4gICAgICAgIC8vIENsZWFyIE9BdXRoIHN1Y2Nlc3MgZmxhZyBpZiBpdCBleGlzdHNcbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgICBkb2N1bWVudC5jb29raWUgPSBcIm9hdXRoX3N1Y2Nlc3M9OyBleHBpcmVzPVRodSwgMDEgSmFuIDE5NzAgMDA6MDA6MDAgVVRDOyBwYXRoPS87XCI7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoZXZlbnQgPT09IFwiU0lHTkVEX09VVFwiKSB7XG4gICAgICAgIHNldFNlc3Npb24obnVsbCk7XG4gICAgICAgIHNldFVzZXIobnVsbCk7XG4gICAgICAgIHNldFN0YXR1cyhcInVuYXV0aGVudGljYXRlZFwiKTtcblxuICAgICAgICAvLyBDbGVhciBjYWNoZSBhbmQgYmFja3VwXG4gICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKEFVVEhfQ0FDSEVfS0VZKTtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oQVVUSF9DQUNIRV9FWFBJUllfS0VZKTtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oU0VTU0lPTl9CQUNLVVBfS0VZKTtcbiAgICAgIH0gZWxzZSBpZiAoZXZlbnQgPT09IFwiVE9LRU5fUkVGUkVTSEVEXCIgJiYgc2Vzc2lvbikge1xuICAgICAgICBjb25zb2xlLmxvZyhcIkF1dGhDb250ZXh0OiBUb2tlbiByZWZyZXNoZWQgc3VjY2Vzc2Z1bGx5XCIpO1xuICAgICAgICBzZXRTZXNzaW9uKHNlc3Npb24pO1xuICAgICAgICBzZXRVc2VyKHNlc3Npb24udXNlcik7XG4gICAgICAgIHNldFN0YXR1cyhcImF1dGhlbnRpY2F0ZWRcIik7XG5cbiAgICAgICAgLy8gVXBkYXRlIGJhY2t1cCBhbmQgY2FjaGUgd2l0aCByZWZyZXNoZWQgc2Vzc2lvblxuICAgICAgICBiYWNrdXBTZXNzaW9uKHNlc3Npb24pO1xuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcbiAgICAgICAgICBBVVRIX0NBQ0hFX0tFWSxcbiAgICAgICAgICBKU09OLnN0cmluZ2lmeSh7IHNlc3Npb24sIHVzZXI6IHNlc3Npb24udXNlciB9KVxuICAgICAgICApO1xuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcbiAgICAgICAgICBBVVRIX0NBQ0hFX0VYUElSWV9LRVksXG4gICAgICAgICAgKERhdGUubm93KCkgKyBDQUNIRV9EVVJBVElPTikudG9TdHJpbmcoKVxuICAgICAgICApO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgLy8gRW5oYW5jZWQgdmlzaWJpbGl0eSBjaGFuZ2UgaGFuZGxlciBmb3IgZXh0ZXJuYWwgcmV0dXJuc1xuICAgIGNvbnN0IGhhbmRsZVZpc2liaWxpdHlDaGFuZ2UgPSAoKSA9PiB7XG4gICAgICBpZiAoZG9jdW1lbnQudmlzaWJpbGl0eVN0YXRlID09PSBcInZpc2libGVcIikge1xuICAgICAgICAvLyBNYXJrIGFzIHBvdGVudGlhbCBleHRlcm5hbCByZXR1cm5cbiAgICAgICAgc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbShFWFRFUk5BTF9SRVRVUk5fS0VZLCBcInRydWVcIik7XG4gICAgICAgIFxuICAgICAgICAvLyBSZWZyZXNoIGF1dGggc3RhdGVcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgcmVmcmVzaCgpO1xuICAgICAgICB9LCAxMDApOyAvLyBTbWFsbCBkZWxheSB0byBlbnN1cmUgcGFnZSBpcyBmdWxseSB2aXNpYmxlXG4gICAgICB9XG4gICAgfTtcblxuICAgIC8vIEVuaGFuY2VkIGZvY3VzIGhhbmRsZXIgZm9yIGV4dGVybmFsIHJldHVybnNcbiAgICBjb25zdCBoYW5kbGVXaW5kb3dGb2N1cyA9ICgpID0+IHtcbiAgICAgIC8vIE1hcmsgYXMgcG90ZW50aWFsIGV4dGVybmFsIHJldHVyblxuICAgICAgc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbShFWFRFUk5BTF9SRVRVUk5fS0VZLCBcInRydWVcIik7XG4gICAgICBcbiAgICAgIC8vIFJlZnJlc2ggYXV0aCBzdGF0ZVxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHJlZnJlc2goKTtcbiAgICAgIH0sIDEwMCk7XG4gICAgfTtcblxuICAgIC8vIEFkZCBldmVudCBsaXN0ZW5lcnNcbiAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKFwidmlzaWJpbGl0eWNoYW5nZVwiLCBoYW5kbGVWaXNpYmlsaXR5Q2hhbmdlKTtcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcImZvY3VzXCIsIGhhbmRsZVdpbmRvd0ZvY3VzKTtcblxuICAgIC8vIENsZWFudXAgb24gdW5tb3VudFxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBhdXRoTGlzdGVuZXIuc3Vic2NyaXB0aW9uLnVuc3Vic2NyaWJlKCk7XG4gICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKFwidmlzaWJpbGl0eWNoYW5nZVwiLCBoYW5kbGVWaXNpYmlsaXR5Q2hhbmdlKTtcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKFwiZm9jdXNcIiwgaGFuZGxlV2luZG93Rm9jdXMpO1xuICAgIH07XG4gIH0sIFtyZWZyZXNoLCBiYWNrdXBTZXNzaW9uXSk7XG5cbiAgcmV0dXJuIChcbiAgICA8QXV0aENvbnRleHQuUHJvdmlkZXJcbiAgICAgIHZhbHVlPXt7XG4gICAgICAgIHVzZXIsXG4gICAgICAgIHNlc3Npb24sXG4gICAgICAgIHN0YXR1cyxcbiAgICAgICAgc2lnbkluLFxuICAgICAgICBzaWduSW5XaXRoR29vZ2xlLFxuICAgICAgICBzaWduT3V0LFxuICAgICAgICByZWZyZXNoLFxuICAgICAgfX1cbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9BdXRoQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZVJlZiIsInN1cGFiYXNlIiwiQXV0aENvbnRleHQiLCJ1c2VyIiwic2Vzc2lvbiIsInN0YXR1cyIsInNpZ25JbiIsInN1Y2Nlc3MiLCJzaWduSW5XaXRoR29vZ2xlIiwic2lnbk91dCIsInJlZnJlc2giLCJ1c2VBdXRoIiwiQVVUSF9DQUNIRV9LRVkiLCJBVVRIX0NBQ0hFX0VYUElSWV9LRVkiLCJTRVNTSU9OX0JBQ0tVUF9LRVkiLCJFWFRFUk5BTF9SRVRVUk5fS0VZIiwiQ0FDSEVfRFVSQVRJT04iLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsInNldFVzZXIiLCJzZXRTZXNzaW9uIiwic2V0U3RhdHVzIiwibGFzdFJlZnJlc2hUaW1lIiwiUkVGUkVTSF9ERUJPVU5DRV9NUyIsImRldGVjdEV4dGVybmFsUmV0dXJuIiwiaGFzU2Vzc2lvbkJhY2t1cCIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJoYXNDdXJyZW50U2Vzc2lvbiIsInVybFBhcmFtcyIsIlVSTFNlYXJjaFBhcmFtcyIsIndpbmRvdyIsImxvY2F0aW9uIiwic2VhcmNoIiwiaXNTdHJpcGVSZXR1cm4iLCJoYXMiLCJwYXRobmFtZSIsImluY2x1ZGVzIiwiZG9jdW1lbnQiLCJyZWZlcnJlciIsIndhc0V4dGVybmFsIiwic2Vzc2lvblN0b3JhZ2UiLCJoYXNPQXV0aFN1Y2Nlc3MiLCJjb29raWUiLCJoYXNPQXV0aE5ld1VzZXIiLCJiYWNrdXBTZXNzaW9uIiwic2V0SXRlbSIsIkpTT04iLCJzdHJpbmdpZnkiLCJ0aW1lc3RhbXAiLCJEYXRlIiwibm93IiwicmVzdG9yZVNlc3Npb25Gcm9tQmFja3VwIiwiYmFja3VwIiwiYmFja2VkVXBTZXNzaW9uIiwicGFyc2UiLCJyZW1vdmVJdGVtIiwiYWNjZXNzX3Rva2VuIiwiY29uc29sZSIsImxvZyIsImRhdGEiLCJlcnJvciIsImF1dGgiLCJyZWZyZXNoX3Rva2VuIiwiaXNOZXdPQXV0aFVzZXIiLCJjdXJyZW50Iiwic2Vzc2lvbkF0dGVtcHRzIiwic2Vzc2lvbkRhdGEiLCJzZXNzaW9uRXJyb3IiLCJhdHRlbXB0IiwiZ2V0U2Vzc2lvbiIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImVtYWlsIiwidG9TdHJpbmciLCJzdG9yYWdlRXJyb3IiLCJ3YXJuIiwiaXNFeHRlcm5hbFJldHVybiIsInJlc3RvcmVkU2Vzc2lvbiIsInBhc3N3b3JkIiwic2lnbkluV2l0aFBhc3N3b3JkIiwia2V5c1RvUmVtb3ZlIiwiaSIsImxlbmd0aCIsImtleSIsInB1c2giLCJmb3JFYWNoIiwib3JpZ2luIiwiY2FsbGJhY2tVcmwiLCJzaWduSW5XaXRoT0F1dGgiLCJwcm92aWRlciIsIm9wdGlvbnMiLCJyZWRpcmVjdFRvIiwicmVkaXJlY3RVcmwiLCJpZCIsImhyZWYiLCJhdXRoTGlzdGVuZXIiLCJvbkF1dGhTdGF0ZUNoYW5nZSIsImV2ZW50IiwiaGFzU2Vzc2lvbiIsInVzZXJFbWFpbCIsImhhbmRsZVZpc2liaWxpdHlDaGFuZ2UiLCJ2aXNpYmlsaXR5U3RhdGUiLCJoYW5kbGVXaW5kb3dGb2N1cyIsImFkZEV2ZW50TGlzdGVuZXIiLCJzdWJzY3JpcHRpb24iLCJ1bnN1YnNjcmliZSIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJQcm92aWRlciIsInZhbHVlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/FamilyMembersContext.tsx":
/*!******************************************!*\
  !*** ./src/lib/FamilyMembersContext.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FamilyMembersProvider: () => (/* binding */ FamilyMembersProvider),\n/* harmony export */   useFamilyMembers: () => (/* binding */ useFamilyMembers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/lib/AuthContext.tsx\");\n/* harmony import */ var _family_members_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./family-members/api */ \"(app-pages-browser)/./src/lib/family-members/api.ts\");\n/* __next_internal_client_entry_do_not_use__ FamilyMembersProvider,useFamilyMembers auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Create initial data\nconst initialFamilyMembers = [\n    {\n        id: 1,\n        firstName: \"Félix\",\n        lastName: \"Tremblay\",\n        healthCard: \"TREF\",\n        birthDate: new Date(1985, 3, 12),\n        editing: false\n    },\n    {\n        id: 2,\n        firstName: \"Marie\",\n        lastName: \"Tremblay\",\n        healthCard: \"TREM\",\n        birthDate: new Date(1987, 8, 23),\n        editing: false\n    },\n    {\n        id: 3,\n        firstName: \"\",\n        lastName: \"\",\n        healthCard: \"\",\n        birthDate: undefined,\n        editing: false\n    },\n    {\n        id: 4,\n        firstName: \"\",\n        lastName: \"\",\n        healthCard: \"\",\n        birthDate: undefined,\n        editing: false\n    },\n    {\n        id: 5,\n        firstName: \"\",\n        lastName: \"\",\n        healthCard: \"\",\n        birthDate: undefined,\n        editing: false\n    }\n];\n// Create a default empty context value for SSR\nconst defaultContextValue = {\n    familyMembers: initialFamilyMembers,\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    setFamilyMembers: ()=>{},\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    updateFamilyMember: ()=>{},\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    updateTempFamilyMember: ()=>{},\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    toggleEditing: ()=>{},\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    saveMemberChanges: ()=>false,\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    deleteMember: ()=>{},\n    isLoading: false,\n    error: null\n};\n// Create the context with the default value\nconst FamilyMembersContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(defaultContextValue);\nfunction FamilyMembersProvider(param) {\n    let { children } = param;\n    _s();\n    const { user, status } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [familyMembers, setFamilyMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFamilyMembers);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Update actual family member data\n    const updateFamilyMember = (id, data)=>{\n        setFamilyMembers((prev)=>prev.map((member)=>member.id === id ? {\n                    ...member,\n                    ...data\n                } : member));\n    };\n    // Update temporary data during editing\n    const updateTempFamilyMember = (id, data)=>{\n        setFamilyMembers((prev)=>prev.map((member)=>member.id === id ? {\n                    ...member,\n                    tempFirstName: 'tempFirstName' in data ? data.tempFirstName : member.tempFirstName,\n                    tempLastName: 'tempLastName' in data ? data.tempLastName : member.tempLastName,\n                    tempHealthCard: 'tempHealthCard' in data ? data.tempHealthCard : member.tempHealthCard,\n                    tempBirthDate: 'tempBirthDate' in data ? data.tempBirthDate : member.tempBirthDate\n                } : member));\n    };\n    // Toggle editing mode for a member\n    const toggleEditing = (id)=>{\n        setFamilyMembers((prev)=>prev.map((member)=>{\n                if (member.id === id) {\n                    if (!member.editing) {\n                        // When starting to edit, initialize temp values\n                        return {\n                            ...member,\n                            editing: true,\n                            tempFirstName: member.firstName,\n                            tempLastName: member.lastName,\n                            tempHealthCard: member.healthCard,\n                            tempBirthDate: member.birthDate\n                        };\n                    } else {\n                        // When canceling edit, discard temp values\n                        return {\n                            ...member,\n                            editing: false,\n                            tempFirstName: undefined,\n                            tempLastName: undefined,\n                            tempHealthCard: undefined,\n                            tempBirthDate: undefined\n                        };\n                    }\n                }\n                return member;\n            }));\n    };\n    // Save changes from temp values to actual values\n    const saveMemberChanges = (id)=>{\n        // Validation - check if required fields are filled\n        const memberToSave = familyMembers.find((m)=>m.id === id);\n        if (!memberToSave) return false;\n        const firstName = memberToSave.tempFirstName !== undefined ? memberToSave.tempFirstName : memberToSave.firstName;\n        const lastName = memberToSave.tempLastName !== undefined ? memberToSave.tempLastName : memberToSave.lastName;\n        const healthCard = memberToSave.tempHealthCard !== undefined ? memberToSave.tempHealthCard : memberToSave.healthCard;\n        const birthDate = memberToSave.tempBirthDate !== undefined ? memberToSave.tempBirthDate : memberToSave.birthDate;\n        // Validate required fields\n        const isValid = !!firstName && !!lastName && !!healthCard && healthCard.length === 4 && !!birthDate;\n        // If validation fails, return false and don't save\n        if (!isValid) return false;\n        setFamilyMembers((prev)=>{\n            const updatedMembers = prev.map((member)=>{\n                if (member.id === id && member.editing) {\n                    // Apply temporary values to actual values\n                    const updatedMember = {\n                        ...member,\n                        editing: false,\n                        firstName: member.tempFirstName !== undefined ? member.tempFirstName : member.firstName,\n                        lastName: member.tempLastName !== undefined ? member.tempLastName : member.lastName,\n                        healthCard: member.tempHealthCard !== undefined ? member.tempHealthCard : member.healthCard,\n                        birthDate: member.tempBirthDate !== undefined ? member.tempBirthDate : member.birthDate,\n                        // Clear temp values\n                        tempFirstName: undefined,\n                        tempLastName: undefined,\n                        tempHealthCard: undefined,\n                        tempBirthDate: undefined\n                    };\n                    // Save to Supabase if user is authenticated\n                    if ((user === null || user === void 0 ? void 0 : user.id) && (updatedMember.firstName || updatedMember.lastName || updatedMember.healthCard || updatedMember.birthDate)) {\n                        (0,_family_members_api__WEBPACK_IMPORTED_MODULE_3__.saveFamilyMember)(user.id, updatedMember, member.id).then((result)=>{\n                            if (result.success && result.id) {\n                                // Update the member with the Supabase ID\n                                setFamilyMembers((current)=>current.map((m)=>m.id === member.id ? {\n                                            ...m,\n                                            supabaseId: result.id\n                                        } : m));\n                            }\n                        }).catch((err)=>console.error(\"Error saving member:\", err));\n                    }\n                    return updatedMember;\n                }\n                return member;\n            });\n            // Ensure we always have exactly 5 members after saving\n            if (updatedMembers.length < 5) {\n                // If we somehow have fewer than 5 members, add empty slots\n                const existingIds = updatedMembers.map((m)=>m.id);\n                for(let i = 1; i <= 5; i++){\n                    if (!existingIds.includes(i)) {\n                        updatedMembers.push({\n                            id: i,\n                            firstName: \"\",\n                            lastName: \"\",\n                            healthCard: \"\",\n                            birthDate: undefined,\n                            editing: false\n                        });\n                    }\n                }\n                // Sort by ID to ensure correct order\n                updatedMembers.sort((a, b)=>a.id - b.id);\n            }\n            return updatedMembers;\n        });\n        return true; // Return true if validation passes and save is initiated\n    };\n    // Function to save all family members to Supabase\n    // Load family members from Supabase when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FamilyMembersProvider.useEffect\": ()=>{\n            if (status === \"loading\") return;\n            if (status === \"authenticated\" && (user === null || user === void 0 ? void 0 : user.id)) {\n                setIsLoading(true);\n                setError(null);\n                (0,_family_members_api__WEBPACK_IMPORTED_MODULE_3__.getFamilyMembers)(user.id).then({\n                    \"FamilyMembersProvider.useEffect\": (members)=>{\n                        // The API now always returns exactly 5 slots\n                        setFamilyMembers(members);\n                    }\n                }[\"FamilyMembersProvider.useEffect\"]).catch({\n                    \"FamilyMembersProvider.useEffect\": (err)=>{\n                        console.error(\"Error loading family members:\", err);\n                        setError(new Error(\"Failed to load family members\"));\n                        // On error, fall back to empty members\n                        const emptyMembers = Array(5).fill(null).map({\n                            \"FamilyMembersProvider.useEffect.emptyMembers\": (_, index)=>({\n                                    id: index + 1,\n                                    firstName: \"\",\n                                    lastName: \"\",\n                                    healthCard: \"\",\n                                    birthDate: undefined,\n                                    editing: false\n                                })\n                        }[\"FamilyMembersProvider.useEffect.emptyMembers\"]);\n                        setFamilyMembers(emptyMembers);\n                    }\n                }[\"FamilyMembersProvider.useEffect\"]).finally({\n                    \"FamilyMembersProvider.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"FamilyMembersProvider.useEffect\"]);\n            } else if (status === \"unauthenticated\") {\n                // Not authenticated, use initial data\n                setFamilyMembers(initialFamilyMembers);\n                setIsLoading(false);\n            }\n        }\n    }[\"FamilyMembersProvider.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id,\n        status\n    ]);\n    // Delete a family member\n    const deleteMember = (id)=>{\n        const member = familyMembers.find((m)=>m.id === id);\n        if (!member || !member.supabaseId) {\n            // If there's no Supabase ID, just reset the member data in the UI and close the form\n            setFamilyMembers((prev)=>prev.map((m)=>m.id === id ? {\n                        ...m,\n                        firstName: \"\",\n                        lastName: \"\",\n                        healthCard: \"\",\n                        birthDate: undefined,\n                        editing: false,\n                        tempFirstName: undefined,\n                        tempLastName: undefined,\n                        tempHealthCard: undefined,\n                        tempBirthDate: undefined\n                    } : m));\n            return;\n        }\n        // If we have a Supabase ID, delete from database\n        if (user === null || user === void 0 ? void 0 : user.id) {\n            (0,_family_members_api__WEBPACK_IMPORTED_MODULE_3__.deleteFamilyMember)(member.supabaseId).then((result)=>{\n                if (result.success) {\n                    // Reset the member data in the UI but keep the slot and close the form\n                    setFamilyMembers((prev)=>prev.map((m)=>m.id === id ? {\n                                ...m,\n                                firstName: \"\",\n                                lastName: \"\",\n                                healthCard: \"\",\n                                birthDate: undefined,\n                                supabaseId: undefined,\n                                editing: false,\n                                tempFirstName: undefined,\n                                tempLastName: undefined,\n                                tempHealthCard: undefined,\n                                tempBirthDate: undefined\n                            } : m));\n                }\n            }).catch((err)=>console.error(\"Error deleting member:\", err));\n        }\n    };\n    // Provide the actual implementation\n    const contextValue = {\n        familyMembers,\n        setFamilyMembers,\n        updateFamilyMember,\n        updateTempFamilyMember,\n        toggleEditing,\n        saveMemberChanges,\n        deleteMember,\n        isLoading,\n        error\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FamilyMembersContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/lib/FamilyMembersContext.tsx\",\n        lineNumber: 339,\n        columnNumber: 5\n    }, this);\n}\n_s(FamilyMembersProvider, \"cqFbt2OhW/+sb0KCE2CXUiEyAQs=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = FamilyMembersProvider;\nfunction useFamilyMembers() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(FamilyMembersContext);\n    return context;\n}\n_s1(useFamilyMembers, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"FamilyMembersProvider\");\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvRmFtaWx5TWVtYmVyc0NvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUV3RjtBQUNqRDtBQUM0RTtBQThCbkgsc0JBQXNCO0FBQ3RCLE1BQU1TLHVCQUF1QztJQUMzQztRQUNFQyxJQUFJO1FBQ0pDLFdBQVc7UUFDWEMsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLFdBQVcsSUFBSUMsS0FBSyxNQUFNLEdBQUc7UUFDN0JDLFNBQVM7SUFDWDtJQUNBO1FBQ0VOLElBQUk7UUFDSkMsV0FBVztRQUNYQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsV0FBVyxJQUFJQyxLQUFLLE1BQU0sR0FBRztRQUM3QkMsU0FBUztJQUNYO0lBQ0E7UUFDRU4sSUFBSTtRQUNKQyxXQUFXO1FBQ1hDLFVBQVU7UUFDVkMsWUFBWTtRQUNaQyxXQUFXRztRQUNYRCxTQUFTO0lBQ1g7SUFDQTtRQUNFTixJQUFJO1FBQ0pDLFdBQVc7UUFDWEMsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLFdBQVdHO1FBQ1hELFNBQVM7SUFDWDtJQUNBO1FBQ0VOLElBQUk7UUFDSkMsV0FBVztRQUNYQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsV0FBV0c7UUFDWEQsU0FBUztJQUNYO0NBQ0Q7QUFFRCwrQ0FBK0M7QUFDL0MsTUFBTUUsc0JBQWdEO0lBQ3BEQyxlQUFlVjtJQUNmLGdFQUFnRTtJQUNoRVcsa0JBQWtCLEtBQU87SUFDekIsZ0VBQWdFO0lBQ2hFQyxvQkFBb0IsS0FBTztJQUMzQixnRUFBZ0U7SUFDaEVDLHdCQUF3QixLQUFPO0lBQy9CLGdFQUFnRTtJQUNoRUMsZUFBZSxLQUFPO0lBQ3RCLGdFQUFnRTtJQUNoRUMsbUJBQW1CLElBQU07SUFDekIsZ0VBQWdFO0lBQ2hFQyxjQUFjLEtBQU87SUFDckJDLFdBQVc7SUFDWEMsT0FBTztBQUNUO0FBRUEsNENBQTRDO0FBQzVDLE1BQU1DLHFDQUF1QjNCLG9EQUFhQSxDQUEyQmlCO0FBRTlELFNBQVNXLHNCQUFzQixLQUFxQztRQUFyQyxFQUFFQyxRQUFRLEVBQTJCLEdBQXJDOztJQUNwQyxNQUFNLEVBQUVDLElBQUksRUFBRUMsTUFBTSxFQUFFLEdBQUczQixxREFBT0E7SUFDaEMsTUFBTSxDQUFDYyxlQUFlQyxpQkFBaUIsR0FBR2pCLCtDQUFRQSxDQUFpQk07SUFDbkUsTUFBTSxDQUFDaUIsV0FBV08sYUFBYSxHQUFHOUIsK0NBQVFBLENBQVU7SUFDcEQsTUFBTSxDQUFDd0IsT0FBT08sU0FBUyxHQUFHL0IsK0NBQVFBLENBQWU7SUFFakQsbUNBQW1DO0lBQ25DLE1BQU1rQixxQkFBcUIsQ0FBQ1gsSUFBWXlCO1FBQ3RDZixpQkFBaUJnQixDQUFBQSxPQUNmQSxLQUFLQyxHQUFHLENBQUNDLENBQUFBLFNBQ1BBLE9BQU81QixFQUFFLEtBQUtBLEtBQ1Y7b0JBQUUsR0FBRzRCLE1BQU07b0JBQUUsR0FBR0gsSUFBSTtnQkFBQyxJQUNyQkc7SUFHVjtJQUVBLHVDQUF1QztJQUN2QyxNQUFNaEIseUJBQXlCLENBQUNaLElBQVl5QjtRQUMxQ2YsaUJBQWlCZ0IsQ0FBQUEsT0FDZkEsS0FBS0MsR0FBRyxDQUFDQyxDQUFBQSxTQUNQQSxPQUFPNUIsRUFBRSxLQUFLQSxLQUNWO29CQUNFLEdBQUc0QixNQUFNO29CQUNUQyxlQUFlLG1CQUFtQkosT0FBT0EsS0FBS0ksYUFBYSxHQUFHRCxPQUFPQyxhQUFhO29CQUNsRkMsY0FBYyxrQkFBa0JMLE9BQU9BLEtBQUtLLFlBQVksR0FBR0YsT0FBT0UsWUFBWTtvQkFDOUVDLGdCQUFnQixvQkFBb0JOLE9BQU9BLEtBQUtNLGNBQWMsR0FBR0gsT0FBT0csY0FBYztvQkFDdEZDLGVBQWUsbUJBQW1CUCxPQUFPQSxLQUFLTyxhQUFhLEdBQUdKLE9BQU9JLGFBQWE7Z0JBQ3BGLElBQ0FKO0lBR1Y7SUFFQSxtQ0FBbUM7SUFDbkMsTUFBTWYsZ0JBQWdCLENBQUNiO1FBQ3JCVSxpQkFBaUJnQixDQUFBQSxPQUNmQSxLQUFLQyxHQUFHLENBQUNDLENBQUFBO2dCQUNQLElBQUlBLE9BQU81QixFQUFFLEtBQUtBLElBQUk7b0JBQ3BCLElBQUksQ0FBQzRCLE9BQU90QixPQUFPLEVBQUU7d0JBQ25CLGdEQUFnRDt3QkFDaEQsT0FBTzs0QkFDTCxHQUFHc0IsTUFBTTs0QkFDVHRCLFNBQVM7NEJBQ1R1QixlQUFlRCxPQUFPM0IsU0FBUzs0QkFDL0I2QixjQUFjRixPQUFPMUIsUUFBUTs0QkFDN0I2QixnQkFBZ0JILE9BQU96QixVQUFVOzRCQUNqQzZCLGVBQWVKLE9BQU94QixTQUFTO3dCQUNqQztvQkFDRixPQUFPO3dCQUNMLDJDQUEyQzt3QkFDM0MsT0FBTzs0QkFDTCxHQUFHd0IsTUFBTTs0QkFDVHRCLFNBQVM7NEJBQ1R1QixlQUFldEI7NEJBQ2Z1QixjQUFjdkI7NEJBQ2R3QixnQkFBZ0J4Qjs0QkFDaEJ5QixlQUFlekI7d0JBQ2pCO29CQUNGO2dCQUNGO2dCQUNBLE9BQU9xQjtZQUNUO0lBRUo7SUFFQSxpREFBaUQ7SUFDakQsTUFBTWQsb0JBQW9CLENBQUNkO1FBQ3pCLG1EQUFtRDtRQUNuRCxNQUFNaUMsZUFBZXhCLGNBQWN5QixJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVuQyxFQUFFLEtBQUtBO1FBRXRELElBQUksQ0FBQ2lDLGNBQWMsT0FBTztRQUUxQixNQUFNaEMsWUFBWWdDLGFBQWFKLGFBQWEsS0FBS3RCLFlBQVkwQixhQUFhSixhQUFhLEdBQUdJLGFBQWFoQyxTQUFTO1FBQ2hILE1BQU1DLFdBQVcrQixhQUFhSCxZQUFZLEtBQUt2QixZQUFZMEIsYUFBYUgsWUFBWSxHQUFHRyxhQUFhL0IsUUFBUTtRQUM1RyxNQUFNQyxhQUFhOEIsYUFBYUYsY0FBYyxLQUFLeEIsWUFBWTBCLGFBQWFGLGNBQWMsR0FBR0UsYUFBYTlCLFVBQVU7UUFDcEgsTUFBTUMsWUFBWTZCLGFBQWFELGFBQWEsS0FBS3pCLFlBQVkwQixhQUFhRCxhQUFhLEdBQUdDLGFBQWE3QixTQUFTO1FBRWhILDJCQUEyQjtRQUMzQixNQUFNZ0MsVUFBVSxDQUFDLENBQUNuQyxhQUFhLENBQUMsQ0FBQ0MsWUFBWSxDQUFDLENBQUNDLGNBQWNBLFdBQVdrQyxNQUFNLEtBQUssS0FBSyxDQUFDLENBQUNqQztRQUUxRixtREFBbUQ7UUFDbkQsSUFBSSxDQUFDZ0MsU0FBUyxPQUFPO1FBRXJCMUIsaUJBQWlCZ0IsQ0FBQUE7WUFDZixNQUFNWSxpQkFBaUJaLEtBQUtDLEdBQUcsQ0FBQ0MsQ0FBQUE7Z0JBQzlCLElBQUlBLE9BQU81QixFQUFFLEtBQUtBLE1BQU00QixPQUFPdEIsT0FBTyxFQUFFO29CQUN0QywwQ0FBMEM7b0JBQzFDLE1BQU1pQyxnQkFBZ0I7d0JBQ3BCLEdBQUdYLE1BQU07d0JBQ1R0QixTQUFTO3dCQUNUTCxXQUFXMkIsT0FBT0MsYUFBYSxLQUFLdEIsWUFBWXFCLE9BQU9DLGFBQWEsR0FBR0QsT0FBTzNCLFNBQVM7d0JBQ3ZGQyxVQUFVMEIsT0FBT0UsWUFBWSxLQUFLdkIsWUFBWXFCLE9BQU9FLFlBQVksR0FBR0YsT0FBTzFCLFFBQVE7d0JBQ25GQyxZQUFZeUIsT0FBT0csY0FBYyxLQUFLeEIsWUFBWXFCLE9BQU9HLGNBQWMsR0FBR0gsT0FBT3pCLFVBQVU7d0JBQzNGQyxXQUFXd0IsT0FBT0ksYUFBYSxLQUFLekIsWUFBWXFCLE9BQU9JLGFBQWEsR0FBR0osT0FBT3hCLFNBQVM7d0JBQ3ZGLG9CQUFvQjt3QkFDcEJ5QixlQUFldEI7d0JBQ2Z1QixjQUFjdkI7d0JBQ2R3QixnQkFBZ0J4Qjt3QkFDaEJ5QixlQUFlekI7b0JBQ2pCO29CQUVBLDRDQUE0QztvQkFDNUMsSUFBSWMsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNckIsRUFBRSxLQUFLdUMsQ0FBQUEsY0FBY3RDLFNBQVMsSUFBSXNDLGNBQWNyQyxRQUFRLElBQUlxQyxjQUFjcEMsVUFBVSxJQUFJb0MsY0FBY25DLFNBQVMsR0FBRzt3QkFDMUhQLHFFQUFnQkEsQ0FBQ3dCLEtBQUtyQixFQUFFLEVBQUV1QyxlQUFlWCxPQUFPNUIsRUFBRSxFQUMvQ3dDLElBQUksQ0FBQ0MsQ0FBQUE7NEJBQ0osSUFBSUEsT0FBT0MsT0FBTyxJQUFJRCxPQUFPekMsRUFBRSxFQUFFO2dDQUMvQix5Q0FBeUM7Z0NBQ3pDVSxpQkFBaUJpQyxDQUFBQSxVQUNmQSxRQUFRaEIsR0FBRyxDQUFDUSxDQUFBQSxJQUNWQSxFQUFFbkMsRUFBRSxLQUFLNEIsT0FBTzVCLEVBQUUsR0FBRzs0Q0FBRSxHQUFHbUMsQ0FBQzs0Q0FBRVMsWUFBWUgsT0FBT3pDLEVBQUU7d0NBQUMsSUFBSW1DOzRCQUc3RDt3QkFDRixHQUNDVSxLQUFLLENBQUNDLENBQUFBLE1BQU9DLFFBQVE5QixLQUFLLENBQUMsd0JBQXdCNkI7b0JBQ3hEO29CQUVBLE9BQU9QO2dCQUNUO2dCQUNBLE9BQU9YO1lBQ1Q7WUFFQSx1REFBdUQ7WUFDdkQsSUFBSVUsZUFBZUQsTUFBTSxHQUFHLEdBQUc7Z0JBQzdCLDJEQUEyRDtnQkFDM0QsTUFBTVcsY0FBY1YsZUFBZVgsR0FBRyxDQUFDUSxDQUFBQSxJQUFLQSxFQUFFbkMsRUFBRTtnQkFFaEQsSUFBSyxJQUFJaUQsSUFBSSxHQUFHQSxLQUFLLEdBQUdBLElBQUs7b0JBQzNCLElBQUksQ0FBQ0QsWUFBWUUsUUFBUSxDQUFDRCxJQUFJO3dCQUM1QlgsZUFBZWEsSUFBSSxDQUFDOzRCQUNsQm5ELElBQUlpRDs0QkFDSmhELFdBQVc7NEJBQ1hDLFVBQVU7NEJBQ1ZDLFlBQVk7NEJBQ1pDLFdBQVdHOzRCQUNYRCxTQUFTO3dCQUNYO29CQUNGO2dCQUNGO2dCQUVBLHFDQUFxQztnQkFDckNnQyxlQUFlYyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsRUFBRXJELEVBQUUsR0FBR3NELEVBQUV0RCxFQUFFO1lBQzNDO1lBRUEsT0FBT3NDO1FBQ1Q7UUFFQSxPQUFPLE1BQU0seURBQXlEO0lBQ3hFO0lBRUEsa0RBQWtEO0lBQ2xELCtEQUErRDtJQUMvRDVDLGdEQUFTQTsyQ0FBQztZQUNSLElBQUk0QixXQUFXLFdBQVc7WUFFMUIsSUFBSUEsV0FBVyxvQkFBbUJELGlCQUFBQSwyQkFBQUEsS0FBTXJCLEVBQUUsR0FBRTtnQkFDMUN1QixhQUFhO2dCQUNiQyxTQUFTO2dCQUVUNUIscUVBQWdCQSxDQUFDeUIsS0FBS3JCLEVBQUUsRUFDckJ3QyxJQUFJO3VEQUFDZSxDQUFBQTt3QkFDSiw2Q0FBNkM7d0JBQzdDN0MsaUJBQWlCNkM7b0JBQ25CO3NEQUNDVixLQUFLO3VEQUFDQyxDQUFBQTt3QkFDTEMsUUFBUTlCLEtBQUssQ0FBQyxpQ0FBaUM2Qjt3QkFDL0N0QixTQUFTLElBQUlnQyxNQUFNO3dCQUVuQix1Q0FBdUM7d0JBQ3ZDLE1BQU1DLGVBQStCQyxNQUFNLEdBQ3hDQyxJQUFJLENBQUMsTUFDTGhDLEdBQUc7NEVBQUMsQ0FBQ2lDLEdBQUdDLFFBQVc7b0NBQ2xCN0QsSUFBSTZELFFBQVE7b0NBQ1o1RCxXQUFXO29DQUNYQyxVQUFVO29DQUNWQyxZQUFZO29DQUNaQyxXQUFXRztvQ0FDWEQsU0FBUztnQ0FDWDs7d0JBRUZJLGlCQUFpQitDO29CQUNuQjtzREFDQ0ssT0FBTzt1REFBQzt3QkFDUHZDLGFBQWE7b0JBQ2Y7O1lBQ0osT0FBTyxJQUFJRCxXQUFXLG1CQUFtQjtnQkFDdkMsc0NBQXNDO2dCQUN0Q1osaUJBQWlCWDtnQkFDakJ3QixhQUFhO1lBQ2Y7UUFDRjswQ0FBRztRQUFDRixpQkFBQUEsMkJBQUFBLEtBQU1yQixFQUFFO1FBQUVzQjtLQUFPO0lBRXJCLHlCQUF5QjtJQUN6QixNQUFNUCxlQUFlLENBQUNmO1FBQ3BCLE1BQU00QixTQUFTbkIsY0FBY3lCLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRW5DLEVBQUUsS0FBS0E7UUFFaEQsSUFBSSxDQUFDNEIsVUFBVSxDQUFDQSxPQUFPZ0IsVUFBVSxFQUFFO1lBQ2pDLHFGQUFxRjtZQUNyRmxDLGlCQUFpQmdCLENBQUFBLE9BQVFBLEtBQUtDLEdBQUcsQ0FBQ1EsQ0FBQUEsSUFDaENBLEVBQUVuQyxFQUFFLEtBQUtBLEtBQ0w7d0JBQUUsR0FBR21DLENBQUM7d0JBQUVsQyxXQUFXO3dCQUFJQyxVQUFVO3dCQUFJQyxZQUFZO3dCQUFJQyxXQUFXRzt3QkFBV0QsU0FBUzt3QkFBT3VCLGVBQWV0Qjt3QkFBV3VCLGNBQWN2Qjt3QkFBV3dCLGdCQUFnQnhCO3dCQUFXeUIsZUFBZXpCO29CQUFVLElBQ2xNNEI7WUFFTjtRQUNGO1FBRUEsaURBQWlEO1FBQ2pELElBQUlkLGlCQUFBQSwyQkFBQUEsS0FBTXJCLEVBQUUsRUFBRTtZQUNaRix1RUFBa0JBLENBQUM4QixPQUFPZ0IsVUFBVSxFQUNqQ0osSUFBSSxDQUFDQyxDQUFBQTtnQkFDSixJQUFJQSxPQUFPQyxPQUFPLEVBQUU7b0JBQ2xCLHVFQUF1RTtvQkFDdkVoQyxpQkFBaUJnQixDQUFBQSxPQUFRQSxLQUFLQyxHQUFHLENBQUNRLENBQUFBLElBQ2hDQSxFQUFFbkMsRUFBRSxLQUFLQSxLQUNMO2dDQUFFLEdBQUdtQyxDQUFDO2dDQUFFbEMsV0FBVztnQ0FBSUMsVUFBVTtnQ0FBSUMsWUFBWTtnQ0FBSUMsV0FBV0c7Z0NBQVdxQyxZQUFZckM7Z0NBQVdELFNBQVM7Z0NBQU91QixlQUFldEI7Z0NBQVd1QixjQUFjdkI7Z0NBQVd3QixnQkFBZ0J4QjtnQ0FBV3lCLGVBQWV6Qjs0QkFBVSxJQUN6TjRCO2dCQUVSO1lBQ0YsR0FDQ1UsS0FBSyxDQUFDQyxDQUFBQSxNQUFPQyxRQUFROUIsS0FBSyxDQUFDLDBCQUEwQjZCO1FBQzFEO0lBQ0Y7SUFFQSxvQ0FBb0M7SUFDcEMsTUFBTWlCLGVBQXlDO1FBQzdDdEQ7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7SUFDRjtJQUVBLHFCQUNFLDhEQUFDQyxxQkFBcUI4QyxRQUFRO1FBQUNDLE9BQU9GO2tCQUNuQzNDOzs7Ozs7QUFHUDtHQWxQZ0JEOztRQUNXeEIsaURBQU9BOzs7S0FEbEJ3QjtBQW9QVCxTQUFTK0M7O0lBQ2QsTUFBTUMsVUFBVTNFLGlEQUFVQSxDQUFDMEI7SUFDM0IsT0FBT2lEO0FBQ1Q7SUFIZ0JEIiwic291cmNlcyI6WyIvVXNlcnMvYW1pamV0dGUvc3JjL1NSVlhQL3NyYy9saWIvRmFtaWx5TWVtYmVyc0NvbnRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCBSZWFjdE5vZGUgfSBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gXCIuL0F1dGhDb250ZXh0XCJcbmltcG9ydCB7IGdldEZhbWlseU1lbWJlcnMsIHNhdmVBbGxGYW1pbHlNZW1iZXJzLCBzYXZlRmFtaWx5TWVtYmVyLCBkZWxldGVGYW1pbHlNZW1iZXIgfSBmcm9tIFwiLi9mYW1pbHktbWVtYmVycy9hcGlcIlxuXG5leHBvcnQgaW50ZXJmYWNlIEZhbWlseU1lbWJlciB7XG4gIGlkOiBudW1iZXJcbiAgZmlyc3ROYW1lOiBzdHJpbmdcbiAgbGFzdE5hbWU6IHN0cmluZ1xuICBoZWFsdGhDYXJkOiBzdHJpbmcgIC8vIDQgY2hhcmFjdGVyc1xuICBiaXJ0aERhdGU6IERhdGUgfCB1bmRlZmluZWRcbiAgZWRpdGluZz86IGJvb2xlYW5cbiAgLy8gVGVtcG9yYXJ5IHZhbHVlcyBmb3IgZWRpdGluZ1xuICB0ZW1wRmlyc3ROYW1lPzogc3RyaW5nXG4gIHRlbXBMYXN0TmFtZT86IHN0cmluZ1xuICB0ZW1wSGVhbHRoQ2FyZD86IHN0cmluZ1xuICB0ZW1wQmlydGhEYXRlPzogRGF0ZSB8IHVuZGVmaW5lZFxuICAvLyBTdXBhYmFzZSBJRCBmb3IgZGF0YWJhc2Ugb3BlcmF0aW9uc1xuICBzdXBhYmFzZUlkPzogc3RyaW5nXG59XG5cbmludGVyZmFjZSBGYW1pbHlNZW1iZXJzQ29udGV4dFR5cGUge1xuICBmYW1pbHlNZW1iZXJzOiBGYW1pbHlNZW1iZXJbXVxuICBzZXRGYW1pbHlNZW1iZXJzOiBSZWFjdC5EaXNwYXRjaDxSZWFjdC5TZXRTdGF0ZUFjdGlvbjxGYW1pbHlNZW1iZXJbXT4+XG4gIHVwZGF0ZUZhbWlseU1lbWJlcjogKGlkOiBudW1iZXIsIGRhdGE6IFBhcnRpYWw8RmFtaWx5TWVtYmVyPikgPT4gdm9pZFxuICB1cGRhdGVUZW1wRmFtaWx5TWVtYmVyOiAoaWQ6IG51bWJlciwgZGF0YTogUGFydGlhbDxGYW1pbHlNZW1iZXI+KSA9PiB2b2lkXG4gIHRvZ2dsZUVkaXRpbmc6IChpZDogbnVtYmVyKSA9PiB2b2lkXG4gIHNhdmVNZW1iZXJDaGFuZ2VzOiAoaWQ6IG51bWJlcikgPT4gYm9vbGVhblxuICBkZWxldGVNZW1iZXI6IChpZDogbnVtYmVyKSA9PiB2b2lkXG4gIGlzTG9hZGluZzogYm9vbGVhblxuICBlcnJvcjogRXJyb3IgfCBudWxsXG59XG5cbi8vIENyZWF0ZSBpbml0aWFsIGRhdGFcbmNvbnN0IGluaXRpYWxGYW1pbHlNZW1iZXJzOiBGYW1pbHlNZW1iZXJbXSA9IFtcbiAge1xuICAgIGlkOiAxLFxuICAgIGZpcnN0TmFtZTogXCJGw6lsaXhcIixcbiAgICBsYXN0TmFtZTogXCJUcmVtYmxheVwiLFxuICAgIGhlYWx0aENhcmQ6IFwiVFJFRlwiLFxuICAgIGJpcnRoRGF0ZTogbmV3IERhdGUoMTk4NSwgMywgMTIpLFxuICAgIGVkaXRpbmc6IGZhbHNlXG4gIH0sXG4gIHtcbiAgICBpZDogMixcbiAgICBmaXJzdE5hbWU6IFwiTWFyaWVcIixcbiAgICBsYXN0TmFtZTogXCJUcmVtYmxheVwiLFxuICAgIGhlYWx0aENhcmQ6IFwiVFJFTVwiLFxuICAgIGJpcnRoRGF0ZTogbmV3IERhdGUoMTk4NywgOCwgMjMpLFxuICAgIGVkaXRpbmc6IGZhbHNlXG4gIH0sXG4gIHtcbiAgICBpZDogMyxcbiAgICBmaXJzdE5hbWU6IFwiXCIsXG4gICAgbGFzdE5hbWU6IFwiXCIsXG4gICAgaGVhbHRoQ2FyZDogXCJcIixcbiAgICBiaXJ0aERhdGU6IHVuZGVmaW5lZCxcbiAgICBlZGl0aW5nOiBmYWxzZVxuICB9LFxuICB7XG4gICAgaWQ6IDQsXG4gICAgZmlyc3ROYW1lOiBcIlwiLFxuICAgIGxhc3ROYW1lOiBcIlwiLFxuICAgIGhlYWx0aENhcmQ6IFwiXCIsXG4gICAgYmlydGhEYXRlOiB1bmRlZmluZWQsXG4gICAgZWRpdGluZzogZmFsc2VcbiAgfSxcbiAge1xuICAgIGlkOiA1LFxuICAgIGZpcnN0TmFtZTogXCJcIixcbiAgICBsYXN0TmFtZTogXCJcIixcbiAgICBoZWFsdGhDYXJkOiBcIlwiLFxuICAgIGJpcnRoRGF0ZTogdW5kZWZpbmVkLFxuICAgIGVkaXRpbmc6IGZhbHNlXG4gIH1cbl1cblxuLy8gQ3JlYXRlIGEgZGVmYXVsdCBlbXB0eSBjb250ZXh0IHZhbHVlIGZvciBTU1JcbmNvbnN0IGRlZmF1bHRDb250ZXh0VmFsdWU6IEZhbWlseU1lbWJlcnNDb250ZXh0VHlwZSA9IHtcbiAgZmFtaWx5TWVtYmVyczogaW5pdGlhbEZhbWlseU1lbWJlcnMsXG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZW1wdHktZnVuY3Rpb25cbiAgc2V0RmFtaWx5TWVtYmVyczogKCkgPT4ge30sXG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZW1wdHktZnVuY3Rpb25cbiAgdXBkYXRlRmFtaWx5TWVtYmVyOiAoKSA9PiB7fSxcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby1lbXB0eS1mdW5jdGlvblxuICB1cGRhdGVUZW1wRmFtaWx5TWVtYmVyOiAoKSA9PiB7fSxcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby1lbXB0eS1mdW5jdGlvblxuICB0b2dnbGVFZGl0aW5nOiAoKSA9PiB7fSxcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby1lbXB0eS1mdW5jdGlvblxuICBzYXZlTWVtYmVyQ2hhbmdlczogKCkgPT4gZmFsc2UsXG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZW1wdHktZnVuY3Rpb25cbiAgZGVsZXRlTWVtYmVyOiAoKSA9PiB7fSxcbiAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgZXJyb3I6IG51bGxcbn1cblxuLy8gQ3JlYXRlIHRoZSBjb250ZXh0IHdpdGggdGhlIGRlZmF1bHQgdmFsdWVcbmNvbnN0IEZhbWlseU1lbWJlcnNDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxGYW1pbHlNZW1iZXJzQ29udGV4dFR5cGU+KGRlZmF1bHRDb250ZXh0VmFsdWUpXG5cbmV4cG9ydCBmdW5jdGlvbiBGYW1pbHlNZW1iZXJzUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdE5vZGUgfSkge1xuICBjb25zdCB7IHVzZXIsIHN0YXR1cyB9ID0gdXNlQXV0aCgpXG4gIGNvbnN0IFtmYW1pbHlNZW1iZXJzLCBzZXRGYW1pbHlNZW1iZXJzXSA9IHVzZVN0YXRlPEZhbWlseU1lbWJlcltdPihpbml0aWFsRmFtaWx5TWVtYmVycylcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlPGJvb2xlYW4+KHRydWUpXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8RXJyb3IgfCBudWxsPihudWxsKVxuXG4gIC8vIFVwZGF0ZSBhY3R1YWwgZmFtaWx5IG1lbWJlciBkYXRhXG4gIGNvbnN0IHVwZGF0ZUZhbWlseU1lbWJlciA9IChpZDogbnVtYmVyLCBkYXRhOiBQYXJ0aWFsPEZhbWlseU1lbWJlcj4pID0+IHtcbiAgICBzZXRGYW1pbHlNZW1iZXJzKHByZXYgPT5cbiAgICAgIHByZXYubWFwKG1lbWJlciA9PlxuICAgICAgICBtZW1iZXIuaWQgPT09IGlkXG4gICAgICAgICAgPyB7IC4uLm1lbWJlciwgLi4uZGF0YSB9XG4gICAgICAgICAgOiBtZW1iZXJcbiAgICAgIClcbiAgICApXG4gIH1cblxuICAvLyBVcGRhdGUgdGVtcG9yYXJ5IGRhdGEgZHVyaW5nIGVkaXRpbmdcbiAgY29uc3QgdXBkYXRlVGVtcEZhbWlseU1lbWJlciA9IChpZDogbnVtYmVyLCBkYXRhOiBQYXJ0aWFsPEZhbWlseU1lbWJlcj4pID0+IHtcbiAgICBzZXRGYW1pbHlNZW1iZXJzKHByZXYgPT5cbiAgICAgIHByZXYubWFwKG1lbWJlciA9PlxuICAgICAgICBtZW1iZXIuaWQgPT09IGlkXG4gICAgICAgICAgPyB7XG4gICAgICAgICAgICAgIC4uLm1lbWJlcixcbiAgICAgICAgICAgICAgdGVtcEZpcnN0TmFtZTogJ3RlbXBGaXJzdE5hbWUnIGluIGRhdGEgPyBkYXRhLnRlbXBGaXJzdE5hbWUgOiBtZW1iZXIudGVtcEZpcnN0TmFtZSxcbiAgICAgICAgICAgICAgdGVtcExhc3ROYW1lOiAndGVtcExhc3ROYW1lJyBpbiBkYXRhID8gZGF0YS50ZW1wTGFzdE5hbWUgOiBtZW1iZXIudGVtcExhc3ROYW1lLFxuICAgICAgICAgICAgICB0ZW1wSGVhbHRoQ2FyZDogJ3RlbXBIZWFsdGhDYXJkJyBpbiBkYXRhID8gZGF0YS50ZW1wSGVhbHRoQ2FyZCA6IG1lbWJlci50ZW1wSGVhbHRoQ2FyZCxcbiAgICAgICAgICAgICAgdGVtcEJpcnRoRGF0ZTogJ3RlbXBCaXJ0aERhdGUnIGluIGRhdGEgPyBkYXRhLnRlbXBCaXJ0aERhdGUgOiBtZW1iZXIudGVtcEJpcnRoRGF0ZVxuICAgICAgICAgICAgfVxuICAgICAgICAgIDogbWVtYmVyXG4gICAgICApXG4gICAgKVxuICB9XG5cbiAgLy8gVG9nZ2xlIGVkaXRpbmcgbW9kZSBmb3IgYSBtZW1iZXJcbiAgY29uc3QgdG9nZ2xlRWRpdGluZyA9IChpZDogbnVtYmVyKSA9PiB7XG4gICAgc2V0RmFtaWx5TWVtYmVycyhwcmV2ID0+XG4gICAgICBwcmV2Lm1hcChtZW1iZXIgPT4ge1xuICAgICAgICBpZiAobWVtYmVyLmlkID09PSBpZCkge1xuICAgICAgICAgIGlmICghbWVtYmVyLmVkaXRpbmcpIHtcbiAgICAgICAgICAgIC8vIFdoZW4gc3RhcnRpbmcgdG8gZWRpdCwgaW5pdGlhbGl6ZSB0ZW1wIHZhbHVlc1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgLi4ubWVtYmVyLFxuICAgICAgICAgICAgICBlZGl0aW5nOiB0cnVlLFxuICAgICAgICAgICAgICB0ZW1wRmlyc3ROYW1lOiBtZW1iZXIuZmlyc3ROYW1lLFxuICAgICAgICAgICAgICB0ZW1wTGFzdE5hbWU6IG1lbWJlci5sYXN0TmFtZSxcbiAgICAgICAgICAgICAgdGVtcEhlYWx0aENhcmQ6IG1lbWJlci5oZWFsdGhDYXJkLFxuICAgICAgICAgICAgICB0ZW1wQmlydGhEYXRlOiBtZW1iZXIuYmlydGhEYXRlXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIFdoZW4gY2FuY2VsaW5nIGVkaXQsIGRpc2NhcmQgdGVtcCB2YWx1ZXNcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgIC4uLm1lbWJlcixcbiAgICAgICAgICAgICAgZWRpdGluZzogZmFsc2UsXG4gICAgICAgICAgICAgIHRlbXBGaXJzdE5hbWU6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgICAgdGVtcExhc3ROYW1lOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgIHRlbXBIZWFsdGhDYXJkOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgIHRlbXBCaXJ0aERhdGU6IHVuZGVmaW5lZFxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbWVtYmVyXG4gICAgICB9KVxuICAgIClcbiAgfVxuXG4gIC8vIFNhdmUgY2hhbmdlcyBmcm9tIHRlbXAgdmFsdWVzIHRvIGFjdHVhbCB2YWx1ZXNcbiAgY29uc3Qgc2F2ZU1lbWJlckNoYW5nZXMgPSAoaWQ6IG51bWJlcik6IGJvb2xlYW4gPT4ge1xuICAgIC8vIFZhbGlkYXRpb24gLSBjaGVjayBpZiByZXF1aXJlZCBmaWVsZHMgYXJlIGZpbGxlZFxuICAgIGNvbnN0IG1lbWJlclRvU2F2ZSA9IGZhbWlseU1lbWJlcnMuZmluZChtID0+IG0uaWQgPT09IGlkKTtcblxuICAgIGlmICghbWVtYmVyVG9TYXZlKSByZXR1cm4gZmFsc2U7XG5cbiAgICBjb25zdCBmaXJzdE5hbWUgPSBtZW1iZXJUb1NhdmUudGVtcEZpcnN0TmFtZSAhPT0gdW5kZWZpbmVkID8gbWVtYmVyVG9TYXZlLnRlbXBGaXJzdE5hbWUgOiBtZW1iZXJUb1NhdmUuZmlyc3ROYW1lO1xuICAgIGNvbnN0IGxhc3ROYW1lID0gbWVtYmVyVG9TYXZlLnRlbXBMYXN0TmFtZSAhPT0gdW5kZWZpbmVkID8gbWVtYmVyVG9TYXZlLnRlbXBMYXN0TmFtZSA6IG1lbWJlclRvU2F2ZS5sYXN0TmFtZTtcbiAgICBjb25zdCBoZWFsdGhDYXJkID0gbWVtYmVyVG9TYXZlLnRlbXBIZWFsdGhDYXJkICE9PSB1bmRlZmluZWQgPyBtZW1iZXJUb1NhdmUudGVtcEhlYWx0aENhcmQgOiBtZW1iZXJUb1NhdmUuaGVhbHRoQ2FyZDtcbiAgICBjb25zdCBiaXJ0aERhdGUgPSBtZW1iZXJUb1NhdmUudGVtcEJpcnRoRGF0ZSAhPT0gdW5kZWZpbmVkID8gbWVtYmVyVG9TYXZlLnRlbXBCaXJ0aERhdGUgOiBtZW1iZXJUb1NhdmUuYmlydGhEYXRlO1xuXG4gICAgLy8gVmFsaWRhdGUgcmVxdWlyZWQgZmllbGRzXG4gICAgY29uc3QgaXNWYWxpZCA9ICEhZmlyc3ROYW1lICYmICEhbGFzdE5hbWUgJiYgISFoZWFsdGhDYXJkICYmIGhlYWx0aENhcmQubGVuZ3RoID09PSA0ICYmICEhYmlydGhEYXRlO1xuXG4gICAgLy8gSWYgdmFsaWRhdGlvbiBmYWlscywgcmV0dXJuIGZhbHNlIGFuZCBkb24ndCBzYXZlXG4gICAgaWYgKCFpc1ZhbGlkKSByZXR1cm4gZmFsc2U7XG5cbiAgICBzZXRGYW1pbHlNZW1iZXJzKHByZXYgPT4ge1xuICAgICAgY29uc3QgdXBkYXRlZE1lbWJlcnMgPSBwcmV2Lm1hcChtZW1iZXIgPT4ge1xuICAgICAgICBpZiAobWVtYmVyLmlkID09PSBpZCAmJiBtZW1iZXIuZWRpdGluZykge1xuICAgICAgICAgIC8vIEFwcGx5IHRlbXBvcmFyeSB2YWx1ZXMgdG8gYWN0dWFsIHZhbHVlc1xuICAgICAgICAgIGNvbnN0IHVwZGF0ZWRNZW1iZXIgPSB7XG4gICAgICAgICAgICAuLi5tZW1iZXIsXG4gICAgICAgICAgICBlZGl0aW5nOiBmYWxzZSxcbiAgICAgICAgICAgIGZpcnN0TmFtZTogbWVtYmVyLnRlbXBGaXJzdE5hbWUgIT09IHVuZGVmaW5lZCA/IG1lbWJlci50ZW1wRmlyc3ROYW1lIDogbWVtYmVyLmZpcnN0TmFtZSxcbiAgICAgICAgICAgIGxhc3ROYW1lOiBtZW1iZXIudGVtcExhc3ROYW1lICE9PSB1bmRlZmluZWQgPyBtZW1iZXIudGVtcExhc3ROYW1lIDogbWVtYmVyLmxhc3ROYW1lLFxuICAgICAgICAgICAgaGVhbHRoQ2FyZDogbWVtYmVyLnRlbXBIZWFsdGhDYXJkICE9PSB1bmRlZmluZWQgPyBtZW1iZXIudGVtcEhlYWx0aENhcmQgOiBtZW1iZXIuaGVhbHRoQ2FyZCxcbiAgICAgICAgICAgIGJpcnRoRGF0ZTogbWVtYmVyLnRlbXBCaXJ0aERhdGUgIT09IHVuZGVmaW5lZCA/IG1lbWJlci50ZW1wQmlydGhEYXRlIDogbWVtYmVyLmJpcnRoRGF0ZSxcbiAgICAgICAgICAgIC8vIENsZWFyIHRlbXAgdmFsdWVzXG4gICAgICAgICAgICB0ZW1wRmlyc3ROYW1lOiB1bmRlZmluZWQsXG4gICAgICAgICAgICB0ZW1wTGFzdE5hbWU6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIHRlbXBIZWFsdGhDYXJkOiB1bmRlZmluZWQsXG4gICAgICAgICAgICB0ZW1wQmlydGhEYXRlOiB1bmRlZmluZWRcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBTYXZlIHRvIFN1cGFiYXNlIGlmIHVzZXIgaXMgYXV0aGVudGljYXRlZFxuICAgICAgICAgIGlmICh1c2VyPy5pZCAmJiAodXBkYXRlZE1lbWJlci5maXJzdE5hbWUgfHwgdXBkYXRlZE1lbWJlci5sYXN0TmFtZSB8fCB1cGRhdGVkTWVtYmVyLmhlYWx0aENhcmQgfHwgdXBkYXRlZE1lbWJlci5iaXJ0aERhdGUpKSB7XG4gICAgICAgICAgICBzYXZlRmFtaWx5TWVtYmVyKHVzZXIuaWQsIHVwZGF0ZWRNZW1iZXIsIG1lbWJlci5pZClcbiAgICAgICAgICAgICAgLnRoZW4ocmVzdWx0ID0+IHtcbiAgICAgICAgICAgICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MgJiYgcmVzdWx0LmlkKSB7XG4gICAgICAgICAgICAgICAgICAvLyBVcGRhdGUgdGhlIG1lbWJlciB3aXRoIHRoZSBTdXBhYmFzZSBJRFxuICAgICAgICAgICAgICAgICAgc2V0RmFtaWx5TWVtYmVycyhjdXJyZW50ID0+XG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnQubWFwKG0gPT5cbiAgICAgICAgICAgICAgICAgICAgICBtLmlkID09PSBtZW1iZXIuaWQgPyB7IC4uLm0sIHN1cGFiYXNlSWQ6IHJlc3VsdC5pZCB9IDogbVxuICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAuY2F0Y2goZXJyID0+IGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzYXZpbmcgbWVtYmVyOlwiLCBlcnIpKVxuICAgICAgICAgIH1cblxuICAgICAgICAgIHJldHVybiB1cGRhdGVkTWVtYmVyXG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG1lbWJlclxuICAgICAgfSlcblxuICAgICAgLy8gRW5zdXJlIHdlIGFsd2F5cyBoYXZlIGV4YWN0bHkgNSBtZW1iZXJzIGFmdGVyIHNhdmluZ1xuICAgICAgaWYgKHVwZGF0ZWRNZW1iZXJzLmxlbmd0aCA8IDUpIHtcbiAgICAgICAgLy8gSWYgd2Ugc29tZWhvdyBoYXZlIGZld2VyIHRoYW4gNSBtZW1iZXJzLCBhZGQgZW1wdHkgc2xvdHNcbiAgICAgICAgY29uc3QgZXhpc3RpbmdJZHMgPSB1cGRhdGVkTWVtYmVycy5tYXAobSA9PiBtLmlkKTtcblxuICAgICAgICBmb3IgKGxldCBpID0gMTsgaSA8PSA1OyBpKyspIHtcbiAgICAgICAgICBpZiAoIWV4aXN0aW5nSWRzLmluY2x1ZGVzKGkpKSB7XG4gICAgICAgICAgICB1cGRhdGVkTWVtYmVycy5wdXNoKHtcbiAgICAgICAgICAgICAgaWQ6IGksXG4gICAgICAgICAgICAgIGZpcnN0TmFtZTogXCJcIixcbiAgICAgICAgICAgICAgbGFzdE5hbWU6IFwiXCIsXG4gICAgICAgICAgICAgIGhlYWx0aENhcmQ6IFwiXCIsXG4gICAgICAgICAgICAgIGJpcnRoRGF0ZTogdW5kZWZpbmVkLFxuICAgICAgICAgICAgICBlZGl0aW5nOiBmYWxzZVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8gU29ydCBieSBJRCB0byBlbnN1cmUgY29ycmVjdCBvcmRlclxuICAgICAgICB1cGRhdGVkTWVtYmVycy5zb3J0KChhLCBiKSA9PiBhLmlkIC0gYi5pZCk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB1cGRhdGVkTWVtYmVycztcbiAgICB9KTtcblxuICAgIHJldHVybiB0cnVlOyAvLyBSZXR1cm4gdHJ1ZSBpZiB2YWxpZGF0aW9uIHBhc3NlcyBhbmQgc2F2ZSBpcyBpbml0aWF0ZWRcbiAgfVxuXG4gIC8vIEZ1bmN0aW9uIHRvIHNhdmUgYWxsIGZhbWlseSBtZW1iZXJzIHRvIFN1cGFiYXNlXG4gIC8vIExvYWQgZmFtaWx5IG1lbWJlcnMgZnJvbSBTdXBhYmFzZSB3aGVuIHVzZXIgaXMgYXV0aGVudGljYXRlZFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzdGF0dXMgPT09IFwibG9hZGluZ1wiKSByZXR1cm5cblxuICAgIGlmIChzdGF0dXMgPT09IFwiYXV0aGVudGljYXRlZFwiICYmIHVzZXI/LmlkKSB7XG4gICAgICBzZXRJc0xvYWRpbmcodHJ1ZSlcbiAgICAgIHNldEVycm9yKG51bGwpXG5cbiAgICAgIGdldEZhbWlseU1lbWJlcnModXNlci5pZClcbiAgICAgICAgLnRoZW4obWVtYmVycyA9PiB7XG4gICAgICAgICAgLy8gVGhlIEFQSSBub3cgYWx3YXlzIHJldHVybnMgZXhhY3RseSA1IHNsb3RzXG4gICAgICAgICAgc2V0RmFtaWx5TWVtYmVycyhtZW1iZXJzKVxuICAgICAgICB9KVxuICAgICAgICAuY2F0Y2goZXJyID0+IHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgbG9hZGluZyBmYW1pbHkgbWVtYmVyczpcIiwgZXJyKVxuICAgICAgICAgIHNldEVycm9yKG5ldyBFcnJvcihcIkZhaWxlZCB0byBsb2FkIGZhbWlseSBtZW1iZXJzXCIpKVxuXG4gICAgICAgICAgLy8gT24gZXJyb3IsIGZhbGwgYmFjayB0byBlbXB0eSBtZW1iZXJzXG4gICAgICAgICAgY29uc3QgZW1wdHlNZW1iZXJzOiBGYW1pbHlNZW1iZXJbXSA9IEFycmF5KDUpXG4gICAgICAgICAgICAuZmlsbChudWxsKVxuICAgICAgICAgICAgLm1hcCgoXywgaW5kZXgpID0+ICh7XG4gICAgICAgICAgICAgIGlkOiBpbmRleCArIDEsXG4gICAgICAgICAgICAgIGZpcnN0TmFtZTogXCJcIixcbiAgICAgICAgICAgICAgbGFzdE5hbWU6IFwiXCIsXG4gICAgICAgICAgICAgIGhlYWx0aENhcmQ6IFwiXCIsXG4gICAgICAgICAgICAgIGJpcnRoRGF0ZTogdW5kZWZpbmVkLFxuICAgICAgICAgICAgICBlZGl0aW5nOiBmYWxzZVxuICAgICAgICAgICAgfSkpXG5cbiAgICAgICAgICBzZXRGYW1pbHlNZW1iZXJzKGVtcHR5TWVtYmVycylcbiAgICAgICAgfSlcbiAgICAgICAgLmZpbmFsbHkoKCkgPT4ge1xuICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgICAgICAgfSlcbiAgICB9IGVsc2UgaWYgKHN0YXR1cyA9PT0gXCJ1bmF1dGhlbnRpY2F0ZWRcIikge1xuICAgICAgLy8gTm90IGF1dGhlbnRpY2F0ZWQsIHVzZSBpbml0aWFsIGRhdGFcbiAgICAgIHNldEZhbWlseU1lbWJlcnMoaW5pdGlhbEZhbWlseU1lbWJlcnMpXG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9LCBbdXNlcj8uaWQsIHN0YXR1c10pXG5cbiAgLy8gRGVsZXRlIGEgZmFtaWx5IG1lbWJlclxuICBjb25zdCBkZWxldGVNZW1iZXIgPSAoaWQ6IG51bWJlcikgPT4ge1xuICAgIGNvbnN0IG1lbWJlciA9IGZhbWlseU1lbWJlcnMuZmluZChtID0+IG0uaWQgPT09IGlkKTtcblxuICAgIGlmICghbWVtYmVyIHx8ICFtZW1iZXIuc3VwYWJhc2VJZCkge1xuICAgICAgLy8gSWYgdGhlcmUncyBubyBTdXBhYmFzZSBJRCwganVzdCByZXNldCB0aGUgbWVtYmVyIGRhdGEgaW4gdGhlIFVJIGFuZCBjbG9zZSB0aGUgZm9ybVxuICAgICAgc2V0RmFtaWx5TWVtYmVycyhwcmV2ID0+IHByZXYubWFwKG0gPT5cbiAgICAgICAgbS5pZCA9PT0gaWRcbiAgICAgICAgICA/IHsgLi4ubSwgZmlyc3ROYW1lOiBcIlwiLCBsYXN0TmFtZTogXCJcIiwgaGVhbHRoQ2FyZDogXCJcIiwgYmlydGhEYXRlOiB1bmRlZmluZWQsIGVkaXRpbmc6IGZhbHNlLCB0ZW1wRmlyc3ROYW1lOiB1bmRlZmluZWQsIHRlbXBMYXN0TmFtZTogdW5kZWZpbmVkLCB0ZW1wSGVhbHRoQ2FyZDogdW5kZWZpbmVkLCB0ZW1wQmlydGhEYXRlOiB1bmRlZmluZWQgfVxuICAgICAgICAgIDogbVxuICAgICAgKSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gSWYgd2UgaGF2ZSBhIFN1cGFiYXNlIElELCBkZWxldGUgZnJvbSBkYXRhYmFzZVxuICAgIGlmICh1c2VyPy5pZCkge1xuICAgICAgZGVsZXRlRmFtaWx5TWVtYmVyKG1lbWJlci5zdXBhYmFzZUlkKVxuICAgICAgICAudGhlbihyZXN1bHQgPT4ge1xuICAgICAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICAgICAgLy8gUmVzZXQgdGhlIG1lbWJlciBkYXRhIGluIHRoZSBVSSBidXQga2VlcCB0aGUgc2xvdCBhbmQgY2xvc2UgdGhlIGZvcm1cbiAgICAgICAgICAgIHNldEZhbWlseU1lbWJlcnMocHJldiA9PiBwcmV2Lm1hcChtID0+XG4gICAgICAgICAgICAgIG0uaWQgPT09IGlkXG4gICAgICAgICAgICAgICAgPyB7IC4uLm0sIGZpcnN0TmFtZTogXCJcIiwgbGFzdE5hbWU6IFwiXCIsIGhlYWx0aENhcmQ6IFwiXCIsIGJpcnRoRGF0ZTogdW5kZWZpbmVkLCBzdXBhYmFzZUlkOiB1bmRlZmluZWQsIGVkaXRpbmc6IGZhbHNlLCB0ZW1wRmlyc3ROYW1lOiB1bmRlZmluZWQsIHRlbXBMYXN0TmFtZTogdW5kZWZpbmVkLCB0ZW1wSGVhbHRoQ2FyZDogdW5kZWZpbmVkLCB0ZW1wQmlydGhEYXRlOiB1bmRlZmluZWQgfVxuICAgICAgICAgICAgICAgIDogbVxuICAgICAgICAgICAgKSk7XG4gICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgICAuY2F0Y2goZXJyID0+IGNvbnNvbGUuZXJyb3IoXCJFcnJvciBkZWxldGluZyBtZW1iZXI6XCIsIGVycikpO1xuICAgIH1cbiAgfTtcblxuICAvLyBQcm92aWRlIHRoZSBhY3R1YWwgaW1wbGVtZW50YXRpb25cbiAgY29uc3QgY29udGV4dFZhbHVlOiBGYW1pbHlNZW1iZXJzQ29udGV4dFR5cGUgPSB7XG4gICAgZmFtaWx5TWVtYmVycyxcbiAgICBzZXRGYW1pbHlNZW1iZXJzLFxuICAgIHVwZGF0ZUZhbWlseU1lbWJlcixcbiAgICB1cGRhdGVUZW1wRmFtaWx5TWVtYmVyLFxuICAgIHRvZ2dsZUVkaXRpbmcsXG4gICAgc2F2ZU1lbWJlckNoYW5nZXMsXG4gICAgZGVsZXRlTWVtYmVyLFxuICAgIGlzTG9hZGluZyxcbiAgICBlcnJvclxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8RmFtaWx5TWVtYmVyc0NvbnRleHQuUHJvdmlkZXIgdmFsdWU9e2NvbnRleHRWYWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9GYW1pbHlNZW1iZXJzQ29udGV4dC5Qcm92aWRlcj5cbiAgKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlRmFtaWx5TWVtYmVycygpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoRmFtaWx5TWVtYmVyc0NvbnRleHQpXG4gIHJldHVybiBjb250ZXh0XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQXV0aCIsImdldEZhbWlseU1lbWJlcnMiLCJzYXZlRmFtaWx5TWVtYmVyIiwiZGVsZXRlRmFtaWx5TWVtYmVyIiwiaW5pdGlhbEZhbWlseU1lbWJlcnMiLCJpZCIsImZpcnN0TmFtZSIsImxhc3ROYW1lIiwiaGVhbHRoQ2FyZCIsImJpcnRoRGF0ZSIsIkRhdGUiLCJlZGl0aW5nIiwidW5kZWZpbmVkIiwiZGVmYXVsdENvbnRleHRWYWx1ZSIsImZhbWlseU1lbWJlcnMiLCJzZXRGYW1pbHlNZW1iZXJzIiwidXBkYXRlRmFtaWx5TWVtYmVyIiwidXBkYXRlVGVtcEZhbWlseU1lbWJlciIsInRvZ2dsZUVkaXRpbmciLCJzYXZlTWVtYmVyQ2hhbmdlcyIsImRlbGV0ZU1lbWJlciIsImlzTG9hZGluZyIsImVycm9yIiwiRmFtaWx5TWVtYmVyc0NvbnRleHQiLCJGYW1pbHlNZW1iZXJzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInVzZXIiLCJzdGF0dXMiLCJzZXRJc0xvYWRpbmciLCJzZXRFcnJvciIsImRhdGEiLCJwcmV2IiwibWFwIiwibWVtYmVyIiwidGVtcEZpcnN0TmFtZSIsInRlbXBMYXN0TmFtZSIsInRlbXBIZWFsdGhDYXJkIiwidGVtcEJpcnRoRGF0ZSIsIm1lbWJlclRvU2F2ZSIsImZpbmQiLCJtIiwiaXNWYWxpZCIsImxlbmd0aCIsInVwZGF0ZWRNZW1iZXJzIiwidXBkYXRlZE1lbWJlciIsInRoZW4iLCJyZXN1bHQiLCJzdWNjZXNzIiwiY3VycmVudCIsInN1cGFiYXNlSWQiLCJjYXRjaCIsImVyciIsImNvbnNvbGUiLCJleGlzdGluZ0lkcyIsImkiLCJpbmNsdWRlcyIsInB1c2giLCJzb3J0IiwiYSIsImIiLCJtZW1iZXJzIiwiRXJyb3IiLCJlbXB0eU1lbWJlcnMiLCJBcnJheSIsImZpbGwiLCJfIiwiaW5kZXgiLCJmaW5hbGx5IiwiY29udGV4dFZhbHVlIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZUZhbWlseU1lbWJlcnMiLCJjb250ZXh0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/FamilyMembersContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/LanguageContext.tsx":
/*!*************************************!*\
  !*** ./src/lib/LanguageContext.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _translation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./translation-utils */ \"(app-pages-browser)/./src/lib/translation-utils.ts\");\n/* harmony import */ var _user_preferences_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./user-preferences-utils */ \"(app-pages-browser)/./src/lib/user-preferences-utils.ts\");\n/* harmony import */ var _supabase_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/lib/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useLanguage,LanguageProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n// Create context with default values\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    language: \"fr\",\n    translations: {},\n    setLanguage: ()=>{},\n    translate: (key)=>key,\n    isLoading: true\n});\n// Custom hook to use language context\nconst useLanguage = ()=>{\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n};\n_s(useLanguage, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nfunction LanguageProvider(param) {\n    let { children } = param;\n    _s1();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"fr\");\n    const [translations, setTranslations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Rate limiting for language updates\n    const lastUpdateTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const UPDATE_DEBOUNCE_MS = 5000; // 5 seconds between updates\n    // Function to handle language change with rate limiting\n    const handleLanguageChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LanguageProvider.useCallback[handleLanguageChange]\": async (newLanguage)=>{\n            const now = Date.now();\n            if (now - lastUpdateTime.current < UPDATE_DEBOUNCE_MS) {\n                console.log('Language update rate limited, skipping server update');\n                setLanguageState(newLanguage);\n                if (true) {\n                    localStorage.setItem(\"language\", newLanguage);\n                    document.documentElement.lang = newLanguage;\n                }\n                return;\n            }\n            lastUpdateTime.current = now;\n            setIsLoading(true);\n            console.log(\"Language context: changing to \".concat(newLanguage));\n            try {\n                // Update local state immediately for responsive UI\n                setLanguageState(newLanguage);\n                // Update local storage and document language\n                if (true) {\n                    localStorage.setItem(\"language\", newLanguage);\n                    document.documentElement.lang = newLanguage;\n                }\n                // Load translations for the new language\n                try {\n                    const translationData = await (0,_translation_utils__WEBPACK_IMPORTED_MODULE_2__.loadTranslations)(newLanguage);\n                    setTranslations(translationData);\n                } catch (translationError) {\n                    console.error(\"Failed to load \".concat(newLanguage, \" translations:\"), translationError);\n                    // Continue with empty translations rather than failing\n                    setTranslations({});\n                }\n                // Update user metadata in Supabase with error handling\n                if (user) {\n                    try {\n                        const { error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.updateUser({\n                            data: {\n                                language: newLanguage\n                            }\n                        });\n                        if (error) {\n                            console.error('Error updating user metadata with language preference:', error);\n                        // Don't throw here, allow the UI to continue working\n                        } else {\n                            console.log('Language context updated successfully to', newLanguage);\n                        }\n                    } catch (authError) {\n                        console.error('Error updating user metadata with language preference:', authError);\n                    // Don't throw here, allow the UI to continue working\n                    }\n                    // Also save to user preferences with error handling\n                    try {\n                        await (0,_user_preferences_utils__WEBPACK_IMPORTED_MODULE_3__.saveUserPreferences)(user.id, {\n                            language: newLanguage,\n                            theme: \"light\" // Provide default theme to satisfy interface\n                        });\n                    } catch (prefError) {\n                        console.error('Error saving language preference:', prefError);\n                    // Don't throw here, allow the UI to continue working\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error updating language:\", error);\n            // Keep the UI language change even if server update fails\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"LanguageProvider.useCallback[handleLanguageChange]\"], [\n        user\n    ]);\n    // Wrapper function for external use\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LanguageProvider.useCallback[setLanguage]\": (newLanguage)=>{\n            if (newLanguage !== language) {\n                handleLanguageChange(newLanguage);\n            }\n        }\n    }[\"LanguageProvider.useCallback[setLanguage]\"], [\n        language,\n        handleLanguageChange\n    ]);\n    // Initialize language on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            const initializeLanguage = {\n                \"LanguageProvider.useEffect.initializeLanguage\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        let initialLanguage = \"fr\"; // Default\n                        // Check localStorage first\n                        const savedLanguage = localStorage.getItem(\"language\");\n                        if (savedLanguage && (savedLanguage === \"fr\" || savedLanguage === \"en\")) {\n                            initialLanguage = savedLanguage;\n                            setLanguageState(savedLanguage);\n                        } else if (user) {\n                            var _user_user_metadata;\n                            // If user is logged in and no saved language, try to get from user metadata\n                            const userLanguage = (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.language;\n                            if (userLanguage && (userLanguage === \"fr\" || userLanguage === \"en\")) {\n                                initialLanguage = userLanguage;\n                                setLanguageState(userLanguage);\n                                localStorage.setItem(\"language\", userLanguage);\n                            }\n                        }\n                        // Set document language\n                        document.documentElement.lang = initialLanguage;\n                        // Load translations for the initial language\n                        try {\n                            const translationData = await (0,_translation_utils__WEBPACK_IMPORTED_MODULE_2__.loadTranslations)(initialLanguage);\n                            setTranslations(translationData);\n                        } catch (translationError) {\n                            console.error(\"Failed to load \".concat(initialLanguage, \" translations:\"), translationError);\n                            setTranslations({});\n                        }\n                    } catch (error) {\n                        console.error(\"Error initializing language:\", error);\n                        // Use default language if initialization fails\n                        setLanguageState(\"fr\");\n                        document.documentElement.lang = \"fr\";\n                        setTranslations({});\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"LanguageProvider.useEffect.initializeLanguage\"];\n            initializeLanguage();\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        user\n    ]);\n    // Translation function\n    const translate = (key)=>{\n        // Return the translation if it exists, otherwise return the key\n        return translations[key] || key;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            translations,\n            setLanguage,\n            translate,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/lib/LanguageContext.tsx\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, this);\n}\n_s1(LanguageProvider, \"McZokB5cOEGjnOVFlImeqgqGN/8=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = LanguageProvider;\nvar _c;\n$RefreshReg$(_c, \"LanguageProvider\");\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/LanguageContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/family-members/api.ts":
/*!***************************************!*\
  !*** ./src/lib/family-members/api.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteFamilyMember: () => (/* binding */ deleteFamilyMember),\n/* harmony export */   getFamilyMembers: () => (/* binding */ getFamilyMembers),\n/* harmony export */   saveAllFamilyMembers: () => (/* binding */ saveAllFamilyMembers),\n/* harmony export */   saveFamilyMember: () => (/* binding */ saveFamilyMember)\n/* harmony export */ });\n/* harmony import */ var _supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ getFamilyMembers,saveFamilyMember,saveAllFamilyMembers,deleteFamilyMember auto */ \n/**\n * Fetch all family members for a user\n * @param userId The user's ID\n * @returns Promise with array of family members (always 5 slots)\n */ async function getFamilyMembers(userId) {\n    try {\n        const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"family_members\").select(\"*\").eq(\"user_id\", userId).order(\"position\", {\n            ascending: true\n        });\n        if (error) {\n            throw error;\n        }\n        // Convert database records to application format\n        const existingMembers = data.map((item)=>({\n                id: item.position,\n                firstName: item.first_name || \"\",\n                lastName: item.last_name || \"\",\n                healthCard: item.health_card || \"\",\n                birthDate: item.birth_date ? new Date(item.birth_date) : undefined,\n                editing: false,\n                supabaseId: item.id\n            }));\n        // Ensure we always have 5 slots by creating a result array with 5 elements\n        const result = [];\n        // Fill slots 1-5\n        for(let i = 1; i <= 5; i++){\n            // Find if we have an existing member for this position\n            const existingMember = existingMembers.find((m)=>m.id === i);\n            if (existingMember) {\n                // Use the existing member\n                result.push(existingMember);\n            } else {\n                // Create an empty slot\n                result.push({\n                    id: i,\n                    firstName: \"\",\n                    lastName: \"\",\n                    healthCard: \"\",\n                    birthDate: undefined,\n                    editing: false\n                });\n            }\n        }\n        return result;\n    } catch (error) {\n        console.error(\"Error fetching family members:\", error);\n        throw error;\n    }\n}\n/**\n * Save a single family member to Supabase\n * @param userId The user's ID\n * @param member The family member to save\n * @param position The position in the list (1-5)\n * @returns Promise with the result\n */ async function saveFamilyMember(userId, member, position) {\n    try {\n        var _member_birthDate;\n        const memberData = {\n            user_id: userId,\n            first_name: member.firstName,\n            last_name: member.lastName,\n            health_card: member.healthCard,\n            birth_date: ((_member_birthDate = member.birthDate) === null || _member_birthDate === void 0 ? void 0 : _member_birthDate.toISOString()) || null,\n            position\n        };\n        // If we have a Supabase ID, update the record\n        if (member.supabaseId) {\n            const { error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"family_members\").update(memberData).eq(\"id\", member.supabaseId);\n            if (error) throw error;\n            return {\n                success: true,\n                id: member.supabaseId\n            };\n        } else {\n            // Otherwise, insert a new record\n            const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"family_members\").insert(memberData).select(\"id\").single();\n            if (error) throw error;\n            return {\n                success: true,\n                id: data.id\n            };\n        }\n    } catch (error) {\n        console.error(\"Error saving family member:\", error);\n        return {\n            success: false\n        };\n    }\n}\n/**\n * Save all family members for a user\n * @param userId The user's ID\n * @param members Array of family members\n * @returns Promise with the result\n */ async function saveAllFamilyMembers(userId, members) {\n    try {\n        // Process members with data only (non-empty)\n        const membersWithData = members.filter((member)=>member.firstName || member.lastName || member.healthCard || member.birthDate);\n        // Save each member\n        const results = await Promise.all(membersWithData.map((member, index)=>saveFamilyMember(userId, member, index + 1)));\n        // Check if all saves were successful\n        const allSuccess = results.every((result)=>result.success);\n        return {\n            success: allSuccess\n        };\n    } catch (error) {\n        console.error(\"Error saving all family members:\", error);\n        return {\n            success: false\n        };\n    }\n}\n/**\n * Delete a family member\n * @param memberId The family member's Supabase ID\n * @returns Promise with the result\n */ async function deleteFamilyMember(memberId) {\n    try {\n        const { error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"family_members\").delete().eq(\"id\", memberId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error(\"Error deleting family member:\", error);\n        return {\n            success: false\n        };\n    }\n}\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/family-members/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/profile-update-context.tsx":
/*!********************************************!*\
  !*** ./src/lib/profile-update-context.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfileUpdateProvider: () => (/* binding */ ProfileUpdateProvider),\n/* harmony export */   useProfileUpdates: () => (/* binding */ useProfileUpdates)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ProfileUpdateProvider,useProfileUpdates auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ProfileUpdateContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    notifyProfileUpdate: ()=>{},\n    lastUpdatedUserId: null,\n    lastUpdateTimestamp: 0\n});\n/**\n * Provider component for profile update notifications\n */ function ProfileUpdateProvider(param) {\n    let { children } = param;\n    _s();\n    const [lastUpdatedUserId, setLastUpdatedUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lastUpdateTimestamp, setLastUpdateTimestamp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const notifyProfileUpdate = (userId)=>{\n        setLastUpdatedUserId(userId);\n        setLastUpdateTimestamp(Date.now());\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProfileUpdateContext.Provider, {\n        value: {\n            notifyProfileUpdate,\n            lastUpdatedUserId,\n            lastUpdateTimestamp\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/lib/profile-update-context.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfileUpdateProvider, \"/qjNfXttFEe2aEz+lVVNayN0Qdw=\");\n_c = ProfileUpdateProvider;\n/**\n * Hook to access the profile update context\n */ const useProfileUpdates = ()=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ProfileUpdateContext);\n};\n_s1(useProfileUpdates, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"ProfileUpdateProvider\");\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvcHJvZmlsZS11cGRhdGUtY29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU2RTtBQW9CN0UsTUFBTUkscUNBQXVCSCxvREFBYUEsQ0FBMkI7SUFDbkVJLHFCQUFxQixLQUFPO0lBQzVCQyxtQkFBbUI7SUFDbkJDLHFCQUFxQjtBQUN2QjtBQUVBOztDQUVDLEdBQ00sU0FBU0Msc0JBQXNCLEtBQXFDO1FBQXJDLEVBQUVDLFFBQVEsRUFBMkIsR0FBckM7O0lBQ3BDLE1BQU0sQ0FBQ0gsbUJBQW1CSSxxQkFBcUIsR0FBR1AsK0NBQVFBLENBQWdCO0lBQzFFLE1BQU0sQ0FBQ0kscUJBQXFCSSx1QkFBdUIsR0FBR1IsK0NBQVFBLENBQVM7SUFFdkUsTUFBTUUsc0JBQXNCLENBQUNPO1FBQzNCRixxQkFBcUJFO1FBQ3JCRCx1QkFBdUJFLEtBQUtDLEdBQUc7SUFDakM7SUFFQSxxQkFDRSw4REFBQ1YscUJBQXFCVyxRQUFRO1FBQzVCQyxPQUFPO1lBQ0xYO1lBQ0FDO1lBQ0FDO1FBQ0Y7a0JBRUNFOzs7Ozs7QUFHUDtHQXBCZ0JEO0tBQUFBO0FBc0JoQjs7Q0FFQyxHQUNNLE1BQU1TLG9CQUFvQjs7SUFBTWYsT0FBQUEsaURBQVVBLENBQUNFO0FBQW9CLEVBQUM7SUFBMURhIiwic291cmNlcyI6WyIvVXNlcnMvYW1pamV0dGUvc3JjL1NSVlhQL3NyYy9saWIvcHJvZmlsZS11cGRhdGUtY29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCdcblxuaW50ZXJmYWNlIFByb2ZpbGVVcGRhdGVDb250ZXh0VHlwZSB7XG4gIC8qKlxuICAgKiBUcmlnZ2VyIGEgcHJvZmlsZSB1cGRhdGUgbm90aWZpY2F0aW9uXG4gICAqIEBwYXJhbSB1c2VySWQgVGhlIHVzZXIgSUQgd2hvc2UgcHJvZmlsZSB3YXMgdXBkYXRlZFxuICAgKi9cbiAgbm90aWZ5UHJvZmlsZVVwZGF0ZTogKHVzZXJJZDogc3RyaW5nKSA9PiB2b2lkXG4gIFxuICAvKipcbiAgICogVGhlIGxhc3QgdXBkYXRlZCB1c2VyIElEICh1c2VkIGZvciBzdWJzY3JpcHRpb25zKVxuICAgKi9cbiAgbGFzdFVwZGF0ZWRVc2VySWQ6IHN0cmluZyB8IG51bGxcbiAgXG4gIC8qKlxuICAgKiBUaW1lc3RhbXAgb2YgdGhlIGxhc3QgdXBkYXRlICh1c2VkIGZvciBmb3JjaW5nIHJlZnJlc2hlcylcbiAgICovXG4gIGxhc3RVcGRhdGVUaW1lc3RhbXA6IG51bWJlclxufVxuXG5jb25zdCBQcm9maWxlVXBkYXRlQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8UHJvZmlsZVVwZGF0ZUNvbnRleHRUeXBlPih7XG4gIG5vdGlmeVByb2ZpbGVVcGRhdGU6ICgpID0+IHt9LFxuICBsYXN0VXBkYXRlZFVzZXJJZDogbnVsbCxcbiAgbGFzdFVwZGF0ZVRpbWVzdGFtcDogMFxufSlcblxuLyoqXG4gKiBQcm92aWRlciBjb21wb25lbnQgZm9yIHByb2ZpbGUgdXBkYXRlIG5vdGlmaWNhdGlvbnNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIFByb2ZpbGVVcGRhdGVQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IFtsYXN0VXBkYXRlZFVzZXJJZCwgc2V0TGFzdFVwZGF0ZWRVc2VySWRdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2xhc3RVcGRhdGVUaW1lc3RhbXAsIHNldExhc3RVcGRhdGVUaW1lc3RhbXBdID0gdXNlU3RhdGU8bnVtYmVyPigwKVxuXG4gIGNvbnN0IG5vdGlmeVByb2ZpbGVVcGRhdGUgPSAodXNlcklkOiBzdHJpbmcpID0+IHtcbiAgICBzZXRMYXN0VXBkYXRlZFVzZXJJZCh1c2VySWQpXG4gICAgc2V0TGFzdFVwZGF0ZVRpbWVzdGFtcChEYXRlLm5vdygpKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8UHJvZmlsZVVwZGF0ZUNvbnRleHQuUHJvdmlkZXJcbiAgICAgIHZhbHVlPXt7XG4gICAgICAgIG5vdGlmeVByb2ZpbGVVcGRhdGUsXG4gICAgICAgIGxhc3RVcGRhdGVkVXNlcklkLFxuICAgICAgICBsYXN0VXBkYXRlVGltZXN0YW1wXG4gICAgICB9fVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1Byb2ZpbGVVcGRhdGVDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG5cbi8qKlxuICogSG9vayB0byBhY2Nlc3MgdGhlIHByb2ZpbGUgdXBkYXRlIGNvbnRleHRcbiAqL1xuZXhwb3J0IGNvbnN0IHVzZVByb2ZpbGVVcGRhdGVzID0gKCkgPT4gdXNlQ29udGV4dChQcm9maWxlVXBkYXRlQ29udGV4dClcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJQcm9maWxlVXBkYXRlQ29udGV4dCIsIm5vdGlmeVByb2ZpbGVVcGRhdGUiLCJsYXN0VXBkYXRlZFVzZXJJZCIsImxhc3RVcGRhdGVUaW1lc3RhbXAiLCJQcm9maWxlVXBkYXRlUHJvdmlkZXIiLCJjaGlsZHJlbiIsInNldExhc3RVcGRhdGVkVXNlcklkIiwic2V0TGFzdFVwZGF0ZVRpbWVzdGFtcCIsInVzZXJJZCIsIkRhdGUiLCJub3ciLCJQcm92aWRlciIsInZhbHVlIiwidXNlUHJvZmlsZVVwZGF0ZXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/profile-update-context.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/@supabase/ssr/dist/module/index.js\");\n\nconst supabaseUrl = \"https://tfvswgreslsbctjrvdbd.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRmdnN3Z3Jlc2xzYmN0anJ2ZGJkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIzMjEyODUsImV4cCI6MjA1Nzg5NzI4NX0.5bNos8JS_77EUjWOxIGHpgNaYE28qQvO2g4qj0wRWiU\" || 0;\nif (false) {}\n// Cookie configuration optimized for OAuth session persistence\nconst getCookieOptions = ()=>{\n    if (false) {}\n    return {\n        cookies: {\n            getAll () {\n                if (typeof document === 'undefined' || \"object\" === 'undefined') return [];\n                try {\n                    return document.cookie.split(';').map((cookie)=>cookie.trim().split('=')).filter((param)=>{\n                        let [name] = param;\n                        return name;\n                    }).map((param)=>{\n                        let [name, value] = param;\n                        return {\n                            name,\n                            value: decodeURIComponent(value || '')\n                        };\n                    });\n                } catch (error) {\n                    console.warn('Error reading cookies:', error);\n                    return [];\n                }\n            },\n            setAll (cookiesToSet) {\n                if (typeof document === 'undefined' || \"object\" === 'undefined') return;\n                try {\n                    cookiesToSet.forEach((param)=>{\n                        let { name, value, options = {} } = param;\n                        const cookieOptions = {\n                            path: '/',\n                            maxAge: 60 * 60 * 24 * 30,\n                            sameSite: 'lax',\n                            secure: \"development\" === 'production',\n                            ...options\n                        };\n                        const cookieString = [\n                            \"\".concat(name, \"=\").concat(encodeURIComponent(value)),\n                            \"Path=\".concat(cookieOptions.path),\n                            \"Max-Age=\".concat(cookieOptions.maxAge),\n                            \"SameSite=\".concat(cookieOptions.sameSite),\n                            cookieOptions.secure ? 'Secure' : '',\n                            cookieOptions.domain ? \"Domain=\".concat(cookieOptions.domain) : ''\n                        ].filter(Boolean).join('; ');\n                        document.cookie = cookieString;\n                    });\n                } catch (error) {\n                    console.warn('Error setting cookies:', error);\n                }\n            }\n        }\n    };\n};\n// Create browser client with proper cookie handling for OAuth\nconst supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, getCookieOptions());\n// Export a function to create a new client (useful for hooks)\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, getCookieOptions());\n}\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/translation-utils.ts":
/*!**************************************!*\
  !*** ./src/lib/translation-utils.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearTranslationCache: () => (/* binding */ clearTranslationCache),\n/* harmony export */   createTranslationKey: () => (/* binding */ createTranslationKey),\n/* harmony export */   loadTranslations: () => (/* binding */ loadTranslations)\n/* harmony export */ });\n// Utility functions for translation management with lazy loading\n// Cache for translations\nlet translationCache = {\n    en: null,\n    fr: null\n};\n/**\n * Load translation file asynchronously\n * @param lang - The language to load\n * @returns Promise resolving to a dictionary of translations\n */ async function loadTranslations(lang) {\n    // Return from cache if available\n    if (translationCache[lang]) {\n        return translationCache[lang];\n    }\n    try {\n        // Fetch the translation file\n        const response = await fetch(\"/translations/\".concat(lang, \".json\"));\n        if (!response.ok) {\n            throw new Error(\"Failed to load \".concat(lang, \" translations: \").concat(response.statusText));\n        }\n        const translations = await response.json();\n        // Cache the translations\n        translationCache[lang] = translations;\n        return translations;\n    } catch (error) {\n        console.error(\"Error loading \".concat(lang, \" translations:\"), error);\n        // Return empty object in case of error\n        return {};\n    }\n}\n/**\n * Clear the translation cache for testing or forced reloads\n * @param lang - Optional language to clear, or all if not specified\n */ function clearTranslationCache(lang) {\n    if (lang) {\n        translationCache[lang] = null;\n    } else {\n        translationCache = {\n            en: null,\n            fr: null\n        };\n    }\n}\n/**\n * Transform nested key path into flat key\n * @param section - The section of the translation\n * @param key - The key within the section\n * @returns A flat key in the format \"section.key\"\n */ function createTranslationKey(section, key) {\n    return \"\".concat(section, \".\").concat(key);\n}\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/translation-utils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/translations.ts":
/*!*********************************!*\
  !*** ./src/lib/translations.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   translationKeys: () => (/* binding */ translationKeys)\n/* harmony export */ });\n/* harmony import */ var _translation_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./translation-utils */ \"(app-pages-browser)/./src/lib/translation-utils.ts\");\n// This file contains type definitions and keys for translations\n// Define language type\n\n// Create a keys object for consistent key access across the app\nconst translationKeys = {\n    errors: {\n        tryAgainLater: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('errors', 'tryAgainLater'),\n        saveFailed: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('errors', 'saveFailed'),\n        notFound: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('errors', 'notFound'),\n        unauthorized: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('errors', 'unauthorized'),\n        generalError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('errors', 'generalError')\n    },\n    common: {\n        save: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'save'),\n        cancel: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'cancel'),\n        confirm: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'confirm'),\n        delete: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'delete'),\n        loading: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'loading'),\n        search: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'search'),\n        logout: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'logout'),\n        loggingOut: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'loggingOut'),\n        close: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'close'),\n        active: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'active'),\n        inactive: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'inactive'),\n        submit: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'submit'),\n        submitting: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'submitting'),\n        processing: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'processing'),\n        newRequest: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'newRequest'),\n        required: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'required'),\n        yes: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'yes'),\n        no: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'no'),\n        continue: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'continue'),\n        manage: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'manage'),\n        modify: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'modify'),\n        back: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'back'),\n        saved: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'saved'),\n        saving: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'saving'),\n        saveChanges: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'saveChanges'),\n        errorOccurred: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'errorOccurred')\n    },\n    auth: {\n        signIn: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'signIn'),\n        signUp: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'signUp'),\n        email: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'email'),\n        password: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'password'),\n        confirmPassword: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'confirmPassword'),\n        forgotPassword: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'forgotPassword'),\n        resetPassword: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'resetPassword'),\n        enterCredentials: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'enterCredentials'),\n        createAccount: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'createAccount'),\n        alreadyHaveAccount: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'alreadyHaveAccount'),\n        noAccount: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'noAccount'),\n        passwordReset: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'passwordReset'),\n        passwordResetInstructions: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'passwordResetInstructions'),\n        passwordResetSent: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'passwordResetSent'),\n        successfulReset: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'successfulReset'),\n        firstName: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'firstName'),\n        lastName: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'lastName'),\n        errors: {\n            invalidCredentials: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth.errors', 'invalidCredentials'),\n            passwordsDontMatch: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth.errors', 'passwordsDontMatch'),\n            emailInUse: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth.errors', 'emailInUse'),\n            invalidEmail: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth.errors', 'invalidEmail'),\n            passwordTooShort: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth.errors', 'passwordTooShort'),\n            generalError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth.errors', 'generalError')\n        }\n    },\n    nav: {\n        home: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'home'),\n        dashboard: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'dashboard'),\n        appointments: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'appointments'),\n        findAppointment: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'findAppointment'),\n        calendar: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'calendar'),\n        help: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'help'),\n        account: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'account'),\n        mobileNavigation: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'mobileNavigation'),\n        needHelp: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'needHelp')\n    },\n    account: {\n        profile: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'profile'),\n        preferences: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'preferences'),\n        subscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'subscription'),\n        users: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'users'),\n        manageUsers: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageUsers'),\n        yourAccount: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'yourAccount'),\n        individualPlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'individualPlan'),\n        individualPlanMonthly: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'individualPlanMonthly'),\n        individualPlanAnnual: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'individualPlanAnnual'),\n        familyPlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'familyPlan'),\n        familyPlanMonthly: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'familyPlanMonthly'),\n        familyPlanAnnual: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'familyPlanAnnual'),\n        manageInformation: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageInformation'),\n        personalInfoDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'personalInfoDescription'),\n        modifyProfile: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'modifyProfile'),\n        modifySubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'modifySubscription'),\n        subscriptionDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'subscriptionDescription'),\n        manageSubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageSubscription'),\n        appearanceLanguage: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'appearanceLanguage'),\n        appearanceDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'appearanceDescription'),\n        modifyPreferences: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'modifyPreferences'),\n        manageAccountUsers: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageAccountUsers'),\n        manageUsersDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageUsersDescription'),\n        manageProfile: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageProfile'),\n        manageProfileDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageProfileDescription'),\n        manageProfileButton: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageProfileButton'),\n        subscribePlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'subscribePlan'),\n        choosePlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'choosePlan'),\n        editPersonalInfo: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'editPersonalInfo'),\n        firstName: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'firstName'),\n        lastName: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'lastName'),\n        email: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'email'),\n        phone: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'phone'),\n        invalidEmail: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'invalidEmail'),\n        invalidPhone: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'invalidPhone'),\n        emailCannotBeEmpty: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'emailCannotBeEmpty'),\n        firstNameRequired: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'firstNameRequired'),\n        lastNameRequired: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'lastNameRequired'),\n        emailVerificationSent: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'emailVerificationSent')\n    },\n    home: {\n        greeting: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'greeting'),\n        welcome: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'welcome'),\n        findAppointmentTitle: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'findAppointmentTitle'),\n        findAppointmentDesc: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'findAppointmentDesc'),\n        viewRequests: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'viewRequests'),\n        manageAppointmentsDesc: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'manageAppointmentsDesc'),\n        manageUsersDesc: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'manageUsersDesc'),\n        manageProfileTitle: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'manageProfileTitle'),\n        manageProfileDesc: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'manageProfileDesc'),\n        manageProfileButton: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'manageProfileButton')\n    },\n    appointments: {\n        title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'title'),\n        description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'description'),\n        requestsTitle: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'requestsTitle'),\n        all: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'all'),\n        inProgress: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'inProgress'),\n        completed: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'completed'),\n        noRequests: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'noRequests'),\n        noRequestsInProgress: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'noRequestsInProgress'),\n        noRequestsCompleted: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'noRequestsCompleted'),\n        noRequestsCancelled: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'noRequestsCancelled'),\n        postalCode: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'postalCode'),\n        sentOn: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'sentOn'),\n        pending: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'pending'),\n        done: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'done'),\n        cancelAppointment: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'cancelAppointment'),\n        cancelConfirmation: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'cancelConfirmation'),\n        cancelConfirmationText: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'cancelConfirmationText'),\n        noContinue: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'noContinue'),\n        yesCancel: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'yesCancel'),\n        viewAll: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'viewAll')\n    },\n    meta: {\n        title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('meta', 'title'),\n        description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('meta', 'description')\n    },\n    subscription: {\n        modifySubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'modifySubscription'),\n        individualPlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'individualPlan'),\n        monthlyCost: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'monthlyCost'),\n        benefits: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'benefits'),\n        unlimitedAccess: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'unlimitedAccess'),\n        emailNotifications: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'emailNotifications'),\n        familyProfiles: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'familyProfiles'),\n        modifyPlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'modifyPlan'),\n        cancelPlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'cancelPlan'),\n        paymentHistory: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'paymentHistory'),\n        monthlySubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'monthlySubscription'),\n        march: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'march'),\n        february: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'february'),\n        january: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'january'),\n        cost: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'cost'),\n        changePlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'changePlan'),\n        changePlanDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'changePlanDescription'),\n        confirmChange: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'confirmChange'),\n        cancelConfirmation: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'cancelConfirmation'),\n        cancelWarning: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'cancelWarning'),\n        yesCancel: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'yesCancel'),\n        noCancel: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'noCancel'),\n        // Success page translations\n        status: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'status'),\n        verifyingPayment: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'verifyingPayment'),\n        success: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'success'),\n        successMessage: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'successMessage'),\n        details: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'details'),\n        plan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'plan'),\n        billing: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'billing'),\n        amount: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'amount'),\n        currentPeriod: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'currentPeriod'),\n        nextSteps: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'nextSteps'),\n        goToDashboard: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'goToDashboard'),\n        manageAccount: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'manageAccount'),\n        error: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'error'),\n        errorMessage: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'errorMessage'),\n        needHelp: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'needHelp'),\n        returnToPlans: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'returnToPlans'),\n        contactSupport: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'contactSupport'),\n        canceledCheckout: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'canceledCheckout'),\n        processingSubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'processingSubscription'),\n        noSessionId: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'noSessionId'),\n        notLoggedIn: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'notLoggedIn')\n    },\n    preferences: {\n        managePreferences: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'managePreferences'),\n        languageAppearance: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'languageAppearance'),\n        customizeInterface: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'customizeInterface'),\n        preferredLanguage: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'preferredLanguage'),\n        french: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'french'),\n        english: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'english'),\n        languageDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'languageDescription'),\n        appTheme: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'appTheme'),\n        light: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'light'),\n        dark: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'dark'),\n        themeDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'themeDescription'),\n        saveChanges: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'saveChanges'),\n        saving: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'saving'),\n        changesSaved: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'changesSaved'),\n        errorSaving: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'errorSaving')\n    },\n    users: {\n        manageAccountUsers: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'manageAccountUsers'),\n        manageProfile: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'manageProfile'),\n        familyMembers: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'familyMembers'),\n        userProfile: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'userProfile'),\n        familyMembersDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'familyMembersDescription'),\n        userProfileDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'userProfileDescription'),\n        addYourInfo: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'addYourInfo'),\n        addYourInfoPrompt: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'addYourInfoPrompt'),\n        cancel: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'cancel'),\n        firstName: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'firstName'),\n        lastName: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'lastName'),\n        healthCardPrefix: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'healthCardPrefix'),\n        healthCardDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'healthCardDescription'),\n        birthDate: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'birthDate'),\n        save: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'save'),\n        edit: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'edit'),\n        healthCard: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'healthCard'),\n        addMember: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'addMember'),\n        editMemberPrompt: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'editMemberPrompt'),\n        selectDate: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'selectDate'),\n        validationError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'validationError')\n    },\n    help: {\n        needHelp: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'needHelp'),\n        helpDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'helpDescription'),\n        faq: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'faq'),\n        faqDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'faqDescription'),\n        howToBookAppointment: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'howToBookAppointment'),\n        howToBookDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'howToBookDescription'),\n        howToCancelAppointment: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'howToCancelAppointment'),\n        howToCancelDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'howToCancelDescription'),\n        howToChangePlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'howToChangePlan'),\n        howToChangePlanDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'howToChangePlanDescription'),\n        customerSupport: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'customerSupport'),\n        supportDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'supportDescription'),\n        email: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'email'),\n        supportEmail: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'supportEmail'),\n        responseTime: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'responseTime'),\n        contactSupport: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'contactSupport')\n    },\n    landing: {\n        hero: {\n            title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.hero', 'title'),\n            subtitle: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.hero', 'subtitle'),\n            findAppointment: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.hero', 'findAppointment'),\n            learnMore: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.hero', 'learnMore'),\n            imageAlt: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.hero', 'imageAlt')\n        },\n        features: {\n            sameDay: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.features.sameDay', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.features.sameDay', 'description')\n            },\n            nearbyClinic: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.features.nearbyClinic', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.features.nearbyClinic', 'description')\n            },\n            anywhereInQuebec: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.features.anywhereInQuebec', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.features.anywhereInQuebec', 'description')\n            }\n        },\n        howItWorks: {\n            title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.howItWorks', 'title'),\n            customAppointments: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.howItWorks.customAppointments', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.howItWorks.customAppointments', 'description')\n            },\n            easyManagement: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.howItWorks.easyManagement', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.howItWorks.easyManagement', 'description')\n            },\n            imageAlt: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.howItWorks', 'imageAlt')\n        },\n        pricing: {\n            title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'title'),\n            description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'description'),\n            period: {\n                monthly: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.period', 'monthly'),\n                annually: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.period', 'annually')\n            },\n            individual: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.individual', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.individual', 'description'),\n                features: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.individual', 'features'),\n                annualSavings: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.individual', 'annualSavings')\n            },\n            family: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.family', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.family', 'description'),\n                features: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.family', 'features'),\n                annualSavings: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.family', 'annualSavings')\n            },\n            choosePlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'choosePlan'),\n            included: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'included'),\n            manageSubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'manageSubscription'),\n            feature1: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'feature1'),\n            feature2: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'feature2'),\n            feature3: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'feature3'),\n            feature4: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'feature4')\n        },\n        faq: {\n            title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq', 'title'),\n            viewFullFaq: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq', 'viewFullFaq'),\n            questions: [\n                {\n                    question: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.0', 'question'),\n                    answer: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.0', 'answer')\n                },\n                {\n                    question: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.1', 'question'),\n                    answer: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.1', 'answer')\n                },\n                {\n                    question: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.2', 'question'),\n                    answer: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.2', 'answer')\n                },\n                {\n                    question: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.3', 'question'),\n                    answer: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.3', 'answer')\n                },\n                {\n                    question: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.4', 'question'),\n                    answer: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.4', 'answer')\n                }\n            ]\n        },\n        cta: {\n            title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.cta', 'title'),\n            subtitle: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.cta', 'subtitle'),\n            buttonText: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.cta', 'buttonText'),\n            imageAlt: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.cta', 'imageAlt')\n        },\n        navbar: {\n            title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.navbar', 'title'),\n            signIn: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.navbar', 'signIn'),\n            signUp: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.navbar', 'signUp'),\n            service: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.navbar', 'service'),\n            pricing: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.navbar', 'pricing'),\n            faq: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.navbar', 'faq')\n        },\n        footer: {\n            description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.footer', 'description'),\n            contactUs: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.footer', 'contactUs'),\n            privacyPolicy: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.footer', 'privacyPolicy'),\n            termsOfUse: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.footer', 'termsOfUse'),\n            termsOfSale: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.footer', 'termsOfSale'),\n            copyright: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.footer', 'copyright')\n        }\n    },\n    findAppointment: {\n        title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'title'),\n        description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'description'),\n        searchCriteria: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'searchCriteria'),\n        requiredFields: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'requiredFields'),\n        appointmentFor: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'appointmentFor'),\n        selectPerson: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'selectPerson'),\n        managedInUsersSection: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'managedInUsersSection'),\n        healthCard: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'healthCard'),\n        healthCardOf: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'healthCardOf'),\n        lastDigits: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'lastDigits'),\n        enterEightDigits: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'enterEightDigits'),\n        format: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'format'),\n        sequenceNumber: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'sequenceNumber'),\n        sequenceInfo: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'sequenceInfo'),\n        enterTwoDigits: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'enterTwoDigits'),\n        postalCode: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'postalCode'),\n        postalExample: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'postalExample'),\n        invalidPostalFormat: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'invalidPostalFormat'),\n        postalFormatWarning: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'postalFormatWarning'),\n        postalCodeDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'postalCodeDescription'),\n        fromDate: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'fromDate'),\n        selectDate: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'selectDate'),\n        appointmentTime: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'appointmentTime'),\n        chooseTime: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'chooseTime'),\n        morning: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'morning'),\n        afternoon: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'afternoon'),\n        evening: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'evening'),\n        asap: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'asap'),\n        submitRequest: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'submitRequest'),\n        thankYou: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'thankYou'),\n        confirmationMessage: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'confirmationMessage'),\n        viewRequests: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'viewRequests'),\n        // Form validation messages\n        selectDateError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'selectDateError'),\n        selectTimeError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'selectTimeError'),\n        enterPostalError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'enterPostalError'),\n        invalidPostalError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'invalidPostalError'),\n        selectPersonError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'selectPersonError'),\n        healthCardDigitsError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'healthCardDigitsError'),\n        sequenceNumberError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'sequenceNumberError'),\n        noSubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'noSubscription')\n    }\n};\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/translations.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/user-preferences-utils.ts":
/*!*******************************************!*\
  !*** ./src/lib/user-preferences-utils.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUserPreferences: () => (/* binding */ getUserPreferences),\n/* harmony export */   saveUserPreferences: () => (/* binding */ saveUserPreferences)\n/* harmony export */ });\n/* harmony import */ var _supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ saveUserPreferences,getUserPreferences auto */ \n/**\n * Saves the user's preferences to Supabase\n * \n * @param userId The user's ID\n * @param preferences The preferences to save\n * @returns Promise with the result of the operation\n */ async function saveUserPreferences(userId, preferences) {\n    try {\n        // Update preferences directly in the users table\n        const { error: updateError } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('users').update({\n            language: preferences.language,\n            theme: preferences.theme,\n            updated_at: new Date().toISOString()\n        }).eq('id', userId);\n        if (updateError) {\n            throw updateError;\n        }\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error(\"Error saving user preferences:\", error);\n        return {\n            success: false,\n            error: error\n        };\n    }\n}\nasync function getUserPreferences(userId) {\n    try {\n        // Query the users table for preferences\n        const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('users').select('language, theme').eq('id', userId).maybeSingle();\n        if (error) {\n            throw error;\n        }\n        if (!data || !data.language && !data.theme) {\n            return {\n                language: \"fr\",\n                theme: \"light\"\n            };\n        }\n        return {\n            language: data.language || \"fr\",\n            theme: data.theme || \"light\"\n        };\n    } catch (error) {\n        console.error(\"Error getting user preferences:\", error);\n        return null;\n    }\n}\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/user-preferences-utils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatHealthCardNumber: () => (/* binding */ formatHealthCardNumber),\n/* harmony export */   isHealthCardComplete: () => (/* binding */ isHealthCardComplete)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Utility for parsing and validating health card inputs\nfunction formatHealthCardNumber(input) {\n    let maxDigits = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 8;\n    // Remove all non-numeric characters\n    const digits = input.replace(/\\D/g, \"\").substring(0, maxDigits);\n    // Format as xxxx-xxxx if we have more than 4 digits\n    if (digits.length <= 4) {\n        return digits;\n    } else {\n        return \"\".concat(digits.slice(0, 4), \"-\").concat(digits.slice(4));\n    }\n}\n// Check if health card number is complete (has the required number of digits)\nfunction isHealthCardComplete(input) {\n    let requiredDigits = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 8;\n    const digits = input.replace(/\\D/g, \"\");\n    return digits.length === requiredDigits;\n}\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/zaply/hooks/useScrollReveal.ts":
/*!************************************************!*\
  !*** ./src/lib/zaply/hooks/useScrollReveal.ts ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useScrollReveal: () => (/* binding */ useScrollReveal)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useScrollReveal = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const [isIntersecting, setIsIntersecting] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { threshold = 0.1, rootMargin = '0px' } = options;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useScrollReveal.useEffect\": ()=>{\n            const observer = new IntersectionObserver({\n                \"useScrollReveal.useEffect\": (param)=>{\n                    let [entry] = param;\n                    if (entry.isIntersecting) {\n                        setIsIntersecting(true);\n                        // Once element is revealed, stop observing\n                        if (ref.current) {\n                            observer.unobserve(ref.current);\n                        }\n                    }\n                }\n            }[\"useScrollReveal.useEffect\"], {\n                threshold,\n                rootMargin\n            });\n            const currentRef = ref.current;\n            if (currentRef) {\n                observer.observe(currentRef);\n            }\n            return ({\n                \"useScrollReveal.useEffect\": ()=>{\n                    if (currentRef) {\n                        observer.unobserve(currentRef);\n                    }\n                }\n            })[\"useScrollReveal.useEffect\"];\n        }\n    }[\"useScrollReveal.useEffect\"], [\n        threshold,\n        rootMargin\n    ]);\n    return {\n        ref,\n        isIntersecting\n    };\n};\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/zaply/hooks/useScrollReveal.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/zaply/utils.ts":
/*!********************************!*\
  !*** ./src/lib/zaply/utils.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvemFwbHkvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0U7SUFBRztRQUFHQyxPQUFILHVCQUF1Qjs7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiL1VzZXJzL2FtaWpldHRlL3NyYy9TUlZYUC9zcmMvbGliL3phcGx5L3V0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/zaply/utils.ts\n"));

/***/ })

}]);