"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["routes"],{

/***/ "(app-pages-browser)/./src/app/ClientBody.tsx":
/*!********************************!*\
  !*** ./src/app/ClientBody.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientBody)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_FamilyMembersContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/FamilyMembersContext */ \"(app-pages-browser)/./src/lib/FamilyMembersContext.tsx\");\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/LanguageContext */ \"(app-pages-browser)/./src/lib/LanguageContext.tsx\");\n/* harmony import */ var _lib_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/AuthContext */ \"(app-pages-browser)/./src/lib/AuthContext.tsx\");\n/* harmony import */ var _components_translation_loader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/translation-loader */ \"(app-pages-browser)/./src/components/translation-loader.tsx\");\n/* harmony import */ var _lib_profile_update_context__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/profile-update-context */ \"(app-pages-browser)/./src/lib/profile-update-context.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./src/components/theme-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ClientBody(param) {\n    let { children } = param;\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Check if the current page is landing page or auth page\n    const isLandingOrAuthPage = pathname === \"/\" || pathname === \"/landing\" || (pathname === null || pathname === void 0 ? void 0 : pathname.startsWith(\"/auth\"));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientBody.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"ClientBody.useEffect\"], []);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_AuthContext__WEBPACK_IMPORTED_MODULE_5__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_profile_update_context__WEBPACK_IMPORTED_MODULE_7__.ProfileUpdateProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_8__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: isLandingOrAuthPage ? \"light\" : \"system\",\n                enableSystem: !isLandingOrAuthPage,\n                forcedTheme: isLandingOrAuthPage ? \"light\" : undefined,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.LanguageProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_translation_loader__WEBPACK_IMPORTED_MODULE_6__.TranslationLoader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_FamilyMembersContext__WEBPACK_IMPORTED_MODULE_3__.FamilyMembersProvider, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n_s(ClientBody, \"H/yIPE7j4G1daKxqKF7IYIq5KBI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = ClientBody;\nvar _c;\n$RefreshReg$(_c, \"ClientBody\");\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ClientBody.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/auth/layout.tsx":
/*!*********************************!*\
  !*** ./src/app/auth/layout.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_language_toggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/language-toggle */ \"(app-pages-browser)/./src/components/language-toggle.tsx\");\n/* harmony import */ var _components_t__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/t */ \"(app-pages-browser)/./src/components/t.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// Force light mode for auth pages\n\n\n\n\nfunction AuthLayout(param) {\n    let { children } = param;\n    _s();\n    const { t, language } = (0,_components_t__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col justify-center items-center bg-gray-50 light relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_language_toggle__WEBPACK_IMPORTED_MODULE_3__.LanguageToggle, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md bg-white p-8 rounded-lg shadow-sm text-gray-900\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"flex items-center group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 flex items-center justify-center transition-transform duration-300 group-hover:scale-110\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/zaply-images/srvxp_logorevised.svg\",\n                                        alt: \"Logo\",\n                                        width: 32,\n                                        height: 32,\n                                        priority: true,\n                                        suppressHydrationWarning: true,\n                                        className: \"text-brandBlue fill-current\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-4 text-lg sm:text-xl font-bold text-[#212242]\",\n                                    children: \"Sans rendez-vous express\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-4 left-4 flex items-center space-x-4 text-xs text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"\\xa9 Sans rendez-vous express\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: language === 'en' ? \"/politique-de-confidentialite-EN\" : \"/politique-de-confidentialite\",\n                        className: \"hover:text-gray-600 transition-colors\",\n                        children: t('landing.footer.privacyPolicy')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthLayout, \"AzpBYIbE/ASMGCxYS42BdRBkw3s=\", false, function() {\n    return [\n        _components_t__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = AuthLayout;\nvar _c;\n$RefreshReg$(_c, \"AuthLayout\");\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/layout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/auth/sign-in/page.tsx":
/*!***************************************!*\
  !*** ./src/app/auth/sign-in/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_t__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/t */ \"(app-pages-browser)/./src/components/t.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _lib_AuthContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/AuthContext */ \"(app-pages-browser)/./src/lib/AuthContext.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SignInPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const redirectTo = searchParams.get(\"redirectedFrom\") || \"/dashboard\";\n    const { t } = (0,_components_t__WEBPACK_IMPORTED_MODULE_7__.useTranslation)();\n    const { signIn, signInWithGoogle, status } = (0,_lib_AuthContext__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGoogleLoading, setIsGoogleLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Redirect if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignInPage.useEffect\": ()=>{\n            if (status === \"authenticated\") {\n                router.push(redirectTo);\n            }\n        }\n    }[\"SignInPage.useEffect\"], [\n        status,\n        redirectTo,\n        router\n    ]);\n    const handleSignIn = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await signIn(email, password);\n            if (result.success) {\n                // Force redirect to dashboard to ensure consistent behavior\n                window.location.href = '/dashboard';\n            } else {\n                var _result_error;\n                throw new Error(((_result_error = result.error) === null || _result_error === void 0 ? void 0 : _result_error.message) || \"Authentication failed\");\n            }\n        } catch (error) {\n            console.error(\"Error signing in:\", error);\n            setError(t('auth.errors.invalidCredentials'));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        setIsGoogleLoading(true);\n        setError(null);\n        try {\n            await signInWithGoogle();\n        // The redirect will be handled by Supabase OAuth\n        } catch (error) {\n            console.error(\"Error signing in with Google:\", error);\n            setError(t('auth.errors.socialAuthFailed'));\n            setIsGoogleLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold\",\n                    children: t('auth.signIn')\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                variant: \"destructive\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        type: \"button\",\n                        variant: \"outline\",\n                        className: \"w-full flex items-center justify-center gap-2\",\n                        onClick: handleGoogleSignIn,\n                        disabled: isGoogleLoading,\n                        children: [\n                            isGoogleLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-5 h-5 border-2 border-gray-300 border-t-primary rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                height: \"24\",\n                                viewBox: \"0 0 24 24\",\n                                width: \"24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\",\n                                        fill: \"#4285F4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\",\n                                        fill: \"#34A853\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\",\n                                        fill: \"#FBBC05\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\",\n                                        fill: \"#EA4335\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M1 1h22v22H1z\",\n                                        fill: \"none\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: isGoogleLoading ? t('common.loading') : t('auth.continueWithGoogle')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center justify-center mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                className: \"absolute w-full bg-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"relative bg-white px-2 text-xs text-gray-500\",\n                                children: t('auth.orContinueWith')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSignIn,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                htmlFor: \"email\",\n                                children: t('auth.email')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                id: \"email\",\n                                type: \"email\",\n                                placeholder: \"<EMAIL>\",\n                                autoComplete: \"email\",\n                                required: true,\n                                value: email,\n                                onChange: (e)=>setEmail(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"password\",\n                                        children: t('auth.password')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/forgot-password\",\n                                        className: \"text-sm text-primary hover:underline\",\n                                        children: t('auth.forgotPassword')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                id: \"password\",\n                                type: \"password\",\n                                autoComplete: \"current-password\",\n                                required: true,\n                                value: password,\n                                onChange: (e)=>setPassword(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        type: \"submit\",\n                        className: \"w-full py-6\",\n                        disabled: isLoading,\n                        children: isLoading ? t('common.loading') : t('auth.signIn')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\n                        t('auth.noAccount'),\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/auth/sign-up\",\n                            className: \"text-primary hover:underline\",\n                            children: t('auth.createAccount')\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(SignInPage, \"Cp1rjZ57/KNw90TrpSF5Tbbr4bs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _components_t__WEBPACK_IMPORTED_MODULE_7__.useTranslation,\n        _lib_AuthContext__WEBPACK_IMPORTED_MODULE_9__.useAuth\n    ];\n});\n_c = SignInPage;\nvar _c;\n$RefreshReg$(_c, \"SignInPage\");\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/sign-in/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"18b1e56e6e31\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvVXNlcnMvYW1pamV0dGUvc3JjL1NSVlhQL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxOGIxZTU2ZTZlMzFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/test-oauth/page.tsx":
/*!*************************************!*\
  !*** ./src/app/test-oauth/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestOAuthPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/AuthContext */ \"(app-pages-browser)/./src/lib/AuthContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TestOAuthPage() {\n    _s();\n    const { user, session, status, signInWithGoogle, signOut, refresh } = (0,_lib_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cookieInfo, setCookieInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [storageInfo, setStorageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const addLog = (message)=>{\n        const timestamp = new Date().toLocaleTimeString();\n        setLogs((prev)=>[\n                ...prev,\n                \"[\".concat(timestamp, \"] \").concat(message)\n            ]);\n        console.log(message);\n    };\n    const checkCookies = ()=>{\n        if (true) {\n            const cookies = document.cookie.split(';').map((c)=>c.trim()).filter((c)=>c);\n            const authCookies = cookies.filter((c)=>c.includes('supabase') || c.includes('auth') || c.includes('oauth') || c.includes('session'));\n            // Check for specific OAuth flags\n            const hasOAuthSuccess = document.cookie.includes('oauth_success=true');\n            const hasOAuthNewUser = document.cookie.includes('oauth_new_user=true');\n            let cookieDetails = authCookies.join('\\n') || 'No auth-related cookies found';\n            if (hasOAuthSuccess || hasOAuthNewUser) {\n                cookieDetails += \"\\n\\n\\uD83D\\uDD0D OAuth Flags:\\n- oauth_success: \".concat(hasOAuthSuccess, \"\\n- oauth_new_user: \").concat(hasOAuthNewUser);\n            }\n            setCookieInfo(cookieDetails);\n            addLog(\"Found \".concat(authCookies.length, \" auth-related cookies (OAuth success: \").concat(hasOAuthSuccess, \", OAuth new user: \").concat(hasOAuthNewUser, \")\"));\n        }\n    };\n    const checkStorage = ()=>{\n        if (true) {\n            const authKeys = [];\n            for(let i = 0; i < localStorage.length; i++){\n                const key = localStorage.key(i);\n                if (key && (key.includes('auth') || key.includes('supabase') || key.includes('session'))) {\n                    var _localStorage_getItem;\n                    authKeys.push(\"\".concat(key, \": \").concat((_localStorage_getItem = localStorage.getItem(key)) === null || _localStorage_getItem === void 0 ? void 0 : _localStorage_getItem.substring(0, 100), \"...\"));\n                }\n            }\n            setStorageInfo(authKeys.join('\\n') || 'No auth-related localStorage items found');\n            addLog(\"Found \".concat(authKeys.length, \" auth-related localStorage items\"));\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        addLog('Initiating Google OAuth...');\n        try {\n            const result = await signInWithGoogle();\n            if (result.success) {\n                addLog('Google OAuth initiated successfully');\n            } else {\n                var _result_error;\n                addLog(\"Google OAuth failed: \".concat((_result_error = result.error) === null || _result_error === void 0 ? void 0 : _result_error.message));\n            }\n        } catch (error) {\n            addLog(\"Error during Google OAuth: \".concat(error));\n        }\n    };\n    const handleTestNewUserFlow = async ()=>{\n        addLog('Testing new user OAuth flow (with test flag)...');\n        try {\n            // Get the current origin to ensure correct redirect URL\n            const origin = window.location.origin;\n            // Create the callback URL with test flag for new user simulation\n            const callbackUrl = \"\".concat(origin, \"/auth/callback?redirectTo=/dashboard&test_new_user=true\");\n            addLog(\"Callback URL with test flag: \".concat(callbackUrl));\n            const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/@supabase/supabase-js/dist/module/index.js\"));\n            const supabase = createClient('https://tfvswgreslsbctjrvdbd.supabase.co', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.5bNos8JS_77EUjWOxIGHpgNaYE28qQvO2g4qj0wRWiU');\n            const { data, error } = await supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: callbackUrl\n                }\n            });\n            if (error) {\n                addLog(\"Test new user OAuth failed: \".concat(error.message));\n            } else {\n                addLog('Test new user OAuth initiated successfully');\n            }\n        } catch (error) {\n            addLog(\"Error during test new user OAuth: \".concat(error));\n        }\n    };\n    const handleSignOut = async ()=>{\n        addLog('Signing out...');\n        try {\n            await signOut();\n            addLog('Sign out successful');\n        } catch (error) {\n            addLog(\"Error during sign out: \".concat(error));\n        }\n    };\n    const handleRefresh = async ()=>{\n        addLog('Refreshing auth state...');\n        try {\n            await refresh();\n            addLog('Auth state refreshed');\n        } catch (error) {\n            addLog(\"Error refreshing auth state: \".concat(error));\n        }\n    };\n    const simulateStripeNavigation = ()=>{\n        addLog('Simulating Stripe navigation...');\n        if (true) {\n            sessionStorage.setItem('stripe_redirect', 'true');\n            sessionStorage.setItem('external_return', 'true');\n            addLog('Stripe navigation flags set');\n        }\n    };\n    const clearLogs = ()=>{\n        setLogs([]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestOAuthPage.useEffect\": ()=>{\n            addLog('OAuth test page loaded');\n            checkCookies();\n            checkStorage();\n        }\n    }[\"TestOAuthPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestOAuthPage.useEffect\": ()=>{\n            addLog(\"Auth status changed: \".concat(status));\n            if (user) {\n                addLog(\"User: \".concat(user.email));\n            }\n        }\n    }[\"TestOAuthPage.useEffect\"], [\n        status,\n        user\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 max-w-4xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold mb-6\",\n                children: \"\\uD83D\\uDD0D Google OAuth Test\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"\\uD83D\\uDD10 Authentication Status\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Status:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 rounded \".concat(status === 'authenticated' ? 'bg-green-100 text-green-800' : status === 'loading' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'),\n                                                    children: status\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 42\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"User:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                user.email\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Session:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" Active (expires: \",\n                                                new Date(session.expires_at * 1000).toLocaleString(),\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 flex-wrap\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleGoogleSignIn,\n                                                    disabled: status === 'authenticated',\n                                                    children: \"Test Google OAuth\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleTestNewUserFlow,\n                                                    disabled: status === 'authenticated',\n                                                    variant: \"secondary\",\n                                                    children: \"Test New User Flow\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleSignOut,\n                                                    disabled: status !== 'authenticated',\n                                                    variant: \"outline\",\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleRefresh,\n                                                    variant: \"outline\",\n                                                    children: \"Refresh Auth\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"\\uD83D\\uDD04 Session Persistence Test\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: \"Test session persistence after external navigation\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: simulateStripeNavigation,\n                                            variant: \"outline\",\n                                            children: \"Simulate Stripe Navigation\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"This simulates navigating to Stripe and back. After clicking, refresh the page to test session restoration.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"\\uD83C\\uDF6A Cookie Information\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: checkCookies,\n                                            variant: \"outline\",\n                                            children: \"Refresh Cookies\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40\",\n                                            children: cookieInfo || 'No cookie information available'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"\\uD83D\\uDCBE Local Storage\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: checkStorage,\n                                            variant: \"outline\",\n                                            children: \"Refresh Storage\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40\",\n                                            children: storageInfo || 'No storage information available'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"\\uD83D\\uDCDD Test Log\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: clearLogs,\n                                            variant: \"outline\",\n                                            children: \"Clear Log\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-60\",\n                                            children: logs.join('\\n') || 'No logs yet'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(TestOAuthPage, \"4InRCjWQcJRujJi7jr5smGAER5c=\", false, function() {\n    return [\n        _lib_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = TestOAuthPage;\nvar _c;\n$RefreshReg$(_c, \"TestOAuthPage\");\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdGVzdC1vYXV0aC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFMkM7QUFDQTtBQUNJO0FBQ2lEO0FBRWpGLFNBQVNTOztJQUN0QixNQUFNLEVBQUVDLElBQUksRUFBRUMsT0FBTyxFQUFFQyxNQUFNLEVBQUVDLGdCQUFnQixFQUFFQyxPQUFPLEVBQUVDLE9BQU8sRUFBRSxHQUFHYix5REFBT0E7SUFDN0UsTUFBTSxDQUFDYyxNQUFNQyxRQUFRLEdBQUdqQiwrQ0FBUUEsQ0FBVyxFQUFFO0lBQzdDLE1BQU0sQ0FBQ2tCLFlBQVlDLGNBQWMsR0FBR25CLCtDQUFRQSxDQUFTO0lBQ3JELE1BQU0sQ0FBQ29CLGFBQWFDLGVBQWUsR0FBR3JCLCtDQUFRQSxDQUFTO0lBRXZELE1BQU1zQixTQUFTLENBQUNDO1FBQ2QsTUFBTUMsWUFBWSxJQUFJQyxPQUFPQyxrQkFBa0I7UUFDL0NULFFBQVFVLENBQUFBLE9BQVE7bUJBQUlBO2dCQUFPLElBQWlCSixPQUFkQyxXQUFVLE1BQVksT0FBUkQ7YUFBVTtRQUN0REssUUFBUUMsR0FBRyxDQUFDTjtJQUNkO0lBRUEsTUFBTU8sZUFBZTtRQUNuQixJQUFJLElBQTZCLEVBQUU7WUFDakMsTUFBTUMsVUFBVUMsU0FBU0MsTUFBTSxDQUFDQyxLQUFLLENBQUMsS0FBS0MsR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLElBQUlDLE1BQU0sQ0FBQ0YsQ0FBQUEsSUFBS0E7WUFDMUUsTUFBTUcsY0FBY1IsUUFBUU8sTUFBTSxDQUFDRixDQUFBQSxJQUNqQ0EsRUFBRUksUUFBUSxDQUFDLGVBQ1hKLEVBQUVJLFFBQVEsQ0FBQyxXQUNYSixFQUFFSSxRQUFRLENBQUMsWUFDWEosRUFBRUksUUFBUSxDQUFDO1lBR2IsaUNBQWlDO1lBQ2pDLE1BQU1DLGtCQUFrQlQsU0FBU0MsTUFBTSxDQUFDTyxRQUFRLENBQUM7WUFDakQsTUFBTUUsa0JBQWtCVixTQUFTQyxNQUFNLENBQUNPLFFBQVEsQ0FBQztZQUVqRCxJQUFJRyxnQkFBZ0JKLFlBQVlLLElBQUksQ0FBQyxTQUFTO1lBQzlDLElBQUlILG1CQUFtQkMsaUJBQWlCO2dCQUN0Q0MsaUJBQWlCLG1EQUErRUQsT0FBdENELGlCQUFnQix3QkFBc0MsT0FBaEJDO1lBQ2xHO1lBRUF2QixjQUFjd0I7WUFDZHJCLE9BQU8sU0FBb0VtQixPQUEzREYsWUFBWU0sTUFBTSxFQUFDLDBDQUE0RUgsT0FBcENELGlCQUFnQixzQkFBb0MsT0FBaEJDLGlCQUFnQjtRQUNqSTtJQUNGO0lBRUEsTUFBTUksZUFBZTtRQUNuQixJQUFJLElBQTZCLEVBQUU7WUFDakMsTUFBTUMsV0FBVyxFQUFFO1lBQ25CLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJQyxhQUFhSixNQUFNLEVBQUVHLElBQUs7Z0JBQzVDLE1BQU1FLE1BQU1ELGFBQWFDLEdBQUcsQ0FBQ0Y7Z0JBQzdCLElBQUlFLE9BQVFBLENBQUFBLElBQUlWLFFBQVEsQ0FBQyxXQUFXVSxJQUFJVixRQUFRLENBQUMsZUFBZVUsSUFBSVYsUUFBUSxDQUFDLFVBQVMsR0FBSTt3QkFDL0RTO29CQUF6QkYsU0FBU0ksSUFBSSxDQUFDLFVBQUdELEtBQUksTUFBaUQsUUFBN0NELHdCQUFBQSxhQUFhRyxPQUFPLENBQUNGLGtCQUFyQkQsNENBQUFBLHNCQUEyQkksU0FBUyxDQUFDLEdBQUcsTUFBSztnQkFDeEU7WUFDRjtZQUNBaEMsZUFBZTBCLFNBQVNILElBQUksQ0FBQyxTQUFTO1lBQ3RDdEIsT0FBTyxTQUF5QixPQUFoQnlCLFNBQVNGLE1BQU0sRUFBQztRQUNsQztJQUNGO0lBRUEsTUFBTVMscUJBQXFCO1FBQ3pCaEMsT0FBTztRQUNQLElBQUk7WUFDRixNQUFNaUMsU0FBUyxNQUFNMUM7WUFDckIsSUFBSTBDLE9BQU9DLE9BQU8sRUFBRTtnQkFDbEJsQyxPQUFPO1lBQ1QsT0FBTztvQkFDMEJpQztnQkFBL0JqQyxPQUFPLHdCQUE4QyxRQUF0QmlDLGdCQUFBQSxPQUFPRSxLQUFLLGNBQVpGLG9DQUFBQSxjQUFjaEMsT0FBTztZQUN0RDtRQUNGLEVBQUUsT0FBT2tDLE9BQU87WUFDZG5DLE9BQU8sOEJBQW9DLE9BQU5tQztRQUN2QztJQUNGO0lBRUEsTUFBTUMsd0JBQXdCO1FBQzVCcEMsT0FBTztRQUNQLElBQUk7WUFDRix3REFBd0Q7WUFDeEQsTUFBTXFDLFNBQVNDLE9BQU9DLFFBQVEsQ0FBQ0YsTUFBTTtZQUNyQyxpRUFBaUU7WUFDakUsTUFBTUcsY0FBYyxHQUFVLE9BQVBILFFBQU87WUFFOUJyQyxPQUFPLGdDQUE0QyxPQUFad0M7WUFFdkMsTUFBTSxFQUFFQyxZQUFZLEVBQUUsR0FBRyxNQUFNLHNOQUErQjtZQUM5RCxNQUFNQyxXQUFXRCxhQUNmLDRDQUNBO1lBR0YsTUFBTSxFQUFFRSxJQUFJLEVBQUVSLEtBQUssRUFBRSxHQUFHLE1BQU1PLFNBQVNFLElBQUksQ0FBQ0MsZUFBZSxDQUFDO2dCQUMxREMsVUFBVTtnQkFDVkMsU0FBUztvQkFDUEMsWUFBWVI7Z0JBQ2Q7WUFDRjtZQUVBLElBQUlMLE9BQU87Z0JBQ1RuQyxPQUFPLCtCQUE2QyxPQUFkbUMsTUFBTWxDLE9BQU87WUFDckQsT0FBTztnQkFDTEQsT0FBTztZQUNUO1FBQ0YsRUFBRSxPQUFPbUMsT0FBTztZQUNkbkMsT0FBTyxxQ0FBMkMsT0FBTm1DO1FBQzlDO0lBQ0Y7SUFFQSxNQUFNYyxnQkFBZ0I7UUFDcEJqRCxPQUFPO1FBQ1AsSUFBSTtZQUNGLE1BQU1SO1lBQ05RLE9BQU87UUFDVCxFQUFFLE9BQU9tQyxPQUFPO1lBQ2RuQyxPQUFPLDBCQUFnQyxPQUFObUM7UUFDbkM7SUFDRjtJQUVBLE1BQU1lLGdCQUFnQjtRQUNwQmxELE9BQU87UUFDUCxJQUFJO1lBQ0YsTUFBTVA7WUFDTk8sT0FBTztRQUNULEVBQUUsT0FBT21DLE9BQU87WUFDZG5DLE9BQU8sZ0NBQXNDLE9BQU5tQztRQUN6QztJQUNGO0lBRUEsTUFBTWdCLDJCQUEyQjtRQUMvQm5ELE9BQU87UUFDUCxJQUFJLElBQTZCLEVBQUU7WUFDakNvRCxlQUFlQyxPQUFPLENBQUMsbUJBQW1CO1lBQzFDRCxlQUFlQyxPQUFPLENBQUMsbUJBQW1CO1lBQzFDckQsT0FBTztRQUNUO0lBQ0Y7SUFFQSxNQUFNc0QsWUFBWTtRQUNoQjNELFFBQVEsRUFBRTtJQUNaO0lBRUFoQixnREFBU0E7bUNBQUM7WUFDUnFCLE9BQU87WUFDUFE7WUFDQWdCO1FBQ0Y7a0NBQUcsRUFBRTtJQUVMN0MsZ0RBQVNBO21DQUFDO1lBQ1JxQixPQUFPLHdCQUErQixPQUFQVjtZQUMvQixJQUFJRixNQUFNO2dCQUNSWSxPQUFPLFNBQW9CLE9BQVhaLEtBQUttRSxLQUFLO1lBQzVCO1FBQ0Y7a0NBQUc7UUFBQ2pFO1FBQVFGO0tBQUs7SUFFakIscUJBQ0UsOERBQUNvRTtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQUdELFdBQVU7MEJBQTBCOzs7Ozs7MEJBRXhDLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUMzRSxxREFBSUE7OzBDQUNILDhEQUFDRywyREFBVUE7MENBQ1QsNEVBQUNDLDBEQUFTQTs4Q0FBQzs7Ozs7Ozs7Ozs7MENBRWIsOERBQUNILDREQUFXQTswQ0FDViw0RUFBQ3lFO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7OzhEQUNDLDhEQUFDRzs4REFBTzs7Ozs7O2dEQUFnQjs4REFBQyw4REFBQ0M7b0RBQUtILFdBQVcscUJBSXpDLE9BSENuRSxXQUFXLGtCQUFrQixnQ0FDN0JBLFdBQVcsWUFBWSxrQ0FDdkI7OERBQ0dBOzs7Ozs7Ozs7Ozs7d0NBRU5GLHNCQUNDLDhEQUFDb0U7OzhEQUNDLDhEQUFDRzs4REFBTzs7Ozs7O2dEQUFjO2dEQUFFdkUsS0FBS21FLEtBQUs7Ozs7Ozs7d0NBR3JDbEUseUJBQ0MsOERBQUNtRTs7OERBQ0MsOERBQUNHOzhEQUFPOzs7Ozs7Z0RBQWlCO2dEQUFtQixJQUFJeEQsS0FBS2QsUUFBUXdFLFVBQVUsR0FBSSxNQUFNQyxjQUFjO2dEQUFHOzs7Ozs7O3NEQUd0Ryw4REFBQ047NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDNUUseURBQU1BO29EQUFDa0YsU0FBUy9CO29EQUFvQmdDLFVBQVUxRSxXQUFXOzhEQUFpQjs7Ozs7OzhEQUczRSw4REFBQ1QseURBQU1BO29EQUFDa0YsU0FBUzNCO29EQUF1QjRCLFVBQVUxRSxXQUFXO29EQUFpQjJFLFNBQVE7OERBQVk7Ozs7Ozs4REFHbEcsOERBQUNwRix5REFBTUE7b0RBQUNrRixTQUFTZDtvREFBZWUsVUFBVTFFLFdBQVc7b0RBQWlCMkUsU0FBUTs4REFBVTs7Ozs7OzhEQUd4Riw4REFBQ3BGLHlEQUFNQTtvREFBQ2tGLFNBQVNiO29EQUFlZSxTQUFROzhEQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FTMUQsOERBQUNuRixxREFBSUE7OzBDQUNILDhEQUFDRywyREFBVUE7O2tEQUNULDhEQUFDQywwREFBU0E7a0RBQUM7Ozs7OztrREFDWCw4REFBQ0YsZ0VBQWVBO2tEQUFDOzs7Ozs7Ozs7Ozs7MENBRW5CLDhEQUFDRCw0REFBV0E7MENBQ1YsNEVBQUN5RTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM1RSx5REFBTUE7NENBQUNrRixTQUFTWjs0Q0FBMEJjLFNBQVE7c0RBQVU7Ozs7OztzREFHN0QsOERBQUNDOzRDQUFFVCxXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUTNDLDhEQUFDM0UscURBQUlBOzswQ0FDSCw4REFBQ0csMkRBQVVBOzBDQUNULDRFQUFDQywwREFBU0E7OENBQUM7Ozs7Ozs7Ozs7OzBDQUViLDhEQUFDSCw0REFBV0E7MENBQ1YsNEVBQUN5RTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM1RSx5REFBTUE7NENBQUNrRixTQUFTdkQ7NENBQWN5RCxTQUFRO3NEQUFVOzs7Ozs7c0RBQ2pELDhEQUFDRTs0Q0FBSVYsV0FBVTtzREFDWjdELGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU92Qiw4REFBQ2QscURBQUlBOzswQ0FDSCw4REFBQ0csMkRBQVVBOzBDQUNULDRFQUFDQywwREFBU0E7OENBQUM7Ozs7Ozs7Ozs7OzBDQUViLDhEQUFDSCw0REFBV0E7MENBQ1YsNEVBQUN5RTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM1RSx5REFBTUE7NENBQUNrRixTQUFTdkM7NENBQWN5QyxTQUFRO3NEQUFVOzs7Ozs7c0RBQ2pELDhEQUFDRTs0Q0FBSVYsV0FBVTtzREFDWjNELGVBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU94Qiw4REFBQ2hCLHFEQUFJQTs7MENBQ0gsOERBQUNHLDJEQUFVQTswQ0FDVCw0RUFBQ0MsMERBQVNBOzhDQUFDOzs7Ozs7Ozs7OzswQ0FFYiw4REFBQ0gsNERBQVdBOzBDQUNWLDRFQUFDeUU7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDNUUseURBQU1BOzRDQUFDa0YsU0FBU1Q7NENBQVdXLFNBQVE7c0RBQVU7Ozs7OztzREFDOUMsOERBQUNFOzRDQUFJVixXQUFVO3NEQUNaL0QsS0FBSzRCLElBQUksQ0FBQyxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFwQztHQS9Qd0JuQzs7UUFDZ0RQLHFEQUFPQTs7O0tBRHZETyIsInNvdXJjZXMiOlsiL1VzZXJzL2FtaWpldHRlL3NyYy9TUlZYUC9zcmMvYXBwL3Rlc3Qtb2F1dGgvcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvbGliL0F1dGhDb250ZXh0J1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUZXN0T0F1dGhQYWdlKCkge1xuICBjb25zdCB7IHVzZXIsIHNlc3Npb24sIHN0YXR1cywgc2lnbkluV2l0aEdvb2dsZSwgc2lnbk91dCwgcmVmcmVzaCB9ID0gdXNlQXV0aCgpXG4gIGNvbnN0IFtsb2dzLCBzZXRMb2dzXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSlcbiAgY29uc3QgW2Nvb2tpZUluZm8sIHNldENvb2tpZUluZm9dID0gdXNlU3RhdGU8c3RyaW5nPignJylcbiAgY29uc3QgW3N0b3JhZ2VJbmZvLCBzZXRTdG9yYWdlSW5mb10gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKVxuXG4gIGNvbnN0IGFkZExvZyA9IChtZXNzYWdlOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCB0aW1lc3RhbXAgPSBuZXcgRGF0ZSgpLnRvTG9jYWxlVGltZVN0cmluZygpXG4gICAgc2V0TG9ncyhwcmV2ID0+IFsuLi5wcmV2LCBgWyR7dGltZXN0YW1wfV0gJHttZXNzYWdlfWBdKVxuICAgIGNvbnNvbGUubG9nKG1lc3NhZ2UpXG4gIH1cblxuICBjb25zdCBjaGVja0Nvb2tpZXMgPSAoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb25zdCBjb29raWVzID0gZG9jdW1lbnQuY29va2llLnNwbGl0KCc7JykubWFwKGMgPT4gYy50cmltKCkpLmZpbHRlcihjID0+IGMpXG4gICAgICBjb25zdCBhdXRoQ29va2llcyA9IGNvb2tpZXMuZmlsdGVyKGMgPT5cbiAgICAgICAgYy5pbmNsdWRlcygnc3VwYWJhc2UnKSB8fFxuICAgICAgICBjLmluY2x1ZGVzKCdhdXRoJykgfHxcbiAgICAgICAgYy5pbmNsdWRlcygnb2F1dGgnKSB8fFxuICAgICAgICBjLmluY2x1ZGVzKCdzZXNzaW9uJylcbiAgICAgIClcblxuICAgICAgLy8gQ2hlY2sgZm9yIHNwZWNpZmljIE9BdXRoIGZsYWdzXG4gICAgICBjb25zdCBoYXNPQXV0aFN1Y2Nlc3MgPSBkb2N1bWVudC5jb29raWUuaW5jbHVkZXMoJ29hdXRoX3N1Y2Nlc3M9dHJ1ZScpXG4gICAgICBjb25zdCBoYXNPQXV0aE5ld1VzZXIgPSBkb2N1bWVudC5jb29raWUuaW5jbHVkZXMoJ29hdXRoX25ld191c2VyPXRydWUnKVxuXG4gICAgICBsZXQgY29va2llRGV0YWlscyA9IGF1dGhDb29raWVzLmpvaW4oJ1xcbicpIHx8ICdObyBhdXRoLXJlbGF0ZWQgY29va2llcyBmb3VuZCdcbiAgICAgIGlmIChoYXNPQXV0aFN1Y2Nlc3MgfHwgaGFzT0F1dGhOZXdVc2VyKSB7XG4gICAgICAgIGNvb2tpZURldGFpbHMgKz0gYFxcblxcbvCflI0gT0F1dGggRmxhZ3M6XFxuLSBvYXV0aF9zdWNjZXNzOiAke2hhc09BdXRoU3VjY2Vzc31cXG4tIG9hdXRoX25ld191c2VyOiAke2hhc09BdXRoTmV3VXNlcn1gXG4gICAgICB9XG5cbiAgICAgIHNldENvb2tpZUluZm8oY29va2llRGV0YWlscylcbiAgICAgIGFkZExvZyhgRm91bmQgJHthdXRoQ29va2llcy5sZW5ndGh9IGF1dGgtcmVsYXRlZCBjb29raWVzIChPQXV0aCBzdWNjZXNzOiAke2hhc09BdXRoU3VjY2Vzc30sIE9BdXRoIG5ldyB1c2VyOiAke2hhc09BdXRoTmV3VXNlcn0pYClcbiAgICB9XG4gIH1cblxuICBjb25zdCBjaGVja1N0b3JhZ2UgPSAoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb25zdCBhdXRoS2V5cyA9IFtdXG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxvY2FsU3RvcmFnZS5sZW5ndGg7IGkrKykge1xuICAgICAgICBjb25zdCBrZXkgPSBsb2NhbFN0b3JhZ2Uua2V5KGkpXG4gICAgICAgIGlmIChrZXkgJiYgKGtleS5pbmNsdWRlcygnYXV0aCcpIHx8IGtleS5pbmNsdWRlcygnc3VwYWJhc2UnKSB8fCBrZXkuaW5jbHVkZXMoJ3Nlc3Npb24nKSkpIHtcbiAgICAgICAgICBhdXRoS2V5cy5wdXNoKGAke2tleX06ICR7bG9jYWxTdG9yYWdlLmdldEl0ZW0oa2V5KT8uc3Vic3RyaW5nKDAsIDEwMCl9Li4uYClcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgc2V0U3RvcmFnZUluZm8oYXV0aEtleXMuam9pbignXFxuJykgfHwgJ05vIGF1dGgtcmVsYXRlZCBsb2NhbFN0b3JhZ2UgaXRlbXMgZm91bmQnKVxuICAgICAgYWRkTG9nKGBGb3VuZCAke2F1dGhLZXlzLmxlbmd0aH0gYXV0aC1yZWxhdGVkIGxvY2FsU3RvcmFnZSBpdGVtc2ApXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlR29vZ2xlU2lnbkluID0gYXN5bmMgKCkgPT4ge1xuICAgIGFkZExvZygnSW5pdGlhdGluZyBHb29nbGUgT0F1dGguLi4nKVxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzaWduSW5XaXRoR29vZ2xlKClcbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBhZGRMb2coJ0dvb2dsZSBPQXV0aCBpbml0aWF0ZWQgc3VjY2Vzc2Z1bGx5JylcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGFkZExvZyhgR29vZ2xlIE9BdXRoIGZhaWxlZDogJHtyZXN1bHQuZXJyb3I/Lm1lc3NhZ2V9YClcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgYWRkTG9nKGBFcnJvciBkdXJpbmcgR29vZ2xlIE9BdXRoOiAke2Vycm9yfWApXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlVGVzdE5ld1VzZXJGbG93ID0gYXN5bmMgKCkgPT4ge1xuICAgIGFkZExvZygnVGVzdGluZyBuZXcgdXNlciBPQXV0aCBmbG93ICh3aXRoIHRlc3QgZmxhZykuLi4nKVxuICAgIHRyeSB7XG4gICAgICAvLyBHZXQgdGhlIGN1cnJlbnQgb3JpZ2luIHRvIGVuc3VyZSBjb3JyZWN0IHJlZGlyZWN0IFVSTFxuICAgICAgY29uc3Qgb3JpZ2luID0gd2luZG93LmxvY2F0aW9uLm9yaWdpbjtcbiAgICAgIC8vIENyZWF0ZSB0aGUgY2FsbGJhY2sgVVJMIHdpdGggdGVzdCBmbGFnIGZvciBuZXcgdXNlciBzaW11bGF0aW9uXG4gICAgICBjb25zdCBjYWxsYmFja1VybCA9IGAke29yaWdpbn0vYXV0aC9jYWxsYmFjaz9yZWRpcmVjdFRvPS9kYXNoYm9hcmQmdGVzdF9uZXdfdXNlcj10cnVlYDtcblxuICAgICAgYWRkTG9nKGBDYWxsYmFjayBVUkwgd2l0aCB0ZXN0IGZsYWc6ICR7Y2FsbGJhY2tVcmx9YCk7XG5cbiAgICAgIGNvbnN0IHsgY3JlYXRlQ2xpZW50IH0gPSBhd2FpdCBpbXBvcnQoJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcycpO1xuICAgICAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQoXG4gICAgICAgICdodHRwczovL3RmdnN3Z3Jlc2xzYmN0anJ2ZGJkLnN1cGFiYXNlLmNvJyxcbiAgICAgICAgJ2V5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpwYzNNaU9pSnpkWEJoWW1GelpTSXNJbkpsWmlJNkluUm1kbk4zWjNKbGMyeHpZbU4wYW5KMlpHSmtJaXdpY205c1pTSTZJbUZ1YjI0aUxDSnBZWFFpT2pFM05ESXpNakV5T0RVc0ltVjRjQ0k2TWpBMU56ZzVOekk0TlgwLjViTm9zOEpTXzc3RVVqV094SUdIcGdOYVlFMjhxUXZPMmc0cWowd1JXaVUnXG4gICAgICApO1xuXG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25JbldpdGhPQXV0aCh7XG4gICAgICAgIHByb3ZpZGVyOiAnZ29vZ2xlJyxcbiAgICAgICAgb3B0aW9uczoge1xuICAgICAgICAgIHJlZGlyZWN0VG86IGNhbGxiYWNrVXJsXG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgYWRkTG9nKGBUZXN0IG5ldyB1c2VyIE9BdXRoIGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgYWRkTG9nKCdUZXN0IG5ldyB1c2VyIE9BdXRoIGluaXRpYXRlZCBzdWNjZXNzZnVsbHknKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgYWRkTG9nKGBFcnJvciBkdXJpbmcgdGVzdCBuZXcgdXNlciBPQXV0aDogJHtlcnJvcn1gKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVNpZ25PdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgYWRkTG9nKCdTaWduaW5nIG91dC4uLicpXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHNpZ25PdXQoKVxuICAgICAgYWRkTG9nKCdTaWduIG91dCBzdWNjZXNzZnVsJylcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgYWRkTG9nKGBFcnJvciBkdXJpbmcgc2lnbiBvdXQ6ICR7ZXJyb3J9YClcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVSZWZyZXNoID0gYXN5bmMgKCkgPT4ge1xuICAgIGFkZExvZygnUmVmcmVzaGluZyBhdXRoIHN0YXRlLi4uJylcbiAgICB0cnkge1xuICAgICAgYXdhaXQgcmVmcmVzaCgpXG4gICAgICBhZGRMb2coJ0F1dGggc3RhdGUgcmVmcmVzaGVkJylcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgYWRkTG9nKGBFcnJvciByZWZyZXNoaW5nIGF1dGggc3RhdGU6ICR7ZXJyb3J9YClcbiAgICB9XG4gIH1cblxuICBjb25zdCBzaW11bGF0ZVN0cmlwZU5hdmlnYXRpb24gPSAoKSA9PiB7XG4gICAgYWRkTG9nKCdTaW11bGF0aW5nIFN0cmlwZSBuYXZpZ2F0aW9uLi4uJylcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oJ3N0cmlwZV9yZWRpcmVjdCcsICd0cnVlJylcbiAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oJ2V4dGVybmFsX3JldHVybicsICd0cnVlJylcbiAgICAgIGFkZExvZygnU3RyaXBlIG5hdmlnYXRpb24gZmxhZ3Mgc2V0JylcbiAgICB9XG4gIH1cblxuICBjb25zdCBjbGVhckxvZ3MgPSAoKSA9PiB7XG4gICAgc2V0TG9ncyhbXSlcbiAgfVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgYWRkTG9nKCdPQXV0aCB0ZXN0IHBhZ2UgbG9hZGVkJylcbiAgICBjaGVja0Nvb2tpZXMoKVxuICAgIGNoZWNrU3RvcmFnZSgpXG4gIH0sIFtdKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgYWRkTG9nKGBBdXRoIHN0YXR1cyBjaGFuZ2VkOiAke3N0YXR1c31gKVxuICAgIGlmICh1c2VyKSB7XG4gICAgICBhZGRMb2coYFVzZXI6ICR7dXNlci5lbWFpbH1gKVxuICAgIH1cbiAgfSwgW3N0YXR1cywgdXNlcl0pXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHAtNiBtYXgtdy00eGxcIj5cbiAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgbWItNlwiPvCflI0gR29vZ2xlIE9BdXRoIFRlc3Q8L2gxPlxuICAgICAgXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ2FwLTZcIj5cbiAgICAgICAgey8qIEF1dGhlbnRpY2F0aW9uIFN0YXR1cyAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlPvCflJAgQXV0aGVudGljYXRpb24gU3RhdHVzPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPHN0cm9uZz5TdGF0dXM6PC9zdHJvbmc+IDxzcGFuIGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkICR7XG4gICAgICAgICAgICAgICAgICBzdGF0dXMgPT09ICdhdXRoZW50aWNhdGVkJyA/ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnIDpcbiAgICAgICAgICAgICAgICAgIHN0YXR1cyA9PT0gJ2xvYWRpbmcnID8gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwJyA6XG4gICAgICAgICAgICAgICAgICAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnXG4gICAgICAgICAgICAgICAgfWB9PntzdGF0dXN9PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAge3VzZXIgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8c3Ryb25nPlVzZXI6PC9zdHJvbmc+IHt1c2VyLmVtYWlsfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICB7c2Vzc2lvbiAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxzdHJvbmc+U2Vzc2lvbjo8L3N0cm9uZz4gQWN0aXZlIChleHBpcmVzOiB7bmV3IERhdGUoc2Vzc2lvbi5leHBpcmVzX2F0ISAqIDEwMDApLnRvTG9jYWxlU3RyaW5nKCl9KVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTIgZmxleC13cmFwXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVHb29nbGVTaWduSW59IGRpc2FibGVkPXtzdGF0dXMgPT09ICdhdXRoZW50aWNhdGVkJ30+XG4gICAgICAgICAgICAgICAgICBUZXN0IEdvb2dsZSBPQXV0aFxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17aGFuZGxlVGVzdE5ld1VzZXJGbG93fSBkaXNhYmxlZD17c3RhdHVzID09PSAnYXV0aGVudGljYXRlZCd9IHZhcmlhbnQ9XCJzZWNvbmRhcnlcIj5cbiAgICAgICAgICAgICAgICAgIFRlc3QgTmV3IFVzZXIgRmxvd1xuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17aGFuZGxlU2lnbk91dH0gZGlzYWJsZWQ9e3N0YXR1cyAhPT0gJ2F1dGhlbnRpY2F0ZWQnfSB2YXJpYW50PVwib3V0bGluZVwiPlxuICAgICAgICAgICAgICAgICAgU2lnbiBPdXRcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2hhbmRsZVJlZnJlc2h9IHZhcmlhbnQ9XCJvdXRsaW5lXCI+XG4gICAgICAgICAgICAgICAgICBSZWZyZXNoIEF1dGhcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIFNlc3Npb24gUGVyc2lzdGVuY2UgVGVzdCAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlPvCflIQgU2Vzc2lvbiBQZXJzaXN0ZW5jZSBUZXN0PC9DYXJkVGl0bGU+XG4gICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlRlc3Qgc2Vzc2lvbiBwZXJzaXN0ZW5jZSBhZnRlciBleHRlcm5hbCBuYXZpZ2F0aW9uPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17c2ltdWxhdGVTdHJpcGVOYXZpZ2F0aW9ufSB2YXJpYW50PVwib3V0bGluZVwiPlxuICAgICAgICAgICAgICAgIFNpbXVsYXRlIFN0cmlwZSBOYXZpZ2F0aW9uXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICBUaGlzIHNpbXVsYXRlcyBuYXZpZ2F0aW5nIHRvIFN0cmlwZSBhbmQgYmFjay4gQWZ0ZXIgY2xpY2tpbmcsIHJlZnJlc2ggdGhlIHBhZ2UgdG8gdGVzdCBzZXNzaW9uIHJlc3RvcmF0aW9uLlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIENvb2tpZSBJbmZvcm1hdGlvbiAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlPvCfjaogQ29va2llIEluZm9ybWF0aW9uPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17Y2hlY2tDb29raWVzfSB2YXJpYW50PVwib3V0bGluZVwiPlJlZnJlc2ggQ29va2llczwvQnV0dG9uPlxuICAgICAgICAgICAgICA8cHJlIGNsYXNzTmFtZT1cImJnLWdyYXktMTAwIHAtMyByb3VuZGVkIHRleHQtc20gb3ZlcmZsb3ctYXV0byBtYXgtaC00MFwiPlxuICAgICAgICAgICAgICAgIHtjb29raWVJbmZvIHx8ICdObyBjb29raWUgaW5mb3JtYXRpb24gYXZhaWxhYmxlJ31cbiAgICAgICAgICAgICAgPC9wcmU+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIExvY2FsIFN0b3JhZ2UgKi99XG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRUaXRsZT7wn5K+IExvY2FsIFN0b3JhZ2U8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtjaGVja1N0b3JhZ2V9IHZhcmlhbnQ9XCJvdXRsaW5lXCI+UmVmcmVzaCBTdG9yYWdlPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxwcmUgY2xhc3NOYW1lPVwiYmctZ3JheS0xMDAgcC0zIHJvdW5kZWQgdGV4dC1zbSBvdmVyZmxvdy1hdXRvIG1heC1oLTQwXCI+XG4gICAgICAgICAgICAgICAge3N0b3JhZ2VJbmZvIHx8ICdObyBzdG9yYWdlIGluZm9ybWF0aW9uIGF2YWlsYWJsZSd9XG4gICAgICAgICAgICAgIDwvcHJlPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiBUZXN0IExvZyAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlPvCfk50gVGVzdCBMb2c8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtjbGVhckxvZ3N9IHZhcmlhbnQ9XCJvdXRsaW5lXCI+Q2xlYXIgTG9nPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxwcmUgY2xhc3NOYW1lPVwiYmctZ3JheS0xMDAgcC0zIHJvdW5kZWQgdGV4dC1zbSBvdmVyZmxvdy1hdXRvIG1heC1oLTYwXCI+XG4gICAgICAgICAgICAgICAge2xvZ3Muam9pbignXFxuJykgfHwgJ05vIGxvZ3MgeWV0J31cbiAgICAgICAgICAgICAgPC9wcmU+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQXV0aCIsIkJ1dHRvbiIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJUZXN0T0F1dGhQYWdlIiwidXNlciIsInNlc3Npb24iLCJzdGF0dXMiLCJzaWduSW5XaXRoR29vZ2xlIiwic2lnbk91dCIsInJlZnJlc2giLCJsb2dzIiwic2V0TG9ncyIsImNvb2tpZUluZm8iLCJzZXRDb29raWVJbmZvIiwic3RvcmFnZUluZm8iLCJzZXRTdG9yYWdlSW5mbyIsImFkZExvZyIsIm1lc3NhZ2UiLCJ0aW1lc3RhbXAiLCJEYXRlIiwidG9Mb2NhbGVUaW1lU3RyaW5nIiwicHJldiIsImNvbnNvbGUiLCJsb2ciLCJjaGVja0Nvb2tpZXMiLCJjb29raWVzIiwiZG9jdW1lbnQiLCJjb29raWUiLCJzcGxpdCIsIm1hcCIsImMiLCJ0cmltIiwiZmlsdGVyIiwiYXV0aENvb2tpZXMiLCJpbmNsdWRlcyIsImhhc09BdXRoU3VjY2VzcyIsImhhc09BdXRoTmV3VXNlciIsImNvb2tpZURldGFpbHMiLCJqb2luIiwibGVuZ3RoIiwiY2hlY2tTdG9yYWdlIiwiYXV0aEtleXMiLCJpIiwibG9jYWxTdG9yYWdlIiwia2V5IiwicHVzaCIsImdldEl0ZW0iLCJzdWJzdHJpbmciLCJoYW5kbGVHb29nbGVTaWduSW4iLCJyZXN1bHQiLCJzdWNjZXNzIiwiZXJyb3IiLCJoYW5kbGVUZXN0TmV3VXNlckZsb3ciLCJvcmlnaW4iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImNhbGxiYWNrVXJsIiwiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2UiLCJkYXRhIiwiYXV0aCIsInNpZ25JbldpdGhPQXV0aCIsInByb3ZpZGVyIiwib3B0aW9ucyIsInJlZGlyZWN0VG8iLCJoYW5kbGVTaWduT3V0IiwiaGFuZGxlUmVmcmVzaCIsInNpbXVsYXRlU3RyaXBlTmF2aWdhdGlvbiIsInNlc3Npb25TdG9yYWdlIiwic2V0SXRlbSIsImNsZWFyTG9ncyIsImVtYWlsIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJzdHJvbmciLCJzcGFuIiwiZXhwaXJlc19hdCIsInRvTG9jYWxlU3RyaW5nIiwib25DbGljayIsImRpc2FibGVkIiwidmFyaWFudCIsInAiLCJwcmUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-oauth/page.tsx\n"));

/***/ })

}]);