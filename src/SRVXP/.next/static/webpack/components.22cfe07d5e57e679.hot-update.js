"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("components",{

/***/ "(app-pages-browser)/./src/components/language-toggle.tsx":
/*!********************************************!*\
  !*** ./src/components/language-toggle.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageToggle: () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Globe!=!lucide-react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/LanguageContext */ \"(app-pages-browser)/./src/lib/LanguageContext.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ LanguageToggle auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst LanguageToggle = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_3__.memo)(_c = _s(function LanguageToggle(param) {\n    let { className, variant = \"default\" } = param;\n    _s();\n    const { language, setLanguage, isLoading } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const toggleLanguage = ()=>{\n        // Switch between FR and EN directly\n        const newLanguage = language === \"fr\" ? \"en\" : \"fr\";\n        setLanguage(newLanguage);\n    // The language context will handle persisting the preference\n    };\n    // Don't render until translations are loaded to prevent flickering\n    if (isLoading) {\n        return null;\n    }\n    // For mobile, we'll keep the current approach as it's a dual-button \n    // interface that shows both options simultaneously\n    if (variant === \"mobile\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-4 border-t pt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-2 px-2 text-sm font-medium text-muted-foreground\",\n                    children: \"Langue / Language\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/components/language-toggle.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: language === \"fr\" ? \"secondary\" : \"ghost\",\n                            size: \"sm\",\n                            className: \"flex-1 justify-start gap-2\",\n                            onClick: ()=>setLanguage(\"fr\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/components/language-toggle.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                \"Fran\\xe7ais\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/components/language-toggle.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: language === \"en\" ? \"secondary\" : \"ghost\",\n                            size: \"sm\",\n                            className: \"flex-1 justify-start gap-2\",\n                            onClick: ()=>setLanguage(\"en\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/components/language-toggle.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                \"English\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/components/language-toggle.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/components/language-toggle.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/src/SRVXP/src/components/language-toggle.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this);\n    }\n    // For desktop: button that toggles directly with full language name and globe icon\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n        variant: \"outline\",\n        onClick: toggleLanguage,\n        className: \"\".concat(className, \" flex items-center justify-center gap-2 border border-border bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3\"),\n        \"aria-label\": language === \"fr\" ? \"Switch to English\" : \"Passer au français\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/components/language-toggle.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-normal\",\n                children: language === \"fr\" ? \"English\" : \"Français\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/components/language-toggle.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/components/language-toggle.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}, \"Y8nwm51gCZ/S9IyngOApQ0DuS5E=\", false, function() {\n    return [\n        _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage\n    ];\n})), \"Y8nwm51gCZ/S9IyngOApQ0DuS5E=\", false, function() {\n    return [\n        _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage\n    ];\n});\n_c1 = LanguageToggle;\nvar _c, _c1;\n$RefreshReg$(_c, \"LanguageToggle$memo\");\n$RefreshReg$(_c1, \"LanguageToggle\");\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/language-toggle.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, variant, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/components/ui/alert.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Alert;\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/components/ui/alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined);\n});\n_c3 = AlertTitle;\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/components/ui/alert.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined);\n});\n_c5 = AlertDescription;\nAlertDescription.displayName = \"AlertDescription\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"Alert$React.forwardRef\");\n$RefreshReg$(_c1, \"Alert\");\n$RefreshReg$(_c2, \"AlertTitle$React.forwardRef\");\n$RefreshReg$(_c3, \"AlertTitle\");\n$RefreshReg$(_c4, \"AlertDescription$React.forwardRef\");\n$RefreshReg$(_c5, \"AlertDescription\");\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/alert.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, type, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/components/ui/input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Input;\nInput.displayName = \"Input\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Input$React.forwardRef\");\n$RefreshReg$(_c1, \"Input\");\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBOEI7QUFFRTtBQUVoQyxNQUFNRSxzQkFBUUYsNkNBQWdCLE1BQzVCLFFBQWdDSTtRQUEvQixFQUFFQyxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPO0lBQzVCLHFCQUNFLDhEQUFDQztRQUNDRixNQUFNQTtRQUNORCxXQUFXSiw4Q0FBRUEsQ0FDWCwyV0FDQUk7UUFFRkQsS0FBS0E7UUFDSixHQUFHRyxLQUFLOzs7Ozs7QUFHZjs7QUFFRkwsTUFBTU8sV0FBVyxHQUFHO0FBRUoiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWlqZXR0ZS9zcmMvU1JWWFAvc3JjL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiaW5wdXRcIj4+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC05IHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctdHJhbnNwYXJlbnQgcHgtMyBweS0xIHRleHQtYmFzZSBzaGFkb3ctc20gdHJhbnNpdGlvbi1jb2xvcnMgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMSBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MCBtZDp0ZXh0LXNtXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJyZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/input.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/components/ui/label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Label;\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c1, \"Label\");\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUU4QjtBQUN5QjtBQUNVO0FBRWpDO0FBRWhDLE1BQU1JLGdCQUFnQkYsNkRBQUdBLENBQ3ZCO0FBR0YsTUFBTUcsc0JBQVFMLDZDQUFnQixNQUk1QixRQUEwQk87UUFBekIsRUFBRUMsU0FBUyxFQUFFLEdBQUdDLE9BQU87eUJBQ3hCLDhEQUFDUix1REFBbUI7UUFDbEJNLEtBQUtBO1FBQ0xDLFdBQVdMLDhDQUFFQSxDQUFDQyxpQkFBaUJJO1FBQzlCLEdBQUdDLEtBQUs7Ozs7Ozs7O0FBR2JKLE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWlqZXR0ZS9zcmMvU1JWWFAvc3JjL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBMYWJlbFByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWxhYmVsXCJcbmltcG9ydCB7IGN2YSwgdHlwZSBWYXJpYW50UHJvcHMgfSBmcm9tIFwiY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBsYWJlbFZhcmlhbnRzID0gY3ZhKFxuICBcInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lIHBlZXItZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHBlZXItZGlzYWJsZWQ6b3BhY2l0eS03MFwiXG4pXG5cbmNvbnN0IExhYmVsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgbGFiZWxWYXJpYW50cz5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKGxhYmVsVmFyaWFudHMoKSwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmV4cG9ydCB7IExhYmVsIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwicmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJSb290IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/label.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/separator.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/separator.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-separator */ \"(app-pages-browser)/../SRVXP_dependencies/node_modules/@radix-ui/react-separator/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Separator auto */ \n\n\n\nconst Separator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, orientation = \"horizontal\", decorative = true, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        decorative: decorative,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"shrink-0 bg-border\", orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/components/ui/separator.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = Separator;\nSeparator.displayName = _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Separator$React.forwardRef\");\n$RefreshReg$(_c1, \"Separator\");\n\n\n;\n    (function() {\n      var _a, _b;\n      if (typeof self !== \"undefined\" && \"$RefreshHelpers$\" in self) {\n        var currentExports = module.exports, prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n          module.hot.dispose(function(data) {\n            data.prevSignature = self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n          });\n          module.hot.accept();\n          if (prevSignature !== null)\n            if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports)))\n              module.hot.invalidate();\n            else\n              self.$RefreshHelpers$.scheduleUpdate();\n        } else {\n          var isNoLongerABoundary = prevSignature !== null;\n          if (isNoLongerABoundary)\n            module.hot.invalidate();\n        }\n      }\n    })();\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL3NlcGFyYXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU4QjtBQUNpQztBQUUvQjtBQUVoQyxNQUFNRywwQkFBWUgsNkNBQWdCLE1BSWhDLFFBRUVLO1FBREEsRUFBRUMsU0FBUyxFQUFFQyxjQUFjLFlBQVksRUFBRUMsYUFBYSxJQUFJLEVBQUUsR0FBR0MsT0FBTzt5QkFHdEUsOERBQUNSLDJEQUF1QjtRQUN0QkksS0FBS0E7UUFDTEcsWUFBWUE7UUFDWkQsYUFBYUE7UUFDYkQsV0FBV0osOENBQUVBLENBQ1gsc0JBQ0FLLGdCQUFnQixlQUFlLG1CQUFtQixrQkFDbEREO1FBRUQsR0FBR0csS0FBSzs7Ozs7Ozs7QUFJZk4sVUFBVVEsV0FBVyxHQUFHViwyREFBdUIsQ0FBQ1UsV0FBVztBQUV2QyIsInNvdXJjZXMiOlsiL1VzZXJzL2FtaWpldHRlL3NyYy9TUlZYUC9zcmMvY29tcG9uZW50cy91aS9zZXBhcmF0b3IudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBTZXBhcmF0b3JQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zZXBhcmF0b3JcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IFNlcGFyYXRvciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFNlcGFyYXRvclByaW1pdGl2ZS5Sb290PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBTZXBhcmF0b3JQcmltaXRpdmUuUm9vdD5cbj4oXG4gIChcbiAgICB7IGNsYXNzTmFtZSwgb3JpZW50YXRpb24gPSBcImhvcml6b250YWxcIiwgZGVjb3JhdGl2ZSA9IHRydWUsIC4uLnByb3BzIH0sXG4gICAgcmVmXG4gICkgPT4gKFxuICAgIDxTZXBhcmF0b3JQcmltaXRpdmUuUm9vdFxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBkZWNvcmF0aXZlPXtkZWNvcmF0aXZlfVxuICAgICAgb3JpZW50YXRpb249e29yaWVudGF0aW9ufVxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJzaHJpbmstMCBiZy1ib3JkZXJcIixcbiAgICAgICAgb3JpZW50YXRpb24gPT09IFwiaG9yaXpvbnRhbFwiID8gXCJoLVsxcHhdIHctZnVsbFwiIDogXCJoLWZ1bGwgdy1bMXB4XVwiLFxuICAgICAgICBjbGFzc05hbWVcbiAgICAgICl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxuKVxuU2VwYXJhdG9yLmRpc3BsYXlOYW1lID0gU2VwYXJhdG9yUHJpbWl0aXZlLlJvb3QuZGlzcGxheU5hbWVcblxuZXhwb3J0IHsgU2VwYXJhdG9yIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNlcGFyYXRvclByaW1pdGl2ZSIsImNuIiwiU2VwYXJhdG9yIiwiZm9yd2FyZFJlZiIsInJlZiIsImNsYXNzTmFtZSIsIm9yaWVudGF0aW9uIiwiZGVjb3JhdGl2ZSIsInByb3BzIiwiUm9vdCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/separator.tsx\n"));

/***/ })

});