"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "routes";
exports.ids = ["routes"];
exports.modules = {

/***/ "(rsc)/./src/app/ClientBody.tsx":
/*!********************************!*\
  !*** ./src/app/ClientBody.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/auth/callback/route.ts":
/*!****************************************!*\
  !*** ./src/app/auth/callback/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/../SRVXP_dependencies/node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/../SRVXP_dependencies/node_modules/next/dist/api/server.js\");\n\n\n\n/**\n * Updates the user's profile data in Supabase after Google Auth\n */ async function updateUserProfileFromGoogleAuth(supabase, userId) {\n    try {\n        // Get the admin client for privileged operations\n        const adminClient = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createAdminClient)();\n        // Get the user's metadata from auth.users\n        const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(userId);\n        if (userError || !userData) {\n            console.error('Error fetching user data:', userError);\n            return;\n        }\n        const metadata = userData.user.user_metadata;\n        // Check if this is a Google Auth user\n        const isGoogleAuth = metadata?.provider_id || metadata?.iss === 'https://accounts.google.com' || metadata?.sub?.startsWith('google');\n        if (!isGoogleAuth) {\n            console.log('Not a Google Auth user, skipping profile update');\n            return;\n        }\n        console.log('Processing Google Auth user profile update');\n        // Extract name information from Google metadata\n        // Google provides different fields depending on the authentication flow\n        const firstName = metadata.given_name || metadata.first_name || metadata.name?.split(' ')[0] || '';\n        const lastName = metadata.family_name || metadata.last_name || metadata.name?.split(' ').slice(1).join(' ') || '';\n        // Get the language from the cookie or default to French\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n        const languageCookie = cookieStore.get('NEXT_LOCALE');\n        const language = languageCookie?.value || 'fr';\n        console.log(`Extracted user info - First: \"${firstName}\", Last: \"${lastName}\", Language: \"${language}\"`);\n        // Update the auth.users metadata to ensure consistency\n        const { error: updateAuthError } = await adminClient.auth.admin.updateUserById(userId, {\n            user_metadata: {\n                ...metadata,\n                first_name: firstName,\n                last_name: lastName,\n                language: language\n            }\n        });\n        if (updateAuthError) {\n            console.error('Error updating auth user metadata:', updateAuthError);\n        }\n        // The database trigger should handle updating the users table,\n        // but we'll do it explicitly as well to ensure it happens\n        const { error: updateUserError } = await adminClient.from('users').upsert({\n            id: userId,\n            user_id: userId,\n            email: userData.user.email || '',\n            first_name: firstName,\n            last_name: lastName,\n            language: language,\n            avatar_url: metadata.avatar_url || metadata.picture || '',\n            token_identifier: userData.user.email || '',\n            created_at: userData.user.created_at,\n            updated_at: new Date().toISOString()\n        }, {\n            onConflict: 'id'\n        });\n        if (updateUserError) {\n            console.error('Error updating user profile:', updateUserError);\n        } else {\n            console.log('Successfully updated user profile with Google Auth data');\n        }\n    } catch (error) {\n        console.error('Error in updateUserProfileFromGoogleAuth:', error);\n    }\n}\nasync function GET(request) {\n    const requestUrl = new URL(request.url);\n    const code = requestUrl.searchParams.get('code');\n    const redirectTo = requestUrl.searchParams.get('redirectTo') || '/dashboard';\n    const origin = requestUrl.origin;\n    console.log(`OAuth callback - Code: ${!!code}, RedirectTo: ${redirectTo}`);\n    if (code) {\n        try {\n            // Create the Supabase client\n            const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n            // Exchange the code for a session\n            const { data, error } = await supabase.auth.exchangeCodeForSession(code);\n            if (error) {\n                console.error('Error exchanging code for session:', error);\n                // Redirect to sign-in page with error\n                const errorUrl = `${origin}/auth/sign-in?error=oauth_callback_failed`;\n                return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(errorUrl);\n            }\n            console.log('Successfully exchanged code for session');\n            if (!data?.session?.user?.id) {\n                console.error('No session or user ID after code exchange');\n                const errorUrl = `${origin}/auth/sign-in?error=no_session`;\n                return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(errorUrl);\n            }\n            const session = data.session;\n            const user = session.user;\n            // Check if this is a new user registration vs existing user login\n            const userCreatedAt = new Date(user.created_at).getTime();\n            const now = Date.now();\n            const timeSinceCreation = now - userCreatedAt;\n            const isNewUser = timeSinceCreation < 10 * 60 * 1000 // Created within last 10 minutes\n            ;\n            // Check for manual testing override\n            const forceNewUserTest = requestUrl.searchParams.get('test_new_user') === 'true';\n            const finalIsNewUser = isNewUser || forceNewUserTest;\n            console.log(`OAuth user analysis:`);\n            console.log(`- User created at: ${user.created_at}`);\n            console.log(`- Time since creation: ${Math.round(timeSinceCreation / 1000)} seconds`);\n            console.log(`- Is new user (auto): ${isNewUser}`);\n            console.log(`- Force new user test: ${forceNewUserTest}`);\n            console.log(`- Final user type: ${finalIsNewUser ? 'NEW USER REGISTRATION' : 'EXISTING USER LOGIN'}`);\n            // For new users, we need to ensure proper session establishment\n            if (finalIsNewUser) {\n                console.log('Processing new user registration - ensuring session establishment...');\n                // Update user profile first\n                await updateUserProfileFromGoogleAuth(supabase, user.id);\n                // Wait a moment for database operations to complete\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                // Verify session is still valid after profile operations\n                const { data: verifyData, error: verifyError } = await supabase.auth.getSession();\n                if (verifyError || !verifyData?.session) {\n                    console.error('Session lost during new user setup:', verifyError);\n                    // Try to refresh the session\n                    const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();\n                    if (refreshError || !refreshData?.session) {\n                        console.error('Failed to refresh session for new user:', refreshError);\n                        const errorUrl = `${origin}/auth/sign-in?error=session_lost`;\n                        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(errorUrl);\n                    }\n                    console.log('Session refreshed successfully for new user');\n                }\n            } else {\n                // For existing users, just update profile without delays\n                await updateUserProfileFromGoogleAuth(supabase, user.id);\n            }\n            // Create the redirect URL - respect the redirectTo parameter\n            const redirectUrl = `${origin}${redirectTo}`;\n            console.log(`Redirecting OAuth user (${finalIsNewUser ? 'new' : 'existing'}) to: ${redirectUrl}`);\n            const response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(redirectUrl);\n            // Set proper cookies for OAuth session persistence\n            const cookieOptions = {\n                httpOnly: false,\n                secure: \"development\" === 'production',\n                sameSite: 'lax',\n                maxAge: 60 * 60 * 24 * 30,\n                path: '/',\n                domain:  false ? 0 : 'localhost'\n            };\n            // Set different flags for new vs existing users\n            if (finalIsNewUser) {\n                response.cookies.set('oauth_new_user', 'true', {\n                    ...cookieOptions,\n                    maxAge: 120\n                }) // 2 minutes\n                ;\n                response.cookies.set('oauth_success', 'true', {\n                    ...cookieOptions,\n                    maxAge: 120\n                });\n            } else {\n                response.cookies.set('oauth_success', 'true', {\n                    ...cookieOptions,\n                    maxAge: 60\n                });\n            }\n            response.cookies.delete('isLoggedOut');\n            // Add session verification headers for debugging\n            response.headers.set('X-OAuth-User-Type', finalIsNewUser ? 'new' : 'existing');\n            response.headers.set('X-OAuth-User-ID', user.id);\n            return response;\n        } catch (error) {\n            console.error('Error in auth callback:', error);\n            // Redirect to sign-in page with error\n            const errorUrl = `${origin}/auth/sign-in?error=oauth_callback_error`;\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(errorUrl);\n        }\n    }\n    // No code provided - redirect to sign-in\n    console.log('No OAuth code provided, redirecting to sign-in');\n    const signInUrl = `${origin}/auth/sign-in`;\n    return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(signInUrl);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/auth/callback/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/layout.tsx":
/*!*********************************!*\
  !*** ./src/app/auth/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/auth/sign-in/page.tsx":
/*!***************************************!*\
  !*** ./src/app/auth/sign-in/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"357c991f04ce\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYW1pamV0dGUvc3JjL1NSVlhQL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzNTdjOTkxZjA0Y2VcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/../SRVXP_dependencies/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/../SRVXP_dependencies/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _app_ClientBody__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/ClientBody */ \"(rsc)/./src/app/ClientBody.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Rendez-vous Médicaux\",\n    description: \"Gérez vos rendez-vous médicaux en ligne\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"fr\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)}`,\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ClientBody__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/src/SRVXP/src/app/layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/app/layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_zaply_layout_Navbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/zaply/layout/Navbar */ \"(rsc)/./src/components/zaply/layout/Navbar.tsx\");\n/* harmony import */ var _components_zaply_layout_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/zaply/layout/Footer */ \"(rsc)/./src/components/zaply/layout/Footer.tsx\");\n/* harmony import */ var _components_zaply_sections_HeroSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/zaply/sections/HeroSection */ \"(rsc)/./src/components/zaply/sections/HeroSection.tsx\");\n/* harmony import */ var _components_zaply_sections_FeaturesSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/zaply/sections/FeaturesSection */ \"(rsc)/./src/components/zaply/sections/FeaturesSection.tsx\");\n/* harmony import */ var _components_zaply_sections_HowItWorksSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/zaply/sections/HowItWorksSection */ \"(rsc)/./src/components/zaply/sections/HowItWorksSection.tsx\");\n/* harmony import */ var _components_zaply_sections_PricingSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/zaply/sections/PricingSection */ \"(rsc)/./src/components/zaply/sections/PricingSection.tsx\");\n/* harmony import */ var _components_zaply_sections_FaqSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/zaply/sections/FaqSection */ \"(rsc)/./src/components/zaply/sections/FaqSection.tsx\");\n/* harmony import */ var _components_zaply_sections_CtaSection2__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/zaply/sections/CtaSection2 */ \"(rsc)/./src/components/zaply/sections/CtaSection2.tsx\");\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/LanguageContext */ \"(rsc)/./src/lib/LanguageContext.tsx\");\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_9__.LanguageProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_zaply_layout_Navbar__WEBPACK_IMPORTED_MODULE_1__.Navbar, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#f8f9fb]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_zaply_sections_HeroSection__WEBPACK_IMPORTED_MODULE_3__.HeroSection, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_zaply_sections_FeaturesSection__WEBPACK_IMPORTED_MODULE_4__.FeaturesSection, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_zaply_sections_HowItWorksSection__WEBPACK_IMPORTED_MODULE_5__.HowItWorksSection, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#f8f9fb] w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_zaply_sections_PricingSection__WEBPACK_IMPORTED_MODULE_6__.PricingSection, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_zaply_sections_FaqSection__WEBPACK_IMPORTED_MODULE_7__.FaqSection, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_zaply_sections_CtaSection2__WEBPACK_IMPORTED_MODULE_8__.CtaSection2, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_zaply_layout_Footer__WEBPACK_IMPORTED_MODULE_2__.Footer, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/app/page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/test-oauth/page.tsx":
/*!*************************************!*\
  !*** ./src/app/test-oauth/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./src/app/ClientBody.tsx":
/*!********************************!*\
  !*** ./src/app/ClientBody.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientBody)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_FamilyMembersContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/FamilyMembersContext */ \"(ssr)/./src/lib/FamilyMembersContext.tsx\");\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/LanguageContext */ \"(ssr)/./src/lib/LanguageContext.tsx\");\n/* harmony import */ var _lib_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/AuthContext */ \"(ssr)/./src/lib/AuthContext.tsx\");\n/* harmony import */ var _components_translation_loader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/translation-loader */ \"(ssr)/./src/components/translation-loader.tsx\");\n/* harmony import */ var _lib_profile_update_context__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/profile-update-context */ \"(ssr)/./src/lib/profile-update-context.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/theme-provider */ \"(ssr)/./src/components/theme-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction ClientBody({ children }) {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Check if the current page is landing page or auth page\n    const isLandingOrAuthPage = pathname === \"/\" || pathname === \"/landing\" || pathname?.startsWith(\"/auth\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientBody.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"ClientBody.useEffect\"], []);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_AuthContext__WEBPACK_IMPORTED_MODULE_5__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_profile_update_context__WEBPACK_IMPORTED_MODULE_7__.ProfileUpdateProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_8__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: isLandingOrAuthPage ? \"light\" : \"system\",\n                enableSystem: !isLandingOrAuthPage,\n                forcedTheme: isLandingOrAuthPage ? \"light\" : undefined,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.LanguageProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_translation_loader__WEBPACK_IMPORTED_MODULE_6__.TranslationLoader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_FamilyMembersContext__WEBPACK_IMPORTED_MODULE_3__.FamilyMembersProvider, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/ClientBody.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/layout.tsx":
/*!*********************************!*\
  !*** ./src/app/auth/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_language_toggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/language-toggle */ \"(ssr)/./src/components/language-toggle.tsx\");\n/* harmony import */ var _components_t__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/t */ \"(ssr)/./src/components/t.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// Force light mode for auth pages\n\n\n\n\nfunction AuthLayout({ children }) {\n    const { t, language } = (0,_components_t__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col justify-center items-center bg-gray-50 light relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_language_toggle__WEBPACK_IMPORTED_MODULE_3__.LanguageToggle, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md bg-white p-8 rounded-lg shadow-sm text-gray-900\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"flex items-center group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 flex items-center justify-center transition-transform duration-300 group-hover:scale-110\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/zaply-images/srvxp_logorevised.svg\",\n                                        alt: \"Logo\",\n                                        width: 32,\n                                        height: 32,\n                                        priority: true,\n                                        suppressHydrationWarning: true,\n                                        className: \"text-brandBlue fill-current\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-4 text-lg sm:text-xl font-bold text-[#212242]\",\n                                    children: \"Sans rendez-vous express\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-4 left-4 flex items-center space-x-4 text-xs text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"\\xa9 Sans rendez-vous express\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: language === 'en' ? \"/politique-de-confidentialite-EN\" : \"/politique-de-confidentialite\",\n                        className: \"hover:text-gray-600 transition-colors\",\n                        children: t('landing.footer.privacyPolicy')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/sign-in/page.tsx":
/*!***************************************!*\
  !*** ./src/app/auth/sign-in/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_t__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/t */ \"(ssr)/./src/components/t.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _lib_AuthContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/AuthContext */ \"(ssr)/./src/lib/AuthContext.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(ssr)/./src/components/ui/separator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction SignInPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const redirectTo = searchParams.get(\"redirectedFrom\") || \"/dashboard\";\n    const { t } = (0,_components_t__WEBPACK_IMPORTED_MODULE_7__.useTranslation)();\n    const { signIn, signInWithGoogle, status } = (0,_lib_AuthContext__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGoogleLoading, setIsGoogleLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Redirect if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignInPage.useEffect\": ()=>{\n            if (status === \"authenticated\") {\n                router.push(redirectTo);\n            }\n        }\n    }[\"SignInPage.useEffect\"], [\n        status,\n        redirectTo,\n        router\n    ]);\n    const handleSignIn = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await signIn(email, password);\n            if (result.success) {\n                // Force redirect to dashboard to ensure consistent behavior\n                window.location.href = '/dashboard';\n            } else {\n                throw new Error(result.error?.message || \"Authentication failed\");\n            }\n        } catch (error) {\n            console.error(\"Error signing in:\", error);\n            setError(t('auth.errors.invalidCredentials'));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        setIsGoogleLoading(true);\n        setError(null);\n        try {\n            await signInWithGoogle();\n        // The redirect will be handled by Supabase OAuth\n        } catch (error) {\n            console.error(\"Error signing in with Google:\", error);\n            setError(t('auth.errors.socialAuthFailed'));\n            setIsGoogleLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold\",\n                    children: t('auth.signIn')\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                variant: \"destructive\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        type: \"button\",\n                        variant: \"outline\",\n                        className: \"w-full flex items-center justify-center gap-2\",\n                        onClick: handleGoogleSignIn,\n                        disabled: isGoogleLoading,\n                        children: [\n                            isGoogleLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-5 h-5 border-2 border-gray-300 border-t-primary rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                height: \"24\",\n                                viewBox: \"0 0 24 24\",\n                                width: \"24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\",\n                                        fill: \"#4285F4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\",\n                                        fill: \"#34A853\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\",\n                                        fill: \"#FBBC05\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\",\n                                        fill: \"#EA4335\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M1 1h22v22H1z\",\n                                        fill: \"none\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: isGoogleLoading ? t('common.loading') : t('auth.continueWithGoogle')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center justify-center mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                className: \"absolute w-full bg-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"relative bg-white px-2 text-xs text-gray-500\",\n                                children: t('auth.orContinueWith')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSignIn,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                htmlFor: \"email\",\n                                children: t('auth.email')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                id: \"email\",\n                                type: \"email\",\n                                placeholder: \"<EMAIL>\",\n                                autoComplete: \"email\",\n                                required: true,\n                                value: email,\n                                onChange: (e)=>setEmail(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"password\",\n                                        children: t('auth.password')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/forgot-password\",\n                                        className: \"text-sm text-primary hover:underline\",\n                                        children: t('auth.forgotPassword')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                id: \"password\",\n                                type: \"password\",\n                                autoComplete: \"current-password\",\n                                required: true,\n                                value: password,\n                                onChange: (e)=>setPassword(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        type: \"submit\",\n                        className: \"w-full py-6\",\n                        disabled: isLoading,\n                        children: isLoading ? t('common.loading') : t('auth.signIn')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\n                        t('auth.noAccount'),\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/auth/sign-up\",\n                            className: \"text-primary hover:underline\",\n                            children: t('auth.createAccount')\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/sign-in/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/test-oauth/page.tsx":
/*!*************************************!*\
  !*** ./src/app/test-oauth/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestOAuthPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/AuthContext */ \"(ssr)/./src/lib/AuthContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction TestOAuthPage() {\n    const { user, session, status, signInWithGoogle, signOut, refresh } = (0,_lib_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cookieInfo, setCookieInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [storageInfo, setStorageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const addLog = (message)=>{\n        const timestamp = new Date().toLocaleTimeString();\n        setLogs((prev)=>[\n                ...prev,\n                `[${timestamp}] ${message}`\n            ]);\n        console.log(message);\n    };\n    const checkCookies = ()=>{\n        if (false) {}\n    };\n    const checkStorage = ()=>{\n        if (false) {}\n    };\n    const handleGoogleSignIn = async ()=>{\n        addLog('Initiating Google OAuth...');\n        try {\n            const result = await signInWithGoogle();\n            if (result.success) {\n                addLog('Google OAuth initiated successfully');\n            } else {\n                addLog(`Google OAuth failed: ${result.error?.message}`);\n            }\n        } catch (error) {\n            addLog(`Error during Google OAuth: ${error}`);\n        }\n    };\n    const handleTestNewUserFlow = async ()=>{\n        addLog('Testing new user OAuth flow (with test flag)...');\n        try {\n            // Get the current origin to ensure correct redirect URL\n            const origin = window.location.origin;\n            // Create the callback URL with test flag for new user simulation\n            const callbackUrl = `${origin}/auth/callback?redirectTo=/dashboard&test_new_user=true`;\n            addLog(`Callback URL with test flag: ${callbackUrl}`);\n            const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(ssr)/../SRVXP_dependencies/node_modules/@supabase/supabase-js/dist/module/index.js\"));\n            const supabase = createClient('https://tfvswgreslsbctjrvdbd.supabase.co', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.5bNos8JS_77EUjWOxIGHpgNaYE28qQvO2g4qj0wRWiU');\n            const { data, error } = await supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: callbackUrl\n                }\n            });\n            if (error) {\n                addLog(`Test new user OAuth failed: ${error.message}`);\n            } else {\n                addLog('Test new user OAuth initiated successfully');\n            }\n        } catch (error) {\n            addLog(`Error during test new user OAuth: ${error}`);\n        }\n    };\n    const handleSignOut = async ()=>{\n        addLog('Signing out...');\n        try {\n            await signOut();\n            addLog('Sign out successful');\n        } catch (error) {\n            addLog(`Error during sign out: ${error}`);\n        }\n    };\n    const handleRefresh = async ()=>{\n        addLog('Refreshing auth state...');\n        try {\n            await refresh();\n            addLog('Auth state refreshed');\n        } catch (error) {\n            addLog(`Error refreshing auth state: ${error}`);\n        }\n    };\n    const simulateStripeNavigation = ()=>{\n        addLog('Simulating Stripe navigation...');\n        if (false) {}\n    };\n    const clearLogs = ()=>{\n        setLogs([]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestOAuthPage.useEffect\": ()=>{\n            addLog('OAuth test page loaded');\n            checkCookies();\n            checkStorage();\n        }\n    }[\"TestOAuthPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestOAuthPage.useEffect\": ()=>{\n            addLog(`Auth status changed: ${status}`);\n            if (user) {\n                addLog(`User: ${user.email}`);\n            }\n        }\n    }[\"TestOAuthPage.useEffect\"], [\n        status,\n        user\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 max-w-4xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold mb-6\",\n                children: \"\\uD83D\\uDD0D Google OAuth Test\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"\\uD83D\\uDD10 Authentication Status\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Status:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `px-2 py-1 rounded ${status === 'authenticated' ? 'bg-green-100 text-green-800' : status === 'loading' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`,\n                                                    children: status\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 42\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"User:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                user.email\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Session:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" Active (expires: \",\n                                                new Date(session.expires_at * 1000).toLocaleString(),\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 flex-wrap\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleGoogleSignIn,\n                                                    disabled: status === 'authenticated',\n                                                    children: \"Test Google OAuth\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleTestNewUserFlow,\n                                                    disabled: status === 'authenticated',\n                                                    variant: \"secondary\",\n                                                    children: \"Test New User Flow\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleSignOut,\n                                                    disabled: status !== 'authenticated',\n                                                    variant: \"outline\",\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleRefresh,\n                                                    variant: \"outline\",\n                                                    children: \"Refresh Auth\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"\\uD83D\\uDD04 Session Persistence Test\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: \"Test session persistence after external navigation\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: simulateStripeNavigation,\n                                            variant: \"outline\",\n                                            children: \"Simulate Stripe Navigation\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"This simulates navigating to Stripe and back. After clicking, refresh the page to test session restoration.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"\\uD83C\\uDF6A Cookie Information\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: checkCookies,\n                                            variant: \"outline\",\n                                            children: \"Refresh Cookies\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40\",\n                                            children: cookieInfo || 'No cookie information available'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"\\uD83D\\uDCBE Local Storage\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: checkStorage,\n                                            variant: \"outline\",\n                                            children: \"Refresh Storage\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40\",\n                                            children: storageInfo || 'No storage information available'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"\\uD83D\\uDCDD Test Log\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: clearLogs,\n                                            variant: \"outline\",\n                                            children: \"Clear Log\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-60\",\n                                            children: logs.join('\\n') || 'No logs yet'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/test-oauth/page.tsx\n");

/***/ })

};
;