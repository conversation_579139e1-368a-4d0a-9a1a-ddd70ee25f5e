"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "utils";
exports.ids = ["utils"];
exports.modules = {

/***/ "(rsc)/./src/lib/LanguageContext.tsx":
/*!*************************************!*\
  !*** ./src/lib/LanguageContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),
/* harmony export */   useLanguage: () => (/* binding */ useLanguage)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useLanguage = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/src/SRVXP/src/lib/LanguageContext.tsx",
"useLanguage",
);const LanguageProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/src/SRVXP/src/lib/LanguageContext.tsx",
"LanguageProvider",
);

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminClient: () => (/* binding */ createAdminClient),\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/../SRVXP_dependencies/node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/../SRVXP_dependencies/node_modules/next/dist/api/headers.js\");\n\n\nconst supabaseUrl = \"https://tfvswgreslsbctjrvdbd.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRmdnN3Z3Jlc2xzYmN0anJ2ZGJkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIzMjEyODUsImV4cCI6MjA1Nzg5NzI4NX0.5bNos8JS_77EUjWOxIGHpgNaYE28qQvO2g4qj0wRWiU\" || 0;\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';\nconst createClient = async ()=>{\n    let cookieStore;\n    try {\n        cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    } catch (error) {\n        console.error('Failed to access cookies:', error);\n        // Fallback: create client without cookie access\n        return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(supabaseUrl, supabaseAnonKey, {\n            cookies: {\n                getAll () {\n                    return [];\n                },\n                setAll () {\n                // No-op if cookies are not available\n                }\n            }\n        });\n    }\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            getAll () {\n                try {\n                    return cookieStore.getAll().map(({ name, value })=>({\n                            name,\n                            value\n                        }));\n                } catch (error) {\n                    console.error('Error accessing cookies:', error);\n                    return [];\n                }\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>{\n                        // Enhanced cookie options for better persistence\n                        const enhancedOptions = {\n                            ...options,\n                            httpOnly: false,\n                            secure: \"development\" === 'production',\n                            sameSite: 'lax',\n                            maxAge: options?.maxAge || 60 * 60 * 24 * 7,\n                            path: '/'\n                        };\n                        cookieStore.set(name, value, enhancedOptions);\n                    });\n                } catch (error) {\n                    console.error('Error setting cookies:', error);\n                }\n            }\n        }\n    });\n};\n// Create a service role client for admin operations\nconst createAdminClient = async ()=>{\n    // Only create admin client if service role key is available\n    if (!supabaseServiceRoleKey) {\n        console.error('SUPABASE_SERVICE_ROLE_KEY is not defined');\n        // Return regular client as fallback\n        return createClient();\n    }\n    let cookieStore;\n    try {\n        cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    } catch (error) {\n        console.error('Failed to access cookies in admin client:', error);\n        // Fallback: create admin client without cookie access\n        return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(supabaseUrl, supabaseServiceRoleKey, {\n            cookies: {\n                getAll () {\n                    return [];\n                },\n                setAll () {\n                // No-op if cookies are not available\n                }\n            }\n        });\n    }\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(supabaseUrl, supabaseServiceRoleKey, {\n        cookies: {\n            getAll () {\n                try {\n                    return cookieStore.getAll().map(({ name, value })=>({\n                            name,\n                            value\n                        }));\n                } catch (error) {\n                    console.error('Error accessing cookies:', error);\n                    return [];\n                }\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>{\n                        // Enhanced cookie options for admin client\n                        const enhancedOptions = {\n                            ...options,\n                            httpOnly: false,\n                            secure: \"development\" === 'production',\n                            sameSite: 'lax',\n                            maxAge: options?.maxAge || 60 * 60 * 24 * 7,\n                            path: '/'\n                        };\n                        cookieStore.set(name, value, enhancedOptions);\n                    });\n                } catch (error) {\n                    console.error('Error setting cookies:', error);\n                }\n            }\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWtEO0FBQ1o7QUFFdEMsTUFBTUUsY0FBY0MsMENBQW9DLElBQUksQ0FBaUM7QUFDN0YsTUFBTUcsa0JBQWtCSCxrTkFBeUMsSUFBSSxDQUFpQjtBQUN0RixNQUFNSyx5QkFBeUJMLFFBQVFDLEdBQUcsQ0FBQ0sseUJBQXlCLElBQUk7QUFFakUsTUFBTUMsZUFBZTtJQUMxQixJQUFJQztJQUVKLElBQUk7UUFDRkEsY0FBYyxNQUFNVixxREFBT0E7SUFDN0IsRUFBRSxPQUFPVyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyw2QkFBNkJBO1FBQzNDLGdEQUFnRDtRQUNoRCxPQUFPWixpRUFBa0JBLENBQ3ZCRSxhQUNBSSxpQkFDQTtZQUNFTCxTQUFTO2dCQUNQYTtvQkFDRSxPQUFPLEVBQUU7Z0JBQ1g7Z0JBQ0FDO2dCQUNFLHFDQUFxQztnQkFDdkM7WUFDRjtRQUNGO0lBRUo7SUFFQSxPQUFPZixpRUFBa0JBLENBQ3ZCRSxhQUNBSSxpQkFDQTtRQUNFTCxTQUFTO1lBQ1BhO2dCQUNFLElBQUk7b0JBQ0YsT0FBT0gsWUFBWUcsTUFBTSxHQUFHRSxHQUFHLENBQUMsQ0FBQyxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFNOzRCQUNwREQ7NEJBQ0FDO3dCQUNGO2dCQUNGLEVBQUUsT0FBT04sT0FBTztvQkFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7b0JBQzFDLE9BQU8sRUFBRTtnQkFDWDtZQUNGO1lBQ0FHLFFBQU9JLFlBQVk7Z0JBQ2pCLElBQUk7b0JBQ0ZBLGFBQWFDLE9BQU8sQ0FBQyxDQUFDLEVBQUVILElBQUksRUFBRUMsS0FBSyxFQUFFRyxPQUFPLEVBQUU7d0JBQzVDLGlEQUFpRDt3QkFDakQsTUFBTUMsa0JBQWtCOzRCQUN0QixHQUFHRCxPQUFPOzRCQUNWRSxVQUFVOzRCQUNWQyxRQUFRckIsa0JBQXlCOzRCQUNqQ3NCLFVBQVU7NEJBQ1ZDLFFBQVFMLFNBQVNLLFVBQVUsS0FBSyxLQUFLLEtBQUs7NEJBQzFDQyxNQUFNO3dCQUNSO3dCQUVBaEIsWUFBWWlCLEdBQUcsQ0FBQ1gsTUFBTUMsT0FBT0k7b0JBQy9CO2dCQUNGLEVBQUUsT0FBT1YsT0FBTztvQkFDZEMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7Z0JBQzFDO1lBQ0Y7UUFDRjtJQUNGO0FBRUosRUFBQztBQUVELG9EQUFvRDtBQUM3QyxNQUFNaUIsb0JBQW9CO0lBQy9CLDREQUE0RDtJQUM1RCxJQUFJLENBQUNyQix3QkFBd0I7UUFDM0JLLFFBQVFELEtBQUssQ0FBQztRQUNkLG9DQUFvQztRQUNwQyxPQUFPRjtJQUNUO0lBRUEsSUFBSUM7SUFFSixJQUFJO1FBQ0ZBLGNBQWMsTUFBTVYscURBQU9BO0lBQzdCLEVBQUUsT0FBT1csT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsNkNBQTZDQTtRQUMzRCxzREFBc0Q7UUFDdEQsT0FBT1osaUVBQWtCQSxDQUN2QkUsYUFDQU0sd0JBQ0E7WUFDRVAsU0FBUztnQkFDUGE7b0JBQ0UsT0FBTyxFQUFFO2dCQUNYO2dCQUNBQztnQkFDRSxxQ0FBcUM7Z0JBQ3ZDO1lBQ0Y7UUFDRjtJQUVKO0lBRUEsT0FBT2YsaUVBQWtCQSxDQUN2QkUsYUFDQU0sd0JBQ0E7UUFDRVAsU0FBUztZQUNQYTtnQkFDRSxJQUFJO29CQUNGLE9BQU9ILFlBQVlHLE1BQU0sR0FBR0UsR0FBRyxDQUFDLENBQUMsRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBTTs0QkFDcEREOzRCQUNBQzt3QkFDRjtnQkFDRixFQUFFLE9BQU9OLE9BQU87b0JBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO29CQUMxQyxPQUFPLEVBQUU7Z0JBQ1g7WUFDRjtZQUNBRyxRQUFPSSxZQUFZO2dCQUNqQixJQUFJO29CQUNGQSxhQUFhQyxPQUFPLENBQUMsQ0FBQyxFQUFFSCxJQUFJLEVBQUVDLEtBQUssRUFBRUcsT0FBTyxFQUFFO3dCQUM1QywyQ0FBMkM7d0JBQzNDLE1BQU1DLGtCQUFrQjs0QkFDdEIsR0FBR0QsT0FBTzs0QkFDVkUsVUFBVTs0QkFDVkMsUUFBUXJCLGtCQUF5Qjs0QkFDakNzQixVQUFVOzRCQUNWQyxRQUFRTCxTQUFTSyxVQUFVLEtBQUssS0FBSyxLQUFLOzRCQUMxQ0MsTUFBTTt3QkFDUjt3QkFFQWhCLFlBQVlpQixHQUFHLENBQUNYLE1BQU1DLE9BQU9JO29CQUMvQjtnQkFDRixFQUFFLE9BQU9WLE9BQU87b0JBQ2RDLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO2dCQUMxQztZQUNGO1FBQ0Y7SUFDRjtBQUVKLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWlqZXR0ZS9zcmMvU1JWWFAvc3JjL2xpYi9zdXBhYmFzZS9zZXJ2ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlU2VydmVyQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3NzcidcbmltcG9ydCB7IGNvb2tpZXMgfSBmcm9tICduZXh0L2hlYWRlcnMnXG5cbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIHx8ICdodHRwczovL3BsYWNlaG9sZGVyLnN1cGFiYXNlLmNvJ1xuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkgfHwgJ3BsYWNlaG9sZGVyLWtleSdcbmNvbnN0IHN1cGFiYXNlU2VydmljZVJvbGVLZXkgPSBwcm9jZXNzLmVudi5TVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIHx8ICcnXG5cbmV4cG9ydCBjb25zdCBjcmVhdGVDbGllbnQgPSBhc3luYyAoKSA9PiB7XG4gIGxldCBjb29raWVTdG9yZVxuICBcbiAgdHJ5IHtcbiAgICBjb29raWVTdG9yZSA9IGF3YWl0IGNvb2tpZXMoKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBhY2Nlc3MgY29va2llczonLCBlcnJvcilcbiAgICAvLyBGYWxsYmFjazogY3JlYXRlIGNsaWVudCB3aXRob3V0IGNvb2tpZSBhY2Nlc3NcbiAgICByZXR1cm4gY3JlYXRlU2VydmVyQ2xpZW50KFxuICAgICAgc3VwYWJhc2VVcmwsXG4gICAgICBzdXBhYmFzZUFub25LZXksXG4gICAgICB7XG4gICAgICAgIGNvb2tpZXM6IHtcbiAgICAgICAgICBnZXRBbGwoKSB7XG4gICAgICAgICAgICByZXR1cm4gW11cbiAgICAgICAgICB9LFxuICAgICAgICAgIHNldEFsbCgpIHtcbiAgICAgICAgICAgIC8vIE5vLW9wIGlmIGNvb2tpZXMgYXJlIG5vdCBhdmFpbGFibGVcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfVxuICAgIClcbiAgfVxuXG4gIHJldHVybiBjcmVhdGVTZXJ2ZXJDbGllbnQoXG4gICAgc3VwYWJhc2VVcmwsXG4gICAgc3VwYWJhc2VBbm9uS2V5LFxuICAgIHtcbiAgICAgIGNvb2tpZXM6IHtcbiAgICAgICAgZ2V0QWxsKCkge1xuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gY29va2llU3RvcmUuZ2V0QWxsKCkubWFwKCh7IG5hbWUsIHZhbHVlIH0pID0+ICh7XG4gICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgIHZhbHVlLFxuICAgICAgICAgICAgfSkpXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFjY2Vzc2luZyBjb29raWVzOicsIGVycm9yKVxuICAgICAgICAgICAgcmV0dXJuIFtdXG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBzZXRBbGwoY29va2llc1RvU2V0KSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvb2tpZXNUb1NldC5mb3JFYWNoKCh7IG5hbWUsIHZhbHVlLCBvcHRpb25zIH0pID0+IHtcbiAgICAgICAgICAgICAgLy8gRW5oYW5jZWQgY29va2llIG9wdGlvbnMgZm9yIGJldHRlciBwZXJzaXN0ZW5jZVxuICAgICAgICAgICAgICBjb25zdCBlbmhhbmNlZE9wdGlvbnMgPSB7XG4gICAgICAgICAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgICAgICAgICBodHRwT25seTogZmFsc2UsIC8vIEFsbG93IGNsaWVudCBhY2Nlc3NcbiAgICAgICAgICAgICAgICBzZWN1cmU6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicsXG4gICAgICAgICAgICAgICAgc2FtZVNpdGU6ICdsYXgnIGFzIGNvbnN0LCAvLyBCZXR0ZXIgZm9yIGV4dGVybmFsIG5hdmlnYXRpb25cbiAgICAgICAgICAgICAgICBtYXhBZ2U6IG9wdGlvbnM/Lm1heEFnZSB8fCA2MCAqIDYwICogMjQgKiA3LCAvLyA3IGRheXMgZGVmYXVsdFxuICAgICAgICAgICAgICAgIHBhdGg6ICcvJyxcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgY29va2llU3RvcmUuc2V0KG5hbWUsIHZhbHVlLCBlbmhhbmNlZE9wdGlvbnMpXG4gICAgICAgICAgICB9KVxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZXR0aW5nIGNvb2tpZXM6JywgZXJyb3IpXG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICB9XG4gIClcbn1cblxuLy8gQ3JlYXRlIGEgc2VydmljZSByb2xlIGNsaWVudCBmb3IgYWRtaW4gb3BlcmF0aW9uc1xuZXhwb3J0IGNvbnN0IGNyZWF0ZUFkbWluQ2xpZW50ID0gYXN5bmMgKCkgPT4ge1xuICAvLyBPbmx5IGNyZWF0ZSBhZG1pbiBjbGllbnQgaWYgc2VydmljZSByb2xlIGtleSBpcyBhdmFpbGFibGVcbiAgaWYgKCFzdXBhYmFzZVNlcnZpY2VSb2xlS2V5KSB7XG4gICAgY29uc29sZS5lcnJvcignU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSBpcyBub3QgZGVmaW5lZCcpXG4gICAgLy8gUmV0dXJuIHJlZ3VsYXIgY2xpZW50IGFzIGZhbGxiYWNrXG4gICAgcmV0dXJuIGNyZWF0ZUNsaWVudCgpXG4gIH1cblxuICBsZXQgY29va2llU3RvcmVcbiAgXG4gIHRyeSB7XG4gICAgY29va2llU3RvcmUgPSBhd2FpdCBjb29raWVzKClcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gYWNjZXNzIGNvb2tpZXMgaW4gYWRtaW4gY2xpZW50OicsIGVycm9yKVxuICAgIC8vIEZhbGxiYWNrOiBjcmVhdGUgYWRtaW4gY2xpZW50IHdpdGhvdXQgY29va2llIGFjY2Vzc1xuICAgIHJldHVybiBjcmVhdGVTZXJ2ZXJDbGllbnQoXG4gICAgICBzdXBhYmFzZVVybCxcbiAgICAgIHN1cGFiYXNlU2VydmljZVJvbGVLZXksXG4gICAgICB7XG4gICAgICAgIGNvb2tpZXM6IHtcbiAgICAgICAgICBnZXRBbGwoKSB7XG4gICAgICAgICAgICByZXR1cm4gW11cbiAgICAgICAgICB9LFxuICAgICAgICAgIHNldEFsbCgpIHtcbiAgICAgICAgICAgIC8vIE5vLW9wIGlmIGNvb2tpZXMgYXJlIG5vdCBhdmFpbGFibGVcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfVxuICAgIClcbiAgfVxuXG4gIHJldHVybiBjcmVhdGVTZXJ2ZXJDbGllbnQoXG4gICAgc3VwYWJhc2VVcmwsXG4gICAgc3VwYWJhc2VTZXJ2aWNlUm9sZUtleSxcbiAgICB7XG4gICAgICBjb29raWVzOiB7XG4gICAgICAgIGdldEFsbCgpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgcmV0dXJuIGNvb2tpZVN0b3JlLmdldEFsbCgpLm1hcCgoeyBuYW1lLCB2YWx1ZSB9KSA9PiAoe1xuICAgICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgICB2YWx1ZSxcbiAgICAgICAgICAgIH0pKVxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhY2Nlc3NpbmcgY29va2llczonLCBlcnJvcilcbiAgICAgICAgICAgIHJldHVybiBbXVxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgc2V0QWxsKGNvb2tpZXNUb1NldCkge1xuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb29raWVzVG9TZXQuZm9yRWFjaCgoeyBuYW1lLCB2YWx1ZSwgb3B0aW9ucyB9KSA9PiB7XG4gICAgICAgICAgICAgIC8vIEVuaGFuY2VkIGNvb2tpZSBvcHRpb25zIGZvciBhZG1pbiBjbGllbnRcbiAgICAgICAgICAgICAgY29uc3QgZW5oYW5jZWRPcHRpb25zID0ge1xuICAgICAgICAgICAgICAgIC4uLm9wdGlvbnMsXG4gICAgICAgICAgICAgICAgaHR0cE9ubHk6IGZhbHNlLFxuICAgICAgICAgICAgICAgIHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJyxcbiAgICAgICAgICAgICAgICBzYW1lU2l0ZTogJ2xheCcgYXMgY29uc3QsXG4gICAgICAgICAgICAgICAgbWF4QWdlOiBvcHRpb25zPy5tYXhBZ2UgfHwgNjAgKiA2MCAqIDI0ICogNyxcbiAgICAgICAgICAgICAgICBwYXRoOiAnLycsXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIGNvb2tpZVN0b3JlLnNldChuYW1lLCB2YWx1ZSwgZW5oYW5jZWRPcHRpb25zKVxuICAgICAgICAgICAgfSlcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2V0dGluZyBjb29raWVzOicsIGVycm9yKVxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgfVxuICApXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlU2VydmVyQ2xpZW50IiwiY29va2llcyIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwic3VwYWJhc2VTZXJ2aWNlUm9sZUtleSIsIlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkiLCJjcmVhdGVDbGllbnQiLCJjb29raWVTdG9yZSIsImVycm9yIiwiY29uc29sZSIsImdldEFsbCIsInNldEFsbCIsIm1hcCIsIm5hbWUiLCJ2YWx1ZSIsImNvb2tpZXNUb1NldCIsImZvckVhY2giLCJvcHRpb25zIiwiZW5oYW5jZWRPcHRpb25zIiwiaHR0cE9ubHkiLCJzZWN1cmUiLCJzYW1lU2l0ZSIsIm1heEFnZSIsInBhdGgiLCJzZXQiLCJjcmVhdGVBZG1pbkNsaWVudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/AuthContext.tsx":
/*!*********************************!*\
  !*** ./src/lib/AuthContext.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\n// Create context with default values\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    session: null,\n    status: \"loading\",\n    signIn: async ()=>({\n            success: false\n        }),\n    signInWithGoogle: async ()=>({\n            success: false\n        }),\n    signOut: async ()=>{},\n    refresh: async ()=>{}\n});\n// Custom hook to use auth context\nconst useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n// Cache keys\nconst AUTH_CACHE_KEY = \"auth_cache\";\nconst AUTH_CACHE_EXPIRY_KEY = \"auth_cache_expiry\";\nconst SESSION_BACKUP_KEY = \"session_backup\";\nconst EXTERNAL_RETURN_KEY = \"external_return_detected\";\nconst CACHE_DURATION = 30 * 60 * 1000; // Increased to 30 minutes\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"loading\");\n    // Rate limiting for refresh function\n    const lastRefreshTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const REFRESH_DEBOUNCE_MS = 3000; // 3 seconds between refreshes\n    // Function to detect if user returned from external site\n    const detectExternalReturn = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[detectExternalReturn]\": ()=>{\n            if (true) return false;\n            // Check if we have a session backup but no current session\n            const hasSessionBackup = localStorage.getItem(SESSION_BACKUP_KEY);\n            const hasCurrentSession = localStorage.getItem(AUTH_CACHE_KEY);\n            // Check if URL contains return parameters from Stripe or other external services\n            const urlParams = new URLSearchParams(window.location.search);\n            const isStripeReturn = urlParams.has('session_id') || window.location.pathname.includes('/compte/abonnement') || document.referrer.includes('stripe.com');\n            // Check if user was marked as returning from external site\n            const wasExternal = sessionStorage.getItem(EXTERNAL_RETURN_KEY);\n            // Check for OAuth success flag (indicates recent OAuth login)\n            const hasOAuthSuccess = document.cookie.includes('oauth_success=true');\n            const hasOAuthNewUser = document.cookie.includes('oauth_new_user=true');\n            return !!(hasSessionBackup && (!hasCurrentSession || isStripeReturn || wasExternal || hasOAuthSuccess || hasOAuthNewUser));\n        }\n    }[\"AuthProvider.useCallback[detectExternalReturn]\"], []);\n    // Function to backup session before external navigation\n    const backupSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[backupSession]\": (session)=>{\n            if (true) return;\n            localStorage.setItem(SESSION_BACKUP_KEY, JSON.stringify({\n                session,\n                timestamp: Date.now(),\n                user: session.user\n            }));\n        }\n    }[\"AuthProvider.useCallback[backupSession]\"], []);\n    // Function to restore session from backup\n    const restoreSessionFromBackup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[restoreSessionFromBackup]\": async ()=>{\n            if (true) return null;\n            const backup = localStorage.getItem(SESSION_BACKUP_KEY);\n            if (!backup) return null;\n            try {\n                const { session: backedUpSession, timestamp } = JSON.parse(backup);\n                // Check if backup is not too old (within 2 hours)\n                if (Date.now() - timestamp > 2 * 60 * 60 * 1000) {\n                    localStorage.removeItem(SESSION_BACKUP_KEY);\n                    return null;\n                }\n                // Try to refresh the session using the backed up access token\n                if (backedUpSession?.access_token) {\n                    console.log('Attempting to restore session from backup...');\n                    // Set the session manually to trigger Supabase to use it\n                    const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.setSession({\n                        access_token: backedUpSession.access_token,\n                        refresh_token: backedUpSession.refresh_token\n                    });\n                    if (!error && data.session) {\n                        console.log('Successfully restored session from backup');\n                        localStorage.removeItem(SESSION_BACKUP_KEY);\n                        return data.session;\n                    }\n                }\n            } catch (error) {\n                console.error('Error restoring session from backup:', error);\n                localStorage.removeItem(SESSION_BACKUP_KEY);\n            }\n            return null;\n        }\n    }[\"AuthProvider.useCallback[restoreSessionFromBackup]\"], []);\n    // Enhanced refresh function with rate limiting and external return detection\n    const refresh = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refresh]\": async ()=>{\n            try {\n                // Check if this is a new OAuth user first\n                const isNewOAuthUser =  false && 0;\n                // Rate limiting - prevent too frequent refreshes (but allow for new OAuth users)\n                const now = Date.now();\n                if (!isNewOAuthUser && now - lastRefreshTime.current < REFRESH_DEBOUNCE_MS) {\n                    console.log('Auth refresh rate limited, skipping');\n                    return;\n                }\n                lastRefreshTime.current = now;\n                console.log(`AuthContext: Starting auth refresh... ${isNewOAuthUser ? '(new OAuth user)' : ''}`);\n                setStatus(\"loading\");\n                // For new OAuth users, try multiple attempts with delays\n                let sessionAttempts = isNewOAuthUser ? 3 : 1;\n                let sessionData = null;\n                let sessionError = null;\n                for(let attempt = 1; attempt <= sessionAttempts; attempt++){\n                    console.log(`AuthContext: Session check attempt ${attempt}/${sessionAttempts}`);\n                    const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                    if (error) {\n                        sessionError = error;\n                        console.error(`AuthContext: Session error (attempt ${attempt}):`, error);\n                    } else if (data?.session) {\n                        sessionData = data;\n                        sessionError = null;\n                        break;\n                    }\n                    if (attempt < sessionAttempts) {\n                        console.log('Waiting before next session check attempt...');\n                        await new Promise({\n                            \"AuthProvider.useCallback[refresh]\": (resolve)=>setTimeout(resolve, 1000)\n                        }[\"AuthProvider.useCallback[refresh]\"]);\n                    }\n                }\n                if (sessionError && !sessionData) {\n                    console.error('AuthContext: Final session error:', sessionError);\n                    throw sessionError;\n                }\n                if (sessionData?.session) {\n                    console.log(`AuthContext: Session found for user: ${sessionData.session.user.email} ${isNewOAuthUser ? '(new OAuth user)' : ''}`);\n                    setSession(sessionData.session);\n                    setUser(sessionData.session.user);\n                    setStatus(\"authenticated\");\n                    // Backup session for potential external navigation recovery\n                    backupSession(sessionData.session);\n                    // Cache the auth state for faster subsequent loads\n                    if (false) {}\n                } else {\n                    console.log('AuthContext: No session found');\n                    // Check if user is returning from external site and try backup restoration\n                    const isExternalReturn = detectExternalReturn();\n                    if (isExternalReturn) {\n                        console.log('External return detected, attempting session restoration...');\n                        sessionStorage.removeItem(EXTERNAL_RETURN_KEY);\n                        // Try to restore from backup\n                        const restoredSession = await restoreSessionFromBackup();\n                        if (restoredSession) {\n                            console.log('Session restored from backup');\n                            setSession(restoredSession);\n                            setUser(restoredSession.user);\n                            setStatus(\"authenticated\");\n                            // Update cache\n                            if (false) {}\n                            return;\n                        }\n                    }\n                    // No session found, set unauthenticated state\n                    setSession(null);\n                    setUser(null);\n                    setStatus(\"unauthenticated\");\n                    // Clear cache and backup\n                    if (false) {}\n                }\n            } catch (error) {\n                console.error(\"AuthContext: Error refreshing auth state:\", error);\n                // On error, assume not authenticated\n                setStatus(\"unauthenticated\");\n                setSession(null);\n                setUser(null);\n                // Clear cache on error\n                if (false) {}\n            }\n        }\n    }[\"AuthProvider.useCallback[refresh]\"], [\n        detectExternalReturn,\n        restoreSessionFromBackup,\n        backupSession\n    ]);\n    // Sign in with email and password\n    const signIn = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[signIn]\": async (email, password)=>{\n            try {\n                const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n                    email,\n                    password\n                });\n                if (error) {\n                    throw error;\n                }\n                if (data.session) {\n                    // Update state with new session\n                    setSession(data.session);\n                    setUser(data.session.user);\n                    setStatus(\"authenticated\");\n                    // Backup session for external navigation\n                    backupSession(data.session);\n                    // Cache the auth state\n                    localStorage.setItem(AUTH_CACHE_KEY, JSON.stringify({\n                        session: data.session,\n                        user: data.session.user\n                    }));\n                    localStorage.setItem(AUTH_CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());\n                    // Clear any old auth-related flags from sessionStorage\n                    const keysToRemove = [];\n                    for(let i = 0; i < sessionStorage.length; i++){\n                        const key = sessionStorage.key(i);\n                        if (key && (key.includes(\"auth\") || key.includes(\"logout\") || key.includes(\"supabase\"))) {\n                            keysToRemove.push(key);\n                        }\n                    }\n                    keysToRemove.forEach({\n                        \"AuthProvider.useCallback[signIn]\": (key)=>sessionStorage.removeItem(key)\n                    }[\"AuthProvider.useCallback[signIn]\"]);\n                }\n                return {\n                    success: true\n                };\n            } catch (error) {\n                console.error(\"Sign in error:\", error);\n                return {\n                    success: false,\n                    error: error\n                };\n            }\n        }\n    }[\"AuthProvider.useCallback[signIn]\"], [\n        backupSession\n    ]);\n    // Sign in with Google\n    const signInWithGoogle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[signInWithGoogle]\": async ()=>{\n            try {\n                // Get the current origin to ensure correct redirect URL\n                const origin = window.location.origin;\n                // Create the callback URL with explicit dashboard redirect\n                const callbackUrl = `${origin}/auth/callback?redirectTo=/dashboard`;\n                console.log(`Setting up Google auth with callback URL: ${callbackUrl}`);\n                const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOAuth({\n                    provider: 'google',\n                    options: {\n                        redirectTo: callbackUrl\n                    }\n                });\n                if (error) {\n                    throw error;\n                }\n                return {\n                    success: true\n                };\n            } catch (error) {\n                console.error(\"Google sign in error:\", error);\n                return {\n                    success: false,\n                    error: error\n                };\n            }\n        }\n    }[\"AuthProvider.useCallback[signInWithGoogle]\"], []);\n    // Enhanced sign out function\n    const signOut = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[signOut]\": async (redirectUrl = \"/\")=>{\n            try {\n                // Sign out with Supabase\n                await _supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n                // Clear auth state\n                setSession(null);\n                setUser(null);\n                setStatus(\"unauthenticated\");\n                // Clear all auth-related storage\n                localStorage.removeItem(AUTH_CACHE_KEY);\n                localStorage.removeItem(AUTH_CACHE_EXPIRY_KEY);\n                localStorage.removeItem(SESSION_BACKUP_KEY);\n                localStorage.removeItem(\"supabase.auth.token\");\n                // Reset theme to system default when logging out\n                localStorage.removeItem(\"theme\");\n                // Clear user-specific preferences cache\n                if (user?.id) {\n                    localStorage.removeItem(`user_preferences_${user.id}`);\n                }\n                // Set a flag in session storage to indicate logout\n                sessionStorage.setItem(\"isLoggedOut\", \"true\");\n                // Force page refresh for clean state\n                window.location.href = redirectUrl;\n            } catch (error) {\n                console.error(\"Sign out error:\", error);\n                // Even if there was an error, try to redirect and clean up state\n                setSession(null);\n                setUser(null);\n                setStatus(\"unauthenticated\");\n                window.location.href = redirectUrl;\n            }\n        }\n    }[\"AuthProvider.useCallback[signOut]\"], [\n        user\n    ]);\n    // Setup auth state with enhanced external navigation detection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Initial auth check\n            refresh();\n            // Setup auth state listener for changes\n            const { data: authListener } = _supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": (event, session)=>{\n                    console.log(`AuthContext: Auth state change - ${event}`, {\n                        hasSession: !!session,\n                        userEmail: session?.user?.email\n                    });\n                    if (event === \"SIGNED_IN\" && session) {\n                        setSession(session);\n                        setUser(session.user);\n                        setStatus(\"authenticated\");\n                        // Backup session for external navigation\n                        backupSession(session);\n                        // Update cache\n                        localStorage.setItem(AUTH_CACHE_KEY, JSON.stringify({\n                            session,\n                            user: session.user\n                        }));\n                        localStorage.setItem(AUTH_CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());\n                        // Clear OAuth success flag if it exists\n                        if (false) {}\n                    } else if (event === \"SIGNED_OUT\") {\n                        setSession(null);\n                        setUser(null);\n                        setStatus(\"unauthenticated\");\n                        // Clear cache and backup\n                        localStorage.removeItem(AUTH_CACHE_KEY);\n                        localStorage.removeItem(AUTH_CACHE_EXPIRY_KEY);\n                        localStorage.removeItem(SESSION_BACKUP_KEY);\n                    } else if (event === \"TOKEN_REFRESHED\" && session) {\n                        console.log(\"AuthContext: Token refreshed successfully\");\n                        setSession(session);\n                        setUser(session.user);\n                        setStatus(\"authenticated\");\n                        // Update backup and cache with refreshed session\n                        backupSession(session);\n                        localStorage.setItem(AUTH_CACHE_KEY, JSON.stringify({\n                            session,\n                            user: session.user\n                        }));\n                        localStorage.setItem(AUTH_CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Enhanced visibility change handler for external returns\n            const handleVisibilityChange = {\n                \"AuthProvider.useEffect.handleVisibilityChange\": ()=>{\n                    if (document.visibilityState === \"visible\") {\n                        // Mark as potential external return\n                        sessionStorage.setItem(EXTERNAL_RETURN_KEY, \"true\");\n                        // Refresh auth state\n                        setTimeout({\n                            \"AuthProvider.useEffect.handleVisibilityChange\": ()=>{\n                                refresh();\n                            }\n                        }[\"AuthProvider.useEffect.handleVisibilityChange\"], 100); // Small delay to ensure page is fully visible\n                    }\n                }\n            }[\"AuthProvider.useEffect.handleVisibilityChange\"];\n            // Enhanced focus handler for external returns\n            const handleWindowFocus = {\n                \"AuthProvider.useEffect.handleWindowFocus\": ()=>{\n                    // Mark as potential external return\n                    sessionStorage.setItem(EXTERNAL_RETURN_KEY, \"true\");\n                    // Refresh auth state\n                    setTimeout({\n                        \"AuthProvider.useEffect.handleWindowFocus\": ()=>{\n                            refresh();\n                        }\n                    }[\"AuthProvider.useEffect.handleWindowFocus\"], 100);\n                }\n            }[\"AuthProvider.useEffect.handleWindowFocus\"];\n            // Add event listeners\n            document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n            window.addEventListener(\"focus\", handleWindowFocus);\n            // Cleanup on unmount\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    authListener.subscription.unsubscribe();\n                    document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n                    window.removeEventListener(\"focus\", handleWindowFocus);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        refresh,\n        backupSession\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            session,\n            status,\n            signIn,\n            signInWithGoogle,\n            signOut,\n            refresh\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/lib/AuthContext.tsx\",\n        lineNumber: 494,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/FamilyMembersContext.tsx":
/*!******************************************!*\
  !*** ./src/lib/FamilyMembersContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FamilyMembersProvider: () => (/* binding */ FamilyMembersProvider),\n/* harmony export */   useFamilyMembers: () => (/* binding */ useFamilyMembers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/lib/AuthContext.tsx\");\n/* harmony import */ var _family_members_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./family-members/api */ \"(ssr)/./src/lib/family-members/api.ts\");\n/* __next_internal_client_entry_do_not_use__ FamilyMembersProvider,useFamilyMembers auto */ \n\n\n\n// Create initial data\nconst initialFamilyMembers = [\n    {\n        id: 1,\n        firstName: \"Félix\",\n        lastName: \"Tremblay\",\n        healthCard: \"TREF\",\n        birthDate: new Date(1985, 3, 12),\n        editing: false\n    },\n    {\n        id: 2,\n        firstName: \"Marie\",\n        lastName: \"Tremblay\",\n        healthCard: \"TREM\",\n        birthDate: new Date(1987, 8, 23),\n        editing: false\n    },\n    {\n        id: 3,\n        firstName: \"\",\n        lastName: \"\",\n        healthCard: \"\",\n        birthDate: undefined,\n        editing: false\n    },\n    {\n        id: 4,\n        firstName: \"\",\n        lastName: \"\",\n        healthCard: \"\",\n        birthDate: undefined,\n        editing: false\n    },\n    {\n        id: 5,\n        firstName: \"\",\n        lastName: \"\",\n        healthCard: \"\",\n        birthDate: undefined,\n        editing: false\n    }\n];\n// Create a default empty context value for SSR\nconst defaultContextValue = {\n    familyMembers: initialFamilyMembers,\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    setFamilyMembers: ()=>{},\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    updateFamilyMember: ()=>{},\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    updateTempFamilyMember: ()=>{},\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    toggleEditing: ()=>{},\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    saveMemberChanges: ()=>false,\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    deleteMember: ()=>{},\n    isLoading: false,\n    error: null\n};\n// Create the context with the default value\nconst FamilyMembersContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(defaultContextValue);\nfunction FamilyMembersProvider({ children }) {\n    const { user, status } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [familyMembers, setFamilyMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFamilyMembers);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Update actual family member data\n    const updateFamilyMember = (id, data)=>{\n        setFamilyMembers((prev)=>prev.map((member)=>member.id === id ? {\n                    ...member,\n                    ...data\n                } : member));\n    };\n    // Update temporary data during editing\n    const updateTempFamilyMember = (id, data)=>{\n        setFamilyMembers((prev)=>prev.map((member)=>member.id === id ? {\n                    ...member,\n                    tempFirstName: 'tempFirstName' in data ? data.tempFirstName : member.tempFirstName,\n                    tempLastName: 'tempLastName' in data ? data.tempLastName : member.tempLastName,\n                    tempHealthCard: 'tempHealthCard' in data ? data.tempHealthCard : member.tempHealthCard,\n                    tempBirthDate: 'tempBirthDate' in data ? data.tempBirthDate : member.tempBirthDate\n                } : member));\n    };\n    // Toggle editing mode for a member\n    const toggleEditing = (id)=>{\n        setFamilyMembers((prev)=>prev.map((member)=>{\n                if (member.id === id) {\n                    if (!member.editing) {\n                        // When starting to edit, initialize temp values\n                        return {\n                            ...member,\n                            editing: true,\n                            tempFirstName: member.firstName,\n                            tempLastName: member.lastName,\n                            tempHealthCard: member.healthCard,\n                            tempBirthDate: member.birthDate\n                        };\n                    } else {\n                        // When canceling edit, discard temp values\n                        return {\n                            ...member,\n                            editing: false,\n                            tempFirstName: undefined,\n                            tempLastName: undefined,\n                            tempHealthCard: undefined,\n                            tempBirthDate: undefined\n                        };\n                    }\n                }\n                return member;\n            }));\n    };\n    // Save changes from temp values to actual values\n    const saveMemberChanges = (id)=>{\n        // Validation - check if required fields are filled\n        const memberToSave = familyMembers.find((m)=>m.id === id);\n        if (!memberToSave) return false;\n        const firstName = memberToSave.tempFirstName !== undefined ? memberToSave.tempFirstName : memberToSave.firstName;\n        const lastName = memberToSave.tempLastName !== undefined ? memberToSave.tempLastName : memberToSave.lastName;\n        const healthCard = memberToSave.tempHealthCard !== undefined ? memberToSave.tempHealthCard : memberToSave.healthCard;\n        const birthDate = memberToSave.tempBirthDate !== undefined ? memberToSave.tempBirthDate : memberToSave.birthDate;\n        // Validate required fields\n        const isValid = !!firstName && !!lastName && !!healthCard && healthCard.length === 4 && !!birthDate;\n        // If validation fails, return false and don't save\n        if (!isValid) return false;\n        setFamilyMembers((prev)=>{\n            const updatedMembers = prev.map((member)=>{\n                if (member.id === id && member.editing) {\n                    // Apply temporary values to actual values\n                    const updatedMember = {\n                        ...member,\n                        editing: false,\n                        firstName: member.tempFirstName !== undefined ? member.tempFirstName : member.firstName,\n                        lastName: member.tempLastName !== undefined ? member.tempLastName : member.lastName,\n                        healthCard: member.tempHealthCard !== undefined ? member.tempHealthCard : member.healthCard,\n                        birthDate: member.tempBirthDate !== undefined ? member.tempBirthDate : member.birthDate,\n                        // Clear temp values\n                        tempFirstName: undefined,\n                        tempLastName: undefined,\n                        tempHealthCard: undefined,\n                        tempBirthDate: undefined\n                    };\n                    // Save to Supabase if user is authenticated\n                    if (user?.id && (updatedMember.firstName || updatedMember.lastName || updatedMember.healthCard || updatedMember.birthDate)) {\n                        (0,_family_members_api__WEBPACK_IMPORTED_MODULE_3__.saveFamilyMember)(user.id, updatedMember, member.id).then((result)=>{\n                            if (result.success && result.id) {\n                                // Update the member with the Supabase ID\n                                setFamilyMembers((current)=>current.map((m)=>m.id === member.id ? {\n                                            ...m,\n                                            supabaseId: result.id\n                                        } : m));\n                            }\n                        }).catch((err)=>console.error(\"Error saving member:\", err));\n                    }\n                    return updatedMember;\n                }\n                return member;\n            });\n            // Ensure we always have exactly 5 members after saving\n            if (updatedMembers.length < 5) {\n                // If we somehow have fewer than 5 members, add empty slots\n                const existingIds = updatedMembers.map((m)=>m.id);\n                for(let i = 1; i <= 5; i++){\n                    if (!existingIds.includes(i)) {\n                        updatedMembers.push({\n                            id: i,\n                            firstName: \"\",\n                            lastName: \"\",\n                            healthCard: \"\",\n                            birthDate: undefined,\n                            editing: false\n                        });\n                    }\n                }\n                // Sort by ID to ensure correct order\n                updatedMembers.sort((a, b)=>a.id - b.id);\n            }\n            return updatedMembers;\n        });\n        return true; // Return true if validation passes and save is initiated\n    };\n    // Function to save all family members to Supabase\n    // Load family members from Supabase when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FamilyMembersProvider.useEffect\": ()=>{\n            if (status === \"loading\") return;\n            if (status === \"authenticated\" && user?.id) {\n                setIsLoading(true);\n                setError(null);\n                (0,_family_members_api__WEBPACK_IMPORTED_MODULE_3__.getFamilyMembers)(user.id).then({\n                    \"FamilyMembersProvider.useEffect\": (members)=>{\n                        // The API now always returns exactly 5 slots\n                        setFamilyMembers(members);\n                    }\n                }[\"FamilyMembersProvider.useEffect\"]).catch({\n                    \"FamilyMembersProvider.useEffect\": (err)=>{\n                        console.error(\"Error loading family members:\", err);\n                        setError(new Error(\"Failed to load family members\"));\n                        // On error, fall back to empty members\n                        const emptyMembers = Array(5).fill(null).map({\n                            \"FamilyMembersProvider.useEffect.emptyMembers\": (_, index)=>({\n                                    id: index + 1,\n                                    firstName: \"\",\n                                    lastName: \"\",\n                                    healthCard: \"\",\n                                    birthDate: undefined,\n                                    editing: false\n                                })\n                        }[\"FamilyMembersProvider.useEffect.emptyMembers\"]);\n                        setFamilyMembers(emptyMembers);\n                    }\n                }[\"FamilyMembersProvider.useEffect\"]).finally({\n                    \"FamilyMembersProvider.useEffect\": ()=>{\n                        setIsLoading(false);\n                    }\n                }[\"FamilyMembersProvider.useEffect\"]);\n            } else if (status === \"unauthenticated\") {\n                // Not authenticated, use initial data\n                setFamilyMembers(initialFamilyMembers);\n                setIsLoading(false);\n            }\n        }\n    }[\"FamilyMembersProvider.useEffect\"], [\n        user?.id,\n        status\n    ]);\n    // Delete a family member\n    const deleteMember = (id)=>{\n        const member = familyMembers.find((m)=>m.id === id);\n        if (!member || !member.supabaseId) {\n            // If there's no Supabase ID, just reset the member data in the UI and close the form\n            setFamilyMembers((prev)=>prev.map((m)=>m.id === id ? {\n                        ...m,\n                        firstName: \"\",\n                        lastName: \"\",\n                        healthCard: \"\",\n                        birthDate: undefined,\n                        editing: false,\n                        tempFirstName: undefined,\n                        tempLastName: undefined,\n                        tempHealthCard: undefined,\n                        tempBirthDate: undefined\n                    } : m));\n            return;\n        }\n        // If we have a Supabase ID, delete from database\n        if (user?.id) {\n            (0,_family_members_api__WEBPACK_IMPORTED_MODULE_3__.deleteFamilyMember)(member.supabaseId).then((result)=>{\n                if (result.success) {\n                    // Reset the member data in the UI but keep the slot and close the form\n                    setFamilyMembers((prev)=>prev.map((m)=>m.id === id ? {\n                                ...m,\n                                firstName: \"\",\n                                lastName: \"\",\n                                healthCard: \"\",\n                                birthDate: undefined,\n                                supabaseId: undefined,\n                                editing: false,\n                                tempFirstName: undefined,\n                                tempLastName: undefined,\n                                tempHealthCard: undefined,\n                                tempBirthDate: undefined\n                            } : m));\n                }\n            }).catch((err)=>console.error(\"Error deleting member:\", err));\n        }\n    };\n    // Provide the actual implementation\n    const contextValue = {\n        familyMembers,\n        setFamilyMembers,\n        updateFamilyMember,\n        updateTempFamilyMember,\n        toggleEditing,\n        saveMemberChanges,\n        deleteMember,\n        isLoading,\n        error\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FamilyMembersContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/lib/FamilyMembersContext.tsx\",\n        lineNumber: 339,\n        columnNumber: 5\n    }, this);\n}\nfunction useFamilyMembers() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(FamilyMembersContext);\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/FamilyMembersContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/LanguageContext.tsx":
/*!*************************************!*\
  !*** ./src/lib/LanguageContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _translation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./translation-utils */ \"(ssr)/./src/lib/translation-utils.ts\");\n/* harmony import */ var _user_preferences_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./user-preferences-utils */ \"(ssr)/./src/lib/user-preferences-utils.ts\");\n/* harmony import */ var _supabase_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/lib/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useLanguage,LanguageProvider auto */ \n\n\n\n\n\n// Create context with default values\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    language: \"fr\",\n    translations: {},\n    setLanguage: ()=>{},\n    translate: (key)=>key,\n    isLoading: true\n});\n// Custom hook to use language context\nconst useLanguage = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\nfunction LanguageProvider({ children }) {\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"fr\");\n    const [translations, setTranslations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Rate limiting for language updates\n    const lastUpdateTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const UPDATE_DEBOUNCE_MS = 5000; // 5 seconds between updates\n    // Function to handle language change with rate limiting\n    const handleLanguageChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LanguageProvider.useCallback[handleLanguageChange]\": async (newLanguage)=>{\n            const now = Date.now();\n            if (now - lastUpdateTime.current < UPDATE_DEBOUNCE_MS) {\n                console.log('Language update rate limited, skipping server update');\n                setLanguageState(newLanguage);\n                if (false) {}\n                return;\n            }\n            lastUpdateTime.current = now;\n            setIsLoading(true);\n            console.log(`Language context: changing to ${newLanguage}`);\n            try {\n                // Update local state immediately for responsive UI\n                setLanguageState(newLanguage);\n                // Update local storage and document language\n                if (false) {}\n                // Load translations for the new language\n                try {\n                    const translationData = await (0,_translation_utils__WEBPACK_IMPORTED_MODULE_2__.loadTranslations)(newLanguage);\n                    setTranslations(translationData);\n                } catch (translationError) {\n                    console.error(`Failed to load ${newLanguage} translations:`, translationError);\n                    // Continue with empty translations rather than failing\n                    setTranslations({});\n                }\n                // Update user metadata in Supabase with error handling\n                if (user) {\n                    try {\n                        const { error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.updateUser({\n                            data: {\n                                language: newLanguage\n                            }\n                        });\n                        if (error) {\n                            console.error('Error updating user metadata with language preference:', error);\n                        // Don't throw here, allow the UI to continue working\n                        } else {\n                            console.log('Language context updated successfully to', newLanguage);\n                        }\n                    } catch (authError) {\n                        console.error('Error updating user metadata with language preference:', authError);\n                    // Don't throw here, allow the UI to continue working\n                    }\n                    // Also save to user preferences with error handling\n                    try {\n                        await (0,_user_preferences_utils__WEBPACK_IMPORTED_MODULE_3__.saveUserPreferences)(user.id, {\n                            language: newLanguage,\n                            theme: \"light\" // Provide default theme to satisfy interface\n                        });\n                    } catch (prefError) {\n                        console.error('Error saving language preference:', prefError);\n                    // Don't throw here, allow the UI to continue working\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error updating language:\", error);\n            // Keep the UI language change even if server update fails\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"LanguageProvider.useCallback[handleLanguageChange]\"], [\n        user\n    ]);\n    // Wrapper function for external use\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LanguageProvider.useCallback[setLanguage]\": (newLanguage)=>{\n            if (newLanguage !== language) {\n                handleLanguageChange(newLanguage);\n            }\n        }\n    }[\"LanguageProvider.useCallback[setLanguage]\"], [\n        language,\n        handleLanguageChange\n    ]);\n    // Initialize language on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            const initializeLanguage = {\n                \"LanguageProvider.useEffect.initializeLanguage\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        let initialLanguage = \"fr\"; // Default\n                        // Check localStorage first\n                        const savedLanguage = localStorage.getItem(\"language\");\n                        if (savedLanguage && (savedLanguage === \"fr\" || savedLanguage === \"en\")) {\n                            initialLanguage = savedLanguage;\n                            setLanguageState(savedLanguage);\n                        } else if (user) {\n                            // If user is logged in and no saved language, try to get from user metadata\n                            const userLanguage = user.user_metadata?.language;\n                            if (userLanguage && (userLanguage === \"fr\" || userLanguage === \"en\")) {\n                                initialLanguage = userLanguage;\n                                setLanguageState(userLanguage);\n                                localStorage.setItem(\"language\", userLanguage);\n                            }\n                        }\n                        // Set document language\n                        document.documentElement.lang = initialLanguage;\n                        // Load translations for the initial language\n                        try {\n                            const translationData = await (0,_translation_utils__WEBPACK_IMPORTED_MODULE_2__.loadTranslations)(initialLanguage);\n                            setTranslations(translationData);\n                        } catch (translationError) {\n                            console.error(`Failed to load ${initialLanguage} translations:`, translationError);\n                            setTranslations({});\n                        }\n                    } catch (error) {\n                        console.error(\"Error initializing language:\", error);\n                        // Use default language if initialization fails\n                        setLanguageState(\"fr\");\n                        document.documentElement.lang = \"fr\";\n                        setTranslations({});\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"LanguageProvider.useEffect.initializeLanguage\"];\n            initializeLanguage();\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        user\n    ]);\n    // Translation function\n    const translate = (key)=>{\n        // Return the translation if it exists, otherwise return the key\n        return translations[key] || key;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            translations,\n            setLanguage,\n            translate,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/lib/LanguageContext.tsx\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/LanguageContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/family-members/api.ts":
/*!***************************************!*\
  !*** ./src/lib/family-members/api.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteFamilyMember: () => (/* binding */ deleteFamilyMember),\n/* harmony export */   getFamilyMembers: () => (/* binding */ getFamilyMembers),\n/* harmony export */   saveAllFamilyMembers: () => (/* binding */ saveAllFamilyMembers),\n/* harmony export */   saveFamilyMember: () => (/* binding */ saveFamilyMember)\n/* harmony export */ });\n/* harmony import */ var _supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ getFamilyMembers,saveFamilyMember,saveAllFamilyMembers,deleteFamilyMember auto */ \n/**\n * Fetch all family members for a user\n * @param userId The user's ID\n * @returns Promise with array of family members (always 5 slots)\n */ async function getFamilyMembers(userId) {\n    try {\n        const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"family_members\").select(\"*\").eq(\"user_id\", userId).order(\"position\", {\n            ascending: true\n        });\n        if (error) {\n            throw error;\n        }\n        // Convert database records to application format\n        const existingMembers = data.map((item)=>({\n                id: item.position,\n                firstName: item.first_name || \"\",\n                lastName: item.last_name || \"\",\n                healthCard: item.health_card || \"\",\n                birthDate: item.birth_date ? new Date(item.birth_date) : undefined,\n                editing: false,\n                supabaseId: item.id\n            }));\n        // Ensure we always have 5 slots by creating a result array with 5 elements\n        const result = [];\n        // Fill slots 1-5\n        for(let i = 1; i <= 5; i++){\n            // Find if we have an existing member for this position\n            const existingMember = existingMembers.find((m)=>m.id === i);\n            if (existingMember) {\n                // Use the existing member\n                result.push(existingMember);\n            } else {\n                // Create an empty slot\n                result.push({\n                    id: i,\n                    firstName: \"\",\n                    lastName: \"\",\n                    healthCard: \"\",\n                    birthDate: undefined,\n                    editing: false\n                });\n            }\n        }\n        return result;\n    } catch (error) {\n        console.error(\"Error fetching family members:\", error);\n        throw error;\n    }\n}\n/**\n * Save a single family member to Supabase\n * @param userId The user's ID\n * @param member The family member to save\n * @param position The position in the list (1-5)\n * @returns Promise with the result\n */ async function saveFamilyMember(userId, member, position) {\n    try {\n        const memberData = {\n            user_id: userId,\n            first_name: member.firstName,\n            last_name: member.lastName,\n            health_card: member.healthCard,\n            birth_date: member.birthDate?.toISOString() || null,\n            position\n        };\n        // If we have a Supabase ID, update the record\n        if (member.supabaseId) {\n            const { error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"family_members\").update(memberData).eq(\"id\", member.supabaseId);\n            if (error) throw error;\n            return {\n                success: true,\n                id: member.supabaseId\n            };\n        } else {\n            // Otherwise, insert a new record\n            const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"family_members\").insert(memberData).select(\"id\").single();\n            if (error) throw error;\n            return {\n                success: true,\n                id: data.id\n            };\n        }\n    } catch (error) {\n        console.error(\"Error saving family member:\", error);\n        return {\n            success: false\n        };\n    }\n}\n/**\n * Save all family members for a user\n * @param userId The user's ID\n * @param members Array of family members\n * @returns Promise with the result\n */ async function saveAllFamilyMembers(userId, members) {\n    try {\n        // Process members with data only (non-empty)\n        const membersWithData = members.filter((member)=>member.firstName || member.lastName || member.healthCard || member.birthDate);\n        // Save each member\n        const results = await Promise.all(membersWithData.map((member, index)=>saveFamilyMember(userId, member, index + 1)));\n        // Check if all saves were successful\n        const allSuccess = results.every((result)=>result.success);\n        return {\n            success: allSuccess\n        };\n    } catch (error) {\n        console.error(\"Error saving all family members:\", error);\n        return {\n            success: false\n        };\n    }\n}\n/**\n * Delete a family member\n * @param memberId The family member's Supabase ID\n * @returns Promise with the result\n */ async function deleteFamilyMember(memberId) {\n    try {\n        const { error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"family_members\").delete().eq(\"id\", memberId);\n        if (error) throw error;\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error(\"Error deleting family member:\", error);\n        return {\n            success: false\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/family-members/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/profile-update-context.tsx":
/*!********************************************!*\
  !*** ./src/lib/profile-update-context.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfileUpdateProvider: () => (/* binding */ ProfileUpdateProvider),\n/* harmony export */   useProfileUpdates: () => (/* binding */ useProfileUpdates)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ProfileUpdateProvider,useProfileUpdates auto */ \n\nconst ProfileUpdateContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    notifyProfileUpdate: ()=>{},\n    lastUpdatedUserId: null,\n    lastUpdateTimestamp: 0\n});\n/**\n * Provider component for profile update notifications\n */ function ProfileUpdateProvider({ children }) {\n    const [lastUpdatedUserId, setLastUpdatedUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lastUpdateTimestamp, setLastUpdateTimestamp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const notifyProfileUpdate = (userId)=>{\n        setLastUpdatedUserId(userId);\n        setLastUpdateTimestamp(Date.now());\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProfileUpdateContext.Provider, {\n        value: {\n            notifyProfileUpdate,\n            lastUpdatedUserId,\n            lastUpdateTimestamp\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/src/SRVXP/src/lib/profile-update-context.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Hook to access the profile update context\n */ const useProfileUpdates = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ProfileUpdateContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/profile-update-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/../SRVXP_dependencies/node_modules/@supabase/ssr/dist/module/index.js\");\n\nconst supabaseUrl = \"https://tfvswgreslsbctjrvdbd.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRmdnN3Z3Jlc2xzYmN0anJ2ZGJkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIzMjEyODUsImV4cCI6MjA1Nzg5NzI4NX0.5bNos8JS_77EUjWOxIGHpgNaYE28qQvO2g4qj0wRWiU\" || 0;\nif (false) {}\n// Cookie configuration optimized for OAuth session persistence\nconst getCookieOptions = ()=>{\n    if (true) return {};\n    return {\n        cookies: {\n            getAll () {\n                if (typeof document === 'undefined' || \"undefined\" === 'undefined') return [];\n                try {\n                    return document.cookie.split(';').map((cookie)=>cookie.trim().split('=')).filter(([name])=>name).map(([name, value])=>({\n                            name,\n                            value: decodeURIComponent(value || '')\n                        }));\n                } catch (error) {\n                    console.warn('Error reading cookies:', error);\n                    return [];\n                }\n            },\n            setAll (cookiesToSet) {\n                if (typeof document === 'undefined' || \"undefined\" === 'undefined') return;\n                try {\n                    cookiesToSet.forEach(({ name, value, options = {} })=>{\n                        const cookieOptions = {\n                            path: '/',\n                            maxAge: 60 * 60 * 24 * 30,\n                            sameSite: 'lax',\n                            secure: \"development\" === 'production',\n                            ...options\n                        };\n                        const cookieString = [\n                            `${name}=${encodeURIComponent(value)}`,\n                            `Path=${cookieOptions.path}`,\n                            `Max-Age=${cookieOptions.maxAge}`,\n                            `SameSite=${cookieOptions.sameSite}`,\n                            cookieOptions.secure ? 'Secure' : '',\n                            cookieOptions.domain ? `Domain=${cookieOptions.domain}` : ''\n                        ].filter(Boolean).join('; ');\n                        document.cookie = cookieString;\n                    });\n                } catch (error) {\n                    console.warn('Error setting cookies:', error);\n                }\n            }\n        }\n    };\n};\n// Create browser client with proper cookie handling for OAuth\nconst supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, getCookieOptions());\n// Export a function to create a new client (useful for hooks)\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, getCookieOptions());\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUQ7QUFFbkQsTUFBTUMsY0FBY0MsMENBQW9DLElBQUksQ0FBaUM7QUFDN0YsTUFBTUcsa0JBQWtCSCxrTkFBeUMsSUFBSSxDQUFpQjtBQUV0RixJQUFJLEtBQW1GLEVBQUUsRUFFeEY7QUFFRCwrREFBK0Q7QUFDL0QsTUFBTU8sbUJBQW1CO0lBQ3ZCLElBQUksSUFBNkIsRUFBRSxPQUFPLENBQUM7SUFFM0MsT0FBTztRQUNMQyxTQUFTO1lBQ1BDO2dCQUNFLElBQUksT0FBT0MsYUFBYSxlQUFlLGdCQUFrQixhQUFhLE9BQU8sRUFBRTtnQkFDL0UsSUFBSTtvQkFDRixPQUFPQSxTQUFTQyxNQUFNLENBQ25CQyxLQUFLLENBQUMsS0FDTkMsR0FBRyxDQUFDRixDQUFBQSxTQUFVQSxPQUFPRyxJQUFJLEdBQUdGLEtBQUssQ0FBQyxNQUNsQ0csTUFBTSxDQUFDLENBQUMsQ0FBQ0MsS0FBSyxHQUFLQSxNQUNuQkgsR0FBRyxDQUFDLENBQUMsQ0FBQ0csTUFBTUMsTUFBTSxHQUFNOzRCQUFFRDs0QkFBTUMsT0FBT0MsbUJBQW1CRCxTQUFTO3dCQUFJO2dCQUM1RSxFQUFFLE9BQU9FLE9BQU87b0JBQ2RkLFFBQVFDLElBQUksQ0FBQywwQkFBMEJhO29CQUN2QyxPQUFPLEVBQUU7Z0JBQ1g7WUFDRjtZQUNBQyxRQUFPQyxZQUFtRTtnQkFDeEUsSUFBSSxPQUFPWCxhQUFhLGVBQWUsZ0JBQWtCLGFBQWE7Z0JBQ3RFLElBQUk7b0JBQ0ZXLGFBQWFDLE9BQU8sQ0FBQyxDQUFDLEVBQUVOLElBQUksRUFBRUMsS0FBSyxFQUFFTSxVQUFVLENBQUMsQ0FBQyxFQUFFO3dCQUNqRCxNQUFNQyxnQkFBZ0I7NEJBQ3BCQyxNQUFNOzRCQUNOQyxRQUFRLEtBQUssS0FBSyxLQUFLOzRCQUN2QkMsVUFBVTs0QkFDVkMsUUFBUTVCLGtCQUF5Qjs0QkFDakMsR0FBR3VCLE9BQU87d0JBQ1o7d0JBRUEsTUFBTU0sZUFBZTs0QkFDbkIsR0FBR2IsS0FBSyxDQUFDLEVBQUVjLG1CQUFtQmIsUUFBUTs0QkFDdEMsQ0FBQyxLQUFLLEVBQUVPLGNBQWNDLElBQUksRUFBRTs0QkFDNUIsQ0FBQyxRQUFRLEVBQUVELGNBQWNFLE1BQU0sRUFBRTs0QkFDakMsQ0FBQyxTQUFTLEVBQUVGLGNBQWNHLFFBQVEsRUFBRTs0QkFDcENILGNBQWNJLE1BQU0sR0FBRyxXQUFXOzRCQUNsQ0osY0FBY08sTUFBTSxHQUFHLENBQUMsT0FBTyxFQUFFUCxjQUFjTyxNQUFNLEVBQUUsR0FBRzt5QkFDM0QsQ0FBQ2hCLE1BQU0sQ0FBQ2lCLFNBQVNDLElBQUksQ0FBQzt3QkFFdkJ2QixTQUFTQyxNQUFNLEdBQUdrQjtvQkFDcEI7Z0JBQ0YsRUFBRSxPQUFPVixPQUFPO29CQUNkZCxRQUFRQyxJQUFJLENBQUMsMEJBQTBCYTtnQkFDekM7WUFDRjtRQUNGO0lBQ0Y7QUFDRjtBQUVBLDhEQUE4RDtBQUN2RCxNQUFNZSxXQUFXcEMsa0VBQW1CQSxDQUFDQyxhQUFhSSxpQkFBaUJJLG9CQUFtQjtBQUU3Riw4REFBOEQ7QUFDdkQsU0FBUzRCO0lBQ2QsT0FBT3JDLGtFQUFtQkEsQ0FBQ0MsYUFBYUksaUJBQWlCSTtBQUMzRCIsInNvdXJjZXMiOlsiL1VzZXJzL2FtaWpldHRlL3NyYy9TUlZYUC9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVCcm93c2VyQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3NzcidcblxuY29uc3Qgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwgfHwgJ2h0dHBzOi8vcGxhY2Vob2xkZXIuc3VwYWJhc2UuY28nXG5jb25zdCBzdXBhYmFzZUFub25LZXkgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSB8fCAncGxhY2Vob2xkZXIta2V5J1xuXG5pZiAoIXByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCB8fCAhcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkpIHtcbiAgY29uc29sZS53YXJuKCdXYXJuaW5nOiBTdXBhYmFzZSBlbnZpcm9ubWVudCB2YXJpYWJsZXMgYXJlIG1pc3NpbmcuIFVzaW5nIHBsYWNlaG9sZGVyIHZhbHVlcy4nKVxufVxuXG4vLyBDb29raWUgY29uZmlndXJhdGlvbiBvcHRpbWl6ZWQgZm9yIE9BdXRoIHNlc3Npb24gcGVyc2lzdGVuY2VcbmNvbnN0IGdldENvb2tpZU9wdGlvbnMgPSAoKSA9PiB7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIHt9XG5cbiAgcmV0dXJuIHtcbiAgICBjb29raWVzOiB7XG4gICAgICBnZXRBbGwoKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZG9jdW1lbnQgPT09ICd1bmRlZmluZWQnIHx8IHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gW11cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICByZXR1cm4gZG9jdW1lbnQuY29va2llXG4gICAgICAgICAgICAuc3BsaXQoJzsnKVxuICAgICAgICAgICAgLm1hcChjb29raWUgPT4gY29va2llLnRyaW0oKS5zcGxpdCgnPScpKVxuICAgICAgICAgICAgLmZpbHRlcigoW25hbWVdKSA9PiBuYW1lKVxuICAgICAgICAgICAgLm1hcCgoW25hbWUsIHZhbHVlXSkgPT4gKHsgbmFtZSwgdmFsdWU6IGRlY29kZVVSSUNvbXBvbmVudCh2YWx1ZSB8fCAnJykgfSkpXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCdFcnJvciByZWFkaW5nIGNvb2tpZXM6JywgZXJyb3IpXG4gICAgICAgICAgcmV0dXJuIFtdXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICBzZXRBbGwoY29va2llc1RvU2V0OiBBcnJheTx7IG5hbWU6IHN0cmluZzsgdmFsdWU6IHN0cmluZzsgb3B0aW9ucz86IGFueSB9Pikge1xuICAgICAgICBpZiAodHlwZW9mIGRvY3VtZW50ID09PSAndW5kZWZpbmVkJyB8fCB0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29va2llc1RvU2V0LmZvckVhY2goKHsgbmFtZSwgdmFsdWUsIG9wdGlvbnMgPSB7fSB9KSA9PiB7XG4gICAgICAgICAgICBjb25zdCBjb29raWVPcHRpb25zID0ge1xuICAgICAgICAgICAgICBwYXRoOiAnLycsXG4gICAgICAgICAgICAgIG1heEFnZTogNjAgKiA2MCAqIDI0ICogMzAsIC8vIDMwIGRheXNcbiAgICAgICAgICAgICAgc2FtZVNpdGU6ICdsYXgnLFxuICAgICAgICAgICAgICBzZWN1cmU6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicsXG4gICAgICAgICAgICAgIC4uLm9wdGlvbnNcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgY29uc3QgY29va2llU3RyaW5nID0gW1xuICAgICAgICAgICAgICBgJHtuYW1lfT0ke2VuY29kZVVSSUNvbXBvbmVudCh2YWx1ZSl9YCxcbiAgICAgICAgICAgICAgYFBhdGg9JHtjb29raWVPcHRpb25zLnBhdGh9YCxcbiAgICAgICAgICAgICAgYE1heC1BZ2U9JHtjb29raWVPcHRpb25zLm1heEFnZX1gLFxuICAgICAgICAgICAgICBgU2FtZVNpdGU9JHtjb29raWVPcHRpb25zLnNhbWVTaXRlfWAsXG4gICAgICAgICAgICAgIGNvb2tpZU9wdGlvbnMuc2VjdXJlID8gJ1NlY3VyZScgOiAnJyxcbiAgICAgICAgICAgICAgY29va2llT3B0aW9ucy5kb21haW4gPyBgRG9tYWluPSR7Y29va2llT3B0aW9ucy5kb21haW59YCA6ICcnXG4gICAgICAgICAgICBdLmZpbHRlcihCb29sZWFuKS5qb2luKCc7ICcpXG5cbiAgICAgICAgICAgIGRvY3VtZW50LmNvb2tpZSA9IGNvb2tpZVN0cmluZ1xuICAgICAgICAgIH0pXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCdFcnJvciBzZXR0aW5nIGNvb2tpZXM6JywgZXJyb3IpXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgfSxcbiAgfVxufVxuXG4vLyBDcmVhdGUgYnJvd3NlciBjbGllbnQgd2l0aCBwcm9wZXIgY29va2llIGhhbmRsaW5nIGZvciBPQXV0aFxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQnJvd3NlckNsaWVudChzdXBhYmFzZVVybCwgc3VwYWJhc2VBbm9uS2V5LCBnZXRDb29raWVPcHRpb25zKCkpXG5cbi8vIEV4cG9ydCBhIGZ1bmN0aW9uIHRvIGNyZWF0ZSBhIG5ldyBjbGllbnQgKHVzZWZ1bCBmb3IgaG9va3MpXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ2xpZW50KCkge1xuICByZXR1cm4gY3JlYXRlQnJvd3NlckNsaWVudChzdXBhYmFzZVVybCwgc3VwYWJhc2VBbm9uS2V5LCBnZXRDb29raWVPcHRpb25zKCkpXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQnJvd3NlckNsaWVudCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiY29uc29sZSIsIndhcm4iLCJnZXRDb29raWVPcHRpb25zIiwiY29va2llcyIsImdldEFsbCIsImRvY3VtZW50IiwiY29va2llIiwic3BsaXQiLCJtYXAiLCJ0cmltIiwiZmlsdGVyIiwibmFtZSIsInZhbHVlIiwiZGVjb2RlVVJJQ29tcG9uZW50IiwiZXJyb3IiLCJzZXRBbGwiLCJjb29raWVzVG9TZXQiLCJmb3JFYWNoIiwib3B0aW9ucyIsImNvb2tpZU9wdGlvbnMiLCJwYXRoIiwibWF4QWdlIiwic2FtZVNpdGUiLCJzZWN1cmUiLCJjb29raWVTdHJpbmciLCJlbmNvZGVVUklDb21wb25lbnQiLCJkb21haW4iLCJCb29sZWFuIiwiam9pbiIsInN1cGFiYXNlIiwiY3JlYXRlQ2xpZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/translation-utils.ts":
/*!**************************************!*\
  !*** ./src/lib/translation-utils.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearTranslationCache: () => (/* binding */ clearTranslationCache),\n/* harmony export */   createTranslationKey: () => (/* binding */ createTranslationKey),\n/* harmony export */   loadTranslations: () => (/* binding */ loadTranslations)\n/* harmony export */ });\n// Utility functions for translation management with lazy loading\n// Cache for translations\nlet translationCache = {\n    en: null,\n    fr: null\n};\n/**\n * Load translation file asynchronously\n * @param lang - The language to load\n * @returns Promise resolving to a dictionary of translations\n */ async function loadTranslations(lang) {\n    // Return from cache if available\n    if (translationCache[lang]) {\n        return translationCache[lang];\n    }\n    try {\n        // Fetch the translation file\n        const response = await fetch(`/translations/${lang}.json`);\n        if (!response.ok) {\n            throw new Error(`Failed to load ${lang} translations: ${response.statusText}`);\n        }\n        const translations = await response.json();\n        // Cache the translations\n        translationCache[lang] = translations;\n        return translations;\n    } catch (error) {\n        console.error(`Error loading ${lang} translations:`, error);\n        // Return empty object in case of error\n        return {};\n    }\n}\n/**\n * Clear the translation cache for testing or forced reloads\n * @param lang - Optional language to clear, or all if not specified\n */ function clearTranslationCache(lang) {\n    if (lang) {\n        translationCache[lang] = null;\n    } else {\n        translationCache = {\n            en: null,\n            fr: null\n        };\n    }\n}\n/**\n * Transform nested key path into flat key\n * @param section - The section of the translation\n * @param key - The key within the section\n * @returns A flat key in the format \"section.key\"\n */ function createTranslationKey(section, key) {\n    return `${section}.${key}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/translation-utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/translations.ts":
/*!*********************************!*\
  !*** ./src/lib/translations.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   translationKeys: () => (/* binding */ translationKeys)\n/* harmony export */ });\n/* harmony import */ var _translation_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./translation-utils */ \"(ssr)/./src/lib/translation-utils.ts\");\n// This file contains type definitions and keys for translations\n// Define language type\n\n// Create a keys object for consistent key access across the app\nconst translationKeys = {\n    errors: {\n        tryAgainLater: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('errors', 'tryAgainLater'),\n        saveFailed: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('errors', 'saveFailed'),\n        notFound: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('errors', 'notFound'),\n        unauthorized: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('errors', 'unauthorized'),\n        generalError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('errors', 'generalError')\n    },\n    common: {\n        save: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'save'),\n        cancel: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'cancel'),\n        confirm: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'confirm'),\n        delete: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'delete'),\n        loading: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'loading'),\n        search: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'search'),\n        logout: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'logout'),\n        loggingOut: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'loggingOut'),\n        close: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'close'),\n        active: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'active'),\n        inactive: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'inactive'),\n        submit: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'submit'),\n        submitting: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'submitting'),\n        processing: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'processing'),\n        newRequest: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'newRequest'),\n        required: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'required'),\n        yes: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'yes'),\n        no: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'no'),\n        continue: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'continue'),\n        manage: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'manage'),\n        modify: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'modify'),\n        back: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'back'),\n        saved: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'saved'),\n        saving: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'saving'),\n        saveChanges: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'saveChanges'),\n        errorOccurred: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('common', 'errorOccurred')\n    },\n    auth: {\n        signIn: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'signIn'),\n        signUp: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'signUp'),\n        email: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'email'),\n        password: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'password'),\n        confirmPassword: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'confirmPassword'),\n        forgotPassword: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'forgotPassword'),\n        resetPassword: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'resetPassword'),\n        enterCredentials: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'enterCredentials'),\n        createAccount: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'createAccount'),\n        alreadyHaveAccount: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'alreadyHaveAccount'),\n        noAccount: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'noAccount'),\n        passwordReset: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'passwordReset'),\n        passwordResetInstructions: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'passwordResetInstructions'),\n        passwordResetSent: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'passwordResetSent'),\n        successfulReset: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'successfulReset'),\n        firstName: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'firstName'),\n        lastName: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth', 'lastName'),\n        errors: {\n            invalidCredentials: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth.errors', 'invalidCredentials'),\n            passwordsDontMatch: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth.errors', 'passwordsDontMatch'),\n            emailInUse: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth.errors', 'emailInUse'),\n            invalidEmail: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth.errors', 'invalidEmail'),\n            passwordTooShort: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth.errors', 'passwordTooShort'),\n            generalError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('auth.errors', 'generalError')\n        }\n    },\n    nav: {\n        home: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'home'),\n        dashboard: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'dashboard'),\n        appointments: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'appointments'),\n        findAppointment: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'findAppointment'),\n        calendar: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'calendar'),\n        help: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'help'),\n        account: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'account'),\n        mobileNavigation: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'mobileNavigation'),\n        needHelp: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('nav', 'needHelp')\n    },\n    account: {\n        profile: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'profile'),\n        preferences: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'preferences'),\n        subscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'subscription'),\n        users: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'users'),\n        manageUsers: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageUsers'),\n        yourAccount: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'yourAccount'),\n        individualPlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'individualPlan'),\n        individualPlanMonthly: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'individualPlanMonthly'),\n        individualPlanAnnual: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'individualPlanAnnual'),\n        familyPlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'familyPlan'),\n        familyPlanMonthly: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'familyPlanMonthly'),\n        familyPlanAnnual: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'familyPlanAnnual'),\n        manageInformation: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageInformation'),\n        personalInfoDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'personalInfoDescription'),\n        modifyProfile: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'modifyProfile'),\n        modifySubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'modifySubscription'),\n        subscriptionDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'subscriptionDescription'),\n        manageSubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageSubscription'),\n        appearanceLanguage: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'appearanceLanguage'),\n        appearanceDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'appearanceDescription'),\n        modifyPreferences: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'modifyPreferences'),\n        manageAccountUsers: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageAccountUsers'),\n        manageUsersDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageUsersDescription'),\n        manageProfile: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageProfile'),\n        manageProfileDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageProfileDescription'),\n        manageProfileButton: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'manageProfileButton'),\n        subscribePlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'subscribePlan'),\n        choosePlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'choosePlan'),\n        editPersonalInfo: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'editPersonalInfo'),\n        firstName: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'firstName'),\n        lastName: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'lastName'),\n        email: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'email'),\n        phone: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'phone'),\n        invalidEmail: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'invalidEmail'),\n        invalidPhone: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'invalidPhone'),\n        emailCannotBeEmpty: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'emailCannotBeEmpty'),\n        firstNameRequired: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'firstNameRequired'),\n        lastNameRequired: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'lastNameRequired'),\n        emailVerificationSent: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('account', 'emailVerificationSent')\n    },\n    home: {\n        greeting: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'greeting'),\n        welcome: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'welcome'),\n        findAppointmentTitle: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'findAppointmentTitle'),\n        findAppointmentDesc: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'findAppointmentDesc'),\n        viewRequests: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'viewRequests'),\n        manageAppointmentsDesc: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'manageAppointmentsDesc'),\n        manageUsersDesc: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'manageUsersDesc'),\n        manageProfileTitle: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'manageProfileTitle'),\n        manageProfileDesc: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'manageProfileDesc'),\n        manageProfileButton: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('home', 'manageProfileButton')\n    },\n    appointments: {\n        title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'title'),\n        description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'description'),\n        requestsTitle: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'requestsTitle'),\n        all: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'all'),\n        inProgress: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'inProgress'),\n        completed: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'completed'),\n        noRequests: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'noRequests'),\n        noRequestsInProgress: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'noRequestsInProgress'),\n        noRequestsCompleted: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'noRequestsCompleted'),\n        noRequestsCancelled: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'noRequestsCancelled'),\n        postalCode: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'postalCode'),\n        sentOn: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'sentOn'),\n        pending: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'pending'),\n        done: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'done'),\n        cancelAppointment: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'cancelAppointment'),\n        cancelConfirmation: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'cancelConfirmation'),\n        cancelConfirmationText: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'cancelConfirmationText'),\n        noContinue: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'noContinue'),\n        yesCancel: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'yesCancel'),\n        viewAll: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('appointments', 'viewAll')\n    },\n    meta: {\n        title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('meta', 'title'),\n        description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('meta', 'description')\n    },\n    subscription: {\n        modifySubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'modifySubscription'),\n        individualPlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'individualPlan'),\n        monthlyCost: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'monthlyCost'),\n        benefits: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'benefits'),\n        unlimitedAccess: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'unlimitedAccess'),\n        emailNotifications: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'emailNotifications'),\n        familyProfiles: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'familyProfiles'),\n        modifyPlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'modifyPlan'),\n        cancelPlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'cancelPlan'),\n        paymentHistory: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'paymentHistory'),\n        monthlySubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'monthlySubscription'),\n        march: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'march'),\n        february: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'february'),\n        january: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'january'),\n        cost: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'cost'),\n        changePlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'changePlan'),\n        changePlanDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'changePlanDescription'),\n        confirmChange: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'confirmChange'),\n        cancelConfirmation: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'cancelConfirmation'),\n        cancelWarning: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'cancelWarning'),\n        yesCancel: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'yesCancel'),\n        noCancel: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'noCancel'),\n        // Success page translations\n        status: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'status'),\n        verifyingPayment: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'verifyingPayment'),\n        success: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'success'),\n        successMessage: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'successMessage'),\n        details: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'details'),\n        plan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'plan'),\n        billing: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'billing'),\n        amount: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'amount'),\n        currentPeriod: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'currentPeriod'),\n        nextSteps: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'nextSteps'),\n        goToDashboard: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'goToDashboard'),\n        manageAccount: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'manageAccount'),\n        error: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'error'),\n        errorMessage: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'errorMessage'),\n        needHelp: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'needHelp'),\n        returnToPlans: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'returnToPlans'),\n        contactSupport: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'contactSupport'),\n        canceledCheckout: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'canceledCheckout'),\n        processingSubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'processingSubscription'),\n        noSessionId: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'noSessionId'),\n        notLoggedIn: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('subscription', 'notLoggedIn')\n    },\n    preferences: {\n        managePreferences: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'managePreferences'),\n        languageAppearance: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'languageAppearance'),\n        customizeInterface: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'customizeInterface'),\n        preferredLanguage: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'preferredLanguage'),\n        french: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'french'),\n        english: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'english'),\n        languageDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'languageDescription'),\n        appTheme: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'appTheme'),\n        light: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'light'),\n        dark: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'dark'),\n        themeDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'themeDescription'),\n        saveChanges: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'saveChanges'),\n        saving: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'saving'),\n        changesSaved: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'changesSaved'),\n        errorSaving: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('preferences', 'errorSaving')\n    },\n    users: {\n        manageAccountUsers: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'manageAccountUsers'),\n        manageProfile: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'manageProfile'),\n        familyMembers: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'familyMembers'),\n        userProfile: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'userProfile'),\n        familyMembersDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'familyMembersDescription'),\n        userProfileDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'userProfileDescription'),\n        addYourInfo: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'addYourInfo'),\n        addYourInfoPrompt: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'addYourInfoPrompt'),\n        cancel: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'cancel'),\n        firstName: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'firstName'),\n        lastName: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'lastName'),\n        healthCardPrefix: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'healthCardPrefix'),\n        healthCardDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'healthCardDescription'),\n        birthDate: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'birthDate'),\n        save: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'save'),\n        edit: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'edit'),\n        healthCard: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'healthCard'),\n        addMember: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'addMember'),\n        editMemberPrompt: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'editMemberPrompt'),\n        selectDate: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'selectDate'),\n        validationError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('users', 'validationError')\n    },\n    help: {\n        needHelp: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'needHelp'),\n        helpDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'helpDescription'),\n        faq: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'faq'),\n        faqDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'faqDescription'),\n        howToBookAppointment: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'howToBookAppointment'),\n        howToBookDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'howToBookDescription'),\n        howToCancelAppointment: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'howToCancelAppointment'),\n        howToCancelDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'howToCancelDescription'),\n        howToChangePlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'howToChangePlan'),\n        howToChangePlanDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'howToChangePlanDescription'),\n        customerSupport: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'customerSupport'),\n        supportDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'supportDescription'),\n        email: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'email'),\n        supportEmail: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'supportEmail'),\n        responseTime: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'responseTime'),\n        contactSupport: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('help', 'contactSupport')\n    },\n    landing: {\n        hero: {\n            title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.hero', 'title'),\n            subtitle: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.hero', 'subtitle'),\n            findAppointment: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.hero', 'findAppointment'),\n            learnMore: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.hero', 'learnMore'),\n            imageAlt: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.hero', 'imageAlt')\n        },\n        features: {\n            sameDay: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.features.sameDay', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.features.sameDay', 'description')\n            },\n            nearbyClinic: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.features.nearbyClinic', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.features.nearbyClinic', 'description')\n            },\n            anywhereInQuebec: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.features.anywhereInQuebec', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.features.anywhereInQuebec', 'description')\n            }\n        },\n        howItWorks: {\n            title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.howItWorks', 'title'),\n            customAppointments: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.howItWorks.customAppointments', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.howItWorks.customAppointments', 'description')\n            },\n            easyManagement: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.howItWorks.easyManagement', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.howItWorks.easyManagement', 'description')\n            },\n            imageAlt: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.howItWorks', 'imageAlt')\n        },\n        pricing: {\n            title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'title'),\n            description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'description'),\n            period: {\n                monthly: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.period', 'monthly'),\n                annually: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.period', 'annually')\n            },\n            individual: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.individual', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.individual', 'description'),\n                features: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.individual', 'features'),\n                annualSavings: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.individual', 'annualSavings')\n            },\n            family: {\n                title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.family', 'title'),\n                description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.family', 'description'),\n                features: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.family', 'features'),\n                annualSavings: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing.family', 'annualSavings')\n            },\n            choosePlan: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'choosePlan'),\n            included: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'included'),\n            manageSubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'manageSubscription'),\n            feature1: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'feature1'),\n            feature2: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'feature2'),\n            feature3: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'feature3'),\n            feature4: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.pricing', 'feature4')\n        },\n        faq: {\n            title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq', 'title'),\n            viewFullFaq: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq', 'viewFullFaq'),\n            questions: [\n                {\n                    question: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.0', 'question'),\n                    answer: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.0', 'answer')\n                },\n                {\n                    question: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.1', 'question'),\n                    answer: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.1', 'answer')\n                },\n                {\n                    question: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.2', 'question'),\n                    answer: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.2', 'answer')\n                },\n                {\n                    question: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.3', 'question'),\n                    answer: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.3', 'answer')\n                },\n                {\n                    question: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.4', 'question'),\n                    answer: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.faq.questions.4', 'answer')\n                }\n            ]\n        },\n        cta: {\n            title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.cta', 'title'),\n            subtitle: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.cta', 'subtitle'),\n            buttonText: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.cta', 'buttonText'),\n            imageAlt: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.cta', 'imageAlt')\n        },\n        navbar: {\n            title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.navbar', 'title'),\n            signIn: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.navbar', 'signIn'),\n            signUp: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.navbar', 'signUp'),\n            service: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.navbar', 'service'),\n            pricing: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.navbar', 'pricing'),\n            faq: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.navbar', 'faq')\n        },\n        footer: {\n            description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.footer', 'description'),\n            contactUs: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.footer', 'contactUs'),\n            privacyPolicy: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.footer', 'privacyPolicy'),\n            termsOfUse: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.footer', 'termsOfUse'),\n            termsOfSale: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.footer', 'termsOfSale'),\n            copyright: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('landing.footer', 'copyright')\n        }\n    },\n    findAppointment: {\n        title: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'title'),\n        description: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'description'),\n        searchCriteria: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'searchCriteria'),\n        requiredFields: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'requiredFields'),\n        appointmentFor: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'appointmentFor'),\n        selectPerson: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'selectPerson'),\n        managedInUsersSection: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'managedInUsersSection'),\n        healthCard: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'healthCard'),\n        healthCardOf: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'healthCardOf'),\n        lastDigits: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'lastDigits'),\n        enterEightDigits: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'enterEightDigits'),\n        format: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'format'),\n        sequenceNumber: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'sequenceNumber'),\n        sequenceInfo: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'sequenceInfo'),\n        enterTwoDigits: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'enterTwoDigits'),\n        postalCode: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'postalCode'),\n        postalExample: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'postalExample'),\n        invalidPostalFormat: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'invalidPostalFormat'),\n        postalFormatWarning: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'postalFormatWarning'),\n        postalCodeDescription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'postalCodeDescription'),\n        fromDate: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'fromDate'),\n        selectDate: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'selectDate'),\n        appointmentTime: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'appointmentTime'),\n        chooseTime: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'chooseTime'),\n        morning: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'morning'),\n        afternoon: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'afternoon'),\n        evening: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'evening'),\n        asap: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'asap'),\n        submitRequest: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'submitRequest'),\n        thankYou: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'thankYou'),\n        confirmationMessage: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'confirmationMessage'),\n        viewRequests: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'viewRequests'),\n        // Form validation messages\n        selectDateError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'selectDateError'),\n        selectTimeError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'selectTimeError'),\n        enterPostalError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'enterPostalError'),\n        invalidPostalError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'invalidPostalError'),\n        selectPersonError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'selectPersonError'),\n        healthCardDigitsError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'healthCardDigitsError'),\n        sequenceNumberError: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'sequenceNumberError'),\n        noSubscription: (0,_translation_utils__WEBPACK_IMPORTED_MODULE_0__.createTranslationKey)('findAppointment', 'noSubscription')\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/translations.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/user-preferences-utils.ts":
/*!*******************************************!*\
  !*** ./src/lib/user-preferences-utils.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUserPreferences: () => (/* binding */ getUserPreferences),\n/* harmony export */   saveUserPreferences: () => (/* binding */ saveUserPreferences)\n/* harmony export */ });\n/* harmony import */ var _supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ saveUserPreferences,getUserPreferences auto */ \n/**\n * Saves the user's preferences to Supabase\n * \n * @param userId The user's ID\n * @param preferences The preferences to save\n * @returns Promise with the result of the operation\n */ async function saveUserPreferences(userId, preferences) {\n    try {\n        // Update preferences directly in the users table\n        const { error: updateError } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('users').update({\n            language: preferences.language,\n            theme: preferences.theme,\n            updated_at: new Date().toISOString()\n        }).eq('id', userId);\n        if (updateError) {\n            throw updateError;\n        }\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error(\"Error saving user preferences:\", error);\n        return {\n            success: false,\n            error: error\n        };\n    }\n}\nasync function getUserPreferences(userId) {\n    try {\n        // Query the users table for preferences\n        const { data, error } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('users').select('language, theme').eq('id', userId).maybeSingle();\n        if (error) {\n            throw error;\n        }\n        if (!data || !data.language && !data.theme) {\n            return {\n                language: \"fr\",\n                theme: \"light\"\n            };\n        }\n        return {\n            language: data.language || \"fr\",\n            theme: data.theme || \"light\"\n        };\n    } catch (error) {\n        console.error(\"Error getting user preferences:\", error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/user-preferences-utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatHealthCardNumber: () => (/* binding */ formatHealthCardNumber),\n/* harmony export */   isHealthCardComplete: () => (/* binding */ isHealthCardComplete)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../SRVXP_dependencies/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../SRVXP_dependencies/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Utility for parsing and validating health card inputs\nfunction formatHealthCardNumber(input, maxDigits = 8) {\n    // Remove all non-numeric characters\n    const digits = input.replace(/\\D/g, \"\").substring(0, maxDigits);\n    // Format as xxxx-xxxx if we have more than 4 digits\n    if (digits.length <= 4) {\n        return digits;\n    } else {\n        return `${digits.slice(0, 4)}-${digits.slice(4)}`;\n    }\n}\n// Check if health card number is complete (has the required number of digits)\nfunction isHealthCardComplete(input, requiredDigits = 8) {\n    const digits = input.replace(/\\D/g, \"\");\n    return digits.length === requiredDigits;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/zaply/hooks/useScrollReveal.ts":
/*!************************************************!*\
  !*** ./src/lib/zaply/hooks/useScrollReveal.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useScrollReveal: () => (/* binding */ useScrollReveal)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../SRVXP_dependencies/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useScrollReveal = (options = {})=>{\n    const [isIntersecting, setIsIntersecting] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { threshold = 0.1, rootMargin = '0px' } = options;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useScrollReveal.useEffect\": ()=>{\n            const observer = new IntersectionObserver({\n                \"useScrollReveal.useEffect\": ([entry])=>{\n                    if (entry.isIntersecting) {\n                        setIsIntersecting(true);\n                        // Once element is revealed, stop observing\n                        if (ref.current) {\n                            observer.unobserve(ref.current);\n                        }\n                    }\n                }\n            }[\"useScrollReveal.useEffect\"], {\n                threshold,\n                rootMargin\n            });\n            const currentRef = ref.current;\n            if (currentRef) {\n                observer.observe(currentRef);\n            }\n            return ({\n                \"useScrollReveal.useEffect\": ()=>{\n                    if (currentRef) {\n                        observer.unobserve(currentRef);\n                    }\n                }\n            })[\"useScrollReveal.useEffect\"];\n        }\n    }[\"useScrollReveal.useEffect\"], [\n        threshold,\n        rootMargin\n    ]);\n    return {\n        ref,\n        isIntersecting\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/zaply/hooks/useScrollReveal.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/zaply/utils.ts":
/*!********************************!*\
  !*** ./src/lib/zaply/utils.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../SRVXP_dependencies/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../SRVXP_dependencies/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3phcGx5L3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiL1VzZXJzL2FtaWpldHRlL3NyYy9TUlZYUC9zcmMvbGliL3phcGx5L3V0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/zaply/utils.ts\n");

/***/ })

};
;