globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/test-oauth/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/ClientBody.tsx":{"*":{"id":"(ssr)/./src/app/ClientBody.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/test-oauth/page.tsx":{"*":{"id":"(ssr)/./src/app/test-oauth/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/async-metadata.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/layout/Footer.tsx":{"*":{"id":"(ssr)/./src/components/zaply/layout/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/layout/Navbar.tsx":{"*":{"id":"(ssr)/./src/components/zaply/layout/Navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/sections/CtaSection2.tsx":{"*":{"id":"(ssr)/./src/components/zaply/sections/CtaSection2.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/sections/FaqSection.tsx":{"*":{"id":"(ssr)/./src/components/zaply/sections/FaqSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/sections/FeaturesSection.tsx":{"*":{"id":"(ssr)/./src/components/zaply/sections/FeaturesSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/sections/HeroSection.tsx":{"*":{"id":"(ssr)/./src/components/zaply/sections/HeroSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/sections/HowItWorksSection.tsx":{"*":{"id":"(ssr)/./src/components/zaply/sections/HowItWorksSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/sections/PricingSection.tsx":{"*":{"id":"(ssr)/./src/components/zaply/sections/PricingSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/LanguageContext.tsx":{"*":{"id":"(ssr)/./src/lib/LanguageContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/layout.tsx":{"*":{"id":"(ssr)/./src/app/auth/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/sign-in/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/sign-in/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","routes","static/chunks/routes.js","app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","routes","static/chunks/routes.js","app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/src/SRVXP/src/app/ClientBody.tsx":{"id":"(app-pages-browser)/./src/app/ClientBody.tsx","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","routes","static/chunks/routes.js","app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/src/SRVXP/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","routes","static/chunks/routes.js","app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/src/SRVXP/src/app/test-oauth/page.tsx":{"id":"(app-pages-browser)/./src/app/test-oauth/page.tsx","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","routes","static/chunks/routes.js","app/test-oauth/page","static/chunks/app/test-oauth/page.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/lib/metadata/async-metadata.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/esm/lib/metadata/async-metadata.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/lib/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP_dependencies/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/src/SRVXP/src/components/zaply/layout/Footer.tsx":{"id":"(app-pages-browser)/./src/components/zaply/layout/Footer.tsx","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/src/SRVXP/src/components/zaply/layout/Navbar.tsx":{"id":"(app-pages-browser)/./src/components/zaply/layout/Navbar.tsx","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/src/SRVXP/src/components/zaply/sections/CtaSection2.tsx":{"id":"(app-pages-browser)/./src/components/zaply/sections/CtaSection2.tsx","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/src/SRVXP/src/components/zaply/sections/FaqSection.tsx":{"id":"(app-pages-browser)/./src/components/zaply/sections/FaqSection.tsx","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/src/SRVXP/src/components/zaply/sections/FeaturesSection.tsx":{"id":"(app-pages-browser)/./src/components/zaply/sections/FeaturesSection.tsx","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/src/SRVXP/src/components/zaply/sections/HeroSection.tsx":{"id":"(app-pages-browser)/./src/components/zaply/sections/HeroSection.tsx","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/src/SRVXP/src/components/zaply/sections/HowItWorksSection.tsx":{"id":"(app-pages-browser)/./src/components/zaply/sections/HowItWorksSection.tsx","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/src/SRVXP/src/components/zaply/sections/PricingSection.tsx":{"id":"(app-pages-browser)/./src/components/zaply/sections/PricingSection.tsx","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/src/SRVXP/src/lib/LanguageContext.tsx":{"id":"(app-pages-browser)/./src/lib/LanguageContext.tsx","name":"*","chunks":["components","static/chunks/components.js","utils","static/chunks/utils.js","app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/src/SRVXP/src/app/auth/layout.tsx":{"id":"(app-pages-browser)/./src/app/auth/layout.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/src/SRVXP/src/app/auth/sign-in/page.tsx":{"id":"(app-pages-browser)/./src/app/auth/sign-in/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/src/SRVXP/src/":[],"/Users/<USER>/src/SRVXP/src/app/layout":[{"inlined":false,"path":"static/css/routes.css"},{"inlined":false,"path":"static/css/app/layout.css"}],"/Users/<USER>/src/SRVXP/src/app/page":[],"/Users/<USER>/src/SRVXP/src/app/test-oauth/page":[{"inlined":false,"path":"static/css/routes.css"}]},"rscModuleMapping":{"(app-pages-browser)/./src/app/ClientBody.tsx":{"*":{"id":"(rsc)/./src/app/ClientBody.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/test-oauth/page.tsx":{"*":{"id":"(rsc)/./src/app/test-oauth/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/../SRVXP_dependencies/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/../SRVXP_dependencies/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/../SRVXP_dependencies/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/../SRVXP_dependencies/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/async-metadata.js":{"*":{"id":"(rsc)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/layout/Footer.tsx":{"*":{"id":"(rsc)/./src/components/zaply/layout/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/layout/Navbar.tsx":{"*":{"id":"(rsc)/./src/components/zaply/layout/Navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/sections/CtaSection2.tsx":{"*":{"id":"(rsc)/./src/components/zaply/sections/CtaSection2.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/sections/FaqSection.tsx":{"*":{"id":"(rsc)/./src/components/zaply/sections/FaqSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/sections/FeaturesSection.tsx":{"*":{"id":"(rsc)/./src/components/zaply/sections/FeaturesSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/sections/HeroSection.tsx":{"*":{"id":"(rsc)/./src/components/zaply/sections/HeroSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/sections/HowItWorksSection.tsx":{"*":{"id":"(rsc)/./src/components/zaply/sections/HowItWorksSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/zaply/sections/PricingSection.tsx":{"*":{"id":"(rsc)/./src/components/zaply/sections/PricingSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/LanguageContext.tsx":{"*":{"id":"(rsc)/./src/lib/LanguageContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/layout.tsx":{"*":{"id":"(rsc)/./src/app/auth/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/sign-in/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/sign-in/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/async-metadata.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/../SRVXP_dependencies/node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}}}}